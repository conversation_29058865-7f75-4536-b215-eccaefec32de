<?php
/**
 * Controller para cancelar una cita
 * 
 * Este controlador recibe una solicitud AJAX para cancelar una cita,
 * estableciendo el estado a 0, la fecha_cancelacion al timestamp actual
 * y guardando la razón de cancelación proporcionada.
 */

session_start();

/** @var PDO $conexion */
global $conexion;

use App\classes\Cita;

require_once dirname(__FILE__, 4) . '/config/config.php';
require_once __ROOT__ . '/src/sessions/sessions.php';
require_once __ROOT__ . '/src/general/general.php';
require_once __ROOT__ . '/src/general/validaciones.php';
require_once __ROOT__ . '/src/general/preparar.php';
require_once __ROOT__ . '/vendor/autoload.php';

// Inicializar respuesta
$response = ['success' => false, 'message' => ''];

try {
    // Verificar que sea una solicitud POST
    if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
        throw new Exception('Método de solicitud no válido');
    }

    // Validar datos de entrada
    if (empty($_POST['id_cita']) || !is_numeric($_POST['id_cita'])) {
        throw new Exception('ID de cita inválido');
    }

    if (empty($_POST['razon_cancelacion'])) {
        throw new Exception('La razón de cancelación es obligatoria');
    }

    $id_cita = (int)$_POST['id_cita'];
    $razon_cancelacion = trim($_POST['razon_cancelacion']);

    // Verificar que la razón de cancelación no esté vacía después de quitar espacios
    if (empty($razon_cancelacion)) {
        throw new Exception('La razón de cancelación no puede estar vacía');
    }

    // Cancelar la cita
    $resultado = Cita::cancelar($id_cita, $razon_cancelacion, $conexion);

    if ($resultado) {
        $response['success'] = true;
        $response['message'] = 'Cita cancelada correctamente';
        $response['id_cita'] = $id_cita;
    } else {
        throw new Exception('Error al cancelar la cita');
    }

} catch (Exception $e) {
    $response['message'] = 'Error: ' . $e->getMessage();
}

// Devolver respuesta JSON
header('Content-Type: application/json');
echo json_encode($response);
exit;
