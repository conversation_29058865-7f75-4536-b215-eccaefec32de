<?php
/**
 * Controller para finalizar una cita
 *
 * Este controlador recibe una solicitud AJAX para finalizar una cita,
 * estableciendo la fecha_fin al timestamp actual y el método de pago.
 */

session_start();

/** @var PDO $conexion */
global $conexion;

use App\classes\Cita;

require_once dirname(__FILE__, 4) . '/config/config.php';   
require_once __ROOT__ . '/src/sessions/sessions.php';
require_once __ROOT__ . '/src/general/general.php';
require_once __ROOT__ . '/src/general/validaciones.php';
require_once __ROOT__ . '/src/general/preparar.php';

// Check for valid database connection before proceeding
if (!$conexion instanceof PDO) {
    error_log("Error crítico: No hay conexión a la base de datos en lcitas_programadas.php.");
    $response['message'] = 'Error crítico: No se pudo conectar a la base de datos.';
    http_response_code(503); // Service Unavailable
    echo json_encode($response);
    exit;
}

// Inicializar respuesta
$response = ['success' => false, 'message' => ''];

try {
    // Verificar que sea una solicitud POST
    if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
        throw new Exception('Método de solicitud no válido');
    }

    // Validar datos de entrada
    if (empty($_POST['id_cita']) || !is_numeric($_POST['id_cita'])) {
        throw new Exception('ID de cita inválido');
    }

    if (empty($_POST['id_metodo_pago']) || !is_numeric($_POST['id_metodo_pago'])) {
        throw new Exception('Método de pago es requerido');
    }

    $id_cita = (int)$_POST['id_cita'];
    $id_metodo_pago = (int)$_POST['id_metodo_pago'];

    // Establecer la zona horaria para Colombia
    date_default_timezone_set('America/Bogota');
    $fecha_fin = date('Y-m-d H:i:s');

    // Verificar que la cita exista
    $cita = Cita::get($id_cita, $conexion);
    if (!$cita) {
        throw new Exception("La cita con ID $id_cita no existe.");
    }

    // Verificar que la cita no esté ya finalizada
    if ($cita->getFecha_fin() !== null) {
        throw new Exception("La cita ya ha sido finalizada.");
    }

    // Finalizar la cita
    $resultado = Cita::finalizar($id_cita, $fecha_fin, $id_metodo_pago, $conexion);

    if ($resultado) {
        $response['success'] = true;
        $response['message'] = 'Cita finalizada correctamente';
        $response['id_cita'] = $id_cita;
        $response['fecha_fin'] = $fecha_fin;
    } else {
        throw new Exception('Error al finalizar la cita');
    }

} catch (Exception $e) {
    $response['message'] = 'Error: ' . $e->getMessage();
}

// Devolver respuesta JSON
header('Content-Type: application/json');
echo json_encode($response);
exit;
