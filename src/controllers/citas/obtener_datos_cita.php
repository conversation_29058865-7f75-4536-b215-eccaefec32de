<?php
/**
 * Controller para obtener datos de una cita para finalización
 * 
 * Este controlador recibe una solicitud AJAX para obtener los datos
 * de una cita específica incluyendo empleado y servicios asociados.
 */

session_start();

/** @var PDO $conexion */
global $conexion;

use App\classes\Cita;

require_once dirname(__FILE__, 4) . '/config/config.php';   
require_once __ROOT__ . '/src/sessions/sessions.php';
require_once __ROOT__ . '/src/general/general.php';
require_once __ROOT__ . '/src/general/validaciones.php';
require_once __ROOT__ . '/src/general/preparar.php';

// Check for valid database connection before proceeding
if (!$conexion instanceof PDO) {
    error_log("Error crítico: No hay conexión a la base de datos en lcitas_programadas.php.");
    $response['message'] = 'Error crítico: No se pudo conectar a la base de datos.';
    http_response_code(503); // Service Unavailable
    echo json_encode($response);
    exit;
}

// Inicializar respuesta
$response = ['success' => false, 'message' => ''];

try {
    // Verificar que sea una solicitud POST
    if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
        throw new Exception('Método de solicitud no válido');
    }

    // Validar datos de entrada
    if (empty($_POST['id_cita']) || !is_numeric($_POST['id_cita'])) {
        throw new Exception('ID de cita inválido');
    }

    $id_cita = (int)$_POST['id_cita'];

    // Obtener la cita
    $cita = Cita::get($id_cita, $conexion);
    if (!$cita) {
        throw new Exception("La cita con ID $id_cita no existe.");
    }

    // Verificar que la cita no esté ya finalizada
    if ($cita->getFecha_fin() !== null) {
        throw new Exception("La cita ya ha sido finalizada.");
    }

    // Verificar que la cita no esté cancelada
    if ($cita->getEstado() === 0) {
        throw new Exception("No se puede finalizar una cita que ha sido cancelada.");
    }

    // Obtener los servicios de la cita
    $servicios = $cita->getServicios($conexion);

    // Preparar datos de respuesta
    $servicios_data = [];
    foreach ($servicios as $servicio) {
        $servicios_data[] = [
            'id' => $servicio->getId(),
            'descripcion' => $servicio->getDescripcion(),
            'valor' => $servicio->getValor()
        ];
    }

    $response['success'] = true;
    $response['empleado'] = $cita->getNombre_empleado();
    $response['servicios'] = $servicios_data;

} catch (Exception $e) {
    $response['message'] = 'Error: ' . $e->getMessage();
}

// Devolver respuesta JSON
header('Content-Type: application/json');
echo json_encode($response);
exit;
