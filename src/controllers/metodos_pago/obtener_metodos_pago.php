<?php
/**
 * Controller para obtener métodos de pago activos
 * 
 * Este controlador devuelve una lista de métodos de pago activos
 * para ser utilizados en formularios.
 */

session_start();

/** @var PDO $conexion */
global $conexion;

use App\classes\MetodoPago;

require_once dirname(__FILE__, 4) . '/config/config.php';
require_once __ROOT__ . '/src/sessions/sessions.php';
require_once __ROOT__ . '/src/general/general.php';
require_once __ROOT__ . '/src/general/validaciones.php';
require_once __ROOT__ . '/src/general/preparar.php';

// Check for valid database connection before proceeding
if (!$conexion instanceof PDO) {
    error_log("Error crítico: No hay conexión a la base de datos en lcitas_programadas.php.");
    $response['message'] = 'Error crítico: No se pudo conectar a la base de datos.';
    http_response_code(503); // Service Unavailable
    echo json_encode($response);
    exit;
}

// Inicializar respuesta
$response = ['success' => false, 'message' => ''];

try {
    // Obtener métodos de pago activos
    $metodos_pago = MetodoPago::get_list($conexion);

    // Preparar datos de respuesta
    $metodos_data = [];
    foreach ($metodos_pago as $metodo) {
        $metodos_data[] = [
            'id' => $metodo->getId(),
            'descripcion' => $metodo->getDescripcion()
        ];
    }

    $response['success'] = true;
    $response['metodos_pago'] = $metodos_data;

} catch (Exception $e) {
    $response['message'] = 'Error: ' . $e->getMessage();
}

// Devolver respuesta JSON
header('Content-Type: application/json');
echo json_encode($response);
exit;
