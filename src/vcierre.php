<?php
/**
 * Controlador para visualizar un cierre de caja específico
 * 
 * Este controlador maneja la visualización de un cierre de caja completado,
 * mostrando todos los documentos incluidos en el cierre.
 */

use App\classes\Cita;
use App\classes\Venta;
use App\classes\VentaDetalle;
use App\classes\GastoOperativo;
use App\classes\OrdenCompra;
use App\classes\OrdenCompraDetalle;
use App\classes\CentroCosto;
use App\classes\Cierre;

if (session_status() === PHP_SESSION_NONE) {
	session_start();
}

/** @var PDO $conexion */
global $conexion;

require_once dirname(__FILE__, 2) . '/config/config.php';
require_once __ROOT__ . '/src/sessions/sessions.php';
require_once __ROOT__ . '/src/general/general.php';
require_once __ROOT__ . '/src/general/validaciones.php';
require_once __ROOT__ . '/src/general/preparar.php';

// Check for valid database connection before proceeding
if (!$conexion instanceof PDO) {
    error_log("Error crítico: No hay conexión a la base de datos en vcierre.php.");
    $error_display = 'show';
    $error_text = 'Error crítico: No se pudo conectar a la base de datos.';
    require_once __ROOT__ . '/views/vcierre.view.php';
    exit;
}

// Initialize variables
$error_display = null;
$error_text = '';
$success_display = null;
$success_text = '';
$cierre = null;
$centro_costo = null;
$citas_cierre = [];
$ventas_cierre = [];
$ordenes_compra_cierre = [];
$gastos_operativos_cierre = [];

#region try
try {
    // Set timezone for any date operations
    date_default_timezone_set('America/Bogota');

    // Get cierre ID from URL parameter
    $id_cierre = isset($_GET['id']) ? (int)$_GET['id'] : 0;
    
    if ($id_cierre <= 0) {
        throw new Exception('ID de cierre no válido.');
    }

    // Get cierre information
    $cierre = Cierre::get($id_cierre, $conexion);
    if (!$cierre) {
        throw new Exception('El cierre especificado no existe.');
    }

    // Get centro de costo information
    $centro_costo = CentroCosto::get($cierre->getId_centro_costo(), $conexion);
    if (!$centro_costo) {
        throw new Exception('El centro de costo del cierre no es válido.');
    }

    // Get all documents included in this cierre
    $citas_cierre = Cita::getCitasByCierre($id_cierre, $conexion);
    $ventas_cierre = Venta::getVentasByCierre($id_cierre, $conexion);
    $ordenes_compra_cierre = OrdenCompra::getOrdenesCompraByCierre($id_cierre, $conexion);
    $gastos_operativos_cierre = GastoOperativo::getGastosOperativosByCierre($id_cierre, $conexion);

    // Set success message only if coming from cash register closing process
    // Check for 'created' parameter which indicates the cierre was just created
    if (isset($_GET['created']) && $_GET['created'] === '1') {
        $success_display = 'show';
        $success_text = 'Cierre generado exitosamente. La información mostrada corresponde al cierre de caja completado.';
    }

} catch (PDOException $e) {
    // Specific handling for database errors
    $error_display = 'show';
    $error_text = "Error de base de datos al obtener la información del cierre.";
    // Initialize empty arrays to prevent undefined variable errors
    $citas_cierre = [];
    $ventas_cierre = [];
    $ordenes_compra_cierre = [];
    $gastos_operativos_cierre = [];
} catch (Exception $e) {
    // General error handling
    $error_display = 'show';
    $error_text = $e->getMessage();
    // Initialize empty arrays to prevent undefined variable errors
    $citas_cierre = [];
    $ventas_cierre = [];
    $ordenes_compra_cierre = [];
    $gastos_operativos_cierre = [];
}
#endregion try

require_once __ROOT__ . '/views/vcierre.view.php';
