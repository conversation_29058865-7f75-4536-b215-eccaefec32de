<?php

// Iniciar sesión si es necesario
use App\classes\Puesto;
use App\classes\CentroCosto;

if (session_status() === PHP_SESSION_NONE) {
	session_start();
}

/** @var PDO $conexion */
global $conexion;

require_once dirname(__FILE__, 2) . '/config/config.php';
require_once __ROOT__ . '/src/sessions/sessions.php';
require_once __ROOT__ . '/src/general/general.php';
require_once __ROOT__ . '/src/general/validaciones.php';
require_once __ROOT__ . '/src/general/preparar.php';

// Check for valid database connection before proceeding
if (!$conexion instanceof PDO) {
	error_log("Error crítico: No hay conexión a la base de datos en lpuestos.php.");
	$response['message'] = 'Error crítico: No se pudo conectar a la base de datos.';
	http_response_code(503); // Service Unavailable
	echo json_encode($response);
	exit;
}

#region init variables
$puestos = []; // Initialize as an empty array
$centros_costos = []; // Initialize as an empty array
#endregion init variables

#region Handle Flash Message Success
// Check for a success flash message from session
if (!empty($_SESSION['flash_message_success'])) {
	$success_text    = $_SESSION['flash_message_success'];
	$success_display = 'show';
	// Clear the flash message so it doesn't show again
	unset($_SESSION['flash_message_success']);
}
#endregion Handle Flash Message Success

#region Handle Flash Message Error
// Check for an error flash message from session
if (!empty($_SESSION['flash_message_error'])) {
	$error_text    = $_SESSION['flash_message_error'];
	$error_display = 'show';
	// Clear the flash message so it doesn't show again
	unset($_SESSION['flash_message_error']);
}
#endregion Handle Flash Message Error

#region Create Puesto
// --- Handle AJAX Request (Create Puesto) ---
if ($_SERVER['REQUEST_METHOD'] == 'POST' && isset($_POST['action']) && $_POST['action'] == 'crear') {
	header('Content-Type: application/json');                            // Set response header for JSON
	$response = ['success' => false, 'message' => 'Error desconocido.']; // Default error response
	
	$descripcion = filter_input(INPUT_POST, 'descripcion', FILTER_SANITIZE_SPECIAL_CHARS); // Sanitize description
	$descripcion = trim($descripcion ?? '');                                               // Trim whitespace
	$id_centro_costo = filter_input(INPUT_POST, 'id_centro_costo', FILTER_VALIDATE_INT);   // Get centro de costo ID

	if (!empty($descripcion) && $id_centro_costo !== null && $id_centro_costo !== false) {
		try {
			$puesto = new Puesto();
			$puesto->setDescripcion($descripcion);
			$puesto->setId_centro_costo($id_centro_costo); // Set centro de costo (can be null)

			$newId = $puesto->crear($conexion);
			
			if ($newId) {
				// Get the created puesto to return complete data including centro de costo name
				$puestoCreado = Puesto::get($newId, $conexion);

				$response['success'] = true;
				$response['message'] = 'Puesto creado correctamente.';
				$response['id']      = $newId;
				$response['descripcion']  = $descripcion;
				$response['id_centro_costo'] = $id_centro_costo;
				$response['nombre_centro_costo'] = $puestoCreado ? $puestoCreado->getNombre_centro_costo() : null;
				$response['estado']  = 1; // Active by default
			} else {
				$response['message'] = 'Error: No se pudo crear el Puesto.';
				http_response_code(500); // Internal Server Error
			}
		} catch (Exception $e) {
			$response['message'] = "Error al crear Puesto: " . $e->getMessage();
			http_response_code(500); // Internal Server Error
		}
	} else if (empty($descripcion)) {
		$response['message'] = 'Error: La descripción no puede estar vacía.';
		http_response_code(400); // Bad Request
	} else {
		$response['message'] = 'Error: Debe seleccionar un centro de costo.';
		http_response_code(400); // Bad Request
	}
	
	echo json_encode($response);
	exit; // IMPORTANT: Stop script execution after sending JSON response for AJAX
}
#endregion Create Puesto

#region Modify Puesto
// --- Handle AJAX Request (Modify Puesto) ---
if ($_SERVER['REQUEST_METHOD'] == 'POST' && isset($_POST['action']) && $_POST['action'] == 'modificar') {
	header('Content-Type: application/json');                            // Set response header for JSON
	$response = ['success' => false, 'message' => 'Error desconocido.']; // Default error response

	$puestoId    = filter_input(INPUT_POST, 'puestoId', FILTER_VALIDATE_INT);
	$nuevaDescripcion = filter_input(INPUT_POST, 'descripcion', FILTER_SANITIZE_SPECIAL_CHARS); // Sanitize description
	$nuevaDescripcion = trim($nuevaDescripcion ?? '');                                          // Trim whitespace

	// Check if someone is trying to modify centro de costo (security validation)
	$id_centro_costo_submitted = filter_input(INPUT_POST, 'id_centro_costo', FILTER_VALIDATE_INT);
	if ($id_centro_costo_submitted !== null && $id_centro_costo_submitted !== false) {
		$response['message'] = 'Error: No se permite modificar el centro de costo de un puesto existente.';
		http_response_code(400); // Bad Request
		echo json_encode($response);
		exit;
	}

	if ($puestoId && !empty($nuevaDescripcion)) {
		try {
			// Get the puesto first
			$puesto = Puesto::get($puestoId, $conexion);

			if ($puesto) {
				// Only update the description, preserve the original centro de costo
				$puesto->setDescripcion($nuevaDescripcion);
				// DO NOT modify centro de costo - keep original value
				$success = $puesto->modificar($conexion);

				if ($success) {
					$response['success'] = true;
					$response['message'] = 'Puesto actualizado correctamente.';
				} else {
					$response['message'] = 'Error: No se pudo actualizar el Puesto.';
					http_response_code(500); // Internal Server Error
				}
			} else {
				$response['message'] = 'Error: No se encontró el Puesto.';
				http_response_code(404); // Not Found
			}
		} catch (Exception $e) {
			$response['message'] = "Error al modificar Puesto: " . $e->getMessage();
			http_response_code(500); // Internal Server Error
		}
	} else if (!$puestoId) {
		$response['message'] = 'Error: ID de Puesto inválido.';
		http_response_code(400); // Bad Request
	} else if (empty($nuevaDescripcion)) {
		$response['message'] = 'Error: La descripción no puede estar vacía.';
		http_response_code(400); // Bad Request
	}

	echo json_encode($response);
	exit; // IMPORTANT: Stop script execution after sending JSON response for AJAX
}
#endregion Modify Puesto

#region Handle POST Actions (Deactivation)
// --- Handle Non-AJAX POST Action (Deactivate Puesto) ---
if ($_SERVER['REQUEST_METHOD'] == 'POST' && isset($_POST['action']) && $_POST['action'] == 'desactivar') {
	$puestoIdToDeactivate = filter_input(INPUT_POST, 'puestoId', FILTER_VALIDATE_INT);
	
	if ($puestoIdToDeactivate) {
		try {
			$success = Puesto::desactivar($puestoIdToDeactivate, $conexion);
			
			if ($success) {
				$_SESSION['flash_message_success'] = "Puesto desactivado correctamente.";
			} else {
				$_SESSION['flash_message_error'] = "Error: No se pudo encontrar o desactivar el Puesto.";
			}
		} catch (Exception $e) {
			$_SESSION['flash_message_error'] = "Error al desactivar Puesto: " . $e->getMessage();
		}
	} else {
		$_SESSION['flash_message_error'] = "Error: ID de Puesto inválido para desactivar.";
	}
	
	// Redirect back to the puesto list page after processing
	header('Location: puestos');
	exit;
}
#endregion Handle POST Actions

#region try
try {
	// Get list of active puestos
	$puestos = Puesto::get_list($conexion);

	// Get list of active centros de costos for dropdowns
	$centros_costos = CentroCosto::get_list($conexion);

} catch (PDOException $e) {
	// Specific handling for database errors
	$error_display = 'show';
	$error_text    = "Error de base de datos al obtener la lista de Puestos.";
} catch (Exception $e) {
	// General error handling
	$error_display = 'show';
	$error_text    = "Ocurrió un error inesperado al obtener la lista de Puestos: " . $e->getMessage();
}
#endregion try

require_once __ROOT__ . '/views/admin/lpuestos.view.php';
