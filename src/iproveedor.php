<?php

use App\classes\Proveedor;

if (session_status() === PHP_SESSION_NONE) {
	session_start();
}

/** @var PDO $conexion */
global $conexion;

require_once dirname(__FILE__, 2) . '/config/config.php';
require_once __ROOT__ . '/src/sessions/sessions.php';
require_once __ROOT__ . '/src/general/general.php';
require_once __ROOT__ . '/src/general/validaciones.php';
require_once __ROOT__ . '/src/general/preparar.php';

// Check for valid database connection before proceeding
if (!$conexion instanceof PDO) {
	error_log("Error crítico: No hay conexión a la base de datos en iproveedor.php.");
	$response['message'] = 'Error crítico: No se pudo conectar a la base de datos.';
	http_response_code(503); // Service Unavailable
	echo json_encode($response);
	exit;
}

#region Handle POST - Create Provider
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
	try {
		// Get and clean form data
		$nombre    = limpiar_datos($_POST['nombre'] ?? '');
		$nit       = limpiar_datos($_POST['nit'] ?? '');
		$telefono  = limpiar_datos($_POST['telefono'] ?? '');
		$correo    = limpiar_datos($_POST['correo'] ?? '');
		$direccion = limpiar_datos($_POST['direccion'] ?? '');

		// Server-side validation
		validar_campovacio($nombre, "El nombre es requerido.");

		// Validate email format if provided
		if (!empty($correo) && !filter_var($correo, FILTER_VALIDATE_EMAIL)) {
			throw new Exception("El formato del correo no es válido.");
		}

		// Create new provider object
		$proveedor = new Proveedor();
		$proveedor->setNombre($nombre);
		$proveedor->setNit(!empty($nit) ? $nit : null);
		$proveedor->setTelefono(!empty($telefono) ? $telefono : null);
		$proveedor->setCorreo(!empty($correo) ? $correo : null);
		$proveedor->setDireccion(!empty($direccion) ? $direccion : null);
		$proveedor->setEstado(1); // Active by default

		// Create provider in database
		$nuevo_id = $proveedor->crear($conexion);

		if ($nuevo_id) {
			$_SESSION['flash_message_success'] = "Proveedor creado exitosamente.";
			header('Location: lproveedores');
			exit;
		} else {
			throw new Exception("Error al crear el proveedor en la base de datos.");
		}

	} catch (Exception $e) {
		error_log("Error creating provider: " . $e->getMessage());
		$error_display = 'show';
		$error_text    = $e->getMessage();
	}
}
#endregion Handle POST

require_once __ROOT__ . '/views/iproveedor.view.php';

?>
