<?php

/**
 * @throws Exception
 */
function comprobar_sesion(): void
{
    if (!isset($_SESSION[USR_SESSION])) {
        header('Location: ' . RUTA . 'login');
        exit();
    }
}

function comprobar_sesion_login(): void
{
    if (isset($_SESSION[USR_SESSION])) {
        // Get user information to determine redirect destination
        global $conexion;

        if ($conexion instanceof PDO) {
            try {
                $usuario = \App\classes\Usuario::get($_SESSION[USR_SESSION], $conexion);
                if ($usuario) {
                    $perfil_usuario = $usuario->getNombre_perfil();
                    $es_barbero = (strtolower(trim($perfil_usuario)) === 'barbero');

                    if ($es_barbero) {
                        header('Location: ' . RUTA . 'dashboard-empleados');
                    } else {
                        header('Location: ' . RUTA . 'dashboard');
                    }
                    exit();
                }
            } catch (Exception $e) {
                // If there's an error getting user info, default to main dashboard
                header('Location: ' . RUTA . 'dashboard');
                exit();
            }
        }

        // Fallback to main dashboard if no database connection
        header('Location: ' . RUTA . 'dashboard');
		exit();
    }
}

?>