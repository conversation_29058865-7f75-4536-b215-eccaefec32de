<?php
/**
 * Controlador para la creación de ventas
 * 
 * Este controlador maneja la creación de nuevas ventas con sus detalles,
 * incluyendo validación de inventario, actualización automática de stock
 * y creación de movimientos de inventario.
 */


use App\classes\Venta;
use App\classes\VentaDetalle;
use App\classes\Cliente;
use App\classes\CentroCosto;
use App\classes\MetodoPago;
use App\classes\Producto;
use App\classes\Inventario;
use App\classes\InventarioMovimiento;

if (session_status() === PHP_SESSION_NONE) {
	session_start();
}

/** @var PDO $conexion */
global $conexion;

require_once dirname(__FILE__, 2) . '/config/config.php';
require_once __ROOT__ . '/src/sessions/sessions.php';
require_once __ROOT__ . '/src/general/general.php';
require_once __ROOT__ . '/src/general/validaciones.php';
require_once __ROOT__ . '/src/general/preparar.php';

// Check for valid database connection before proceeding
if (!$conexion instanceof PDO) {
    error_log("Error crítico: No hay conexión a la base de datos en iventas.php.");
    $response['message'] = 'Error crítico: No se pudo conectar a la base de datos.';
    http_response_code(503); // Service Unavailable
    echo json_encode($response);
    exit;
}

// Initialize response for AJAX requests
$response = ['success' => false, 'message' => ''];

#region Handle POST Actions
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $action = $_POST['action'] ?? '';

    #region Search Clientes
    if ($action === 'search_clientes') {
        $termino = trim($_POST['termino'] ?? '');

        try {
            $clientes_filtrados = Cliente::search_by_nombre_celular($conexion, $termino);

            $response['success'] = true;
            $response['clientes'] = [];

            foreach ($clientes_filtrados as $cliente) {
                $response['clientes'][] = [
                    'id'      => $cliente->getId(),
                    'nombre'  => htmlspecialchars($cliente->getNombre()),
                    'celular' => htmlspecialchars($cliente->getCelular() ?? ''),
                    'texto'   => htmlspecialchars($cliente->getNombre()) . 
                               ($cliente->getCelular() ? ' - ' . htmlspecialchars($cliente->getCelular()) : '')
                ];
            }

        } catch (Exception $e) {
            $response['message'] = "Error al buscar clientes: " . $e->getMessage();
            http_response_code(500);
        }

        header('Content-Type: application/json');
        echo json_encode($response);
        exit;
    }
    #endregion Search Clientes

    #region Create Cliente
    if ($action === 'create_cliente') {
        $nombre = trim($_POST['nombre'] ?? '');
        $celular = trim($_POST['celular'] ?? '');

        if (!empty($nombre)) {
            try {
                $cliente = new Cliente();
                $cliente->setNombre($nombre);
                if (!empty($celular)) {
                    $cliente->setCelular($celular);
                }

                $newId = $cliente->crear($conexion);

                if ($newId) {
                    $response['success'] = true;
                    $response['message'] = 'Cliente creado correctamente.';
                    $response['cliente'] = [
                        'id'      => $newId,
                        'nombre'  => $nombre,
                        'celular' => $celular,
                        'texto'   => $nombre . ($celular ? ' - ' . $celular : '')
                    ];
                } else {
                    $response['message'] = 'Error: No se pudo crear el cliente.';
                    http_response_code(500);
                }

            } catch (Exception $e) {
                $response['message'] = "Error al crear cliente: " . $e->getMessage();
                http_response_code(500);
            }
        } else {
            $response['message'] = 'El nombre del cliente es obligatorio.';
            http_response_code(400);
        }

        header('Content-Type: application/json');
        echo json_encode($response);
        exit;
    }
    #endregion Create Cliente

    #region Search Productos
    if ($action === 'search_productos') {
        $termino = trim($_POST['termino'] ?? '');

        try {
            $productos_filtrados = Producto::search_by_descripcion($conexion, $termino);

            $response['success']   = true;
            $response['productos'] = [];

            foreach ($productos_filtrados as $producto) {
                $response['productos'][] = [
                    'id'               => $producto->getId(),
                    'descripcion'      => htmlspecialchars($producto->getDescripcion()),
                    'valor'            => $producto->getValor(),
                    'valor_formateado' => format_currency_consigno($producto->getValor())
                ];
            }

        } catch (Exception $e) {
            $response['message'] = "Error al buscar productos: " . $e->getMessage();
            http_response_code(500);
        }

        header('Content-Type: application/json');
        echo json_encode($response);
        exit;
    }
    #endregion Search Productos

    #region Check Stock
    if ($action === 'check_stock') {
        $id_centro_costo = !empty($_POST['id_centro_costo']) ? (int)$_POST['id_centro_costo'] : 0;
        $id_producto     = !empty($_POST['id_producto']) ? (int)$_POST['id_producto'] : 0;

        if ($id_centro_costo > 0 && $id_producto > 0) {
            try {
                $inventario = Inventario::get_by_centro_producto($id_centro_costo, $id_producto, $conexion);

                if ($inventario) {
                    $response['success']     = true;
                    $response['stock']       = $inventario->getCantidad();
                    $response['tiene_stock'] = $inventario->tieneStock();
                } else {
                    $response['success']     = true;
                    $response['stock']       = 0;
                    $response['tiene_stock'] = false;
                }

            } catch (Exception $e) {
                $response['message'] = "Error al verificar stock: " . $e->getMessage();
                http_response_code(500);
            }
        } else {
            $response['message'] = 'Centro de costo y producto son requeridos.';
            http_response_code(400);
        }

        header('Content-Type: application/json');
        echo json_encode($response);
        exit;
    }
    #endregion Check Stock

    #region Create Venta
    if ($action === 'create_venta') {
        $id_cliente      = !empty($_POST['id_cliente']) ? (int)$_POST['id_cliente'] : null;
        $id_centro_costo = !empty($_POST['id_centro_costo']) ? (int)$_POST['id_centro_costo'] : 0;
        $id_metodo_pago  = !empty($_POST['id_metodo_pago']) ? (int)$_POST['id_metodo_pago'] : 0;
        $detalles        = json_decode($_POST['detalles'] ?? '[]', true);

        if ($id_centro_costo <= 0 || $id_metodo_pago <= 0 || empty($detalles)) {
            $response['message'] = 'Centro de costo, método de pago y al menos un producto son requeridos.';
            http_response_code(400);
            header('Content-Type: application/json');
            echo json_encode($response);
            exit;
        }

        try {
            // Start transaction
            $conexion->beginTransaction();

            // Validate stock for all products
            $valor_total = 0;
            foreach ($detalles as $detalle) {
                $id_producto = (int)$detalle['id_producto'];
                $cantidad    = (int)$detalle['cantidad'];
                $valor       = (float)$detalle['valor'];
                $descripcion = $detalle['descripcion'] ?? 'Producto desconocido';

                // Check stock
                $inventario = Inventario::get_by_centro_producto($id_centro_costo, $id_producto, $conexion);
                if (!$inventario || $inventario->getCantidad() < $cantidad) {
                    $conexion->rollBack();
                    $response['message'] = "Stock insuficiente para el producto: " . htmlspecialchars($descripcion);
                    http_response_code(400);
                    header('Content-Type: application/json');
                    echo json_encode($response);
                    exit;
                }

                $valor_total += $valor * $cantidad;
            }

            // Create venta
            $venta = new Venta();
            $venta->setId_cliente($id_cliente);
            $venta->setId_centro_costo($id_centro_costo);
            $venta->setId_metodo_pago($id_metodo_pago);
            $venta->setValor_total($valor_total);

            $venta_id = $venta->crear($conexion);

            if (!$venta_id) {
                $conexion->rollBack();
                $response['message'] = 'Error al crear la venta.';
                http_response_code(500);
                header('Content-Type: application/json');
                echo json_encode($response);
                exit;
            }

            // Create venta details and update inventory
            foreach ($detalles as $detalle) {
                $id_producto = (int)$detalle['id_producto'];
                $cantidad    = (int)$detalle['cantidad'];
                $valor       = (float)$detalle['valor'];

                // Create venta detail
                $venta_detalle = new VentaDetalle();
                $venta_detalle->setId_venta($venta_id);
                $venta_detalle->setId_producto($id_producto);
                $venta_detalle->setValor($valor);
                $venta_detalle->setCantidad($cantidad);
                $venta_detalle->crear($conexion);

                // Update inventory
                $inventario     = Inventario::get_by_centro_producto($id_centro_costo, $id_producto, $conexion);
                $nueva_cantidad = $inventario->getCantidad() - $cantidad;
                $inventario->setCantidad($nueva_cantidad);
                $inventario->modificar($conexion);

                // Create inventory movement
                $movimiento = new InventarioMovimiento(
                    $id_centro_costo,
                    $id_producto,
                    'egreso',
                    $cantidad,
                    $logged_usuario_info->getId(),
                    "Venta #" . $venta_id
                );
                $movimiento->crear($conexion);
            }

            $conexion->commit();

            $response['success'] = true;
            $response['message'] = 'Venta creada correctamente.';
            $response['venta_id'] = $venta_id;

        } catch (Exception $e) {
            $conexion->rollBack();
            $response['message'] = "Error al crear venta: " . $e->getMessage();
            http_response_code(500);
        }

        header('Content-Type: application/json');
        echo json_encode($response);
        exit;
    }
    #endregion Create Venta
}
#endregion Handle POST Actions

#region try
try {
    // Set timezone for any date operations
    date_default_timezone_set('America/Bogota');

    // Get list of active centros de costo
    $centros_costos = CentroCosto::get_list($conexion);

    // Get list of active metodos de pago
    $metodos_pagos = MetodoPago::get_list($conexion);

} catch (PDOException $e) {
    // Specific handling for database errors
    $error_display = 'show';
    $error_text    = "Error de base de datos al obtener los datos para crear venta.";
} catch (Exception $e) {
    // General error handling
    $error_display = 'show';
    $error_text    = "Ocurrió un error inesperado: " . $e->getMessage();
}
#endregion try

require_once __ROOT__ . '/views/iventas.view.php';
