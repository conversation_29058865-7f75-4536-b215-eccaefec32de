<?php

// Iniciar sesión si es necesario
use App\classes\Servicio;

if (session_status() === PHP_SESSION_NONE) {
	session_start();
}

/** @var PDO $conexion */
global $conexion;

require_once dirname(__FILE__, 2) . '/config/config.php';
require_once __ROOT__ . '/src/sessions/sessions.php';
require_once __ROOT__ . '/src/general/general.php';
require_once __ROOT__ . '/src/general/validaciones.php';
require_once __ROOT__ . '/src/general/preparar.php';

// Check for valid database connection before proceeding
if (!$conexion instanceof PDO) {
	error_log("Error crítico: No hay conexión a la base de datos en lservicios.php.");
	$response['message'] = 'Error crítico: No se pudo conectar a la base de datos.';
	http_response_code(503); // Service Unavailable
	echo json_encode($response);
	exit;
}

#region init variables
$servicios = []; // Initialize as an empty array
#endregion init variables

#region Handle Flash Message Success
// Check for a success flash message from session
if (!empty($_SESSION['flash_message_success'])) {
	$success_text    = $_SESSION['flash_message_success'];
	$success_display = 'show';
	// Clear the flash message so it doesn't show again
	unset($_SESSION['flash_message_success']);
}
#endregion Handle Flash Message Success

#region Handle Flash Message Error
// Check for an error flash message from session
if (!empty($_SESSION['flash_message_error'])) {
	$error_text    = $_SESSION['flash_message_error'];
	$error_display = 'show';
	// Clear the flash message so it doesn't show again
	unset($_SESSION['flash_message_error']);
}
#endregion Handle Flash Message Error

#region Create Servicio
// --- Handle AJAX Request (Create Servicio) ---
if ($_SERVER['REQUEST_METHOD'] == 'POST' && isset($_POST['action']) && $_POST['action'] == 'crear') {
	header('Content-Type: application/json');                            // Set response header for JSON
	$response = ['success' => false, 'message' => 'Error desconocido.']; // Default error response

	$descripcion = filter_input(INPUT_POST, 'descripcion', FILTER_SANITIZE_SPECIAL_CHARS); // Sanitize description
	$descripcion = trim($descripcion ?? '');                                              // Trim whitespace

	$valor = filter_input(INPUT_POST, 'valor', FILTER_SANITIZE_SPECIAL_CHARS);
	// Clean currency format (remove $ and thousand separators)
	$valor = str_replace('$', '', $valor ?? '0');
	$valor = str_replace('.', '', $valor);
	$valor = str_replace(',', '.', $valor); // Replace comma with dot for decimal
	$valor = floatval($valor);

	$duracion = filter_input(INPUT_POST, 'duracion', FILTER_VALIDATE_INT);
	// If duracion is not provided or invalid, set it to null
	$duracion = ($duracion !== false && $duracion > 0) ? $duracion : null;

	if (!empty($descripcion) && $valor > 0) {
		try {
			$servicio = new Servicio();
			$servicio->setDescripcion($descripcion);
			$servicio->setValor($valor);
			$servicio->setDuracion($duracion);

			$newId = $servicio->crear($conexion);

			if ($newId) {
				$response['success'] = true;
				$response['message'] = 'Servicio creado correctamente.';
				$response['id']      = $newId;
				$response['descripcion'] = $descripcion;
				$response['valor']   = format_currency_consigno($valor);
				$response['duracion'] = $duracion;
				$response['estado']  = 1; // Active by default
			} else {
				$response['message'] = 'Error: No se pudo crear el Servicio.';
				http_response_code(500); // Internal Server Error
			}
		} catch (Exception $e) {
			$response['message'] = "Error al crear Servicio: " . $e->getMessage();
			http_response_code(500); // Internal Server Error
		}
	} else if (empty($descripcion)) {
		$response['message'] = 'Error: La descripción no puede estar vacía.';
		http_response_code(400); // Bad Request
	} else {
		$response['message'] = 'Error: El valor debe ser mayor que cero.';
		http_response_code(400); // Bad Request
	}

	echo json_encode($response);
	exit; // IMPORTANT: Stop script execution after sending JSON response for AJAX
}
#endregion Create Servicio

#region Modify Servicio
// --- Handle AJAX Request (Modify Servicio) ---
if ($_SERVER['REQUEST_METHOD'] == 'POST' && isset($_POST['action']) && $_POST['action'] == 'modificar') {
	header('Content-Type: application/json');                            // Set response header for JSON
	$response = ['success' => false, 'message' => 'Error desconocido.']; // Default error response

	$servicioId = filter_input(INPUT_POST, 'servicioId', FILTER_VALIDATE_INT);
	$nuevaDescripcion = filter_input(INPUT_POST, 'descripcion', FILTER_SANITIZE_SPECIAL_CHARS); // Sanitize description
	$nuevaDescripcion = trim($nuevaDescripcion ?? '');                                         // Trim whitespace

	$nuevoValor = filter_input(INPUT_POST, 'valor', FILTER_SANITIZE_SPECIAL_CHARS);
	// Clean currency format (remove $ and thousand separators)
	$nuevoValor = str_replace('$', '', $nuevoValor ?? '0');
	$nuevoValor = str_replace('.', '', $nuevoValor);
	$nuevoValor = str_replace(',', '.', $nuevoValor); // Replace comma with dot for decimal
	$nuevoValor = floatval($nuevoValor);

	$nuevaDuracion = filter_input(INPUT_POST, 'duracion', FILTER_VALIDATE_INT);
	// If duracion is not provided or invalid, set it to null
	$nuevaDuracion = ($nuevaDuracion !== false && $nuevaDuracion > 0) ? $nuevaDuracion : null;

	if ($servicioId && !empty($nuevaDescripcion) && $nuevoValor > 0) {
		try {
			// Get the servicio first
			$servicio = Servicio::get($servicioId, $conexion);

			if ($servicio) {
				$servicio->setDescripcion($nuevaDescripcion);
				$servicio->setValor($nuevoValor);
				$servicio->setDuracion($nuevaDuracion);
				$success = $servicio->modificar($conexion);

				if ($success) {
					$response['success'] = true;
					$response['message'] = 'Servicio actualizado correctamente.';
					$response['valor_formateado'] = format_currency_consigno($nuevoValor);
					$response['duracion'] = $nuevaDuracion;
				} else {
					$response['message'] = 'Error: No se pudo actualizar el Servicio.';
					http_response_code(500); // Internal Server Error
				}
			} else {
				$response['message'] = 'Error: No se encontró el Servicio.';
				http_response_code(404); // Not Found
			}
		} catch (Exception $e) {
			$response['message'] = "Error al modificar Servicio: " . $e->getMessage();
			http_response_code(500); // Internal Server Error
		}
	} else if (!$servicioId) {
		$response['message'] = 'Error: ID de Servicio inválido.';
		http_response_code(400); // Bad Request
	} else if (empty($nuevaDescripcion)) {
		$response['message'] = 'Error: La descripción no puede estar vacía.';
		http_response_code(400); // Bad Request
	} else {
		$response['message'] = 'Error: El valor debe ser mayor que cero.';
		http_response_code(400); // Bad Request
	}

	echo json_encode($response);
	exit; // IMPORTANT: Stop script execution after sending JSON response for AJAX
}
#endregion Modify Servicio

#region Handle POST Actions (Deactivation)
// --- Handle Non-AJAX POST Action (Deactivate Servicio) ---
if ($_SERVER['REQUEST_METHOD'] == 'POST' && isset($_POST['action']) && $_POST['action'] == 'desactivar') {
	$servicioIdToDeactivate = filter_input(INPUT_POST, 'servicioId', FILTER_VALIDATE_INT);

	if ($servicioIdToDeactivate) {
		try {
			$success = Servicio::desactivar($servicioIdToDeactivate, $conexion);

			if ($success) {
				$_SESSION['flash_message_success'] = "Servicio desactivado correctamente.";
			} else {
				$_SESSION['flash_message_error'] = "Error: No se pudo encontrar o desactivar el Servicio.";
			}
		} catch (Exception $e) {
			$_SESSION['flash_message_error'] = "Error al desactivar Servicio: " . $e->getMessage();
		}
	} else {
		$_SESSION['flash_message_error'] = "Error: ID de Servicio inválido para desactivar.";
	}

	// Redirect back to the servicio list page after processing
	header('Location: servicios');
	exit;
}
#endregion Handle POST Actions

#region try
try {
	// Set timezone for any date operations
	date_default_timezone_set('America/Bogota');

	// Get list of active servicios
	$servicios = Servicio::get_list($conexion);

} catch (PDOException $e) {
	// Specific handling for database errors
	$error_display = 'show';
	$error_text    = "Error de base de datos al obtener la lista de Servicios.";
} catch (Exception $e) {
	// General error handling
	$error_display = 'show';
	$error_text    = "Ocurrió un error inesperado al obtener la lista de Servicios: " . $e->getMessage();
}
#endregion try

require_once __ROOT__ . '/views/lservicios.view.php';
