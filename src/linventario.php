<?php
/**
 * Controlador para la gestión de inventario
 *
 * Este controlador maneja la visualización y filtrado de inventario,
 * incluyendo funcionalidades de búsqueda por centro de costo y descripción de producto.
 * También maneja el descarte de productos con eliminación de inventario y soft delete del producto.
 */

use App\classes\Inventario;
use App\classes\Producto;
use App\classes\CentroCosto;
use App\classes\InventarioMovimiento;

if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

/** @var PDO $conexion */
global $conexion;
global $logged_usuario_info;

require_once dirname(__FILE__, 2) . '/config/config.php';
require_once __ROOT__ . '/src/sessions/sessions.php';
require_once __ROOT__ . '/src/general/general.php';
require_once __ROOT__ . '/src/general/validaciones.php';
require_once __ROOT__ . '/src/general/preparar.php';

// Check for valid database connection before proceeding
if (!$conexion instanceof PDO) {
    error_log("Error crítico: No hay conexión a la base de datos en linventario.php.");
    $response['message'] = 'Error crítico: No se pudo conectar a la base de datos.';
    http_response_code(503); // Service Unavailable
    echo json_encode($response);
    exit;
}

#region Search Inventario
// --- Handle AJAX Request (Search Inventario) ---
if ($_SERVER['REQUEST_METHOD'] == 'POST' && isset($_POST['action']) && $_POST['action'] == 'buscar') {
    header('Content-Type: application/json');
    $response = ['success' => false, 'message' => 'Error desconocido.'];

    $id_centro_costo      = filter_input(INPUT_POST, 'id_centro_costo', FILTER_VALIDATE_INT);
    $descripcion_producto = filter_input(INPUT_POST, 'descripcion_producto', FILTER_SANITIZE_SPECIAL_CHARS);
    $descripcion_producto = trim($descripcion_producto ?? '');

    if (!$id_centro_costo || $id_centro_costo <= 0) {
        $response['message'] = 'Debe seleccionar un centro de costo válido.';
        echo json_encode($response);
        exit;
    }

    try {
        // Get filtered list of inventario
        $inventarios = Inventario::obtenerPorCentroCostoConFiltro($conexion, $id_centro_costo, $descripcion_producto);

        $response['success']     = true;
        $response['inventarios'] = [];

        foreach ($inventarios as $inventario) {
            $response['inventarios'][] = [
                'id'                   => $inventario->getId(),
                'id_centro_costo'      => $inventario->getId_centro_costo(),
                'id_producto'          => $inventario->getId_producto(),
                'cantidad'             => $inventario->getCantidad(),
                'cantidad_formateada'  => $inventario->getCantidadFormateada(),
                'descripcion_producto' => htmlspecialchars($inventario->getDescripcion_producto() ?? ''),
                'nombre_centro_costo'  => htmlspecialchars($inventario->getNombre_centro_costo() ?? ''),
                'tiene_stock'          => $inventario->tieneStock(),
                'producto_estado'      => $inventario->getEstado_producto()
            ];
        }

    } catch (Exception $e) {
        $response['message'] = "Error al buscar inventario: " . $e->getMessage();
        http_response_code(500);
    }

    echo json_encode($response);
    exit;
}
#endregion Search Inventario

#region Discard Product
// --- Handle AJAX Request (Discard Product) ---
if ($_SERVER['REQUEST_METHOD'] == 'POST' && isset($_POST['action']) && $_POST['action'] == 'descartar_producto') {
    header('Content-Type: application/json');
    $response = ['success' => false, 'message' => 'Error desconocido.'];

    $id_producto = filter_input(INPUT_POST, 'id_producto', FILTER_VALIDATE_INT);

    if (!$id_producto || $id_producto <= 0) {
        $response['message'] = 'ID de producto inválido.';
        echo json_encode($response);
        exit;
    }

    try {
        // Start transaction
        $conexion->beginTransaction();

        // Get current user ID from session
        $id_usuario = $logged_usuario_info->getId();

        // Set timezone for Colombia
        date_default_timezone_set('America/Bogota');

        // Get all inventory records for this product
        $inventarios = Inventario::obtenerPorProducto($id_producto, $conexion);

        // Set all inventory quantities to 0 and create movement records
        foreach ($inventarios as $inventario) {
            if ($inventario->getCantidad() > 0) {
                $cantidad_original = $inventario->getCantidad();

                // Create inventory movement record for the discard with specific format
                $movimiento = new InventarioMovimiento(
                    $inventario->getId_centro_costo(),
                    $id_producto,
                    'egreso',
                    $cantidad_original,
                    $id_usuario,
                    "Descarte de producto: {$cantidad_original}"
                );
                $movimiento->crear($conexion);

                // Set inventory quantity to 0
                $inventario->setCantidad(0);
                $inventario->modificar($conexion);
            }
        }

        // Soft delete the product
        $producto = Producto::get($id_producto, $conexion);
        if ($producto) {
            $producto->setEstado(0); // Soft delete
            $producto->modificar($conexion);
        }

        // Commit transaction
        $conexion->commit();

        $response['success'] = true;
        $response['message'] = 'Producto descartado correctamente. Se ha eliminado todo el inventario y el producto ha sido desactivado.';

    } catch (Exception $e) {
        // Rollback transaction on error
        $conexion->rollBack();
        $response['message'] = "Error al descartar producto: " . $e->getMessage();
        http_response_code(500);
    }

    echo json_encode($response);
    exit;
}
#endregion Discard Product

#region Main Page Logic
try {
    // Set timezone for any date operations
    date_default_timezone_set('America/Bogota');

    // Get list of active centros de costo for the dropdown
    $centros_costo = CentroCosto::get_list($conexion);

    // Initialize empty inventarios array (will be populated via AJAX)
    $inventarios = [];

} catch (PDOException $e) {
    // Specific handling for database errors
    $error_display = 'show';
    $error_text = "Error de base de datos al cargar la página de inventario.";
} catch (Exception $e) {
    // General error handling
    $error_display = 'show';
    $error_text = "Ocurrió un error inesperado al cargar la página de inventario: " . $e->getMessage();
}
#endregion Main Page Logic

require_once __ROOT__ . '/views/linventario.view.php';
