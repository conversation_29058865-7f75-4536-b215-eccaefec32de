<?php
// global $conexion, $lastmodule; // Declaration moved down

// BEGIN redirect SSL
// funcion para obligar todo el trafico de la pagina a traves del canal SSL.
if ((empty($_SERVER['HTTPS']) || $_SERVER['HTTPS'] === "off") && ($_SERVER['HTTP_HOST'] !== "localhost" && $_SERVER['HTTP_HOST'] !== "127.0.0.1")) {
	$location = 'https://' . $_SERVER['HTTP_HOST'] . $_SERVER['REQUEST_URI'];
	header('HTTP/1.1 301 Moved Permanently');
	header('Location: ' . $location);
	exit();
}
// END redirect SSL

use \App\classes\Configuracion;

require_once __ROOT__ . '/src/query/connection.php';
require_once __ROOT__ . '/vendor/autoload.php';

global $conexion, $lastmodule;

try {
	$error_text      = '';
	$error_display   = 'hide';
	$success_text 	 = '';
	$success_display = 'hide';
	
	$celular_ventas = Configuracion::get_by_descripcion('Celular ventas', $conexion)->getValor();
	
} catch (Exception $e) {
	$error_display = 'show';
	$error_text = $e->getMessage();
}


?>
