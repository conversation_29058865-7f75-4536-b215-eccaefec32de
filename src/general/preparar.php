<?php
global $conexion, $lastmodule;

use App\classes\Usuario;
use App\classes\Menu;
use App\classes\CentroCosto;

// BEGIN redirect SSL
// funcion para obligar todo el trafico de la pagina a traves del canal SSL.

if ((empty($_SERVER['HTTPS']) || $_SERVER['HTTPS'] === "off") && ($_SERVER['HTTP_HOST'] !== "localhost" && $_SERVER['HTTP_HOST'] !== "127.0.0.1")) {
	$location = 'https://' . $_SERVER['HTTP_HOST'] . $_SERVER['REQUEST_URI'];
	header('HTTP/1.1 301 Moved Permanently');
	header('Location: ' . $location);
	exit();
}
// END redirect SSL

require_once __ROOT__ . '/src/query/connection.php';
require_once __ROOT__ . '/vendor/autoload.php';

#region try
try {
	//comprar que el usuario este logueado
	comprobar_sesion();

} catch (Exception $e) {
	$error_display = 'show';
	$error_text    = $e->getMessage();
}
#endregion try

try {
	// Check for valid database connection before proceeding
	if (!$conexion instanceof PDO) {
		error_log("Error crítico: No hay conexión a la base de datos en linventario.php.");
		$response['message'] = 'Error crítico: No se pudo conectar a la base de datos.';
		http_response_code(503); // Service Unavailable
		echo json_encode($response);
		exit;
	}

	$logged_usuario_info = Usuario::get($_SESSION[USR_SESSION], $conexion);

	// verificar si el usuario esta activo
	if ($logged_usuario_info->getEstado() == 0) {
		// si no esta activo, cerrar sesion
		header('Location: cerrar');
		exit();
	}

	$menu_permitido_list = Menu::getByIdPerfil($logged_usuario_info->getId_perfil(), $conexion);

	// --- Centro de Costo Management ---
	// Get user's associated centro de costos
	$user_centros_costos = CentroCosto::get_by_usuario($logged_usuario_info->getId(), $conexion);

	// Handle centro de costo selection
	$selected_centro_costo        = null;
	$selected_centro_costo_nombre = '';

	if (!empty($user_centros_costos)) {
		// Check if user has a centro de costo selected in session
		if (isset($_SESSION[CENTRO_COSTO_SESSION])) {
			// Validate that the selected centro de costo is still valid for this user
			$session_centro_id = (int)$_SESSION[CENTRO_COSTO_SESSION];
			foreach ($user_centros_costos as $centro) {
				if ($centro->getId() === $session_centro_id) {
					$selected_centro_costo        = $centro;
					$selected_centro_costo_nombre = $centro->getNombre();
					break;
				}
			}
		}

		// If no valid selection in session, auto-select first centro de costo
		if (!$selected_centro_costo) {
			$selected_centro_costo          = $user_centros_costos[0];
			$selected_centro_costo_nombre   = $selected_centro_costo->getNombre();
			$_SESSION[CENTRO_COSTO_SESSION] = $selected_centro_costo->getId();
		}
	}

	$error_text      = '';
	$error_display   = 'hide';
	$success_text    = '';
	$success_display = 'hide';

} catch (Exception $e) {
	$error_display = 'show';
	$error_text    = $e->getMessage();
}


?>