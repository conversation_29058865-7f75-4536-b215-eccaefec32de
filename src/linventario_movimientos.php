<?php

declare(strict_types=1);

use App\classes\InventarioMovimiento;
use App\classes\Usuario;

if (session_status() === PHP_SESSION_NONE) {
	session_start();
}

/** @var PDO $conexion */
global $conexion;

require_once dirname(__FILE__, 2) . '/config/config.php';
require_once __ROOT__ . '/src/sessions/sessions.php';
require_once __ROOT__ . '/src/general/general.php';
require_once __ROOT__ . '/src/general/validaciones.php';
require_once __ROOT__ . '/src/general/preparar.php';

// Check for valid database connection before proceeding
if (!$conexion instanceof PDO) {
	error_log("Error crítico: No hay conexión a la base de datos en linventario_movimientos.php.");
	$response['message'] = 'Error crítico: No se pudo conectar a la base de datos.';
	http_response_code(503); // Service Unavailable
	echo json_encode($response);
	exit;
}

#region init variables
// Establecer la zona horaria para Colombia
date_default_timezone_set('America/Bogota');

$error_text = '';
$error_display = 'none';

// Get list of active users for the dropdown
try {
    $usuarios = Usuario::get_list($conexion);
} catch (Exception $e) {
    $usuarios = [];
    $error_text = 'Error al cargar la lista de usuarios: ' . $e->getMessage();
    $error_display = 'show';
}
#endregion init variables

#region Handle AJAX Request (Consultar por Rango de Fechas)
if ($_SERVER['REQUEST_METHOD'] == 'POST' && isset($_POST['action']) && $_POST['action'] == 'consultar_movimientos') {
    header('Content-Type: application/json');
    $response = ['success' => false, 'message' => 'Error desconocido.'];

    $fecha_inicio = filter_input(INPUT_POST, 'fecha_inicio', FILTER_SANITIZE_FULL_SPECIAL_CHARS);
    $fecha_fin    = filter_input(INPUT_POST, 'fecha_fin', FILTER_SANITIZE_FULL_SPECIAL_CHARS);
    $id_usuario   = filter_input(INPUT_POST, 'id_usuario', FILTER_VALIDATE_INT);
    $tipo         = filter_input(INPUT_POST, 'tipo', FILTER_SANITIZE_FULL_SPECIAL_CHARS);

    // Validar que ambas fechas estén presentes
    if (empty($fecha_inicio) || empty($fecha_fin)) {
        $response['message'] = 'Ambas fechas son obligatorias.';
        http_response_code(400);
        echo json_encode($response);
        exit;
    }

    // Validar formato de fechas
    if (!preg_match('/^\d{4}-\d{2}-\d{2}$/', $fecha_inicio) || !preg_match('/^\d{4}-\d{2}-\d{2}$/', $fecha_fin)) {
        $response['message'] = 'Las fechas deben tener formato YYYY-MM-DD.';
        http_response_code(400);
        echo json_encode($response);
        exit;
    }

    // Validar que fecha_inicio no sea mayor que fecha_fin
    if ($fecha_inicio > $fecha_fin) {
        $response['message'] = 'La fecha de inicio no puede ser mayor que la fecha de fin.';
        http_response_code(400);
        echo json_encode($response);
        exit;
    }

    // Validar tipo si se proporciona
    if (!empty($tipo) && !in_array($tipo, ['ingreso', 'egreso'])) {
        $response['message'] = 'El tipo debe ser "ingreso" o "egreso".';
        http_response_code(400);
        echo json_encode($response);
        exit;
    }

    // Convertir filtros opcionales
    $id_usuario_filtro = ($id_usuario !== false && $id_usuario > 0) ? $id_usuario : null;
    $tipo_filtro = (!empty($tipo)) ? $tipo : null;

    try {
        // Obtener movimientos de inventario por rango de fechas
        $movimientos = InventarioMovimiento::obtenerPorRangoFechas($fecha_inicio, $fecha_fin, $conexion, $id_usuario_filtro, $tipo_filtro);
        
        // Preparar datos para respuesta
        $movimientos_data = [];
        foreach ($movimientos as $movimiento) {
            $movimientos_data[] = [
                'id'                   => $movimiento->getId(),
                'fecha'                => $movimiento->getFecha(),
                'fecha_formateada'     => $movimiento->getFechaFormateada(),
                'tipo'                 => $movimiento->getTipo(),
                'cantidad'             => $movimiento->getCantidad(),
                'nota'                 => $movimiento->getNota(),
                'nombre_centro_costo'  => $movimiento->getNombre_centro_costo(),
                'descripcion_producto' => $movimiento->getDescripcion_producto(),
                'nombre_usuario'       => $movimiento->getNombre_usuario()
            ];
        }

        $response['success'] = true;
        $response['data'] = $movimientos_data;
        $response['total_registros'] = count($movimientos);

    } catch (Exception $e) {
        $response['message'] = 'Error al consultar movimientos: ' . $e->getMessage();
        http_response_code(500);
    }

    echo json_encode($response);
    exit;
}
#endregion Handle AJAX Request (Consultar por Rango de Fechas)

#region Include View
require_once __ROOT__ . '/views/linventario_movimientos.view.php';
#endregion Include View
