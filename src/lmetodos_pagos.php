<?php

// Iniciar sesión si es necesario
use App\classes\MetodoPago;

if (session_status() === PHP_SESSION_NONE) {
	session_start();
}

/** @var PDO $conexion */
global $conexion;

require_once dirname(__FILE__, 2) . '/config/config.php';
require_once __ROOT__ . '/src/sessions/sessions.php';
require_once __ROOT__ . '/src/general/general.php';
require_once __ROOT__ . '/src/general/validaciones.php';
require_once __ROOT__ . '/src/general/preparar.php';

// Check for valid database connection before proceeding
if (!$conexion instanceof PDO) {
	error_log("Error crítico: No hay conexión a la base de datos en lmetodos_pagos.php.");
	$response['message'] = 'Error crítico: No se pudo conectar a la base de datos.';
	http_response_code(503); // Service Unavailable
	echo json_encode($response);
	exit;
}

#region init variables
$metodos_pagos = []; // Initialize as an empty array
#endregion init variables

#region Handle Flash Message Success
// Check for a success flash message from session
if (!empty($_SESSION['flash_message_success'])) {
	$success_text    = $_SESSION['flash_message_success'];
	$success_display = 'show';
	// Clear the flash message so it doesn't show again
	unset($_SESSION['flash_message_success']);
}
#endregion Handle Flash Message Success

#region Handle Flash Message Error
// Check for an error flash message from session
if (!empty($_SESSION['flash_message_error'])) {
	$error_text    = $_SESSION['flash_message_error'];
	$error_display = 'show';
	// Clear the flash message so it doesn't show again
	unset($_SESSION['flash_message_error']);
}
#endregion Handle Flash Message Error

#region Create Metodo Pago
// --- Handle AJAX Request (Create Metodo Pago) ---
if ($_SERVER['REQUEST_METHOD'] == 'POST' && isset($_POST['action']) && $_POST['action'] == 'crear') {
	header('Content-Type: application/json');                            // Set response header for JSON
	$response = ['success' => false, 'message' => 'Error desconocido.']; // Default error response
	
	$descripcion = filter_input(INPUT_POST, 'descripcion', FILTER_SANITIZE_SPECIAL_CHARS); // Sanitize description
	$descripcion = trim($descripcion ?? '');                                               // Trim whitespace
	$es_metodo_electronico = isset($_POST['es_metodo_electronico']) ? 1 : 0;
	
	if (!empty($descripcion)) {
		try {
			$metodoPago = new MetodoPago();
			$metodoPago->setDescripcion($descripcion);
			$metodoPago->setEs_metodo_electronico($es_metodo_electronico);

			$newId = $metodoPago->crear($conexion);
			
			if ($newId) {
				$response['success']     = true;
				$response['message']     = 'Método de Pago creado correctamente.';
				$response['id']          = $newId;
				$response['descripcion'] = $descripcion;
				$response['es_metodo_electronico'] = $es_metodo_electronico;
				$response['estado']      = 1; // Active by default
			} else {
				$response['message'] = 'Error: No se pudo crear el Método de Pago.';
				http_response_code(500); // Internal Server Error
			}
		} catch (Exception $e) {
			$response['message'] = "Error al crear Método de Pago: " . $e->getMessage();
			http_response_code(500); // Internal Server Error
		}
	} else {
		$response['message'] = 'Error: La descripción no puede estar vacía.';
		http_response_code(400); // Bad Request
	}
	
	echo json_encode($response);
	exit; // IMPORTANT: Stop script execution after sending JSON response for AJAX
}
#endregion Create Metodo Pago

#region Modify Metodo Pago
// --- Handle AJAX Request (Modify Metodo Pago) ---
if ($_SERVER['REQUEST_METHOD'] == 'POST' && isset($_POST['action']) && $_POST['action'] == 'modificar') {
	header('Content-Type: application/json');                            // Set response header for JSON
	$response = ['success' => false, 'message' => 'Error desconocido.']; // Default error response
	
	$metodoId         = filter_input(INPUT_POST, 'metodoId', FILTER_VALIDATE_INT);
	$nuevaDescripcion = filter_input(INPUT_POST, 'descripcion', FILTER_SANITIZE_SPECIAL_CHARS); // Sanitize description
	$nuevaDescripcion = trim($nuevaDescripcion ?? '');                                          // Trim whitespace
	$es_metodo_electronico = isset($_POST['es_metodo_electronico']) ? 1 : 0;
	
	if ($metodoId && !empty($nuevaDescripcion)) {
		try {
			// Get the metodo pago first
			$metodoPago = MetodoPago::get($metodoId, $conexion);
			
			if ($metodoPago) {
				$metodoPago->setDescripcion($nuevaDescripcion);
				$metodoPago->setEs_metodo_electronico($es_metodo_electronico);
				$success = $metodoPago->modificar($conexion);
				$success = $metodoPago->modificar($conexion);
				
				if ($success) {
					$response['success'] = true;
					$response['message'] = 'Método de Pago actualizado correctamente.';
					$response['es_metodo_electronico'] = $es_metodo_electronico;
				} else {
					$response['message'] = 'Error: No se pudo actualizar el Método de Pago.';
					http_response_code(500); // Internal Server Error
				}
			} else {
				$response['message'] = 'Error: No se encontró el Método de Pago.';
				http_response_code(404); // Not Found
			}
		} catch (Exception $e) {
			$response['message'] = "Error al modificar Método de Pago: " . $e->getMessage();
			http_response_code(500); // Internal Server Error
		}
	} else if (!$metodoId) {
		$response['message'] = 'Error: ID de Método de Pago inválido.';
		http_response_code(400); // Bad Request
	} else {
		$response['message'] = 'Error: La descripción no puede estar vacía.';
		http_response_code(400); // Bad Request
	}
	
	echo json_encode($response);
	exit; // IMPORTANT: Stop script execution after sending JSON response for AJAX
}
#endregion Modify Metodo Pago

#region Handle POST Actions (Deactivation)
// --- Handle Non-AJAX POST Action (Deactivate Metodo Pago) ---
if ($_SERVER['REQUEST_METHOD'] == 'POST' && isset($_POST['action']) && $_POST['action'] == 'desactivar') {
	$metodoIdToDeactivate = filter_input(INPUT_POST, 'metodoId', FILTER_VALIDATE_INT);
	
	if ($metodoIdToDeactivate) {
		try {
			$success = MetodoPago::desactivar($metodoIdToDeactivate, $conexion);
			
			if ($success) {
				$_SESSION['flash_message_success'] = "Método de Pago desactivado correctamente.";
			} else {
				$_SESSION['flash_message_error'] = "Error: No se pudo encontrar o desactivar el Método de Pago.";
			}
		} catch (Exception $e) {
			$_SESSION['flash_message_error'] = "Error al desactivar Método de Pago: " . $e->getMessage();
		}
	} else {
		$_SESSION['flash_message_error'] = "Error: ID de Método de Pago inválido para desactivar.";
	}
	
	// Redirect back to the metodo pago list page after processing
	header('Location: metodos-pagos');
	exit;
}
#endregion Handle POST Actions

#region try
try {
	// Set timezone for any date operations
	date_default_timezone_set('America/Bogota');
	
	// Get list of active metodos pagos
	$metodos_pagos = MetodoPago::get_list($conexion);
	
} catch (PDOException $e) {
	// Specific handling for database errors
	$error_display = 'show';
	$error_text    = "Error de base de datos al obtener la lista de Métodos de Pago.";
} catch (Exception $e) {
	// General error handling
	$error_display = 'show';
	$error_text    = "Ocurrió un error inesperado al obtener la lista de Métodos de Pago: " . $e->getMessage();
}
#endregion try

require_once __ROOT__ . '/views/lmetodos_pagos.view.php';
