<?php

declare(strict_types=1);

use App\classes\GastoOperativo;
use App\classes\ControlBase;

if (session_status() === PHP_SESSION_NONE) {
	session_start();
}

/** @var PDO $conexion */
global $conexion;

require_once dirname(__FILE__, 2) . '/config/config.php';
require_once __ROOT__ . '/src/sessions/sessions.php';
require_once __ROOT__ . '/src/general/general.php';
require_once __ROOT__ . '/src/general/validaciones.php';
require_once __ROOT__ . '/src/general/preparar.php';

// Check for valid database connection before proceeding
if (!$conexion instanceof PDO) {
	error_log("Error crítico: No hay conexión a la base de datos en gasto_operativo.php.");
	$response['message'] = 'Error crítico: No se pudo conectar a la base de datos.';
	http_response_code(503); // Service Unavailable
	echo json_encode($response);
	exit;
}

#region init variables
// Establecer la zona horaria para Colombia
date_default_timezone_set('America/Bogota');

$gastos_operativos = [];
$total_gastos      = 0.0;
$fecha_inicio      = null;
$fecha_fin         = null;
$show_results      = false;
#endregion init variables

#region Handle Flash Message Success
// Check for a success flash message from session
if (!empty($_SESSION['flash_message_success'])) {
    $success_text    = $_SESSION['flash_message_success'];
    $success_display = 'show';
    // Clear the flash message so it doesn't show again
    unset($_SESSION['flash_message_success']);
}
#endregion Handle Flash Message Success

#region Handle Flash Message Error
// Check for an error flash message from session
if (!empty($_SESSION['flash_message_error'])) {
    $error_text    = $_SESSION['flash_message_error'];
    $error_display = 'show';
    // Clear the flash message so it doesn't show again
    unset($_SESSION['flash_message_error']);
}
#endregion Handle Flash Message Error

#region Handle AJAX Request (Consultar por Rango de Fechas)
if ($_SERVER['REQUEST_METHOD'] == 'POST' && isset($_POST['action']) && $_POST['action'] == 'consultar_gastos') {
    header('Content-Type: application/json');
    $response = ['success' => false, 'message' => 'Error desconocido.'];

    $fecha_inicio = filter_input(INPUT_POST, 'fecha_inicio', FILTER_SANITIZE_STRING);
    $fecha_fin = filter_input(INPUT_POST, 'fecha_fin', FILTER_SANITIZE_STRING);

    // Validar que ambas fechas estén presentes
    if (empty($fecha_inicio) || empty($fecha_fin)) {
        $response['message'] = 'Ambas fechas son obligatorias.';
        http_response_code(400);
        echo json_encode($response);
        exit;
    }

    // Validar formato de fechas
    if (!preg_match('/^\d{4}-\d{2}-\d{2}$/', $fecha_inicio) || !preg_match('/^\d{4}-\d{2}-\d{2}$/', $fecha_fin)) {
        $response['message'] = 'Las fechas deben tener formato YYYY-MM-DD.';
        http_response_code(400);
        echo json_encode($response);
        exit;
    }

    // Validar que fecha_inicio no sea mayor que fecha_fin
    if ($fecha_inicio > $fecha_fin) {
        $response['message'] = 'La fecha de inicio no puede ser mayor que la fecha de fin.';
        http_response_code(400);
        echo json_encode($response);
        exit;
    }

    try {
        // Obtener gastos operativos por rango de fechas
        $gastos_operativos = GastoOperativo::obtenerPorRangoFechas($fecha_inicio, $fecha_fin, $conexion);
        
        // Calcular total
        $total_gastos = 0.0;
        foreach ($gastos_operativos as $gasto) {
            $total_gastos += $gasto->getValor();
        }

        // Preparar datos para respuesta
        $gastos_data = [];
        foreach ($gastos_operativos as $gasto) {
            $gastos_data[] = [
                'id' => $gasto->getId(),
                'fecha' => $gasto->getFecha(),
                'descripcion' => $gasto->getDescripcion(),
                'valor' => $gasto->getValor(),
                'valor_formateado' => $gasto->getValorFormateado()
            ];
        }

        $response['success'] = true;
        $response['message'] = 'Consulta realizada correctamente.';
        $response['gastos'] = $gastos_data;
        $response['total_gastos'] = $total_gastos;
        $response['total_gastos_formateado'] = '$' . number_format($total_gastos, 0, ',', '.');
        $response['fecha_inicio'] = $fecha_inicio;
        $response['fecha_fin'] = $fecha_fin;

    } catch (Exception $e) {
        $response['message'] = 'Error al consultar gastos operativos: ' . $e->getMessage();
        http_response_code(500);
    }

    echo json_encode($response);
    exit;
}
#endregion Handle AJAX Request (Consultar por Rango de Fechas)

#region Handle AJAX Request (Obtener ControlBase por Fecha)
if ($_SERVER['REQUEST_METHOD'] == 'POST' && isset($_POST['action']) && $_POST['action'] == 'obtener_control_base') {
    header('Content-Type: application/json');
    $response = ['success' => false, 'message' => 'Error desconocido.'];

    $fecha = filter_input(INPUT_POST, 'fecha', FILTER_SANITIZE_STRING);

    // Validar que la fecha esté presente
    if (empty($fecha)) {
        $response['message'] = 'La fecha es obligatoria.';
        http_response_code(400);
        echo json_encode($response);
        exit;
    }

    // Validar formato de fecha
    if (!preg_match('/^\d{4}-\d{2}-\d{2}$/', $fecha)) {
        $response['message'] = 'La fecha debe tener formato YYYY-MM-DD.';
        http_response_code(400);
        echo json_encode($response);
        exit;
    }

    try {
        // Obtener control base por fecha
        $control_base = ControlBase::obtenerPorFecha($fecha, $conexion);
        
        if ($control_base) {
            $response['success'] = true;
            $response['message'] = 'Control Base encontrado.';
            $response['control_base'] = [
                'id' => $control_base->getId(),
                'fecha' => $control_base->getFecha(),
                'valor_inicial' => $control_base->getValor_inicial(),
                'valor_actual' => $control_base->getValor_actual(),
                'valor_final' => $control_base->getValor_final(),
                'valor_inicial_formateado' => '$' . number_format($control_base->getValor_inicial(), 0, ',', '.'),
                'valor_actual_formateado' => '$' . number_format($control_base->getValor_actual(), 0, ',', '.'),
                'valor_final_formateado' => ($control_base->getValor_final() === null || $control_base->getValor_final() == 0) 
                    ? '$' . number_format($control_base->getValor_actual(), 0, ',', '.') 
                    : '$' . number_format($control_base->getValor_final(), 0, ',', '.')
            ];
        } else {
            $response['message'] = 'No se encontró Control Base para la fecha especificada.';
            http_response_code(404);
        }

    } catch (Exception $e) {
        $response['message'] = 'Error al obtener Control Base: ' . $e->getMessage();
        http_response_code(500);
    }

    echo json_encode($response);
    exit;
}
#endregion Handle AJAX Request (Obtener ControlBase por Fecha)

#region Include View
require_once __ROOT__ . '/views/admin/consulta_gastos_operativos.view.php';
#endregion Include View
