<?php
/**
 * Controlador para la gestión de gastos fijos
 * 
 * Este controlador maneja la visualización y gestión de gastos fijos,
 * incluyendo funcionalidades de búsqueda, creación, edición y eliminación (soft delete).
 */

use App\classes\GastoFijo;

if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

/** @var PDO $conexion */
global $conexion;

require_once dirname(__FILE__, 2) . '/config/config.php';
require_once __ROOT__ . '/src/sessions/sessions.php';
require_once __ROOT__ . '/src/general/general.php';
require_once __ROOT__ . '/src/general/validaciones.php';
require_once __ROOT__ . '/src/general/preparar.php';

// Check for valid database connection before proceeding
if (!$conexion instanceof PDO) {
	error_log("Error crítico: No hay conexión a la base de datos en lgasto_fijo.php.");
	$response['message'] = 'Error crítico: No se pudo conectar a la base de datos.';
	http_response_code(503); // Service Unavailable
	echo json_encode($response);
	exit;
}

// Set timezone
date_default_timezone_set('America/Bogota');

#region Variables
$gastos_fijos    = [];
$error_display   = 'hide';
$error_text      = '';
$success_display = 'hide';
$success_text    = '';
#endregion Variables

#region Handle POST Actions
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $action   = $_POST['action'] ?? '';
    $response = ['success' => false, 'message' => ''];

    #region AJAX Search Gastos Fijos
    if ($action === 'search_gastos_fijos') {
        $termino_descripcion = trim($_POST['termino_descripcion'] ?? '');
        $id_gasto_fijo       = !empty($_POST['id_gasto_fijo']) ? (int)$_POST['id_gasto_fijo'] : null;

        try {
            // Get all active gastos fijos and filter by description if provided
            $gastos_fijos_filtrados = GastoFijo::obtenerTodos($conexion);

            // Filter by description if provided
            if (!empty($termino_descripcion)) {
                $gastos_fijos_filtrados = array_filter($gastos_fijos_filtrados, function($gasto_fijo) use ($termino_descripcion) {
                    return stripos($gasto_fijo->getDescripcion(), $termino_descripcion) !== false;
                });
            }

            // Filter by ID if provided
            if ($id_gasto_fijo !== null) {
                $gastos_fijos_filtrados = array_filter($gastos_fijos_filtrados, function($gasto_fijo) use ($id_gasto_fijo) {
                    return $gasto_fijo->getId() === $id_gasto_fijo;
                });
            }

            $response['success'] = true;
            $response['gastos_fijos'] = [];

            foreach ($gastos_fijos_filtrados as $gasto_fijo) {
                $response['gastos_fijos'][] = [
                    'id'               => $gasto_fijo->getId(),
                    'descripcion'      => $gasto_fijo->getDescripcion(),
                    'valor'            => $gasto_fijo->getValor(),
                    'valor_formateado' => '$' . number_format($gasto_fijo->getValor(), 0, ',', '.'),
                    'estado'           => $gasto_fijo->getEstado()
                ];
            }

        } catch (Exception $e) {
            $response['message'] = "Error al buscar gastos fijos: " . $e->getMessage();
            http_response_code(500); // Internal Server Error
        }

        header('Content-Type: application/json');
        echo json_encode($response);
        exit;
    }
    #endregion AJAX Search Gastos Fijos

    #region Create Gasto Fijo
    if ($action === 'create_gasto_fijo') {
        $descripcion = trim($_POST['descripcion'] ?? '');
        $valor_raw   = trim($_POST['valor'] ?? '');

        try {
            // Validate required fields
            if (empty($descripcion)) {
                throw new Exception("La descripción es requerida.");
            }

            // Parse currency value (remove $ and dots, convert to float)
            $valor_clean = str_replace(['$', '.', ','], ['', '', '.'], $valor_raw);
            $valor = (float)$valor_clean;
            if ($valor <= 0) {
                throw new Exception("El valor debe ser un número positivo mayor que cero.");
            }

            // Create new GastoFijo
            $gasto_fijo = new GastoFijo();
            $gasto_fijo->setDescripcion($descripcion);
            $gasto_fijo->setValor($valor);

            $nuevo_id = $gasto_fijo->crear($conexion);

            if ($nuevo_id) {
                $response['success'] = true;
                $response['message'] = "Gasto fijo creado exitosamente.";
                $response['gasto_fijo_id'] = $nuevo_id;
            } else {
                $response['message'] = "Error al crear el gasto fijo.";
            }

        } catch (Exception $e) {
            $response['message'] = "Error: " . $e->getMessage();
            http_response_code(400); // Bad Request
        }

        header('Content-Type: application/json');
        echo json_encode($response);
        exit;
    }
    #endregion Create Gasto Fijo

    #region Edit Gasto Fijo
    if ($action === 'edit_gasto_fijo') {
        $id        = !empty($_POST['id']) ? (int)$_POST['id'] : 0;
        $valor_raw = trim($_POST['valor'] ?? '');

        try {
            if ($id <= 0) {
                throw new Exception("ID de gasto fijo inválido.");
            }

            // Parse currency value (remove $ and dots, convert to float)
            $valor_clean = str_replace(['$', '.', ','], ['', '', '.'], $valor_raw);
            $valor       = (float)$valor_clean;
            if ($valor <= 0) {
                throw new Exception("El valor debe ser un número positivo mayor que cero.");
            }

            // Get existing gasto fijo
            $gasto_fijo = GastoFijo::obtenerPorId($id, $conexion);
            if (!$gasto_fijo) {
                throw new Exception("Gasto fijo no encontrado.");
            }

            // Update only the valor
            $gasto_fijo->setValor($valor);
            $success = $gasto_fijo->modificar($conexion);

            if ($success) {
                $response['success'] = true;
                $response['message'] = "Gasto fijo actualizado exitosamente.";
            } else {
                $response['message'] = "Error al actualizar el gasto fijo.";
            }

        } catch (Exception $e) {
            $response['message'] = "Error: " . $e->getMessage();
            http_response_code(400); // Bad Request
        }

        header('Content-Type: application/json');
        echo json_encode($response);
        exit;
    }
    #endregion Edit Gasto Fijo

    #region Delete Gasto Fijo
    if ($action === 'delete_gasto_fijo') {
        $id = !empty($_POST['id']) ? (int)$_POST['id'] : 0;

        try {
            if ($id <= 0) {
                throw new Exception("ID de gasto fijo inválido.");
            }

            // Get existing gasto fijo
            $gasto_fijo = GastoFijo::obtenerPorId($id, $conexion);
            if (!$gasto_fijo) {
                throw new Exception("Gasto fijo no encontrado.");
            }

            // Soft delete using the dedicated eliminar method
            $success = GastoFijo::eliminar($id, $conexion);

            if ($success) {
                $response['success'] = true;
                $response['message'] = "Gasto fijo eliminado exitosamente.";
            } else {
                $response['message'] = "Error al eliminar el gasto fijo.";
            }

        } catch (Exception $e) {
            $response['message'] = "Error: " . $e->getMessage();
            http_response_code(400); // Bad Request
        }

        header('Content-Type: application/json');
        echo json_encode($response);
        exit;
    }
    #endregion Delete Gasto Fijo
}
#endregion Handle POST Actions

#region Get Initial Data
try {
    // Get all active gastos fijos for initial display
    $gastos_fijos = GastoFijo::obtenerTodos($conexion);

} catch (PDOException $e) {
    // Specific handling for database errors
    $error_display = 'show';
    $error_text = "Error de base de datos al obtener los datos para gestión de gastos fijos.";
} catch (Exception $e) {
    // General error handling
    $error_display = 'show';
    $error_text = "Ocurrió un error inesperado: " . $e->getMessage();
}
#endregion Get Initial Data

require_once __ROOT__ . '/views/lgasto_fijo.view.php';

?>
