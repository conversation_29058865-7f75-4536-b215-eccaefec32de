<?php

// Iniciar sesión si es necesario
use App\classes\Proveedor;

if (session_status() === PHP_SESSION_NONE) {
	session_start();
}

/** @var PDO $conexion */
global $conexion;

require_once dirname(__FILE__, 2) . '/config/config.php';
require_once __ROOT__ . '/src/sessions/sessions.php';
require_once __ROOT__ . '/src/general/general.php';
require_once __ROOT__ . '/src/general/validaciones.php';
require_once __ROOT__ . '/src/general/preparar.php';

// Check for valid database connection before proceeding
if (!$conexion instanceof PDO) {
	error_log("Error crítico: No hay conexión a la base de datos en lproveedores.php.");
	$response['message'] = 'Error crítico: No se pudo conectar a la base de datos.';
	http_response_code(503); // Service Unavailable
	echo json_encode($response);
	exit;
}

#region Handle POST Actions
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
	$action = $_POST['action'] ?? '';
	$response = ['success' => false, 'message' => ''];

	try {
		if ($action === 'desactivar') {
			$id = (int)($_POST['id'] ?? 0);

			if ($id > 0) {
				$success = Proveedor::desactivar($id, $conexion);

				if ($success) {
					$_SESSION['flash_message_success'] = "Proveedor desactivado exitosamente.";
				} else {
					$_SESSION['flash_message_error'] = "Error: No se pudo desactivar el proveedor.";
				}
			} else {
				$_SESSION['flash_message_error'] = "Error: ID de proveedor inválido para desactivar.";
			}
		}
	} catch (Exception $e) {
		error_log("Error en lproveedores.php (POST): " . $e->getMessage());
		$_SESSION['flash_message_error'] = "Error: " . $e->getMessage();
	}

	// Redirect back to the provider list page after processing
	header('Location: lproveedores');
	exit;
}
#endregion Handle POST Actions

#region try
try {
	$proveedores = Proveedor::get_list($conexion);

} catch (PDOException $e) {
	// Specific handling for database errors
	error_log("Database error fetching providers: " . $e->getMessage());
	$error_display = 'show';
	$error_text    = "Error de base de datos al obtener la lista de proveedores.";
} catch (Exception $e) {
	// General error handling
	error_log("Error fetching providers: " . $e->getMessage());
	$error_display = 'show';
	$error_text    = "Ocurrió un error inesperado al obtener la lista de proveedores: " . $e->getMessage();
}
#endregion try

require_once __ROOT__ . '/views/lproveedores.view.php';

?>
