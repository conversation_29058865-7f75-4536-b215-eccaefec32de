<?php
// Use the required classes with their namespaces
use App\classes\Empleado;
use App\classes\Usuario;
use App\classes\Perfil;

// Start session if not already started
if (session_status() === PHP_SESSION_NONE) {
	session_start();
}

/** @var PDO $conexion */
global $conexion; // Assuming $conexion is globally available or included

// Include necessary files
require_once dirname(__FILE__, 2) . '/config/config.php';
require_once __ROOT__ . '/src/sessions/sessions.php';
require_once __ROOT__ . '/src/general/general.php';
require_once __ROOT__ . '/src/general/validaciones.php';
require_once __ROOT__ . '/src/general/preparar.php';

// Check for valid database connection before proceeding
if (!$conexion instanceof PDO) {
	error_log("Error crítico: No hay conexión a la base de datos en lgasto_fijo.php.");
	$response['message'] = 'Error crítico: No se pudo conectar a la base de datos.';
	http_response_code(503); // Service Unavailable
	echo json_encode($response);
	exit;
}

#region region INIT VARIABLES
// Variables to hold form input (useful if re-displaying form after error)
$nombre        = '';
$email         = '';
$telefono      = '';
$direccion     = '';
$fecha_ingreso = '';
$porc_comision = 0;  // Initialize porc_comision for view repopulation
$username      = '';  // Initialize username for view repopulation
#endregion INIT VARIABLES

#region region Handle Flash Messages
// Check for a success flash message from session
if (!empty($_SESSION['flash_message_success'])) {
	$success_text    = $_SESSION['flash_message_success'];
	$success_display = 'show';
	// Clear the flash message so it doesn't show again
	unset($_SESSION['flash_message_success']);
}

// Check for an error flash message from session
if (!empty($_SESSION['flash_message_error'])) {
	$error_text    = $_SESSION['flash_message_error'];
	$error_display = 'show';
	// Clear the flash message so it doesn't show again
	unset($_SESSION['flash_message_error']);
}
#endregion Handle Flash Messages

#region POST Request Handling
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
	// 1. Get and sanitize form data
	$nombre = filter_input(INPUT_POST, 'nombre', FILTER_SANITIZE_SPECIAL_CHARS);
	$email = filter_input(INPUT_POST, 'email', FILTER_SANITIZE_EMAIL);
	$telefono = filter_input(INPUT_POST, 'telefono', FILTER_SANITIZE_SPECIAL_CHARS);
	$direccion = filter_input(INPUT_POST, 'direccion', FILTER_SANITIZE_SPECIAL_CHARS);
	$fecha_ingreso = filter_input(INPUT_POST, 'fecha_ingreso', FILTER_SANITIZE_SPECIAL_CHARS);
	$porc_comision_input = filter_input(INPUT_POST, 'porc_comision', FILTER_SANITIZE_SPECIAL_CHARS); // Get as string

	// Usuario fields
	$username = filter_input(INPUT_POST, 'username', FILTER_SANITIZE_SPECIAL_CHARS);
	$clave = filter_input(INPUT_POST, 'clave', FILTER_SANITIZE_SPECIAL_CHARS);
	$confirm_clave = filter_input(INPUT_POST, 'confirm_clave', FILTER_SANITIZE_SPECIAL_CHARS);

	try {
		// Set timezone for date handling
		date_default_timezone_set('America/Bogota');

		// 2. Validate data
		if (empty($nombre)) throw new Exception("El nombre es requerido.");

		// Validate email (required field)
		if (empty($email)) {
			throw new Exception("El correo es requerido.");
		} else if (!filter_var($email, FILTER_VALIDATE_EMAIL)) {
			throw new Exception("El formato del correo no es válido.");
		}

		// Validate date format if provided
		if (!empty($fecha_ingreso)) {
			$date = DateTime::createFromFormat('Y-m-d', $fecha_ingreso);
			if (!$date || $date->format('Y-m-d') !== $fecha_ingreso) {
				throw new Exception("El formato de la fecha de ingreso debe ser YYYY-MM-DD.");
			}
		}

		// Validate Porcentaje Comisión (now mandatory)
		if (empty($porc_comision_input)) {
			throw new Exception("El porcentaje de comisión es requerido.");
		}
		if (!is_numeric($porc_comision_input)) {
			$porc_comision = htmlspecialchars($porc_comision_input); // Repopulate for view
			throw new Exception("El porcentaje de comisión debe ser un valor numérico.");
		}
		$porc_comision_validated_float = (float)$porc_comision_input;
		if ($porc_comision_validated_float <= 0 || $porc_comision_validated_float > 100) {
			$porc_comision = htmlspecialchars($porc_comision_input); // Repopulate for view
			throw new Exception("El porcentaje de comisión debe ser mayor que 0 y no mayor que 100.");
		}

		// Validate Usuario fields
		if (empty($username)) {
			throw new Exception("El nombre de usuario es requerido.");
		}
		if (empty($clave)) {
			throw new Exception("La contraseña es requerida.");
		}
		if (empty($confirm_clave)) {
			throw new Exception("La confirmación de contraseña es requerida.");
		}
		if ($clave !== $confirm_clave) {
			throw new Exception("Las contraseñas no coinciden.");
		}

		// Convert username to uppercase
		$username = strtoupper($username);

		// 3. Get the "Barbero" profile
		$barbero_perfil = Perfil::getByNombre('Barbero', $conexion);
		if (!$barbero_perfil) {
			throw new Exception("No se encontró el perfil 'Barbero'. Contacte al administrador.");
		}

		// 4. Start transaction for creating both Empleado and Usuario
		$conexion->beginTransaction();

		try {
			// Create and save new Empleado using the class methods
			$empleado = new Empleado();

			// Set properties using setters
			$empleado->setNombre($nombre);
			$empleado->setEmail($email ?: null); // Convert empty string to null
			$empleado->setTelefono($telefono ?: null);
			$empleado->setDireccion($direccion ?: null);
			$empleado->setFecha_ingreso($fecha_ingreso ?: null);
			$empleado->setPorc_comision($porc_comision_validated_float);

			// Call the instance method to save to DB
			$newEmpleadoId = $empleado->crear($conexion);

			if ($newEmpleadoId === false || $newEmpleadoId <= 0) {
				throw new Exception("Hubo un error al guardar el empleado. Intente nuevamente.");
			}

			// Create and save new Usuario associated with the Empleado
			$usuario = new Usuario();
			$usuario->setUsername($username);
			$usuario->setClave($clave); // This will hash the password
			$usuario->setNombre($nombre); // Use the same name as the employee
			$usuario->setId_perfil($barbero_perfil->getId());
			$usuario->setId_empleado($newEmpleadoId);

			$newUsuarioId = $usuario->crear($conexion);

			if ($newUsuarioId === false || $newUsuarioId <= 0) {
				throw new Exception("Hubo un error al crear el usuario asociado. Intente nuevamente.");
			}

			// Commit the transaction
			$conexion->commit();

			$_SESSION['flash_message_success'] = "Empleado '$nombre' y usuario '$username' creados exitosamente.";

			header('Location: lempleados'); // Redirect to the employee list
			exit;

		} catch (Exception $e) {
			// Rollback the transaction on any error
			$conexion->rollBack();
			throw $e; // Re-throw the exception to be handled by the outer catch block
		}

	} catch (PDOException $e) {
		// Handle potential database errors
		// Log the detailed error: error_log("Database error: " . $e->getMessage());
		if (str_contains($e->getMessage(), 'Duplicate entry')) { // Basic check for unique constraint
			$error_text = 'Error: Ya existe un empleado con ese email.';
		} else {
			$error_text = 'Error de base de datos al crear el empleado. Por favor, contacte al administrador.';
		}
		$error_display = 'show';
	} catch (Exception $e) {
		// Handle other potential errors during employee creation
		// Log the detailed error: error_log("General error: " . $e->getMessage());
		$error_text    = 'Ocurrió un error inesperado al crear el empleado: ' . $e->getMessage();
		$error_display = 'show';
	}
}
#endregion POST Request Handling

// Load the view file. Variables defined above will be available in the view.
require_once __ROOT__ . '/views/iempleado.view.php';
?>
