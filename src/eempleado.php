<?php
// Use the required classes with their namespaces
use App\classes\Empleado;
use App\classes\Servicio;

// Start session if not already started
if (session_status() === PHP_SESSION_NONE) {
	session_start();
}

/** @var PDO $conexion */
global $conexion; // Assuming $conexion is globally available or included

// Include necessary files
require_once dirname(__FILE__, 2) . '/config/config.php';
require_once __ROOT__ . '/src/sessions/sessions.php';
require_once __ROOT__ . '/src/general/general.php';
require_once __ROOT__ . '/src/general/validaciones.php';
require_once __ROOT__ . '/src/general/preparar.php';

// Check for valid database connection before proceeding
if (!$conexion instanceof PDO) {
	error_log("Error crítico: No hay conexión a la base de datos en lgasto_fijo.php.");
	$response['message'] = 'Error crítico: No se pudo conectar a la base de datos.';
	http_response_code(503); // Service Unavailable
	echo json_encode($response);
	exit;
}
#region region INIT VARIABLES
// Variables to hold form input and employee data
$empleado           = null;
$id                 = 0;
$nombre             = '';
$email              = '';
$telefono           = '';
$direccion          = '';
$fecha_ingreso      = '';
$porc_comision      = 0; // Initialize porc_comision
$servicios          = []; // All active services
$servicios_empleado = []; // Services associated with this employee
#endregion INIT VARIABLES

#region region Handle Flash Messages
// Check for a success flash message from session
if (!empty($_SESSION['flash_message_success'])) {
	$success_text    = $_SESSION['flash_message_success'];
	$success_display = 'show';
	// Clear the flash message so it doesn't show again
	unset($_SESSION['flash_message_success']);
}

// Check for an error flash message from session
if (!empty($_SESSION['flash_message_error'])) {
	$error_text    = $_SESSION['flash_message_error'];
	$error_display = 'show';
	// Clear the flash message so it doesn't show again
	unset($_SESSION['flash_message_error']);
}
#endregion Handle Flash Messages

// First, check if this is an AJAX request to save services
// Handle this before any other processing to avoid redirects
$is_ajax_services_request = ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['action']) && $_POST['action'] === 'guardar_servicios');

#region region GET Request - Load Employee Data
// Skip validation redirects for AJAX requests
if (!$is_ajax_services_request) {
	// Get the employee ID from the query string
	$id = filter_input(INPUT_GET, 'id', FILTER_VALIDATE_INT);

	// If no ID provided or invalid, redirect to the employee list
	if (!$id || $id <= 0) {
		$_SESSION['flash_message_error'] = "ID de empleado inválido o no proporcionado.";
		header('Location: lempleados');
		exit;
	}

	// Try to load the employee data
	try {
		$empleado = Empleado::get($id, $conexion);

		// If employee not found, redirect to the employee list
		if (!$empleado) {
			$_SESSION['flash_message_error'] = "Empleado con ID $id no encontrado.";
			header('Location: lempleados');
			exit;
		}

		// Populate variables with employee data
		$nombre        = $empleado->getNombre();
		$email         = $empleado->getEmail();
		$telefono      = $empleado->getTelefono();
		$direccion     = $empleado->getDireccion();
		$fecha_ingreso = $empleado->getFecha_ingreso();
		$porc_comision = $empleado->getPorc_comision() ?? 0.00; // Load porc_comision

		// Get all active services
		$servicios = Servicio::get_list($conexion);

		// Get services associated with this employee
		$servicios_empleado = $empleado->getServicios($conexion);
	} catch (Exception $e) {
		$_SESSION['flash_message_error'] = "Error al cargar datos del empleado: " . $e->getMessage();
		header('Location: lempleados');
		exit;
	}
} // Close if (!$is_ajax_services_request)
#endregion GET Request - Load Employee Data

#region AJAX Endpoint for Services
// This is a completely separate endpoint for AJAX requests to save services
if ($is_ajax_services_request) {
	// Force content type to JSON
	header('Content-Type: application/json');

	// Initialize response
	$response = ['success' => false, 'message' => 'Error desconocido.'];

	// Debug: Add request data to response
	$response['debug'] = [
		'post_data' => $_POST,
		'empleado_id' => isset($_POST['empleado_id']) ? $_POST['empleado_id'] : 'not set'
	];

	try {
		// Set timezone for date handling
		date_default_timezone_set('America/Bogota');

		// Get the employee ID
		$empleadoId = filter_input(INPUT_POST, 'empleado_id', FILTER_VALIDATE_INT);

		// Validate employee ID
		if (!$empleadoId || $empleadoId <= 0) {
			throw new Exception("ID de empleado inválido.");
		}

		// Get the employee
		$empleado = Empleado::get($empleadoId, $conexion);
		if (!$empleado) {
			throw new Exception("Empleado no encontrado.");
		}

		// Get all services associated with this employee
		$serviciosActuales = $empleado->getServicios($conexion);
		$serviciosActualesIds = array_map(function($servicio) {
			return $servicio->getId();
		}, $serviciosActuales);

		// Get the selected service IDs from the form
		$serviciosSeleccionados = isset($_POST['servicios']) ? $_POST['servicios'] : [];

		// Ensure serviciosSeleccionados is an array
		if (!is_array($serviciosSeleccionados)) {
			$serviciosSeleccionados = [];
		}

		$serviciosSeleccionados = array_map('intval', $serviciosSeleccionados);

		// Add debug info
		$response['debug']['servicios_seleccionados'] = $serviciosSeleccionados;
		$response['debug']['servicios_actuales_ids'] = $serviciosActualesIds;

		// Services to add (selected but not currently associated)
		$serviciosParaAgregar = array_diff($serviciosSeleccionados, $serviciosActualesIds);

		// Services to remove (currently associated but not selected)
		$serviciosParaEliminar = array_diff($serviciosActualesIds, $serviciosSeleccionados);

		// Add new services
		foreach ($serviciosParaAgregar as $idServicio) {
			$empleado->asociarServicio($idServicio, $conexion);
		}

		// Remove unselected services
		foreach ($serviciosParaEliminar as $idServicio) {
			$empleado->desasociarServicio($idServicio, $conexion);
		}

		// Success response
		$response['success'] = true;
		$response['message'] = "Servicios actualizados correctamente para el empleado {$empleado->getNombre()}";

	} catch (PDOException $e) {
		// Handle database errors
		$response['message'] = "Error de base de datos: " . $e->getMessage();
		http_response_code(500);
	} catch (Exception $e) {
		// Handle other errors
		$response['message'] = "Error: " . $e->getMessage();
		http_response_code(400);
	}

	// Return JSON response
	echo json_encode($response);
	exit;
}

#region POST Request Handling for Regular Form
if ($_SERVER['REQUEST_METHOD'] === 'POST' && (!isset($_POST['action']) || $_POST['action'] !== 'guardar_servicios')) {

	// 1. Get and sanitize form data
	$nombre        = filter_input(INPUT_POST, 'nombre', FILTER_SANITIZE_SPECIAL_CHARS);
	$email         = filter_input(INPUT_POST, 'email', FILTER_SANITIZE_EMAIL);
	$telefono      = filter_input(INPUT_POST, 'telefono', FILTER_SANITIZE_SPECIAL_CHARS);
	$direccion     = filter_input(INPUT_POST, 'direccion', FILTER_SANITIZE_SPECIAL_CHARS);
	$fecha_ingreso = filter_input(INPUT_POST, 'fecha_ingreso', FILTER_SANITIZE_SPECIAL_CHARS);
	$porc_comision_input = filter_input(INPUT_POST, 'porc_comision', FILTER_SANITIZE_SPECIAL_CHARS); // Get as string

	try {
		// Set timezone for date handling
		date_default_timezone_set('America/Bogota');

		// 2. Validate data
		if (empty($nombre)) throw new Exception("El nombre es requerido.");

		// Validate email (required field)
		if (empty($email)) {
			throw new Exception("El correo es requerido.");
		} else if (!filter_var($email, FILTER_VALIDATE_EMAIL)) {
			throw new Exception("El formato del correo no es válido.");
		}

		// Validate date format if provided
		if (!empty($fecha_ingreso)) {
			$date = DateTime::createFromFormat('Y-m-d', $fecha_ingreso);
			if (!$date || $date->format('Y-m-d') !== $fecha_ingreso) {
				throw new Exception("El formato de la fecha de ingreso debe ser YYYY-MM-DD.");
			}
		}

		// Validate Porcentaje Comisión
		$porc_comision_validated_float = 0.00; // Default value for the Empleado object
		if (isset($porc_comision_input) && $porc_comision_input !== '') {
			if (!is_numeric($porc_comision_input)) {
				$porc_comision = htmlspecialchars($porc_comision_input); // Repopulate for view
				throw new Exception("El porcentaje de comisión debe ser un valor numérico.");
			}
			$temp_float = (float)$porc_comision_input;
			if ($temp_float < 0 || $temp_float > 100) {
				$porc_comision = htmlspecialchars($porc_comision_input); // Repopulate for view
				throw new Exception("El porcentaje de comisión debe estar entre 0 y 100.");
			}
			$porc_comision_validated_float = $temp_float;
		}

		// 3. Update the employee object
		$empleado->setNombre($nombre);
		$empleado->setEmail($email ?: null); // Convert empty string to null
		$empleado->setTelefono($telefono ?: null);
		$empleado->setDireccion($direccion ?: null);
		$empleado->setFecha_ingreso($fecha_ingreso ?: null);
		$empleado->setPorc_comision($porc_comision_validated_float);

		// 4. Save the changes
		$success = $empleado->modificar($conexion);

		if ($success) {
			$_SESSION['flash_message_success'] = "Empleado '$nombre' actualizado exitosamente.";
			header('Location: lempleados'); // Redirect to the employee list
			exit;
		} else {
			throw new Exception("Hubo un error al actualizar el empleado. Intente nuevamente.");
		}

	} catch (PDOException $e) {
		// Handle potential database errors
		// Log the detailed error: error_log("Database error: " . $e->getMessage());
		if (str_contains($e->getMessage(), 'Duplicate entry')) { // Basic check for unique constraint
			$error_text = 'Error: Ya existe un empleado con ese email.';
		} else {
			$error_text = 'Error de base de datos al actualizar el empleado. Por favor, contacte al administrador.';
		}
		$error_display = 'show';
	} catch (Exception $e) {
		// Handle other potential errors during employee update
		// Log the detailed error: error_log("General error: " . $e->getMessage());
		$error_text    = 'Ocurrió un error inesperado al actualizar el empleado: ' . $e->getMessage();
		$error_display = 'show';
	}
}
#endregion POST Request Handling

// Load the view file. Variables defined above will be available in the view.
require_once __ROOT__ . '/views/eempleado.view.php';
?>
