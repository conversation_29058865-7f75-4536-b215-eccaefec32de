<?php
// Use the Menu and Submenu classes with their namespaces
use App\classes\Menu;
use App\classes\Submenu;

// Start session if not already started
if (session_status() === PHP_SESSION_NONE) {
	session_start();
}

/** @var PDO $conexion */
global $conexion;

// Include necessary files
require_once dirname(__FILE__, 2) . '/config/config.php';
require_once __ROOT__ . '/src/sessions/sessions.php';
require_once __ROOT__ . '/src/general/general.php';
require_once __ROOT__ . '/src/general/validaciones.php';
require_once __ROOT__ . '/src/general/preparar.php';

#region region INIT VARIABLES
// Variables to hold form input
$id           = null;
$url          = '';
$texto        = '';
$icono        = '';
$prioridad    = 0;
$tiene_subs   = 0;
$estado       = 1;
$is_edit_mode = false;
$page_title   = 'Crear Nuevo Menú';
$submenus     = []; // Array to hold submenus if in edit mode
#endregion INIT VARIABLES

#region region EDIT MODE CHECK
// Check if we're in edit mode (ID provided in query string)
if (isset($_GET['id']) && is_numeric($_GET['id'])) {
	$id           = (int)$_GET['id'];
	$is_edit_mode = true;
	$page_title   = 'Editar Menú';

	try {
		// Load the menu data
		$menu = Menu::get($id, $conexion);

		if ($menu) {
			// Populate form variables with menu data
			$url        = $menu->getUrl();
			$texto      = $menu->getTexto();
			$icono      = $menu->getIcono();
			$prioridad  = $menu->getPrioridad();
			$tiene_subs = $menu->getTieneSubs();
			$estado     = $menu->getEstado();

			// Load associated submenus
			$submenus = Submenu::get_list($conexion, $id);
		} else {
			// Menu not found
			$_SESSION['flash_message_error'] = "El menú con ID $id no existe.";
			header('Location: lmenus');
			exit;
		}
	} catch (Exception $e) {
		$_SESSION['flash_message_error'] = "Error al cargar el menú: " . $e->getMessage();
		header('Location: lmenus');
		exit;
	}
}
#endregion EDIT MODE CHECK

#region region POST Request Handling
// First, handle AJAX requests for submenu operations
// Check for AJAX request by looking for the ajax_action parameter and X-Requested-With header
$isAjaxRequest = isset($_POST['ajax_action']) ||
                (isset($_SERVER['HTTP_X_REQUESTED_WITH']) &&
                 strtolower($_SERVER['HTTP_X_REQUESTED_WITH']) === 'xmlhttprequest');

if ($_SERVER['REQUEST_METHOD'] == 'POST' && $isAjaxRequest) {
	$response = ['success' => false, 'message' => '', 'data' => null];

	try {
		// Log the POST data for debugging
		error_log('AJAX Request POST data: ' . print_r($_POST, true));

		// Check if ajax_action is set
		if (!isset($_POST['ajax_action'])) {
			throw new Exception("Acción no especificada.");
		}

		// Check if we have a valid menu ID
		if (!isset($_POST['id_menu'])) {
			throw new Exception("ID de menú no proporcionado.");
		}

		if (!is_numeric($_POST['id_menu']) || (int)$_POST['id_menu'] <= 0) {
			throw new Exception("ID de menú inválido: {$_POST['id_menu']}");
		}

		$id_menu = (int)$_POST['id_menu'];

		// Check if the menu exists
		$menu = Menu::get($id_menu, $conexion);
		if (!$menu) {
			throw new Exception("El menú con ID $id_menu no existe.");
		}

		// Handle different AJAX actions
		switch ($_POST['ajax_action']) {
			case 'create_submenu':
				// Validate required fields
				if (empty(trim($_POST['texto']))) {
					throw new Exception("El texto del submenú es requerido.");
				}

				// Create new submenu
				$submenu = new Submenu();
				$submenu->setIdMenu($id_menu);
				$submenu->setTexto(trim($_POST['texto']));
				$submenu->setUrl(trim($_POST['url'] ?? ''));
				$submenu->setPrioridad(isset($_POST['prioridad']) ? (int)$_POST['prioridad'] : 0);
				$submenu->setEstado(1); // Active by default

				// Log submenu data before saving
				error_log('Creating submenu with data: ' .
					'id_menu=' . $submenu->getIdMenu() . ', ' .
					'texto=' . $submenu->getTexto() . ', ' .
					'url=' . $submenu->getUrl() . ', ' .
					'prioridad=' . $submenu->getPrioridad() . ', ' .
					'estado=' . $submenu->getEstado());

				// Save to database
				try {
					$newSubmenuId = $submenu->crear($conexion);
				} catch (Exception $e) {
					error_log('Error creating submenu: ' . $e->getMessage());
					throw new Exception('Error al crear el submenú: ' . $e->getMessage());
				}

				if ($newSubmenuId !== false && $newSubmenuId > 0) {
					// Update menu's tiene_subs property
					$menu->setTieneSubs(1);
					$menu->modificar($conexion);

					// Return success response with the new submenu data
					$response['success'] = true;
					$response['message'] = "Submenú creado exitosamente.";
					$response['data'] = [
						'id' => $newSubmenuId, // Keep ID for data attributes and operations
						'id_menu' => $id_menu,
						'texto' => $submenu->getTexto(),
						'url' => $submenu->getUrl(),
						'prioridad' => $submenu->getPrioridad()
						// Estado removed as it's no longer displayed in the UI
					];
				} else {
					throw new Exception("Error al crear el submenú.");
				}
				break;

			case 'update_submenu':
				// Validate required fields
				if (empty(trim($_POST['texto']))) {
					throw new Exception("El texto del submenú es requerido.");
				}

				if (!isset($_POST['id']) || !is_numeric($_POST['id'])) {
					throw new Exception("ID del submenú inválido.");
				}

				$submenu_id = (int)$_POST['id'];

				// Get the submenu
				$submenu = Submenu::get($submenu_id, $conexion);
				if (!$submenu) {
					throw new Exception("El submenú con ID $submenu_id no existe.");
				}

				// Update submenu properties
				$submenu->setTexto(trim($_POST['texto']));
				$submenu->setUrl(trim($_POST['url'] ?? ''));
				$submenu->setPrioridad(isset($_POST['prioridad']) ? (int)$_POST['prioridad'] : 0);

				// Save to database
				$success = $submenu->modificar($conexion);

				if ($success) {
					$response['success'] = true;
					$response['message'] = "Submenú actualizado exitosamente.";
					$response['data'] = [
						'id' => $submenu->getId(), // Keep ID for data attributes and operations
						'id_menu' => $submenu->getIdMenu(),
						'texto' => $submenu->getTexto(),
						'url' => $submenu->getUrl(),
						'prioridad' => $submenu->getPrioridad()
						// Estado removed as it's no longer displayed in the UI
					];
				} else {
					throw new Exception("Error al actualizar el submenú.");
				}
				break;

			case 'delete_submenu':
				if (!isset($_POST['id']) || !is_numeric($_POST['id'])) {
					throw new Exception("ID del submenú inválido.");
				}

				$submenu_id = (int)$_POST['id'];

				// Delete (deactivate) submenu
				$success = Submenu::desactivar($submenu_id, $conexion);

				if ($success) {
					// Check if there are any active submenus left for this menu
					$remaining_submenus = Submenu::get_list($conexion, $id_menu);

					// If no submenus left, update menu's tiene_subs property
					if (empty($remaining_submenus)) {
						$menu->setTieneSubs(0);
						$menu->modificar($conexion);
					}

					$response['success'] = true;
					$response['message'] = "Submenú eliminado exitosamente.";
					$response['data'] = ['id' => $submenu_id];
				} else {
					throw new Exception("Error al eliminar el submenú.");
				}
				break;

			default:
				throw new Exception("Acción no reconocida.");
		}
	} catch (PDOException $e) {
		error_log('PDO Exception in AJAX handler: ' . $e->getMessage());
		$response['message'] = "Error de base de datos: " . $e->getMessage();
		$response['error_details'] = $e->getMessage();
	} catch (Exception $e) {
		error_log('Exception in AJAX handler: ' . $e->getMessage());
		$response['message'] = $e->getMessage();
		$response['error_details'] = $e->getMessage();
	}

	// Return JSON response
	// Make sure we're sending the correct content type
	header('Content-Type: application/json');
	echo json_encode($response);
	exit;
}
// Handle regular form submissions for menu creation/editing
else if ($_SERVER['REQUEST_METHOD'] == 'POST') {
	try {
		// 1. Get data from $_POST
		$url        = trim($_POST['url'] ?? '');
		$texto      = trim($_POST['texto'] ?? '');
		$icono      = trim($_POST['icono'] ?? '');
		$prioridad  = isset($_POST['prioridad']) ? (int)$_POST['prioridad'] : 0;
		$tiene_subs = isset($_POST['tiene_subs']) ? 1 : 0;
		// For new menus, estado is always 1 (active)
		// For existing menus, keep the current estado value

		// 2. Validate data
		if (empty($texto)) throw new Exception("El texto del menú es requerido.");
		if (empty($url)) throw new Exception("La URL del menú es requerida.");

		// 3. Create or update Menu
		$menu = new Menu();

		if ($is_edit_mode) {
			$menu->setId($id);
		}

		// Set properties using setters
		$menu->setUrl($url);
		$menu->setTexto($texto);
		$menu->setIcono($icono);
		$menu->setPrioridad($prioridad);
		$menu->setTieneSubs($tiene_subs);

		// Set estado based on whether we're creating or editing
		if ($is_edit_mode) {
			// Keep existing estado for edits
			$menu->setEstado($estado);
		} else {
			// New menus are always active
			$menu->setEstado(1);
		}

		if ($is_edit_mode) {
			// Update existing menu
			$success = $menu->modificar($conexion);
			$message = "Menú actualizado exitosamente.";
		} else {
			// Create new menu
			$newMenuId = $menu->crear($conexion);
			$success   = ($newMenuId !== false && $newMenuId > 0);
			$message   = "Menú creado exitosamente.";
		}

		if ($success) {
			$_SESSION['flash_message_success'] = $message;
			header('Location: lmenus');
			exit;
		} else {
			throw new Exception("No se pudo " . ($is_edit_mode ? "actualizar" : "crear") . " el menú.");
		}

	} catch (PDOException $e) {
		// Handle database errors
		$error_text    = 'Error de base de datos: ' . $e->getMessage();
		$error_display = 'show';
	} catch (Exception $e) {
		// Handle other potential errors
		$error_text    = 'Ocurrió un error: ' . $e->getMessage();
		$error_display = 'show';
	}
}
#endregion POST Request Handling

#region region AJAX Request Handling
// AJAX request handling has been moved to the top of the file
#endregion AJAX Request Handling

// Load the view file. Variables defined above will be available in the view.
require_once __ROOT__ . '/views/imenu.view.php';
?>
