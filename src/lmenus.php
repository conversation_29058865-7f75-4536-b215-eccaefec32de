<?php

// Iniciar sesión si es necesario
use App\classes\Menu;

if (session_status() === PHP_SESSION_NONE) {
	session_start();
}

/** @var PDO $conexion */
global $conexion;

require_once dirname(__FILE__, 2) . '/config/config.php';
require_once __ROOT__ . '/src/sessions/sessions.php';
require_once __ROOT__ . '/src/general/general.php';
require_once __ROOT__ . '/src/general/validaciones.php';
require_once __ROOT__ . '/src/general/preparar.php';

// Check for valid database connection before proceeding
if (!$conexion instanceof PDO) {
	error_log("Error crítico: No hay conexión a la base de datos en lmenus.php (POST).");
	$response['message'] = 'Error crítico: No se pudo conectar a la base de datos.';
	http_response_code(503); // Service Unavailable
	echo json_encode($response);
	exit;
}

#region region init variables
$menus = []; // Initialize as an empty array
$filtro_texto = isset($_GET['filtro_texto']) ? trim($_GET['filtro_texto']) : '';
#endregion init variables

#region region Handle Flash Message Success
// Check for a success flash message from session
if (!empty($_SESSION['flash_message_success'])) {
	$success_text    = $_SESSION['flash_message_success'];
	$success_display = 'show';
	// Clear the flash message so it doesn't show again
	unset($_SESSION['flash_message_success']);
}
#endregion Handle Flash Message Success

#region region Handle Flash Message Error
// Check for an error flash message from session
if (!empty($_SESSION['flash_message_error'])) {
	$error_text    = $_SESSION['flash_message_error'];
	$error_display = 'show';
	// Clear the flash message so it doesn't show again
	unset($_SESSION['flash_message_error']);
}
#endregion Handle Flash Message Error

#region region Handle POST Actions (Deactivation)
// --- Handle Non-AJAX POST Action (Deactivate Menu) ---
if ($_SERVER['REQUEST_METHOD'] == 'POST' && isset($_POST['action']) && $_POST['action'] == 'desactivar') {
	$menuIdToDeactivate = filter_input(INPUT_POST, 'menuId', FILTER_VALIDATE_INT);
	
	if ($menuIdToDeactivate) {
		try {
			$success = Menu::desactivar($menuIdToDeactivate, $conexion);
			
			if ($success) {
				$_SESSION['flash_message_success'] = "Menú desactivado correctamente.";
			} else {
				$_SESSION['flash_message_error'] = "Error: No se pudo encontrar o desactivar el menú.";
			}
		} catch (Exception $e) {
			$_SESSION['flash_message_error'] = "Error al desactivar menú: " . $e->getMessage();
		}
	} else {
		$_SESSION['flash_message_error'] = "Error: ID de menú inválido para desactivar.";
	}
	
	// Redirect back to the menu list page after processing
	header('Location: lmenus');
	exit;
}
#endregion Handle POST Actions

#region try
try {
	// Get all menus
	$menus = Menu::get_list($conexion);
	
	// Apply filter if provided
	if (!empty($filtro_texto)) {
		$menus_filtrados = [];
		foreach ($menus as $menu) {
			if (stripos($menu->getTexto(), $filtro_texto) !== false) {
				$menus_filtrados[] = $menu;
			}
		}
		$menus = $menus_filtrados;
	}
	
} catch (PDOException $e) {
	// Specific handling for database errors
	$error_display = 'show';
	$error_text    = "Error de base de datos al obtener la lista de menús.";
} catch (Exception $e) {
	// General error handling
	$error_display = 'show';
	$error_text    = "Ocurrió un error inesperado al obtener la lista de menús: " . $e->getMessage();
}
#endregion try

require_once __ROOT__ . '/views/lmenus.view.php';

?>
