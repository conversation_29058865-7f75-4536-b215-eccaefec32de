<?php

declare(strict_types=1);

namespace App\classes;

use Exception;
use PDO;
use PDOException;

class Proveedor
{
	// --- Atributos ---
	private ?int    $id        = null;
	private ?string $nombre    = null;
	private ?string $nit       = null;
	private ?string $telefono  = null;
	private ?string $correo    = null;
	private ?string $direccion = null;
	private ?int    $estado    = null;

	/**
	 * Constructor: Inicializa las propiedades del objeto Proveedor.
	 */
	public function __construct()
	{
		$this->id        = 0;     // O null si prefieres no usar 0 por defecto
		$this->nombre    = null;
		$this->nit       = null;
		$this->telefono  = null;
		$this->correo    = null;
		$this->direccion = null;
		$this->estado    = 1;     // Estado activo por defecto
	}

	/**
	 * Método estático para construir un objeto Proveedor desde un array (ej. fila de DB).
	 *
	 * @param array $resultado Array asociativo con los datos del proveedor.
	 *
	 * @return self Instancia de Proveedor.
	 * @throws Exception Si ocurre un error durante la construcción.
	 */
	public static function construct(array $resultado = []): self
	{
		try {
			$objeto            = new self();
			$objeto->id        = isset($resultado['id']) ? (int)$resultado['id'] : 0;
			$objeto->nombre    = $resultado['nombre'] ?? null;
			$objeto->nit       = $resultado['nit'] ?? null;
			$objeto->telefono  = $resultado['telefono'] ?? null;
			$objeto->correo    = $resultado['correo'] ?? null;
			$objeto->direccion = $resultado['direccion'] ?? null;
			$objeto->estado    = isset($resultado['estado']) ? (int)$resultado['estado'] : 1;

			return $objeto;

		} catch (Exception $e) {
			throw new Exception("Error al construir objeto Proveedor: " . $e->getMessage());
		}
	}

	/**
	 * Obtiene un proveedor por su ID.
	 *
	 * @param int $id       ID del proveedor.
	 * @param PDO $conexion Conexión PDO.
	 *
	 * @return self|null Objeto Proveedor o null si no se encuentra.
	 * @throws Exception Si hay error en DB.
	 */
	public static function get(int $id, PDO $conexion): ?self
	{
		try {
			// Consulta para obtener proveedor por ID (Usando Heredoc)
			$query = <<<SQL
            SELECT
            	p.*
            FROM proveedores p
            WHERE
            	p.id = :id
            LIMIT 1
            SQL;

			$statement = $conexion->prepare($query);
			$statement->bindValue(':id', $id, PDO::PARAM_INT);
			$statement->execute();
			$resultado = $statement->fetch(PDO::FETCH_ASSOC);

			return $resultado ? self::construct($resultado) : null;

		} catch (PDOException $e) {
			throw new Exception("Error de base de datos al obtener proveedor: " . $e->getMessage());
		}
	}

	/**
	 * Obtiene una lista de proveedores activos.
	 *
	 * @param PDO $conexion Conexión PDO.
	 *
	 * @return array Array de objetos Proveedor.
	 * @throws Exception Si hay error en DB.
	 */
	public static function get_list(PDO $conexion): array
	{
		try {
			// Consulta para obtener lista de proveedores activos (Usando Heredoc)
			$query = <<<SQL
            SELECT
            	p.*
            FROM proveedores p
            WHERE
            	p.estado = 1
            ORDER BY
            	p.nombre
            SQL;

			$statement = $conexion->prepare($query);
			$statement->execute();
			$resultados = $statement->fetchAll(PDO::FETCH_ASSOC);

			$listado = [];
			foreach ($resultados as $resultado) {
				$listado[] = self::construct($resultado);
			}
			return $listado;

		} catch (PDOException $e) {
			throw new Exception("Error de base de datos al obtener lista de proveedores: " . $e->getMessage());
		}
	}

	/**
	 * Crea un nuevo proveedor en la base de datos a partir de un objeto Proveedor.
	 * El objeto Proveedor debe estar completamente poblado con los datos requeridos.
	 *
	 * @param PDO $conexion Conexión PDO.
	 *
	 * @return int|false El ID del nuevo proveedor creado o false en caso de error.
	 * @throws Exception Si los datos requeridos en el objeto están vacíos o hay error en DB.
	 */
	function crear(PDO $conexion): int|false
	{
		// Validaciones básicas sobre el objeto
		if (empty($this->getNombre())) {
			throw new Exception("El nombre es requerido para crear un proveedor.");
		}

		// Validar formato de email si está presente
		if (!empty($this->getCorreo()) && !filter_var($this->getCorreo(), FILTER_VALIDATE_EMAIL)) {
			throw new Exception("El formato del correo no es válido.");
		}

		try {
			return $this->_insert($conexion);
		} catch (Exception $e) {
			// Capturar otros errores
			throw new Exception("Error al crear proveedor: " . $e->getMessage());
		}
	}

	/**
	 * Método privado para insertar un nuevo proveedor en la base de datos.
	 *
	 * @param PDO $conexion Conexión PDO.
	 *
	 * @return int|false El ID del nuevo proveedor creado o false en caso de error.
	 * @throws Exception Si hay error en DB.
	 */
	private function _insert(PDO $conexion): int|false
	{
		try {
			// Preparar la consulta INSERT usando Heredoc
			$query = <<<SQL
            INSERT INTO proveedores (
            	 nombre
            	,nit
            	,telefono
            	,correo
            	,direccion
            	,estado
            ) VALUES (
            	 :nombre
            	,:nit
            	,:telefono
            	,:correo
            	,:direccion
            	,:estado
            )
            SQL;

			$statement = $conexion->prepare($query);

			// Bind de parámetros desde el objeto
			$statement->bindValue(':nombre', $this->getNombre(), PDO::PARAM_STR);
			$statement->bindValue(':nit', $this->getNit(), $this->getNit() === null ? PDO::PARAM_NULL : PDO::PARAM_STR);
			$statement->bindValue(':telefono', $this->getTelefono(), $this->getTelefono() === null ? PDO::PARAM_NULL : PDO::PARAM_STR);
			$statement->bindValue(':correo', $this->getCorreo(), $this->getCorreo() === null ? PDO::PARAM_NULL : PDO::PARAM_STR);
			$statement->bindValue(':direccion', $this->getDireccion(), $this->getDireccion() === null ? PDO::PARAM_NULL : PDO::PARAM_STR);
			$statement->bindValue(':estado', $this->getEstado(), PDO::PARAM_INT);

			// Ejecutar la consulta
			$success = $statement->execute();

			if ($success) {
				// Devolver el ID del proveedor recién creado
				return (int)$conexion->lastInsertId();
			} else {
				return false; // Error en la ejecución
			}

		} catch (PDOException $e) {
			// Manejar errores específicos de DB (ej. NIT o correo duplicado)
			if ($e->getCode() == 23000 || $e->getCode() == 1062) { // Códigos comunes para violación de UNIQUE constraint
				$errorMessage = $e->getMessage();
				if (strpos($errorMessage, 'nit') !== false) {
					throw new Exception("Error al crear proveedor: El NIT '{$this->getNit()}' ya existe.");
				} elseif (strpos($errorMessage, 'correo') !== false) {
					throw new Exception("Error al crear proveedor: El correo '{$this->getCorreo()}' ya existe.");
				} else {
					throw new Exception("Error al crear proveedor: Datos duplicados.");
				}
			} else {
				throw new Exception("Error de base de datos al crear proveedor: " . $e->getMessage());
			}
		} catch (Exception $e) {
			// Capturar otros errores
			throw new Exception("Error al crear proveedor: " . $e->getMessage());
		}
	}

	/**
	 * Modifica un proveedor existente.
	 *
	 * @param PDO $conexion Conexión PDO.
	 *
	 * @return bool True si la modificación fue exitosa, False en caso contrario.
	 * @throws Exception Si los datos requeridos están vacíos o si ocurre un error de base de datos.
	 */
	function modificar(PDO $conexion): bool
	{
		// Validar que el ID sea válido
		if ($this->getId() <= 0) {
			throw new Exception("ID de proveedor no válido para modificar.");
		}

		// Validar que el nombre no esté vacío
		if (empty(trim($this->getNombre()))) {
			throw new Exception("El nombre no puede estar vacío.");
		}

		// Validar formato de email si está presente
		if (!empty($this->getCorreo()) && !filter_var($this->getCorreo(), FILTER_VALIDATE_EMAIL)) {
			throw new Exception("El formato del correo no es válido.");
		}

		try {
			return $this->_update($conexion);
		} catch (Exception $e) {
			// Capturar otros errores
			throw new Exception("Error al modificar proveedor: " . $e->getMessage());
		}
	}

	/**
	 * Método privado para actualizar un proveedor existente en la base de datos.
	 *
	 * @param PDO $conexion Conexión PDO.
	 *
	 * @return bool True si la actualización fue exitosa, False en caso contrario.
	 * @throws Exception Si hay error en DB.
	 */
	private function _update(PDO $conexion): bool
	{
		try {
			// Consulta para actualizar el proveedor
			$query = <<<SQL
            UPDATE proveedores SET
                nombre = :nombre,
                nit = :nit,
                telefono = :telefono,
                correo = :correo,
                direccion = :direccion,
                estado = :estado
            WHERE
                id = :id
            SQL;

			$statement = $conexion->prepare($query);

			// Bind de parámetros desde el objeto
			$statement->bindValue(':nombre', $this->getNombre(), PDO::PARAM_STR);
			$statement->bindValue(':nit', $this->getNit(), $this->getNit() === null ? PDO::PARAM_NULL : PDO::PARAM_STR);
			$statement->bindValue(':telefono', $this->getTelefono(), $this->getTelefono() === null ? PDO::PARAM_NULL : PDO::PARAM_STR);
			$statement->bindValue(':correo', $this->getCorreo(), $this->getCorreo() === null ? PDO::PARAM_NULL : PDO::PARAM_STR);
			$statement->bindValue(':direccion', $this->getDireccion(), $this->getDireccion() === null ? PDO::PARAM_NULL : PDO::PARAM_STR);
			$statement->bindValue(':estado', $this->getEstado(), PDO::PARAM_INT);
			$statement->bindValue(':id', $this->getId(), PDO::PARAM_INT);

			// execute() devuelve true en éxito, false en error.
			return $statement->execute();

		} catch (PDOException $e) {
			// Manejar errores específicos de DB (ej. NIT o correo duplicado)
			if ($e->getCode() == 23000 || $e->getCode() == 1062) { // Códigos comunes para violación de UNIQUE constraint
				$errorMessage = $e->getMessage();
				if (strpos($errorMessage, 'nit') !== false) {
					throw new Exception("Error al modificar proveedor: El NIT '{$this->getNit()}' ya existe.");
				} elseif (strpos($errorMessage, 'correo') !== false) {
					throw new Exception("Error al modificar proveedor: El correo '{$this->getCorreo()}' ya existe.");
				} else {
					throw new Exception("Error al modificar proveedor: Datos duplicados.");
				}
			} else {
				throw new Exception("Error de base de datos al modificar proveedor: " . $e->getMessage());
			}
		}
	}

	/**
	 * Desactiva un proveedor estableciendo su estado a 0.
	 *
	 * @param int $id       ID del proveedor a desactivar.
	 * @param PDO $conexion Conexión PDO.
	 *
	 * @return bool True si la desactivación fue exitosa, False en caso contrario.
	 * @throws Exception Si ocurre un error de base de datos.
	 */
	public static function desactivar(int $id, PDO $conexion): bool
	{
		try {
			// Consulta para actualizar el estado a 0 (inactivo)
			$query = <<<SQL
            UPDATE proveedores SET
            	estado = 0
            WHERE
            	id = :id
            SQL;

			$statement = $conexion->prepare($query);
			$statement->bindValue(':id', $id, PDO::PARAM_INT);

			// execute() devuelve true en éxito, false en error.
			return $statement->execute();

		} catch (PDOException $e) {
			// Error de base de datos
			throw new Exception("Error de base de datos al desactivar proveedor (ID: $id): " . $e->getMessage());
		}
	}

	// --- Getters y Setters ---

	public function getId(): ?int
	{
		return $this->id;
	}

	public function setId(?int $id): self
	{
		$this->id = $id;
		return $this;
	}

	public function getNombre(): ?string
	{
		return $this->nombre;
	}

	public function setNombre(?string $nombre): self
	{
		$this->nombre = $nombre;
		return $this;
	}

	public function getNit(): ?string
	{
		return $this->nit;
	}

	public function setNit(?string $nit): self
	{
		$this->nit = $nit;
		return $this;
	}

	public function getTelefono(): ?string
	{
		return $this->telefono;
	}

	public function setTelefono(?string $telefono): self
	{
		$this->telefono = $telefono;
		return $this;
	}

	public function getCorreo(): ?string
	{
		return $this->correo;
	}

	public function setCorreo(?string $correo): self
	{
		$this->correo = $correo;
		return $this;
	}

	public function getDireccion(): ?string
	{
		return $this->direccion;
	}

	public function setDireccion(?string $direccion): self
	{
		$this->direccion = $direccion;
		return $this;
	}

	public function getEstado(): ?int
	{
		return $this->estado;
	}

	public function setEstado(?int $estado): self
	{
		$this->estado = $estado;
		return $this;
	}

	// --- Métodos adicionales ---

	/**
	 * Verifica si el proveedor está activo.
	 * @return bool
	 */
	public function isActivo(): bool
	{
		// Asegúrate de que el estado no sea null antes de comparar
		return $this->estado === 1;
	}
}
