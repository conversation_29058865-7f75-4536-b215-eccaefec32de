<?php

declare(strict_types=1);

namespace App\classes;

use Exception;
use PDO;
use PDOException;

// Import PDOException for better error handling

class Usuario
{
	// --- Atributos ---
	private ?int    $id           = null;
	private ?string $username     = null;
	private ?string $clave        = null; // Almacenará el HASH de la clave
	private ?string $nombre       = null;
	private ?int    $estado       = null;
	private ?int    $id_perfil    = null;
	private ?string $nombre_perfil = null;
	private ?int    $id_empleado  = null; // Referencia opcional a la tabla empleados
	private ?string $nombre_empleado = null; // Nombre del empleado asociado

	/**
	 * Constructor: Inicializa las propiedades del objeto Usuario.
	 */
	public function __construct()
	{
		$this->id           = 0; // O null si prefieres no usar 0 por defecto
		$this->username     = null;
		$this->clave        = null;
		$this->nombre       = null;
		$this->estado       = 1; // Estado activo por defecto
		$this->id_perfil    = null;
		$this->nombre_perfil = null;
		$this->id_empleado  = null; // Opcional, puede ser null
		$this->nombre_empleado = ''; // Nombre del empleado asociado
	}

	/**
	 * Método estático para construir un objeto Usuario desde un array (ej. fila de DB).
	 *
	 * @param array $resultado Array asociativo con los datos del usuario.
	 *
	 * @return self Instancia de Usuario.
	 * @throws Exception Si ocurre un error durante la construcción.
	 */
	public static function construct(array $resultado = []): self
	{
		try {
			$objeto               = new self();
			$objeto->id           = isset($resultado['id']) ? (int)$resultado['id'] : 0;
			$objeto->username     = $resultado['username'] ?? null;
			$objeto->clave        = $resultado['clave'] ?? null; // La clave viene hasheada de la DB
			$objeto->nombre       = $resultado['nombre'] ?? null;
			$objeto->estado       = isset($resultado['estado']) ? (int)$resultado['estado'] : 1;
			$objeto->id_perfil    = isset($resultado['id_perfil']) ? (int)$resultado['id_perfil'] : null;
			$objeto->nombre_perfil = $resultado['nombre_perfil'] ?? null;
			$objeto->id_empleado  = isset($resultado['id_empleado']) ? (int)$resultado['id_empleado'] : null;
			$objeto->nombre_empleado = $resultado['nombre_empleado'] ?? '';
			return $objeto;
		} catch (Exception $e) {
			// Considera loggear el error aquí
			throw new Exception("Error al construir Usuario: " . $e->getMessage());
		}
	}

	// --- Métodos de Acceso a Datos (Estáticos) ---

	/**
	 * Valida las credenciales del usuario usando password_verify().
	 *
	 * @param string $username   Nombre de usuario.
	 * @param string $clavePlana Contraseña en texto plano a verificar.
	 * @param PDO    $conexion   Conexión PDO a la base de datos.
	 *
	 * @return self|null Devuelve el objeto Usuario si las credenciales son válidas, null en caso contrario.
	 * @throws Exception Si ocurre un error de base de datos.
	 */
	public static function validarCredenciales(string $username, string $clavePlana, PDO $conexion): ?self
	{
		try {
			// Selecciona el usuario incluyendo el hash de la clave (Usando Heredoc)
			$query = <<<SQL
            SELECT
            	u.*,
            	p.nombre AS nombre_perfil
            FROM usuarios u
            LEFT JOIN perfiles p ON u.id_perfil = p.id
            WHERE
            	u.username = :username AND u.estado = 1
            LIMIT 1
            SQL;
			// Asume tabla 'usuarios'

			$statement = $conexion->prepare($query);
			$statement->bindValue(':username', $username, PDO::PARAM_STR);
			$statement->execute();
			$resultado = $statement->fetch(PDO::FETCH_ASSOC);

			if ($resultado) {
				$claveHasheadaAlmacenada = $resultado['clave'];
				// Verifica la contraseña plana contra el hash almacenado de forma segura
				if (password_verify($clavePlana, $claveHasheadaAlmacenada)) {
					// La contraseña es correcta
					return self::construct($resultado);
				} else {
					// Contraseña incorrecta
					return null;
				}
			} else {
				// Usuario no encontrado o inactivo
				return null;
			}
		} catch (PDOException $e) {
			// Error de base de datos
			throw new Exception("Error de base de datos al validar credenciales: " . $e->getMessage());
		}
	}

	/**
	 * Actualiza la contraseña de un usuario por su ID usando password_hash().
	 *
	 * @param int    $id              ID del usuario.
	 * @param string $nuevaClavePlana La nueva contraseña en texto plano.
	 * @param PDO    $conexion        Conexión PDO a la base de datos.
	 *
	 * @return bool True si la actualización fue exitosa, False en caso contrario.
	 * @throws Exception Si la contraseña está vacía o si ocurre un error de base de datos o hash.
	 */
	public static function actualizarClave(int $id, string $nuevaClavePlana, PDO $conexion): bool
	{
		if (empty($nuevaClavePlana)) {
			throw new Exception("La nueva contraseña no puede estar vacía.");
		}
		try {
			// Crea el hash seguro de la nueva contraseña
			$nuevaClaveHasheada = password_hash($nuevaClavePlana, PASSWORD_DEFAULT);

			if (!$nuevaClaveHasheada) {
				throw new Exception("Error al generar el hash de la contraseña.");
			}

			// Actualiza la base de datos con el nuevo hash (Usando Heredoc)
			$query = <<<SQL
            UPDATE usuarios SET
            	clave = :clave
            WHERE
            	id = :id
            SQL;

			$statement = $conexion->prepare($query);
			$statement->bindValue(':clave', $nuevaClaveHasheada, PDO::PARAM_STR);
			$statement->bindValue(':id', $id, PDO::PARAM_INT);

			return $statement->execute();

		} catch (PDOException $e) {
			// Error de base de datos
			throw new Exception("Error de base de datos al actualizar clave: " . $e->getMessage());
		}
	}

	/**
	 * Obtiene un usuario por su ID.
	 *
	 * @param int $id       ID del usuario.
	 * @param PDO $conexion Conexión PDO.
	 *
	 * @return self|null Objeto Usuario o null si no se encuentra.
	 * @throws Exception Si hay error en DB.
	 */
	public static function get(int $id, PDO $conexion): ?self
	{
		try {
			// Consulta para obtener usuario por ID (Usando Heredoc)
			$query = <<<SQL
            SELECT
            	u.*,
            	p.nombre AS nombre_perfil,
            	e.nombre AS nombre_empleado
            FROM usuarios u
            LEFT JOIN perfiles p ON u.id_perfil = p.id
            LEFT JOIN empleados e ON u.id_empleado = e.id
            WHERE
            	u.id = :id
            LIMIT 1
            SQL;
			// Asume tabla 'usuarios'

			$statement = $conexion->prepare($query);
			$statement->bindValue(":id", $id, PDO::PARAM_INT);
			$statement->execute();
			$resultado = $statement->fetch(PDO::FETCH_ASSOC);

			return $resultado ? self::construct($resultado) : null;

		} catch (PDOException $e) {
			throw new Exception("Error al obtener Usuario (ID: $id): " . $e->getMessage());
		}
	}

	/**
	 * Obtiene una lista de usuarios activos.
	 *
	 * @param PDO $conexion Conexión PDO.
	 *
	 * @return array Array de objetos Usuario.
	 * @throws Exception Si hay error en DB.
	 */
	public static function get_list(PDO $conexion): array
	{
		try {
			// Consulta para obtener lista de usuarios activos (Usando Heredoc)
			$query = <<<SQL
            SELECT
            	u.*,
            	p.nombre AS nombre_perfil,
            	e.nombre AS nombre_empleado
            FROM usuarios u
            LEFT JOIN perfiles p ON u.id_perfil = p.id
            LEFT JOIN empleados e ON u.id_empleado = e.id
            WHERE
            	u.estado = 1
            ORDER BY
            	u.nombre
            SQL;

			$statement = $conexion->prepare($query);
			$statement->execute();
			$resultados = $statement->fetchAll(PDO::FETCH_ASSOC);

			$listado = [];
			foreach ($resultados as $resultado) {
				$listado[] = self::construct($resultado);
			}
			return $listado;

		} catch (PDOException $e) {
			throw new Exception("Error al obtener lista de Usuarios: " . $e->getMessage());
		}
	}

	/**
	 * Crea un nuevo usuario en la base de datos a partir de un objeto Usuario.
	 * El objeto Usuario debe estar completamente poblado, incluyendo la clave
	 * (se asume que setClave() fue llamado previamente con la clave plana, generando el hash).
	 *
	 * @param PDO $conexion Conexión PDO.
	 *
	 * @return int|false El ID del nuevo usuario creado o false en caso de error.
	 * @throws Exception Si los datos requeridos en el objeto están vacíos o hay error en DB.
	 */
	function crear(PDO $conexion): int|false
	{
		// Validaciones básicas sobre el objeto
		// Se verifica que el hash de la clave exista (implica que setClave fue llamado)
		if (empty($this->getUsername()) || empty($this->getClave()) || empty($this->getNombre()) || $this->getId_perfil() === null) {
			throw new Exception("Username, clave (hasheada), nombre y id_perfil son requeridos en el objeto Usuario para crearlo.");
		}

		// Validaciones de negocio
		$this->validarReglasNegocio($conexion);

		// Podríamos añadir más validaciones sobre el objeto si es necesario (ej. formato de username)

		try {
			// La contraseña ya debe estar hasheada en el objeto via setClave()
			$claveHasheada = $this->getClave();
			$username      = $this->getUsername(); // Para usar en mensaje de error

			// Preparar la consulta INSERT usando Heredoc
			$query = <<<SQL
            INSERT INTO usuarios (
            	 username
            	,clave
            	,nombre
            	,id_perfil
            	,id_empleado
            ) VALUES (
            	 :username
            	,:clave
            	,:nombre
            	,:id_perfil
            	,:id_empleado
            )
            SQL;
			// Asume tabla 'usuarios'

			$statement = $conexion->prepare($query);

			// Bind de parámetros desde el objeto
			$statement->bindValue(':username', mb_strtoupper($this->getUsername()), PDO::PARAM_STR);
			$statement->bindValue(':clave', $claveHasheada, PDO::PARAM_STR); // Usar el hash del objeto
			$statement->bindValue(':nombre', $this->getNombre(), PDO::PARAM_STR);
			$statement->bindValue(':id_perfil', $this->getId_perfil(), $this->getId_perfil() === null ? PDO::PARAM_NULL : PDO::PARAM_INT);

			// Handle empty values for id_empleado
			$id_empleado = $this->getId_empleado();
			$id_empleado = (empty($id_empleado) || $id_empleado === 0) ? null : $id_empleado;
			$statement->bindValue(':id_empleado', $id_empleado, $id_empleado === null ? PDO::PARAM_NULL : PDO::PARAM_INT);

			// Ejecutar la consulta
			$success = $statement->execute();

			if ($success) {
				// Devolver el ID del usuario recién creado
				return (int)$conexion->lastInsertId();
			} else {
				return false; // Error en la ejecución
			}

		} catch (PDOException $e) {
			// Manejar errores específicos de DB (ej. username duplicado)
			if ($e->getCode() == 23000 || $e->getCode() == 1062) { // Códigos comunes para violación de UNIQUE constraint
				throw new Exception("Error al crear usuario: El username '$username' ya existe.");
			} else {
				throw new Exception("Error de base de datos al crear usuario: " . $e->getMessage());
			}
		} catch (Exception $e) {
			// Capturar otros errores
			throw new Exception("Error al crear usuario: " . $e->getMessage());
		}
	}

	/**
	 * Modifica un usuario existente.
	 *
	 * @param int    $id          ID del usuario a modificar.
	 * @param string $nuevoNombre El nuevo nombre para el usuario.
	 * @param int    $id_perfil   ID del perfil asociado al usuario.
	 * @param PDO    $conexion    Conexión PDO.
	 * @param int|null $id_empleado ID del empleado asociado (opcional).
	 *
	 * @return bool True si la modificación fue exitosa, False en caso contrario.
	 * @throws Exception Si el nuevo nombre está vacío, el id_perfil es nulo o si ocurre un error de base de datos.
	 */
	public static function modificar(int $id, string $nuevoNombre, int $id_perfil, PDO $conexion, ?int $id_empleado = null): bool
	{
		if (empty(trim($nuevoNombre))) {
			throw new Exception("El nombre no puede estar vacío.");
		}

		if ($id_perfil <= 0) {
			throw new Exception("El ID del perfil debe ser un valor válido mayor que cero.");
		}

		// Validaciones de negocio para modificación
		self::validarReglasNegocioModificacion($id, $id_perfil, $id_empleado, $conexion);

		try {
			// Consulta para actualizar el nombre, el perfil y el empleado asociado
			$query = <<<SQL
            UPDATE usuarios SET
                nombre = :nombre,
                id_perfil = :id_perfil,
                id_empleado = :id_empleado
            WHERE
                id = :id
            SQL;

			$statement = $conexion->prepare($query);
			$statement->bindValue(':nombre', trim($nuevoNombre), PDO::PARAM_STR);
			$statement->bindValue(':id_perfil', $id_perfil, PDO::PARAM_INT);
			// Handle empty string or 0 as NULL for the optional id_empleado field
			$id_empleado = (empty($id_empleado) || $id_empleado === 0) ? null : $id_empleado;
			$statement->bindValue(':id_empleado', $id_empleado, $id_empleado === null ? PDO::PARAM_NULL : PDO::PARAM_INT);
			$statement->bindValue(':id', $id, PDO::PARAM_INT);

			// execute() devuelve true en éxito, false en error.
			// Podríamos chequear rowCount() > 0 si queremos asegurarnos que una fila fue afectada,
			// aunque si los datos son los mismos, rowCount será 0 pero la operación es "exitosa".
			return $statement->execute();

		} catch (PDOException $e) {
			// Error de base de datos
			// Considera loggear el error aquí
			throw new Exception("Error de base de datos al modificar usuario (ID: $id): " . $e->getMessage());
		}
	}


	/**
	 * Desactiva un usuario estableciendo su estado a 0.
	 *
	 * @param int $id       ID del usuario a desactivar.
	 * @param PDO $conexion Conexión PDO.
	 *
	 * @return bool True si la desactivación fue exitosa, False en caso contrario.
	 * @throws Exception Si ocurre un error de base de datos.
	 */
	public static function desactivar(int $id, PDO $conexion): bool
	{
		try {
			// Consulta para actualizar el estado a 0 (inactivo)
			$query = <<<SQL
            UPDATE usuarios SET
            	estado = 0
            WHERE
            	id = :id
            SQL;

			$statement = $conexion->prepare($query);
			$statement->bindValue(':id', $id, PDO::PARAM_INT);

			// execute() devuelve true en éxito, false en error.
			// Podríamos chequear rowCount() > 0 si queremos asegurarnos que una fila fue afectada.
			return $statement->execute();

		} catch (PDOException $e) {
			// Error de base de datos
			// Considera loggear el error aquí
			throw new Exception("Error de base de datos al desactivar usuario (ID: $id): " . $e->getMessage());
		}
	}


	// --- Métodos de Validación de Reglas de Negocio ---

	/**
	 * Valida las reglas de negocio para la creación de usuarios.
	 *
	 * @param PDO $conexion Conexión PDO.
	 * @throws Exception Si alguna regla de negocio es violada.
	 */
	private function validarReglasNegocio(PDO $conexion): void
	{
		// Regla 1: Un empleado solo puede estar asociado a un usuario
		if ($this->getId_empleado() !== null) {
			if (self::empleadoTieneUsuario($this->getId_empleado(), $conexion)) {
				throw new Exception("El empleado seleccionado ya está asociado a otro usuario. Cada empleado solo puede tener un usuario asociado.");
			}
		}

		// Regla 2: Validación bidireccional Perfil-Empleado
		$this->validarConsistenciaPerfilEmpleado($conexion);
	}

	/**
	 * Valida las reglas de negocio para la modificación de usuarios.
	 *
	 * @param int      $id_usuario  ID del usuario que se está modificando.
	 * @param int      $id_perfil   ID del perfil seleccionado.
	 * @param int|null $id_empleado ID del empleado seleccionado (opcional).
	 * @param PDO      $conexion    Conexión PDO.
	 * @throws Exception Si alguna regla de negocio es violada.
	 */
	private static function validarReglasNegocioModificacion(int $id_usuario, int $id_perfil, ?int $id_empleado, PDO $conexion): void
	{
		// Regla 1: Un empleado solo puede estar asociado a un usuario
		if ($id_empleado !== null) {
			if (self::empleadoTieneUsuario($id_empleado, $conexion, $id_usuario)) {
				throw new Exception("El empleado seleccionado ya está asociado a otro usuario. Cada empleado solo puede tener un usuario asociado.");
			}
		}

		// Regla 2: Validación bidireccional Perfil-Empleado
		self::validarConsistenciaPerfilEmpleadoEstatico($id_perfil, $id_empleado, $conexion);
	}

	/**
	 * Verifica si un empleado ya tiene un usuario asociado.
	 *
	 * @param int      $id_empleado     ID del empleado a verificar.
	 * @param PDO      $conexion        Conexión PDO.
	 * @param int|null $excluir_usuario ID del usuario a excluir de la verificación (para modificaciones).
	 * @return bool True si el empleado ya tiene un usuario asociado, False en caso contrario.
	 * @throws Exception Si hay error en la consulta.
	 */
	public static function empleadoTieneUsuario(int $id_empleado, PDO $conexion, ?int $excluir_usuario = null): bool
	{
		try {
			$query = <<<SQL
			SELECT COUNT(*) as total
			FROM usuarios
			WHERE id_empleado = :id_empleado
			  AND estado = 1
			SQL;

			// Si estamos modificando un usuario, excluirlo de la verificación
			if ($excluir_usuario !== null) {
				$query .= " AND id != :excluir_usuario";
			}

			$statement = $conexion->prepare($query);
			$statement->bindValue(':id_empleado', $id_empleado, PDO::PARAM_INT);

			if ($excluir_usuario !== null) {
				$statement->bindValue(':excluir_usuario', $excluir_usuario, PDO::PARAM_INT);
			}

			$statement->execute();
			$resultado = $statement->fetch(PDO::FETCH_ASSOC);

			return ($resultado['total'] ?? 0) > 0;

		} catch (PDOException $e) {
			throw new Exception("Error al verificar si el empleado tiene usuario asociado: " . $e->getMessage());
		}
	}

	/**
	 * Valida la consistencia entre perfil y empleado (método de instancia).
	 *
	 * @param PDO $conexion Conexión PDO.
	 * @throws Exception Si la validación falla.
	 */
	private function validarConsistenciaPerfilEmpleado(PDO $conexion): void
	{
		self::validarConsistenciaPerfilEmpleadoEstatico($this->getId_perfil(), $this->getId_empleado(), $conexion);
	}

	/**
	 * Valida la consistencia entre perfil y empleado (método estático).
	 *
	 * @param int      $id_perfil   ID del perfil.
	 * @param int|null $id_empleado ID del empleado (opcional).
	 * @param PDO      $conexion    Conexión PDO.
	 * @throws Exception Si la validación falla.
	 */
	private static function validarConsistenciaPerfilEmpleadoEstatico(int $id_perfil, ?int $id_empleado, PDO $conexion): void
	{
		// Obtener el nombre del perfil
		$perfil = Perfil::get($id_perfil, $conexion);
		if (!$perfil) {
			throw new Exception("Perfil no encontrado.");
		}

		$nombre_perfil = strtolower(trim($perfil->getNombre()));
		$es_barbero = ($nombre_perfil === 'barbero');

		// Regla: Si el perfil es "Barbero", DEBE tener un empleado asociado
		if ($es_barbero && $id_empleado === null) {
			throw new Exception("Los usuarios con perfil 'Barbero' deben tener un empleado asociado.");
		}

		// Regla: Si se selecciona un empleado, el perfil DEBE ser "Barbero"
		if ($id_empleado !== null && !$es_barbero) {
			throw new Exception("Solo los usuarios con perfil 'Barbero' pueden tener un empleado asociado.");
		}
	}

	// --- Getters y Setters ---

	public function getId(): ?int
	{
		return $this->id;
	}

	public function setId(?int $id): self
	{
		$this->id = $id;
		return $this;
	}

	public function getUsername(): ?string
	{
		return $this->username;
	}

	public function setUsername(?string $username): self
	{
		$this->username = $username;
		return $this;
	}

	/**
	 * Obtiene el HASH de la clave almacenada.
	 * @return string|null
	 */
	public function getClave(): ?string
	{
		return $this->clave;
	}

	/**
	 * Establece la contraseña. Genera un hash seguro antes de asignarla.
	 *
	 * @param string|null $clavePlana La contraseña en texto plano. Null para borrarla.
	 *
	 * @return self
	 * @throws Exception Si hay error al generar el hash.
	 */
	public function setClave(?string $clavePlana): self
	{
		if ($clavePlana === null) {
			$this->clave = null;
		} else {
			// Usa el algoritmo por defecto (actualmente BCRYPT), que es seguro y recomendado
			$hash = password_hash($clavePlana, PASSWORD_DEFAULT);
			if (!$hash) {
				throw new Exception('Error al generar el hash de la contraseña.');
			}
			$this->clave = $hash; // Almacena el hash
		}
		return $this;
	}

	public function getNombre(): ?string
	{
		return $this->nombre;
	}

	public function setNombre(?string $nombre): self
	{
		$this->nombre = $nombre;
		return $this;
	}

	public function getEstado(): ?int
	{
		return $this->estado;
	}

	public function setEstado(?int $estado): self
	{
		$this->estado = $estado;
		return $this;
	}

	public function getId_perfil(): ?int
	{
		return $this->id_perfil;
	}

	public function setId_perfil(?int $id_perfil): self
	{
		$this->id_perfil = $id_perfil;
		return $this;
	}

	public function getNombre_perfil(): ?string
	{
		return $this->nombre_perfil;
	}

	public function setNombre_perfil(?string $nombre_perfil): self
	{
		$this->nombre_perfil = $nombre_perfil;
		return $this;
	}

	public function getId_empleado(): ?int
	{
		return $this->id_empleado;
	}

	public function setId_empleado(?int $id_empleado): self
	{
		$this->id_empleado = $id_empleado;
		return $this;
	}

	public function getNombre_empleado(): ?string
	{
		return $this->nombre_empleado;
	}

	public function setNombre_empleado(?string $nombre_empleado): self
	{
		$this->nombre_empleado = $nombre_empleado;
		return $this;
	}

	// --- Métodos adicionales (Ejemplos) ---

	/**
	 * Verifica si el usuario está activo.
	 * @return bool
	 */
	public function isActivo(): bool
	{
		// Asegúrate de que el estado no sea null antes de comparar
		return $this->estado === 1;
	}

}
