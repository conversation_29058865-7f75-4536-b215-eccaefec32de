<?php

declare(strict_types=1);

namespace App\classes;

use Exception;
use PDO;
use PDOException;

class GastoFijo
{
	// --- Atributos ---
	private ?int    $id          = null;
	private ?string $descripcion = null;
	private ?float  $valor       = null;
	private ?int    $estado      = null;

	/**
	 * Constructor: Inicializa las propiedades del objeto GastoFijo.
	 */
	public function __construct()
	{
		$this->id          = 0;
		$this->descripcion = null;
		$this->valor       = null;
		$this->estado      = 1; // Estado activo por defecto
	}

	/**
	 * Método estático para construir un objeto GastoFijo desde un array (ej. fila de DB).
	 *
	 * @param array $resultado Array asociativo con los datos del gasto fijo.
	 *
	 * @return self Instancia de GastoFijo.
	 * @throws Exception Si ocurre un error durante la construcción.
	 */
	public static function construct(array $resultado = []): self
	{
		try {
			$objeto              = new self();
			$objeto->id          = isset($resultado['id']) ? (int)$resultado['id'] : 0;
			$objeto->descripcion = $resultado['descripcion'] ?? null;
			$objeto->valor       = isset($resultado['valor']) ? (float)$resultado['valor'] : null;
			$objeto->estado      = isset($resultado['estado']) ? (int)$resultado['estado'] : 1;
			return $objeto;
		} catch (Exception $e) {
			// Considera loggear el error aquí
			throw new Exception("Error al construir GastoFijo: " . $e->getMessage());
		}
	}

	/**
	 * Obtiene un gasto fijo por su ID.
	 *
	 * @param int $id       ID del gasto fijo.
	 * @param PDO $conexion Conexión PDO.
	 *
	 * @return self|null Objeto GastoFijo o null si no se encuentra.
	 * @throws Exception Si hay error en DB.
	 */
	public static function obtenerPorId(int $id, PDO $conexion): ?self
	{
		try {
			// Consulta para obtener gasto fijo por ID (Usando Heredoc)
			$query = <<<SQL
            SELECT
            	*
            FROM gastos_fijos
            WHERE
            	id = :id
            LIMIT 1
            SQL;

			$statement = $conexion->prepare($query);
			$statement->bindValue(':id', $id, PDO::PARAM_INT);
			$statement->execute();
			$resultado = $statement->fetch(PDO::FETCH_ASSOC);

			return $resultado ? self::construct($resultado) : null;

		} catch (PDOException $e) {
			throw new Exception("Error al obtener GastoFijo por ID: " . $e->getMessage());
		}
	}

	/**
	 * Obtiene una lista de gastos fijos activos.
	 *
	 * @param PDO $conexion Conexión PDO.
	 *
	 * @return array Array de objetos GastoFijo.
	 * @throws Exception Si hay error en DB.
	 */
	public static function obtenerTodos(PDO $conexion): array
	{
		try {
			// Consulta para obtener lista de gastos fijos activos (Usando Heredoc)
			$query = <<<SQL
            SELECT
            	*
            FROM gastos_fijos
            WHERE
            	estado = 1
            ORDER BY
            	descripcion
            SQL;

			$statement = $conexion->prepare($query);
			$statement->execute();
			$resultados = $statement->fetchAll(PDO::FETCH_ASSOC);

			$listado = [];
			foreach ($resultados as $resultado) {
				$listado[] = self::construct($resultado);
			}
			return $listado;

		} catch (PDOException $e) {
			throw new Exception("Error al obtener lista de GastosFijos: " . $e->getMessage());
		}
	}

	/**
	 * Crea un nuevo gasto fijo en la base de datos a partir de un objeto GastoFijo.
	 * El objeto GastoFijo debe estar completamente poblado con los datos requeridos.
	 *
	 * @param PDO $conexion Conexión PDO.
	 *
	 * @return int|false El ID del nuevo gasto fijo creado o false en caso de error.
	 * @throws Exception Si los datos requeridos en el objeto están vacíos o hay error en DB.
	 */
	function crear(PDO $conexion): int|false
	{
		// Validaciones básicas sobre el objeto
		if (empty($this->getDescripcion()) || $this->getValor() === null) {
			throw new Exception("Descripción y valor son requeridos para crear un gasto fijo.");
		}

		if ($this->getValor() <= 0) {
			throw new Exception("El valor debe ser un número mayor que cero.");
		}

		try {
			// Preparar la consulta INSERT usando Heredoc
			$query = <<<SQL
            INSERT INTO gastos_fijos (
            	 descripcion
            	,valor
            ) VALUES (
            	 :descripcion
            	,:valor
            )
            SQL;

			$statement = $conexion->prepare($query);

			// Bind de parámetros desde el objeto
			$statement->bindValue(':descripcion', $this->getDescripcion(), PDO::PARAM_STR);
			$statement->bindValue(':valor', $this->getValor(), PDO::PARAM_STR);

			// Ejecutar la consulta
			$success = $statement->execute();

			if ($success) {
				// Devolver el ID del gasto fijo recién creado
				return (int)$conexion->lastInsertId();
			} else {
				return false; // Error en la ejecución
			}

		} catch (PDOException $e) {
			throw new Exception("Error de base de datos al crear gasto fijo: " . $e->getMessage());
		} catch (Exception $e) {
			// Capturar otros errores
			throw new Exception("Error al crear gasto fijo: " . $e->getMessage());
		}
	}

	/**
	 * Modifica un gasto fijo existente.
	 *
	 * @param PDO $conexion Conexión PDO.
	 *
	 * @return bool True si la modificación fue exitosa, False en caso contrario.
	 * @throws Exception Si la descripción está vacía, el valor es nulo o si ocurre un error de base de datos.
	 */
	function modificar(PDO $conexion): bool
	{
		if (empty(trim($this->getDescripcion()))) {
			throw new Exception("La descripción no puede estar vacía.");
		}

		if ($this->getValor() === null || $this->getValor() <= 0) {
			throw new Exception("El valor debe ser un número válido mayor que cero.");
		}

		if ($this->getId() <= 0) {
			throw new Exception("ID de gasto fijo inválido para modificar.");
		}

		try {
			// Consulta para actualizar la descripción y el valor
			$query = <<<SQL
            UPDATE gastos_fijos SET
                descripcion = :descripcion,
                valor = :valor
            WHERE
                id = :id
            SQL;

			$statement = $conexion->prepare($query);
			$statement->bindValue(':descripcion', trim($this->getDescripcion()), PDO::PARAM_STR);
			$statement->bindValue(':valor', $this->getValor(), PDO::PARAM_STR);
			$statement->bindValue(':id', $this->getId(), PDO::PARAM_INT);

			// execute() devuelve true en éxito, false en error.
			return $statement->execute();

		} catch (PDOException $e) {
			// Error de base de datos
			throw new Exception("Error de base de datos al modificar gasto fijo (ID: {$this->getId()}): " . $e->getMessage());
		}
	}

	/**
	 * Elimina (desactiva) un gasto fijo estableciendo su estado a 0.
	 *
	 * @param int $id       ID del gasto fijo a eliminar.
	 * @param PDO $conexion Conexión PDO.
	 *
	 * @return bool True si la eliminación fue exitosa, False en caso contrario.
	 * @throws Exception Si ocurre un error de base de datos.
	 */
	public static function eliminar(int $id, PDO $conexion): bool
	{
		try {
			// Consulta para actualizar el estado a 0 (inactivo)
			$query = <<<SQL
            UPDATE gastos_fijos SET
                estado = 0
            WHERE
                id = :id
            SQL;

			$statement = $conexion->prepare($query);
			$statement->bindValue(':id', $id, PDO::PARAM_INT);

			// execute() devuelve true en éxito, false en error.
			return $statement->execute();

		} catch (PDOException $e) {
			// Error de base de datos
			throw new Exception("Error de base de datos al eliminar gasto fijo (ID: $id): " . $e->getMessage());
		}
	}

	// --- Getters y Setters ---

	public function getId(): ?int
	{
		return $this->id;
	}

	public function setId(?int $id): self
	{
		$this->id = $id;
		return $this;
	}

	public function getDescripcion(): ?string
	{
		return $this->descripcion;
	}

	public function setDescripcion(?string $descripcion): self
	{
		$this->descripcion = $descripcion;
		return $this;
	}

	public function getValor(): ?float
	{
		return $this->valor;
	}

	public function setValor(?float $valor): self
	{
		$this->valor = $valor;
		return $this;
	}

	public function getEstado(): ?int
	{
		return $this->estado;
	}

	public function setEstado(?int $estado): self
	{
		$this->estado = $estado;
		return $this;
	}

	// --- Métodos adicionales ---

	/**
	 * Verifica si el gasto fijo está activo.
	 * @return bool
	 */
	public function isActivo(): bool
	{
		// Asegúrate de que el estado no sea null antes de comparar
		return $this->estado === 1;
	}

}
