<?php

declare(strict_types=1);

namespace App\classes;

use App\classes\OrdenCompra;
use App\classes\Producto;
use Exception;
use PDO;
use PDOException;

class OrdenCompraDetalle
{
    // --- Atributos ---
    private ?int    $id                   = null;
    private ?int    $id_orden_compra      = null;
    private ?int    $id_producto          = null;
    private ?int    $cantidad             = null;
    private ?float  $valor                = null;
    private ?float  $valor_total          = null;
    private ?string $producto_descripcion = null;

    /**
     * Constructor: Inicializa las propiedades del objeto OrdenCompraDetalle.
     */
    public function __construct()
    {
        $this->id                   = 0;
        $this->id_orden_compra      = null;
        $this->id_producto          = null;
        $this->cantidad             = 1; // Cantidad por defecto
        $this->valor                = null;
        $this->valor_total          = null;
        $this->producto_descripcion = null;
    }

    /**
     * Método estático para construir un objeto OrdenCompraDetalle desde un array (ej. fila de DB).
     *
     * @param array $resultado Array asociativo con los datos del detalle de orden de compra.
     *
     * @return self Instancia de OrdenCompraDetalle.
     * @throws Exception Si ocurre un error durante la construcción.
     */
    public static function construct(array $resultado = []): self
    {
        try {
            $objeto                        = new self();
            $objeto->id                    = isset($resultado['id']) ? (int)$resultado['id'] : 0;
            $objeto->id_orden_compra       = isset($resultado['id_orden_compra']) ? (int)$resultado['id_orden_compra'] : null;
            $objeto->id_producto           = isset($resultado['id_producto']) ? (int)$resultado['id_producto'] : null;
            $objeto->cantidad              = isset($resultado['cantidad']) ? (int)$resultado['cantidad'] : 1;
            $objeto->valor                 = isset($resultado['valor']) ? (float)$resultado['valor'] : null;
            $objeto->valor_total           = isset($resultado['valor_total']) ? (float)$resultado['valor_total'] : null;
            $objeto->producto_descripcion  = $resultado['producto_descripcion'] ?? null;
            return $objeto;
        } catch (Exception $e) {
            // Considera loggear el error aquí
            throw new Exception("Error al construir objeto OrdenCompraDetalle: " . $e->getMessage());
        }
    }

    /**
     * Obtiene un detalle de orden de compra por su ID.
     *
     * @param int $id       ID del detalle de orden de compra.
     * @param PDO $conexion Conexión PDO.
     *
     * @return self|null Objeto OrdenCompraDetalle o null si no se encuentra.
     * @throws Exception Si hay error en DB.
     */
    public static function get(int $id, PDO $conexion): ?self
    {
        try {
            // Consulta para obtener detalle de orden de compra por ID (Usando Heredoc)
            $query = <<<SQL
            SELECT
                ocd.*,
                p.descripcion AS producto_descripcion
            FROM ordenes_compra_detalle ocd
            INNER JOIN productos p ON ocd.id_producto = p.id
            WHERE
                ocd.id = :id
            LIMIT 1
            SQL;

            $statement = $conexion->prepare($query);
            $statement->bindValue(':id', $id, PDO::PARAM_INT);
            $statement->execute();
            $resultado = $statement->fetch(PDO::FETCH_ASSOC);

            return $resultado ? self::construct($resultado) : null;

        } catch (PDOException $e) {
            throw new Exception("Error de base de datos al obtener detalle de orden de compra: " . $e->getMessage());
        }
    }

    /**
     * Obtiene todos los detalles de una orden de compra específica con información de productos.
     *
     * @param int $id_orden_compra ID de la orden de compra.
     * @param PDO $conexion        Conexión PDO.
     *
     * @return array Array de objetos OrdenCompraDetalle.
     * @throws Exception Si ocurre un error de base de datos.
     */
    public static function obtenerPorOrdenCompra(int $id_orden_compra, PDO $conexion): array
    {
        try {
            $query = <<<SQL
            SELECT
                ocd.*,
                p.descripcion AS producto_descripcion
            FROM ordenes_compra_detalle ocd
            INNER JOIN productos p ON ocd.id_producto = p.id
            WHERE ocd.id_orden_compra = :id_orden_compra
            ORDER BY ocd.id
            SQL;

            $statement = $conexion->prepare($query);
            $statement->bindValue(':id_orden_compra', $id_orden_compra, PDO::PARAM_INT);
            $statement->execute();
            $resultados = $statement->fetchAll(PDO::FETCH_ASSOC);

            $listado = [];
            foreach ($resultados as $resultado) {
                $listado[] = self::construct($resultado);
            }
            return $listado;

        } catch (PDOException $e) {
            throw new Exception("Error de base de datos al obtener detalles de orden de compra: " . $e->getMessage());
        }
    }

    /**
     * Obtiene todos los detalles de una orden de compra específica.
     *
     * @param int $id_orden_compra ID de la orden de compra.
     * @param PDO $conexion        Conexión PDO.
     *
     * @return array Array de objetos OrdenCompraDetalle.
     * @throws Exception Si hay error en DB.
     */
    public static function get_by_orden_compra(int $id_orden_compra, PDO $conexion): array
    {
        try {
            $query = <<<SQL
            SELECT
                ocd.*,
                p.descripcion AS producto_descripcion
            FROM ordenes_compra_detalle ocd
            INNER JOIN productos p ON ocd.id_producto = p.id
            WHERE ocd.id_orden_compra = :id_orden_compra
            ORDER BY p.descripcion
            SQL;

            $statement = $conexion->prepare($query);
            $statement->bindValue(':id_orden_compra', $id_orden_compra, PDO::PARAM_INT);
            $statement->execute();
            $resultados = $statement->fetchAll(PDO::FETCH_ASSOC);

            $listado = [];
            foreach ($resultados as $resultado) {
                $listado[] = self::construct($resultado);
            }
            return $listado;

        } catch (PDOException $e) {
            throw new Exception("Error de base de datos al obtener detalles de orden de compra: " . $e->getMessage());
        }
    }

    /**
     * Obtiene una lista de todos los detalles de órdenes de compra.
     *
     * @param PDO $conexion Conexión PDO.
     *
     * @return array Array de objetos OrdenCompraDetalle.
     * @throws Exception Si hay error en DB.
     */
    public static function get_list(PDO $conexion): array
    {
        try {
            // Consulta para obtener lista de detalles de órdenes de compra (Usando Heredoc)
            $query = <<<SQL
            SELECT
                ocd.*,
                p.descripcion AS producto_descripcion
            FROM ordenes_compra_detalle ocd
            INNER JOIN productos p ON ocd.id_producto = p.id
            ORDER BY
                ocd.id_orden_compra, p.descripcion
            SQL;

            $statement = $conexion->prepare($query);
            $statement->execute();
            $resultados = $statement->fetchAll(PDO::FETCH_ASSOC);

            $listado = [];
            foreach ($resultados as $resultado) {
                $listado[] = self::construct($resultado);
            }
            return $listado;

        } catch (PDOException $e) {
            throw new Exception("Error de base de datos al obtener lista de detalles de órdenes de compra: " . $e->getMessage());
        }
    }

    /**
     * Crea un nuevo detalle de orden de compra en la base de datos a partir de un objeto OrdenCompraDetalle.
     *
     * @param PDO $conexion Conexión PDO.
     *
     * @return int|false El ID del nuevo detalle creado o false en caso de error.
     * @throws Exception Si los datos requeridos en el objeto están vacíos o hay error en DB.
     */
    function crear(PDO $conexion): int|false
    {
        // Validaciones básicas sobre el objeto
        if ($this->getId_orden_compra() === null || $this->getId_producto() === null ||
            $this->getValor() === null || $this->getCantidad() === null) {
            throw new Exception("ID de orden de compra, ID de producto, valor y cantidad son requeridos para crear un detalle de orden de compra.");
        }

        if ($this->getCantidad() <= 0) {
            throw new Exception("La cantidad debe ser un número entero positivo.");
        }

        if ($this->getValor() < 0) {
            throw new Exception("El valor no puede ser negativo.");
        }

        // Auto-calcular valor_total
        $this->setValor_total($this->getCantidad() * $this->getValor());

        try {
            // Validar que la orden de compra existe
            $ordenCompra = OrdenCompra::get($this->getId_orden_compra(), $conexion);
            if (!$ordenCompra) {
                throw new Exception("La orden de compra especificada no existe.");
            }

            // Validar que el producto existe
            $producto = Producto::get($this->getId_producto(), $conexion);
            if (!$producto) {
                throw new Exception("El producto especificado no existe.");
            }

            return $this->_insert($conexion);

        } catch (Exception $e) {
            throw new Exception("Error al crear detalle de orden de compra: " . $e->getMessage());
        }
    }

    /**
     * Modifica un detalle de orden de compra existente.
     *
     * @param PDO $conexion Conexión PDO.
     *
     * @return bool True si la modificación fue exitosa, False en caso contrario.
     * @throws Exception Si los datos requeridos están vacíos o si ocurre un error de base de datos.
     */
    function modificar(PDO $conexion): bool
    {
        if ($this->getId() <= 0) {
            throw new Exception("ID de detalle de orden de compra inválido para modificar.");
        }

        if ($this->getId_orden_compra() === null || $this->getId_producto() === null ||
            $this->getValor() === null || $this->getCantidad() === null) {
            throw new Exception("ID de orden de compra, ID de producto, valor y cantidad son requeridos para modificar un detalle de orden de compra.");
        }

        if ($this->getCantidad() <= 0) {
            throw new Exception("La cantidad debe ser un número entero positivo.");
        }

        if ($this->getValor() < 0) {
            throw new Exception("El valor no puede ser negativo.");
        }

        // Auto-calcular valor_total
        $this->setValor_total($this->getCantidad() * $this->getValor());

        try {
            // Validar que la orden de compra existe
            $ordenCompra = OrdenCompra::get($this->getId_orden_compra(), $conexion);
            if (!$ordenCompra) {
                throw new Exception("La orden de compra especificada no existe.");
            }

            // Validar que el producto existe
            $producto = Producto::get($this->getId_producto(), $conexion);
            if (!$producto) {
                throw new Exception("El producto especificado no existe.");
            }

            return $this->_update($conexion);

        } catch (Exception $e) {
            throw new Exception("Error al modificar detalle de orden de compra: " . $e->getMessage());
        }
    }

    /**
     * Elimina un detalle de orden de compra de la base de datos.
     *
     * @param PDO $conexion Conexión PDO.
     *
     * @return bool True si la eliminación fue exitosa, False en caso contrario.
     * @throws Exception Si el ID es inválido o si ocurre un error de base de datos.
     */
    function eliminar(PDO $conexion): bool
    {
        if ($this->getId() <= 0) {
            throw new Exception("ID de detalle de orden de compra inválido para eliminar.");
        }

        try {
            $query = <<<SQL
            DELETE FROM ordenes_compra_detalle
            WHERE id = :id
            SQL;

            $statement = $conexion->prepare($query);
            $statement->bindValue(':id', $this->getId(), PDO::PARAM_INT);

            return $statement->execute();

        } catch (PDOException $e) {
            throw new Exception("Error de base de datos al eliminar detalle de orden de compra (ID: {$this->getId()}): " . $e->getMessage());
        }
    }

    /**
     * Método privado para insertar un nuevo detalle de orden de compra en la base de datos.
     *
     * @param PDO $conexion Conexión PDO.
     *
     * @return int|false El ID del nuevo detalle creado o false en caso de error.
     * @throws Exception Si hay error en DB.
     */
    private function _insert(PDO $conexion): int|false
    {
        try {
            // Preparar la consulta INSERT usando Heredoc
            $query = <<<SQL
            INSERT INTO ordenes_compra_detalle (
                 id_orden_compra
                ,id_producto
                ,cantidad
                ,valor
                ,valor_total
            ) VALUES (
                 :id_orden_compra
                ,:id_producto
                ,:cantidad
                ,:valor
                ,:valor_total
            )
            SQL;

            $statement = $conexion->prepare($query);

            // Bind de parámetros desde el objeto
            $statement->bindValue(':id_orden_compra', $this->getId_orden_compra(), PDO::PARAM_INT);
            $statement->bindValue(':id_producto', $this->getId_producto(), PDO::PARAM_INT);
            $statement->bindValue(':cantidad', $this->getCantidad(), PDO::PARAM_INT);
            $statement->bindValue(':valor', $this->getValor(), PDO::PARAM_STR);
            $statement->bindValue(':valor_total', $this->getValor_total(), PDO::PARAM_STR);

            // Ejecutar la consulta
            $success = $statement->execute();

            if ($success) {
                // Devolver el ID del detalle recién creado
                return (int)$conexion->lastInsertId();
            } else {
                return false; // Error en la ejecución
            }

        } catch (PDOException $e) {
            throw new Exception("Error de base de datos al crear detalle de orden de compra: " . $e->getMessage());
        }
    }

    /**
     * Método privado para actualizar un detalle de orden de compra existente en la base de datos.
     *
     * @param PDO $conexion Conexión PDO.
     *
     * @return bool True si la actualización fue exitosa, false en caso contrario.
     * @throws Exception Si hay error en DB.
     */
    private function _update(PDO $conexion): bool
    {
        try {
            // Preparar la consulta UPDATE usando Heredoc
            $query = <<<SQL
            UPDATE ordenes_compra_detalle SET
                 id_orden_compra = :id_orden_compra
                ,id_producto = :id_producto
                ,cantidad = :cantidad
                ,valor = :valor
                ,valor_total = :valor_total
            WHERE
                id = :id
            SQL;

            $statement = $conexion->prepare($query);

            // Bind de parámetros desde el objeto
            $statement->bindValue(':id_orden_compra', $this->getId_orden_compra(), PDO::PARAM_INT);
            $statement->bindValue(':id_producto', $this->getId_producto(), PDO::PARAM_INT);
            $statement->bindValue(':cantidad', $this->getCantidad(), PDO::PARAM_INT);
            $statement->bindValue(':valor', $this->getValor(), PDO::PARAM_STR);
            $statement->bindValue(':valor_total', $this->getValor_total(), PDO::PARAM_STR);
            $statement->bindValue(':id', $this->getId(), PDO::PARAM_INT);

            // execute() devuelve true en éxito, false en error.
            return $statement->execute();

        } catch (PDOException $e) {
            throw new Exception("Error de base de datos al actualizar detalle de orden de compra: " . $e->getMessage());
        }
    }

    // --- Getters y Setters ---

    public function getId(): ?int
    {
        return $this->id;
    }

    public function setId(?int $id): self
    {
        $this->id = $id;
        return $this;
    }

    public function getId_orden_compra(): ?int
    {
        return $this->id_orden_compra;
    }

    public function setId_orden_compra(?int $id_orden_compra): self
    {
        $this->id_orden_compra = $id_orden_compra;
        return $this;
    }

    public function getId_producto(): ?int
    {
        return $this->id_producto;
    }

    public function setId_producto(?int $id_producto): self
    {
        $this->id_producto = $id_producto;
        return $this;
    }

    public function getCantidad(): ?int
    {
        return $this->cantidad;
    }

    public function setCantidad(?int $cantidad): self
    {
        $this->cantidad = $cantidad;
        return $this;
    }

    public function getValor(): ?float
    {
        return $this->valor;
    }

    public function setValor(?float $valor): self
    {
        $this->valor = $valor;
        return $this;
    }

    public function getValor_total(): ?float
    {
        return $this->valor_total;
    }

    public function setValor_total(?float $valor_total): self
    {
        $this->valor_total = $valor_total;
        return $this;
    }

    public function getProducto_descripcion(): ?string
    {
        return $this->producto_descripcion;
    }

    public function setProducto_descripcion(?string $producto_descripcion): self
    {
        $this->producto_descripcion = $producto_descripcion;
        return $this;
    }
}
