<?php

declare(strict_types=1);

namespace App\classes;

use Exception;
use PDO;
use PDOException;

class ControlBase
{
	// --- Atributos ---
	private ?int    $id            = null;
	private ?string $fecha         = null;
	private ?float  $valor_inicial = null;
	private ?float  $valor_actual  = null;
	private ?float  $valor_final   = null;
	private ?int    $id_cierre     = null;
	private ?int    $estado        = null;

	/**
	 * Constructor: Inicializa las propiedades del objeto ControlBase.
	 *
	 * @param string|null $fecha Fecha del control base (formato YYYY-MM-DD) - obligatorio
	 * @param float|null $valor_inicial Valor inicial del control base - obligatorio
	 */
	public function __construct(?string $fecha = null, ?float $valor_inicial = null)
	{
		$this->id            = 0;
		$this->fecha         = $fecha;
		$this->valor_inicial = $valor_inicial;
		$this->valor_actual  = $valor_inicial; // Se establece automáticamente al mismo valor que valor_inicial
		$this->valor_final   = 0.0; // Valor por defecto
		$this->id_cierre     = null; // Referencia opcional a la tabla cierres
		$this->estado        = 1; // Estado activo por defecto
	}

	/**
	 * Método estático para construir un objeto ControlBase desde un array (ej. fila de DB).
	 *
	 * @param array $resultado Array asociativo con los datos del control base.
	 *
	 * @return self Instancia de ControlBase.
	 * @throws Exception Si ocurre un error durante la construcción.
	 */
	public static function construct(array $resultado = []): self
	{
		try {
			$objeto                = new self();
			$objeto->id            = isset($resultado['id']) ? (int)$resultado['id'] : 0;
			$objeto->fecha         = $resultado['fecha'] ?? null;
			$objeto->valor_inicial = isset($resultado['valor_inicial']) ? (float)$resultado['valor_inicial'] : null;
			$objeto->valor_actual  = isset($resultado['valor_actual']) ? (float)$resultado['valor_actual'] : null;
			$objeto->valor_final   = isset($resultado['valor_final']) ? (float)$resultado['valor_final'] : 0.0;
			$objeto->id_cierre     = isset($resultado['id_cierre']) ? (int)$resultado['id_cierre'] : null;
			$objeto->estado        = isset($resultado['estado']) ? (int)$resultado['estado'] : 1;
			return $objeto;
		} catch (Exception $e) {
			// Considera loggear el error aquí
			throw new Exception("Error al construir ControlBase: " . $e->getMessage());
		}
	}

	// --- Métodos de Acceso a Datos (Estáticos) ---

	/**
	 * Obtiene un control base por su ID.
	 *
	 * @param int $id       ID del control base.
	 * @param PDO $conexion Conexión PDO.
	 *
	 * @return self|null Objeto ControlBase o null si no se encuentra.
	 * @throws Exception Si hay error en DB.
	 */
	public static function get(int $id, PDO $conexion): ?self
	{
		try {
			// Consulta para obtener control base por ID (Usando Heredoc)
			$query = <<<SQL
            SELECT
            	cb.*
            FROM control_bases cb
            WHERE
            	cb.id = :id
            LIMIT 1
            SQL;

			$statement = $conexion->prepare($query);
			$statement->bindValue(":id", $id, PDO::PARAM_INT);
			$statement->execute();
			$resultado = $statement->fetch(PDO::FETCH_ASSOC);

			return $resultado ? self::construct($resultado) : null;

		} catch (PDOException $e) {
			throw new Exception("Error al obtener ControlBase (ID: $id): " . $e->getMessage());
		}
	}

	/**
	 * Obtiene una lista de controles base activos.
	 *
	 * @param PDO $conexion Conexión PDO.
	 *
	 * @return array Array de objetos ControlBase.
	 * @throws Exception Si hay error en DB.
	 */
	public static function get_list(PDO $conexion): array
	{
		try {
			// Consulta para obtener lista de controles base activos (Usando Heredoc)
			$query = <<<SQL
            SELECT
            	cb.*
            FROM control_bases cb
            WHERE
            	cb.estado = 1
            ORDER BY
            	cb.fecha DESC
            SQL;

			$statement = $conexion->prepare($query);
			$statement->execute();
			$resultados = $statement->fetchAll(PDO::FETCH_ASSOC);

			$listado = [];
			foreach ($resultados as $resultado) {
				$listado[] = self::construct($resultado);
			}
			return $listado;

		} catch (PDOException $e) {
			throw new Exception("Error al obtener lista de ControlBase: " . $e->getMessage());
		}
	}

	/**
	 * Obtiene un control base por su fecha.
	 *
	 * @param string $fecha    Fecha del control base (formato YYYY-MM-DD).
	 * @param PDO    $conexion Conexión PDO.
	 *
	 * @return self|null Objeto ControlBase o null si no se encuentra.
	 * @throws Exception Si hay error en DB.
	 */
	public static function obtenerPorFecha(string $fecha, PDO $conexion): ?self
	{
		try {
			// Consulta para obtener control base por fecha (Usando Heredoc)
			$query = <<<SQL
            SELECT
            	cb.*
            FROM control_bases cb
            WHERE
            	cb.fecha = :fecha
            LIMIT 1
            SQL;

			$statement = $conexion->prepare($query);
			$statement->bindValue(":fecha", $fecha, PDO::PARAM_STR);
			$statement->execute();
			$resultado = $statement->fetch(PDO::FETCH_ASSOC);

			return $resultado ? self::construct($resultado) : null;

		} catch (PDOException $e) {
			throw new Exception("Error al obtener ControlBase por fecha ($fecha): " . $e->getMessage());
		}
	}

	/**
	 * Obtiene controles base por ID de cierre.
	 *
	 * @param int $id_cierre ID del cierre.
	 * @param PDO $conexion  Conexión PDO.
	 *
	 * @return array Array de objetos ControlBase.
	 * @throws Exception Si hay error en DB.
	 */
	public static function obtenerPorCierre(int $id_cierre, PDO $conexion): array
	{
		try {
			// Consulta para obtener controles base por ID de cierre (Usando Heredoc)
			$query = <<<SQL
            SELECT
            	cb.*
            FROM control_bases cb
            WHERE
            	cb.id_cierre = :id_cierre
            ORDER BY
            	cb.fecha DESC
            SQL;

			$statement = $conexion->prepare($query);
			$statement->bindValue(":id_cierre", $id_cierre, PDO::PARAM_INT);
			$statement->execute();
			$resultados = $statement->fetchAll(PDO::FETCH_ASSOC);

			$listado = [];
			foreach ($resultados as $resultado) {
				$listado[] = self::construct($resultado);
			}
			return $listado;

		} catch (PDOException $e) {
			throw new Exception("Error al obtener ControlBase por cierre (ID: $id_cierre): " . $e->getMessage());
		}
	}

	/**
	 * Obtiene controles base por rango de fechas.
	 *
	 * @param string $fecha_inicio Fecha de inicio (formato YYYY-MM-DD).
	 * @param string $fecha_fin    Fecha de fin (formato YYYY-MM-DD).
	 * @param PDO    $conexion     Conexión PDO.
	 *
	 * @return array Array de objetos ControlBase.
	 * @throws Exception Si hay error en DB.
	 */
	public static function obtenerPorRangoFechas(string $fecha_inicio, string $fecha_fin, PDO $conexion): array
	{
		try {
			// Consulta para obtener controles base por rango de fechas (Usando Heredoc)
			$query = <<<SQL
            SELECT
            	cb.*
            FROM control_bases cb
            WHERE
            	cb.fecha BETWEEN :fecha_inicio AND :fecha_fin
            	AND cb.estado = 1
            ORDER BY
            	cb.fecha DESC
            SQL;

			$statement = $conexion->prepare($query);
			$statement->bindValue(":fecha_inicio", $fecha_inicio, PDO::PARAM_STR);
			$statement->bindValue(":fecha_fin", $fecha_fin, PDO::PARAM_STR);
			$statement->execute();
			$resultados = $statement->fetchAll(PDO::FETCH_ASSOC);

			$listado = [];
			foreach ($resultados as $resultado) {
				$listado[] = self::construct($resultado);
			}
			return $listado;

		} catch (PDOException $e) {
			throw new Exception("Error al obtener ControlBase por rango de fechas ($fecha_inicio - $fecha_fin): " . $e->getMessage());
		}
	}

	/**
	 * Crea un nuevo control base en la base de datos a partir de un objeto ControlBase.
	 * El objeto ControlBase debe estar completamente poblado.
	 *
	 * @param PDO $conexion Conexión PDO.
	 *
	 * @return int|false El ID del nuevo control base creado o false en caso de error.
	 * @throws Exception Si los datos requeridos en el objeto están vacíos o hay error en DB.
	 */
	function crear(PDO $conexion): int|false
	{
		// Validaciones básicas sobre el objeto
		if (empty($this->getFecha()) || $this->getValor_inicial() === null) {
			throw new Exception("Fecha y valor inicial son requeridos en el objeto ControlBase para crearlo.");
		}

		// Validar formato de fecha
		if (!$this->validarFecha($this->getFecha())) {
			throw new Exception("La fecha debe tener un formato válido (YYYY-MM-DD).");
		}

		// Validar valores numéricos
		if (!$this->validarValorNumerico($this->getValor_inicial())) {
			throw new Exception("El valor inicial debe ser un número válido mayor o igual a cero.");
		}

		try {
			// Establecer la zona horaria para Colombia
			date_default_timezone_set('America/Bogota');

			// Si valor_actual no está establecido, usar valor_inicial
			if ($this->getValor_actual() === null) {
				$this->setValor_actual($this->getValor_inicial());
			}

			// Si valor_final no está establecido, usar 0.0
			if ($this->getValor_final() === null) {
				$this->setValor_final(0.0);
			}

			return $this->_insert($conexion);

		} catch (Exception $e) {
			// Capturar otros errores
			throw new Exception("Error al crear control base: " . $e->getMessage());
		}
	}

	/**
	 * Actualiza un control base existente.
	 *
	 * @param PDO $conexion Conexión PDO.
	 *
	 * @return bool True si la actualización fue exitosa, false en caso contrario.
	 * @throws Exception Si hay error en DB.
	 */
	function modificar(PDO $conexion): bool
	{
		// Validar que el ID exista
		if (empty($this->getId())) {
			throw new Exception("ID es requerido para actualizar un ControlBase.");
		}

		// Validaciones básicas sobre el objeto
		if (empty($this->getFecha()) || $this->getValor_inicial() === null) {
			throw new Exception("Fecha y valor inicial son requeridos en el objeto ControlBase para modificarlo.");
		}

		// Validar formato de fecha
		if (!$this->validarFecha($this->getFecha())) {
			throw new Exception("La fecha debe tener un formato válido (YYYY-MM-DD).");
		}

		// Validar valores numéricos
		if (!$this->validarValorNumerico($this->getValor_inicial()) ||
			!$this->validarValorNumerico($this->getValor_actual()) ||
			!$this->validarValorNumerico($this->getValor_final())) {
			throw new Exception("Los valores numéricos deben ser válidos y mayores o iguales a cero.");
		}

		try {
			// Establecer la zona horaria para Colombia
			date_default_timezone_set('America/Bogota');

			return $this->_update($conexion);

		} catch (Exception $e) {
			throw new Exception("Error al modificar control base: " . $e->getMessage());
		}
	}

	/**
	 * Método privado para insertar un nuevo control base en la base de datos.
	 *
	 * @param PDO $conexion Conexión PDO.
	 *
	 * @return int|false El ID del nuevo control base creado o false en caso de error.
	 * @throws Exception Si hay error en DB.
	 */
	private function _insert(PDO $conexion): int|false
	{
		try {
			// Preparar la consulta INSERT usando Heredoc
			$query = <<<SQL
            INSERT INTO control_bases (
            	 fecha
            	,valor_inicial
            	,valor_actual
            	,valor_final
            	,id_cierre
            	,estado
            ) VALUES (
            	 :fecha
            	,:valor_inicial
            	,:valor_actual
            	,:valor_final
            	,:id_cierre
            	,:estado
            )
            SQL;

			$statement = $conexion->prepare($query);

			// Bind de parámetros desde el objeto
			$statement->bindValue(':fecha', $this->getFecha(), PDO::PARAM_STR);
			$statement->bindValue(':valor_inicial', $this->getValor_inicial(), PDO::PARAM_STR);
			$statement->bindValue(':valor_actual', $this->getValor_actual(), PDO::PARAM_STR);
			$statement->bindValue(':valor_final', $this->getValor_final(), PDO::PARAM_STR);

			// Handle null values for id_cierre
			$id_cierre = $this->getId_cierre();
			$statement->bindValue(':id_cierre', $id_cierre, $id_cierre === null ? PDO::PARAM_NULL : PDO::PARAM_INT);
			$statement->bindValue(':estado', $this->getEstado(), PDO::PARAM_INT);

			// Ejecutar la consulta
			$success = $statement->execute();

			if ($success) {
				// Devolver el ID del control base recién creado
				return (int)$conexion->lastInsertId();
			} else {
				return false; // Error en la ejecución
			}

		} catch (PDOException $e) {
			// Manejar errores específicos de DB (ej. fecha duplicada)
			if ($e->getCode() == 23000 || $e->getCode() == 1062) { // Códigos comunes para violación de UNIQUE constraint
				throw new Exception("Error al insertar control base: Ya existe un control base para la fecha '{$this->getFecha()}'.");
			} else {
				throw new Exception("Error de base de datos al insertar control base: " . $e->getMessage());
			}
		} catch (Exception $e) {
			// Capturar otros errores
			throw new Exception("Error al insertar control base: " . $e->getMessage());
		}
	}

	/**
	 * Método privado para actualizar un control base existente en la base de datos.
	 *
	 * @param PDO $conexion Conexión PDO.
	 *
	 * @return bool True si la actualización fue exitosa, False en caso contrario.
	 * @throws Exception Si hay error en DB.
	 */
	private function _update(PDO $conexion): bool
	{
		try {
			// Consulta para actualizar el control base
			$query = <<<SQL
            UPDATE control_bases SET
                fecha = :fecha,
                valor_inicial = :valor_inicial,
                valor_actual = :valor_actual,
                valor_final = :valor_final,
                id_cierre = :id_cierre,
                estado = :estado
            WHERE
                id = :id
            SQL;

			$statement = $conexion->prepare($query);
			$statement->bindValue(':fecha', $this->getFecha(), PDO::PARAM_STR);
			$statement->bindValue(':valor_inicial', $this->getValor_inicial(), PDO::PARAM_STR);
			$statement->bindValue(':valor_actual', $this->getValor_actual(), PDO::PARAM_STR);
			$statement->bindValue(':valor_final', $this->getValor_final(), PDO::PARAM_STR);

			// Handle null values for id_cierre
			$id_cierre = $this->getId_cierre();
			$statement->bindValue(':id_cierre', $id_cierre, $id_cierre === null ? PDO::PARAM_NULL : PDO::PARAM_INT);
			$statement->bindValue(':estado', $this->getEstado(), PDO::PARAM_INT);
			$statement->bindValue(':id', $this->getId(), PDO::PARAM_INT);

			// execute() devuelve true en éxito, false en error.
			return $statement->execute();

		} catch (PDOException $e) {
			// Manejar errores específicos de DB (ej. fecha duplicada)
			if ($e->getCode() == 23000 || $e->getCode() == 1062) { // Códigos comunes para violación de UNIQUE constraint
				throw new Exception("Error al actualizar control base: Ya existe un control base para la fecha '{$this->getFecha()}'.");
			} else {
				throw new Exception("Error de base de datos al actualizar control base (ID: {$this->getId()}): " . $e->getMessage());
			}
		}
	}

	/**
	 * Elimina un control base por su ID (eliminación lógica - cambia estado a 0).
	 *
	 * @param int $id       ID del control base a eliminar.
	 * @param PDO $conexion Conexión PDO.
	 *
	 * @return bool True si la eliminación fue exitosa, False en caso contrario.
	 * @throws Exception Si hay error en DB.
	 */
	public static function eliminar(int $id, PDO $conexion): bool
	{
		try {
			// Consulta para eliminar lógicamente el control base (cambiar estado a 0)
			$query = <<<SQL
            UPDATE control_bases SET
                estado = 0
            WHERE
                id = :id
            SQL;

			$statement = $conexion->prepare($query);
			$statement->bindValue(':id', $id, PDO::PARAM_INT);

			// execute() devuelve true en éxito, false en error.
			return $statement->execute();

		} catch (PDOException $e) {
			// Error de base de datos
			throw new Exception("Error de base de datos al eliminar control base (ID: $id): " . $e->getMessage());
		}
	}

	// --- Métodos de Validación ---

	/**
	 * Valida que una fecha tenga el formato correcto (YYYY-MM-DD).
	 *
	 * @param string|null $fecha Fecha a validar.
	 *
	 * @return bool True si la fecha es válida, False en caso contrario.
	 */
	private function validarFecha(?string $fecha): bool
	{
		if (empty($fecha)) {
			return false;
		}

		// Validar formato YYYY-MM-DD usando DateTime
		$dateTime = \DateTime::createFromFormat('Y-m-d', $fecha);
		return $dateTime && $dateTime->format('Y-m-d') === $fecha;
	}

	/**
	 * Valida que un valor numérico sea válido (mayor o igual a cero).
	 *
	 * @param float|null $valor Valor a validar.
	 *
	 * @return bool True si el valor es válido, False en caso contrario.
	 */
	private function validarValorNumerico(?float $valor): bool
	{
		return $valor !== null && is_numeric($valor) && $valor >= 0;
	}

	/**
	 * Valida que el estado sea un valor válido (0 o 1).
	 *
	 * @param int|null $estado Estado a validar.
	 *
	 * @return bool True si el estado es válido, False en caso contrario.
	 */
	private function validarEstado(?int $estado): bool
	{
		return $estado === 0 || $estado === 1;
	}

	// --- Getters y Setters ---

	/**
	 * Obtiene el ID del control base.
	 *
	 * @return int|null ID del control base.
	 */
	public function getId(): ?int
	{
		return $this->id;
	}

	/**
	 * Establece el ID del control base.
	 *
	 * @param int|null $id ID del control base.
	 *
	 * @return self
	 */
	public function setId(?int $id): self
	{
		$this->id = $id;
		return $this;
	}

	/**
	 * Obtiene la fecha del control base.
	 *
	 * @return string|null Fecha del control base.
	 */
	public function getFecha(): ?string
	{
		return $this->fecha;
	}

	/**
	 * Establece la fecha del control base.
	 *
	 * @param string|null $fecha Fecha del control base (formato YYYY-MM-DD).
	 *
	 * @return self
	 * @throws Exception Si la fecha no tiene un formato válido.
	 */
	public function setFecha(?string $fecha): self
	{
		if ($fecha !== null && !$this->validarFecha($fecha)) {
			throw new Exception("La fecha debe tener un formato válido (YYYY-MM-DD).");
		}
		$this->fecha = $fecha;
		return $this;
	}

	/**
	 * Obtiene el valor inicial del control base.
	 *
	 * @return float|null Valor inicial del control base.
	 */
	public function getValor_inicial(): ?float
	{
		return $this->valor_inicial;
	}

	/**
	 * Establece el valor inicial del control base.
	 *
	 * @param float|null $valor_inicial Valor inicial del control base.
	 *
	 * @return self
	 * @throws Exception Si el valor no es válido.
	 */
	public function setValor_inicial(?float $valor_inicial): self
	{
		if ($valor_inicial !== null && !$this->validarValorNumerico($valor_inicial)) {
			throw new Exception("El valor inicial debe ser un número válido mayor o igual a cero.");
		}
		$this->valor_inicial = $valor_inicial;
		return $this;
	}

	/**
	 * Obtiene el valor actual del control base.
	 *
	 * @return float|null Valor actual del control base.
	 */
	public function getValor_actual(): ?float
	{
		return $this->valor_actual;
	}

	/**
	 * Establece el valor actual del control base.
	 *
	 * @param float|null $valor_actual Valor actual del control base.
	 *
	 * @return self
	 * @throws Exception Si el valor no es válido.
	 */
	public function setValor_actual(?float $valor_actual): self
	{
		if ($valor_actual !== null && !$this->validarValorNumerico($valor_actual)) {
			throw new Exception("El valor actual debe ser un número válido mayor o igual a cero.");
		}
		$this->valor_actual = $valor_actual;
		return $this;
	}

	/**
	 * Obtiene el valor final del control base.
	 *
	 * @return float|null Valor final del control base.
	 */
	public function getValor_final(): ?float
	{
		return $this->valor_final;
	}

	/**
	 * Establece el valor final del control base.
	 *
	 * @param float|null $valor_final Valor final del control base.
	 *
	 * @return self
	 * @throws Exception Si el valor no es válido.
	 */
	public function setValor_final(?float $valor_final): self
	{
		if ($valor_final !== null && !$this->validarValorNumerico($valor_final)) {
			throw new Exception("El valor final debe ser un número válido mayor o igual a cero.");
		}
		$this->valor_final = $valor_final;
		return $this;
	}

	/**
	 * Obtiene el ID del cierre asociado.
	 *
	 * @return int|null ID del cierre asociado.
	 */
	public function getId_cierre(): ?int
	{
		return $this->id_cierre;
	}

	/**
	 * Establece el ID del cierre asociado.
	 *
	 * @param int|null $id_cierre ID del cierre asociado.
	 *
	 * @return self
	 */
	public function setId_cierre(?int $id_cierre): self
	{
		$this->id_cierre = $id_cierre;
		return $this;
	}

	/**
	 * Obtiene el estado del control base.
	 *
	 * @return int|null Estado del control base (1 = activo, 0 = inactivo).
	 */
	public function getEstado(): ?int
	{
		return $this->estado;
	}

	/**
	 * Establece el estado del control base.
	 *
	 * @param int|null $estado Estado del control base (1 = activo, 0 = inactivo).
	 *
	 * @return self
	 * @throws Exception Si el estado no es válido.
	 */
	public function setEstado(?int $estado): self
	{
		if ($estado !== null && !$this->validarEstado($estado)) {
			throw new Exception("El estado debe ser 0 (inactivo) o 1 (activo).");
		}
		$this->estado = $estado;
		return $this;
	}

	// --- Métodos adicionales ---

	/**
	 * Verifica si el control base está activo.
	 * @return bool
	 */
	public function isActivo(): bool
	{
		// Asegúrate de que el estado no sea null antes de comparar
		return $this->estado === 1;
	}

	/**
	 * Verifica si el control base está asociado a un cierre.
	 * @return bool
	 */
	public function tieneCierre(): bool
	{
		return $this->id_cierre !== null;
	}

	/**
	 * Obtiene el valor inicial formateado en pesos colombianos.
	 * @return string
	 */
	public function getValor_inicialFormateado(): string
	{
		if ($this->valor_inicial === null) {
			return '$0';
		}
		return '$' . number_format($this->valor_inicial, 0, ',', '.');
	}

	/**
	 * Obtiene el valor actual formateado en pesos colombianos.
	 * @return string
	 */
	public function getValor_actualFormateado(): string
	{
		if ($this->valor_actual === null) {
			return '$0';
		}
		return '$' . number_format($this->valor_actual, 0, ',', '.');
	}

	/**
	 * Obtiene el valor final formateado en pesos colombianos.
	 * @return string
	 */
	public function getValor_finalFormateado(): string
	{
		if ($this->valor_final === null) {
			return '$0';
		}
		return '$' . number_format($this->valor_final, 0, ',', '.');
	}

	/**
	 * Calcula la diferencia entre valor actual y valor inicial.
	 * @return float|null
	 */
	public function calcularDiferencia(): ?float
	{
		if ($this->valor_inicial !== null && $this->valor_actual !== null) {
			return $this->valor_actual - $this->valor_inicial;
		}
		return null;
	}
}
