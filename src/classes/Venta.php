<?php

declare(strict_types=1);

namespace App\classes;

use App\classes\Cliente;
use App\classes\MetodoPago;
use App\classes\CentroCosto;
use App\classes\Cierre;
use Exception;
use PDO;
use PDOException;

class Venta
{
    // --- Atributos ---
    private ?int    $id                    = null;
    private ?int    $id_cliente            = null;
    private ?string $fecha                 = null;
    private ?float  $valor_total           = null;
    private ?int    $id_metodo_pago        = null;
    private ?int    $id_centro_costo       = null;
    private ?int    $estado                = null;
    private ?int    $id_cierre             = null;
    private ?string $cliente_nombre        = null;
    private ?string $metodo_pago_nombre    = null;
    private ?string $centro_costo_nombre   = null;

    /**
     * Constructor: Inicializa las propiedades del objeto Venta.
     */
    public function __construct()
    {
        $this->id                  = 0;
        $this->id_cliente          = null; // Opcional para ventas sin cliente registrado
        $this->fecha               = null;
        $this->valor_total         = null;
        $this->id_metodo_pago      = null;
        $this->id_centro_costo     = null;
        $this->estado              = 1; // Estado activo por defecto
        $this->id_cierre           = null; // Referencia opcional a la tabla cierres
        $this->cliente_nombre      = null;
        $this->metodo_pago_nombre  = null;
        $this->centro_costo_nombre = null;
    }

    /**
     * Método estático para construir un objeto Venta desde un array (ej. fila de DB).
     *
     * @param array $resultado Array asociativo con los datos de la venta.
     *
     * @return self Instancia de Venta.
     * @throws Exception Si ocurre un error durante la construcción.
     */
    public static function construct(array $resultado = []): self
    {
        try {
            $objeto                      = new self();
            $objeto->id                  = isset($resultado['id']) ? (int)$resultado['id'] : 0;
            $objeto->id_cliente          = isset($resultado['id_cliente']) ? (int)$resultado['id_cliente'] : null;
            $objeto->fecha               = $resultado['fecha'] ?? null;
            $objeto->valor_total         = isset($resultado['valor_total']) ? (float)$resultado['valor_total'] : null;
            $objeto->id_metodo_pago      = isset($resultado['id_metodo_pago']) ? (int)$resultado['id_metodo_pago'] : null;
            $objeto->id_centro_costo     = isset($resultado['id_centro_costo']) ? (int)$resultado['id_centro_costo'] : null;
            $objeto->estado              = isset($resultado['estado']) ? (int)$resultado['estado'] : 1;
            $objeto->id_cierre           = isset($resultado['id_cierre']) ? (int)$resultado['id_cierre'] : null;
            $objeto->cliente_nombre      = $resultado['cliente_nombre'] ?? null;
            $objeto->metodo_pago_nombre  = $resultado['metodo_pago_nombre'] ?? null;
            $objeto->centro_costo_nombre = $resultado['centro_costo_nombre'] ?? null;
            return $objeto;
        } catch (Exception $e) {
            throw new Exception("Error al construir Venta: " . $e->getMessage());
        }
    }

    /**
     * Obtiene todas las ventas activas con información de entidades relacionadas.
     *
     * @param PDO $conexion Conexión PDO.
     *
     * @return array Array de objetos Venta.
     * @throws Exception Si ocurre un error de base de datos.
     */
    public static function obtenerTodas(PDO $conexion): array
    {
        try {
            $query = <<<SQL
            SELECT
                v.*,
                c.nombre AS cliente_nombre,
                mp.descripcion AS metodo_pago_nombre,
                cc.nombre AS centro_costo_nombre
            FROM ventas v
            LEFT JOIN clientes c ON v.id_cliente = c.id
            INNER JOIN metodos_pagos mp ON v.id_metodo_pago = mp.id
            INNER JOIN centros_costos cc ON v.id_centro_costo = cc.id
            WHERE v.estado = 1
            ORDER BY v.fecha DESC
            SQL;

            $statement = $conexion->prepare($query);
            $statement->execute();
            $resultados = $statement->fetchAll(PDO::FETCH_ASSOC);

            $ventas = [];
            foreach ($resultados as $resultado) {
                $ventas[] = self::construct($resultado);
            }

            return $ventas;
        } catch (PDOException $e) {
            throw new Exception("Error de base de datos al obtener ventas: " . $e->getMessage());
        }
    }

    /**
     * Busca ventas por criterios específicos.
     *
     * @param PDO $conexion Conexión PDO.
     * @param string $termino_cliente Término de búsqueda para cliente (nombre o celular).
     * @param string $fecha_desde Fecha desde (formato Y-m-d).
     * @param string $fecha_hasta Fecha hasta (formato Y-m-d).
     * @param int|null $id_centro_costo ID del centro de costo.
     * @param int|null $id_venta ID específico de venta para filtrar.
     *
     * @return array Array de objetos Venta.
     * @throws Exception Si hay error en DB.
     */
    public static function search_ventas(PDO $conexion, string $termino_cliente = '', string $fecha_desde = '', string $fecha_hasta = '', ?int $id_centro_costo = null, ?int $id_venta = null): array
    {
        try {
            $where_conditions = ['v.estado = 1'];
            $params = [];

            // Filtro por cliente (nombre o celular)
            if (!empty($termino_cliente)) {
                $where_conditions[] = '(c.nombre LIKE :termino_cliente OR c.celular LIKE :termino_cliente)';
                $params[':termino_cliente'] = '%' . $termino_cliente . '%';
            }

            // Filtro por rango de fechas
            if (!empty($fecha_desde)) {
                $where_conditions[] = 'DATE(v.fecha) >= :fecha_desde';
                $params[':fecha_desde'] = $fecha_desde;
            }

            if (!empty($fecha_hasta)) {
                $where_conditions[] = 'DATE(v.fecha) <= :fecha_hasta';
                $params[':fecha_hasta'] = $fecha_hasta;
            }

            // Filtro por centro de costo
            if ($id_centro_costo !== null) {
                $where_conditions[] = 'v.id_centro_costo = :id_centro_costo';
                $params[':id_centro_costo'] = $id_centro_costo;
            }

            // Filtro por ID de venta
            if ($id_venta !== null) {
                $where_conditions[] = 'v.id = :id_venta';
                $params[':id_venta'] = $id_venta;
            }

            $where_clause = implode(' AND ', $where_conditions);

            $query = <<<SQL
            SELECT
                v.*,
                c.nombre AS cliente_nombre,
                mp.descripcion AS metodo_pago_nombre,
                cc.nombre AS centro_costo_nombre
            FROM ventas v
            LEFT JOIN clientes c ON v.id_cliente = c.id
            INNER JOIN metodos_pagos mp ON v.id_metodo_pago = mp.id
            INNER JOIN centros_costos cc ON v.id_centro_costo = cc.id
            WHERE {$where_clause}
            ORDER BY v.fecha DESC
            SQL;

            $statement = $conexion->prepare($query);

            foreach ($params as $param => $value) {
                $statement->bindValue($param, $value);
            }

            $statement->execute();
            $resultados = $statement->fetchAll(PDO::FETCH_ASSOC);

            $ventas = [];
            foreach ($resultados as $resultado) {
                $ventas[] = self::construct($resultado);
            }

            return $ventas;
        } catch (PDOException $e) {
            throw new Exception("Error de base de datos al buscar ventas: " . $e->getMessage());
        }
    }

    /**
     * Obtiene una venta por su ID con información de entidades relacionadas.
     *
     * @param int $id       ID de la venta.
     * @param PDO $conexion Conexión PDO.
     *
     * @return self|null Objeto Venta o null si no se encuentra.
     * @throws Exception Si ocurre un error de base de datos.
     */
    public static function get(int $id, PDO $conexion): ?self
    {
        try {
            $query = <<<SQL
            SELECT
                v.*,
                c.nombre AS cliente_nombre,
                mp.descripcion AS metodo_pago_nombre,
                cc.nombre AS centro_costo_nombre
            FROM ventas v
            LEFT JOIN clientes c ON v.id_cliente = c.id
            INNER JOIN metodos_pagos mp ON v.id_metodo_pago = mp.id
            INNER JOIN centros_costos cc ON v.id_centro_costo = cc.id
            WHERE v.id = :id AND v.estado = 1
            LIMIT 1
            SQL;

            $statement = $conexion->prepare($query);
            $statement->bindValue(':id', $id, PDO::PARAM_INT);
            $statement->execute();
            $resultado = $statement->fetch(PDO::FETCH_ASSOC);

            return $resultado ? self::construct($resultado) : null;
        } catch (PDOException $e) {
            throw new Exception("Error de base de datos al obtener venta: " . $e->getMessage());
        }
    }

    /**
     * Crea una nueva venta en la base de datos a partir de un objeto Venta.
     *
     * @param PDO $conexion Conexión PDO.
     *
     * @return int|false El ID de la nueva venta creada o false en caso de error.
     * @throws Exception Si los datos requeridos en el objeto están vacíos o hay error en DB.
     */
    function crear(PDO $conexion): int|false
    {
        // Validaciones básicas sobre el objeto
        if ($this->getValor_total() === null || $this->getId_metodo_pago() === null || $this->getId_centro_costo() === null) {
            throw new Exception("Valor total, método de pago y centro de costo son requeridos para crear una venta.");
        }

        // Validar valor total como positivo
        if ($this->getValor_total() <= 0) {
            throw new Exception("El valor total debe ser un número positivo mayor que cero.");
        }

        // Validar que el método de pago existe
        if (!$this->validarMetodoPagoExiste($this->getId_metodo_pago(), $conexion)) {
            throw new Exception("El método de pago especificado no existe.");
        }

        // Validar que el centro de costo existe
        if (!$this->validarCentroCostoExiste($this->getId_centro_costo(), $conexion)) {
            throw new Exception("El centro de costo especificado no existe.");
        }

        // Validar que el cliente existe si se especifica
        if ($this->getId_cliente() !== null && !$this->validarClienteExiste($this->getId_cliente(), $conexion)) {
            throw new Exception("El cliente especificado no existe.");
        }

        try {
            // Establecer la zona horaria para Colombia
            date_default_timezone_set('America/Bogota');

            // Auto-establecer fecha actual si no se especifica
            if (empty($this->getFecha())) {
                $this->setFecha(date('Y-m-d H:i:s'));
            }

            return $this->_insert($conexion);
        } catch (Exception $e) {
            throw new Exception("Error al crear venta: " . $e->getMessage());
        }
    }

    /**
     * Modifica una venta existente.
     *
     * @param PDO $conexion Conexión PDO.
     *
     * @return bool True si la modificación fue exitosa, False en caso contrario.
     * @throws Exception Si los datos requeridos están vacíos o si ocurre un error de base de datos.
     */
    function modificar(PDO $conexion): bool
    {
        // Validar que el ID sea válido
        if ($this->getId() <= 0) {
            throw new Exception("ID de venta no válido para modificar.");
        }

        // Validaciones básicas sobre el objeto
        if ($this->getValor_total() === null || $this->getId_metodo_pago() === null || $this->getId_centro_costo() === null) {
            throw new Exception("Valor total, método de pago y centro de costo son requeridos para modificar una venta.");
        }

        // Validar valor total como positivo
        if ($this->getValor_total() <= 0) {
            throw new Exception("El valor total debe ser un número positivo mayor que cero.");
        }

        // Validar que el método de pago existe
        if (!$this->validarMetodoPagoExiste($this->getId_metodo_pago(), $conexion)) {
            throw new Exception("El método de pago especificado no existe.");
        }

        // Validar que el centro de costo existe
        if (!$this->validarCentroCostoExiste($this->getId_centro_costo(), $conexion)) {
            throw new Exception("El centro de costo especificado no existe.");
        }

        // Validar que el cliente existe si se especifica
        if ($this->getId_cliente() !== null && !$this->validarClienteExiste($this->getId_cliente(), $conexion)) {
            throw new Exception("El cliente especificado no existe.");
        }

        try {
            return $this->_update($conexion);
        } catch (Exception $e) {
            throw new Exception("Error al modificar venta: " . $e->getMessage());
        }
    }

    /**
     * Método privado para insertar una nueva venta en la base de datos.
     *
     * @param PDO $conexion Conexión PDO.
     *
     * @return int|false El ID de la nueva venta creada o false en caso de error.
     * @throws Exception Si hay error en DB.
     */
    private function _insert(PDO $conexion): int|false
    {
        try {
            // Preparar la consulta INSERT usando Heredoc
            $query = <<<SQL
            INSERT INTO ventas (
                 id_cliente
                ,fecha
                ,valor_total
                ,id_metodo_pago
                ,id_centro_costo
                ,estado
                ,id_cierre
            ) VALUES (
                 :id_cliente
                ,:fecha
                ,:valor_total
                ,:id_metodo_pago
                ,:id_centro_costo
                ,:estado
                ,:id_cierre
            )
            SQL;

            $statement = $conexion->prepare($query);

            // Bind de parámetros desde el objeto
            $statement->bindValue(':id_cliente', $this->getId_cliente(), PDO::PARAM_INT);
            $statement->bindValue(':fecha', $this->getFecha(), PDO::PARAM_STR);
            $statement->bindValue(':valor_total', $this->getValor_total(), PDO::PARAM_STR);
            $statement->bindValue(':id_metodo_pago', $this->getId_metodo_pago(), PDO::PARAM_INT);
            $statement->bindValue(':id_centro_costo', $this->getId_centro_costo(), PDO::PARAM_INT);
            $statement->bindValue(':estado', $this->getEstado(), PDO::PARAM_INT);

            // Handle null values for id_cierre
            $id_cierre = $this->getId_cierre();
            $statement->bindValue(':id_cierre', $id_cierre, $id_cierre === null ? PDO::PARAM_NULL : PDO::PARAM_INT);

            // Ejecutar la consulta
            $success = $statement->execute();

            if ($success) {
                // Devolver el ID de la venta recién creada
                return (int)$conexion->lastInsertId();
            } else {
                return false; // Error en la ejecución
            }

        } catch (PDOException $e) {
            throw new Exception("Error de base de datos al crear venta: " . $e->getMessage());
        }
    }

    /**
     * Método privado para actualizar una venta existente en la base de datos.
     *
     * @param PDO $conexion Conexión PDO.
     *
     * @return bool True si la actualización fue exitosa, false en caso contrario.
     * @throws Exception Si hay error en DB.
     */
    private function _update(PDO $conexion): bool
    {
        try {
            // Preparar la consulta UPDATE usando Heredoc
            $query = <<<SQL
            UPDATE ventas SET
                 id_cliente = :id_cliente
                ,fecha = :fecha
                ,valor_total = :valor_total
                ,id_metodo_pago = :id_metodo_pago
                ,id_centro_costo = :id_centro_costo
                ,estado = :estado
                ,id_cierre = :id_cierre
            WHERE
                id = :id
            SQL;

            $statement = $conexion->prepare($query);

            // Bind de parámetros desde el objeto
            $statement->bindValue(':id_cliente', $this->getId_cliente(), PDO::PARAM_INT);
            $statement->bindValue(':fecha', $this->getFecha(), PDO::PARAM_STR);
            $statement->bindValue(':valor_total', $this->getValor_total(), PDO::PARAM_STR);
            $statement->bindValue(':id_metodo_pago', $this->getId_metodo_pago(), PDO::PARAM_INT);
            $statement->bindValue(':id_centro_costo', $this->getId_centro_costo(), PDO::PARAM_INT);
            $statement->bindValue(':estado', $this->getEstado(), PDO::PARAM_INT);

            // Handle null values for id_cierre
            $id_cierre = $this->getId_cierre();
            $statement->bindValue(':id_cierre', $id_cierre, $id_cierre === null ? PDO::PARAM_NULL : PDO::PARAM_INT);

            $statement->bindValue(':id', $this->getId(), PDO::PARAM_INT);

            // execute() devuelve true en éxito, false en error.
            return $statement->execute();

        } catch (PDOException $e) {
            throw new Exception("Error de base de datos al modificar venta (ID: {$this->getId()}): " . $e->getMessage());
        }
    }

    /**
     * Valida que un cliente existe en la base de datos.
     *
     * @param int $id_cliente ID del cliente a validar.
     * @param PDO $conexion   Conexión PDO.
     *
     * @return bool True si el cliente existe, false en caso contrario.
     */
    private function validarClienteExiste(int $id_cliente, PDO $conexion): bool
    {
        try {
            $query = <<<SQL
            SELECT COUNT(*) as count
            FROM clientes
            WHERE id = :id_cliente AND estado = 1
            SQL;

            $statement = $conexion->prepare($query);
            $statement->bindValue(':id_cliente', $id_cliente, PDO::PARAM_INT);
            $statement->execute();
            $resultado = $statement->fetch(PDO::FETCH_ASSOC);

            return $resultado['count'] > 0;
        } catch (PDOException $e) {
            return false;
        }
    }

    /**
     * Valida que un método de pago existe en la base de datos.
     *
     * @param int $id_metodo_pago ID del método de pago a validar.
     * @param PDO $conexion       Conexión PDO.
     *
     * @return bool True si el método de pago existe, false en caso contrario.
     */
    private function validarMetodoPagoExiste(int $id_metodo_pago, PDO $conexion): bool
    {
        try {
            $query = <<<SQL
            SELECT COUNT(*) as count
            FROM metodos_pagos
            WHERE id = :id_metodo_pago AND estado = 1
            SQL;

            $statement = $conexion->prepare($query);
            $statement->bindValue(':id_metodo_pago', $id_metodo_pago, PDO::PARAM_INT);
            $statement->execute();
            $resultado = $statement->fetch(PDO::FETCH_ASSOC);

            return $resultado['count'] > 0;
        } catch (PDOException $e) {
            return false;
        }
    }

    /**
     * Valida que un centro de costo existe en la base de datos.
     *
     * @param int $id_centro_costo ID del centro de costo a validar.
     * @param PDO $conexion        Conexión PDO.
     *
     * @return bool True si el centro de costo existe, false en caso contrario.
     */
    private function validarCentroCostoExiste(int $id_centro_costo, PDO $conexion): bool
    {
        try {
            $query = <<<SQL
            SELECT COUNT(*) as count
            FROM centros_costos
            WHERE id = :id_centro_costo AND estado = 1
            SQL;

            $statement = $conexion->prepare($query);
            $statement->bindValue(':id_centro_costo', $id_centro_costo, PDO::PARAM_INT);
            $statement->execute();
            $resultado = $statement->fetch(PDO::FETCH_ASSOC);

            return $resultado['count'] > 0;
        } catch (PDOException $e) {
            return false;
        }
    }

    // --- Getters y Setters ---

    public function getId(): ?int
    {
        return $this->id;
    }

    public function setId(?int $id): self
    {
        $this->id = $id;
        return $this;
    }

    public function getId_cliente(): ?int
    {
        return $this->id_cliente;
    }

    public function setId_cliente(?int $id_cliente): self
    {
        $this->id_cliente = $id_cliente;
        return $this;
    }

    public function getFecha(): ?string
    {
        return $this->fecha;
    }

    public function setFecha(?string $fecha): self
    {
        $this->fecha = $fecha;
        return $this;
    }

    public function getValor_total(): ?float
    {
        return $this->valor_total;
    }

    public function setValor_total(?float $valor_total): self
    {
        $this->valor_total = $valor_total;
        return $this;
    }

    public function getId_metodo_pago(): ?int
    {
        return $this->id_metodo_pago;
    }

    public function setId_metodo_pago(?int $id_metodo_pago): self
    {
        $this->id_metodo_pago = $id_metodo_pago;
        return $this;
    }

    public function getId_centro_costo(): ?int
    {
        return $this->id_centro_costo;
    }

    public function setId_centro_costo(?int $id_centro_costo): self
    {
        $this->id_centro_costo = $id_centro_costo;
        return $this;
    }

    public function getEstado(): ?int
    {
        return $this->estado;
    }

    public function setEstado(?int $estado): self
    {
        $this->estado = $estado;
        return $this;
    }

    public function getCliente_nombre(): ?string
    {
        return $this->cliente_nombre;
    }

    public function setCliente_nombre(?string $cliente_nombre): self
    {
        $this->cliente_nombre = $cliente_nombre;
        return $this;
    }

    public function getMetodo_pago_nombre(): ?string
    {
        return $this->metodo_pago_nombre;
    }

    public function setMetodo_pago_nombre(?string $metodo_pago_nombre): self
    {
        $this->metodo_pago_nombre = $metodo_pago_nombre;
        return $this;
    }

    public function getCentro_costo_nombre(): ?string
    {
        return $this->centro_costo_nombre;
    }

    public function setCentro_costo_nombre(?string $centro_costo_nombre): self
    {
        $this->centro_costo_nombre = $centro_costo_nombre;
        return $this;
    }

    public function getId_cierre(): ?int
    {
        return $this->id_cierre;
    }

    public function setId_cierre(?int $id_cierre): self
    {
        $this->id_cierre = $id_cierre;
        return $this;
    }

    // --- Métodos adicionales ---

    /**
     * Verifica si la venta está activa.
     * @return bool
     */
    public function isActivo(): bool
    {
        return $this->estado === 1;
    }

    /**
     * Obtiene el valor total formateado en pesos colombianos.
     * @return string
     */
    public function getValorTotalFormateado(): string
    {
        if ($this->valor_total === null) {
            return '$0';
        }
        return '$' . number_format($this->valor_total, 0, ',', '.');
    }

    /**
     * Obtiene la fecha formateada para mostrar.
     * @return string
     */
    public function getFechaFormateada(): string
    {
        if (empty($this->fecha)) {
            return '';
        }
        $fecha = new \DateTime($this->fecha);
        return $fecha->format('Y-m-d H:i:s');
    }

    /**
     * Obtiene las ventas activas listas para cierre de caja por centro de costo.
     *
     * Filtros aplicados:
     * - estado = 1 (ventas activas, no canceladas)
     * - id_cierre IS NULL (no incluidas en cierres anteriores)
     * - centro de costo específico
     *
     * @param int $id_centro_costo ID del centro de costo.
     * @param PDO $conexion Conexión PDO.
     *
     * @return array Array de objetos Venta con información completa.
     * @throws Exception Si hay error en DB.
     */
    public static function getVentasParaCierreByCentroCosto(int $id_centro_costo, PDO $conexion): array
    {
        try {
            // Consulta para obtener ventas listas para cierre con información completa
            $query = <<<SQL
            SELECT
                v.*,
                c.nombre AS cliente_nombre,
                mp.descripcion AS metodo_pago_nombre,
                cc.nombre AS centro_costo_nombre
            FROM ventas v
            LEFT JOIN clientes c ON v.id_cliente = c.id
            INNER JOIN metodos_pagos mp ON v.id_metodo_pago = mp.id
            INNER JOIN centros_costos cc ON v.id_centro_costo = cc.id
            WHERE v.estado = 1
            AND v.id_cierre IS NULL
            AND v.id_centro_costo = :id_centro_costo
            ORDER BY v.fecha DESC
            SQL;

            $statement = $conexion->prepare($query);
            $statement->bindValue(':id_centro_costo', $id_centro_costo, PDO::PARAM_INT);
            $statement->execute();
            $resultados = $statement->fetchAll(PDO::FETCH_ASSOC);

            $listado = [];
            foreach ($resultados as $resultado) {
                $venta = self::construct($resultado);
                $listado[] = $venta;
            }

            return $listado;

        } catch (PDOException $e) {
            throw new Exception("Error al obtener ventas para cierre por centro de costo: " . $e->getMessage());
        }
    }

    /**
     * Obtiene ventas agrupadas por método de pago con los mismos filtros que search_ventas.
     *
     * @param PDO $conexion Conexión PDO.
     * @param string $termino_cliente Término de búsqueda para cliente (nombre o celular).
     * @param string $fecha_desde Fecha desde (formato Y-m-d).
     * @param string $fecha_hasta Fecha hasta (formato Y-m-d).
     * @param int|null $id_centro_costo ID del centro de costo.
     * @param int|null $id_venta ID específico de venta para filtrar.
     *
     * @return array Array con datos agrupados por método de pago.
     * @throws Exception Si hay error en DB.
     */
    public static function obtenerVentasAgrupadasPorMetodoPago(PDO $conexion, string $termino_cliente = '', string $fecha_desde = '', string $fecha_hasta = '', ?int $id_centro_costo = null, ?int $id_venta = null): array
    {
        try {
            $where_conditions = ['v.estado = 1'];
            $params = [];

            // Filtro por cliente (nombre o celular)
            if (!empty($termino_cliente)) {
                $where_conditions[] = '(c.nombre LIKE :termino_cliente OR c.celular LIKE :termino_cliente)';
                $params[':termino_cliente'] = '%' . $termino_cliente . '%';
            }

            // Filtro por rango de fechas
            if (!empty($fecha_desde)) {
                $where_conditions[] = 'DATE(v.fecha) >= :fecha_desde';
                $params[':fecha_desde'] = $fecha_desde;
            }

            if (!empty($fecha_hasta)) {
                $where_conditions[] = 'DATE(v.fecha) <= :fecha_hasta';
                $params[':fecha_hasta'] = $fecha_hasta;
            }

            // Filtro por centro de costo
            if ($id_centro_costo !== null) {
                $where_conditions[] = 'v.id_centro_costo = :id_centro_costo';
                $params[':id_centro_costo'] = $id_centro_costo;
            }

            // Filtro por ID de venta
            if ($id_venta !== null) {
                $where_conditions[] = 'v.id = :id_venta';
                $params[':id_venta'] = $id_venta;
            }

            $where_clause = implode(' AND ', $where_conditions);

            $query = <<<SQL
            SELECT
                mp.descripcion AS metodo_pago_nombre,
                SUM(v.valor_total) AS total_ventas
            FROM ventas v
            LEFT JOIN clientes c ON v.id_cliente = c.id
            INNER JOIN metodos_pagos mp ON v.id_metodo_pago = mp.id
            INNER JOIN centros_costos cc ON v.id_centro_costo = cc.id
            WHERE {$where_clause}
            GROUP BY mp.id, mp.descripcion
            ORDER BY mp.descripcion ASC
            SQL;

            $statement = $conexion->prepare($query);

            foreach ($params as $param => $value) {
                $statement->bindValue($param, $value);
            }

            $statement->execute();
            $resultados = $statement->fetchAll(PDO::FETCH_ASSOC);

            // Format results with Colombian Peso formatting
            $resultados_formateados = [];
            $total_general = 0;

            foreach ($resultados as $resultado) {
                $total_ventas = (float)$resultado['total_ventas'];
                $total_general += $total_ventas;

                $resultados_formateados[] = [
                    'metodo_pago_nombre' => $resultado['metodo_pago_nombre'],
                    'total_ventas' => $total_ventas,
                    'total_ventas_formateado' => '$' . number_format($total_ventas, 0, ',', '.')
                ];
            }

            return [
                'datos' => $resultados_formateados,
                'total_general' => $total_general,
                'total_general_formateado' => '$' . number_format($total_general, 0, ',', '.')
            ];

        } catch (PDOException $e) {
            throw new Exception("Error de base de datos al obtener ventas agrupadas por método de pago: " . $e->getMessage());
        }
    }

    /**
     * Actualiza el id_cierre para múltiples ventas en una sola operación.
     *
     * @param array $ids_ventas Array de IDs de ventas a actualizar.
     * @param int   $id_cierre  ID del cierre a asignar.
     * @param PDO   $conexion   Conexión PDO.
     *
     * @return bool True si la actualización fue exitosa, False en caso contrario.
     * @throws Exception Si hay error en DB.
     */
    public static function actualizarIdCierreBulk(array $ids_ventas, int $id_cierre, PDO $conexion): bool
    {
        if (empty($ids_ventas)) {
            return true; // No hay nada que actualizar
        }

        try {
            // Validar y sanitizar todos los IDs para prevenir inyección SQL
            $ids_validados = [];
            foreach ($ids_ventas as $id) {
                $id_int = filter_var($id, FILTER_VALIDATE_INT, [
                    'options' => ['min_range' => 1]
                ]);
                if ($id_int === false) {
                    throw new Exception("ID de venta inválido detectado: " . var_export($id, true));
                }
                $ids_validados[] = $id_int;
            }

            // Crear placeholders seguros para los IDs validados
            $placeholders = str_repeat('?,', count($ids_validados) - 1) . '?';

            // Consulta para actualizar el id_cierre en múltiples ventas
            $query = <<<SQL
            UPDATE ventas SET
                id_cierre = ?
            WHERE
                id IN ($placeholders)
                AND estado = 1
            SQL;

            $statement = $conexion->prepare($query);

            // Bind del id_cierre primero, luego los IDs validados
            $params = [$id_cierre];
            $params = array_merge($params, $ids_validados);

            return $statement->execute($params);

        } catch (PDOException $e) {
            throw new Exception("Error de base de datos al actualizar id_cierre en ventas: " . $e->getMessage());
        }
    }

    /**
     * Obtiene las ventas asociadas a un cierre específico.
     *
     * @param int $id_cierre ID del cierre.
     * @param PDO $conexion  Conexión PDO.
     *
     * @return array Array de objetos Venta con información completa.
     * @throws Exception Si hay error en DB.
     */
    public static function getVentasByCierre(int $id_cierre, PDO $conexion): array
    {
        try {
            // Consulta para obtener ventas por cierre con información completa
            $query = <<<SQL
            SELECT
                v.*,
                c.nombre AS cliente_nombre,
                mp.descripcion AS metodo_pago_nombre,
                cc.nombre AS centro_costo_nombre
            FROM ventas v
            LEFT JOIN clientes c ON v.id_cliente = c.id
            INNER JOIN metodos_pagos mp ON v.id_metodo_pago = mp.id
            INNER JOIN centros_costos cc ON v.id_centro_costo = cc.id
            WHERE v.id_cierre = :id_cierre
            ORDER BY v.fecha DESC
            SQL;

            $statement = $conexion->prepare($query);
            $statement->bindValue(':id_cierre', $id_cierre, PDO::PARAM_INT);
            $statement->execute();
            $resultados = $statement->fetchAll(PDO::FETCH_ASSOC);

            $listado = [];
            foreach ($resultados as $resultado) {
                $venta = self::construct($resultado);
                $listado[] = $venta;
            }

            return $listado;

        } catch (PDOException $e) {
            throw new Exception("Error al obtener ventas por cierre: " . $e->getMessage());
        }
    }

    /**
     * Calcula los ingresos totales de ventas para una fecha específica y centro de costo.
     * Solo incluye ventas finalizadas (con cierre asignado).
     *
     * @param string $fecha           Fecha a consultar (formato YYYY-MM-DD).
     * @param int    $id_centro_costo ID del centro de costo.
     * @param PDO    $conexion        Conexión PDO.
     *
     * @return float Total de ingresos de ventas para la fecha y centro de costo.
     * @throws Exception Si hay error en DB.
     */
    public static function calcularIngresosPorFecha(string $fecha, int $id_centro_costo, PDO $conexion): float
    {
        try {
            $query = <<<SQL
            SELECT COALESCE(SUM(valor_total), 0) as total
            FROM ventas
            WHERE DATE(fecha) = :fecha
            AND estado = 1
            AND id_cierre IS NOT NULL
            AND id_centro_costo = :id_centro_costo
            SQL;

            $statement = $conexion->prepare($query);
            $statement->bindValue(':fecha', $fecha, PDO::PARAM_STR);
            $statement->bindValue(':id_centro_costo', $id_centro_costo, PDO::PARAM_INT);
            $statement->execute();
            $resultado = $statement->fetch(PDO::FETCH_ASSOC);

            return (float)($resultado['total'] ?? 0);

        } catch (PDOException $e) {
            throw new Exception("Error al calcular ingresos de ventas por fecha ($fecha): " . $e->getMessage());
        }
    }

    /**
     * Obtiene los registros detallados de ventas para una fecha específica y centro de costo.
     * Solo incluye ventas finalizadas (con cierre asignado).
     *
     * @param string $fecha           Fecha a consultar (formato YYYY-MM-DD).
     * @param int    $id_centro_costo ID del centro de costo.
     * @param PDO    $conexion        Conexión PDO.
     *
     * @return array Array de registros de ventas con detalles.
     * @throws Exception Si hay error en DB.
     */
    public static function obtenerRegistrosPorFecha(string $fecha, int $id_centro_costo, PDO $conexion): array
    {
        try {
            $query = <<<SQL
            SELECT
                v.id,
                v.fecha,
                c.nombre AS nombre_cliente,
                c.celular AS celular_cliente,
                mp.descripcion AS metodo_pago,
                v.valor_total
            FROM ventas v
            LEFT JOIN clientes c ON v.id_cliente = c.id
            LEFT JOIN metodos_pagos mp ON v.id_metodo_pago = mp.id
            WHERE DATE(v.fecha) = :fecha
            AND v.estado = 1
            AND v.id_cierre IS NOT NULL
            AND v.id_centro_costo = :id_centro_costo
            ORDER BY v.fecha DESC
            SQL;

            $statement = $conexion->prepare($query);
            $statement->bindValue(':fecha', $fecha, PDO::PARAM_STR);
            $statement->bindValue(':id_centro_costo', $id_centro_costo, PDO::PARAM_INT);
            $statement->execute();
            $resultados = $statement->fetchAll(PDO::FETCH_ASSOC);

            // Format results
            $registros_formateados = [];
            foreach ($resultados as $resultado) {
                $registros_formateados[] = [
                    'id' => $resultado['id'],
                    'fecha' => $resultado['fecha'],
                    'nombre_cliente' => $resultado['nombre_cliente'] ?? 'Cliente no especificado',
                    'celular_cliente' => $resultado['celular_cliente'] ?? '',
                    'metodo_pago' => $resultado['metodo_pago'] ?? 'No especificado',
                    'valor_total' => (float)$resultado['valor_total'],
                    'valor_total_formateado' => '$' . number_format((float)$resultado['valor_total'], 0, ',', '.')
                ];
            }

            return $registros_formateados;

        } catch (PDOException $e) {
            throw new Exception("Error al obtener registros de ventas por fecha ($fecha): " . $e->getMessage());
        }
    }
}