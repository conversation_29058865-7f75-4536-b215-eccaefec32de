<?php

declare(strict_types=1);

namespace App\classes;

use Exception;
use PDO;
use PDOException;

class Puesto
{
	// --- Atributos ---
	private ?int    $id          = null;
	private ?string $descripcion = null;
	private ?int    $id_centro_costo = null;
	private ?int    $estado      = null;
	private ?string $nombre_centro_costo = null; // Nombre del centro de costo asociado

	/**
	 * Constructor: Inicializa las propiedades del objeto Puesto.
	 */
	public function __construct()
	{
		$this->id                  = 0; // O null si prefieres no usar 0 por defecto
		$this->descripcion         = null;
		$this->id_centro_costo     = null;
		$this->estado              = 1; // Estado activo por defecto
		$this->nombre_centro_costo = null;
	}

	/**
	 * Método estático para construir un objeto Puesto desde un array (ej. fila de DB).
	 *
	 * @param array $resultado Array asociativo con los datos del puesto.
	 *
	 * @return self Instancia de Puesto.
	 * @throws Exception Si ocurre un error durante la construcción.
	 */
	public static function construct(array $resultado = []): self
	{
		try {
			$objeto                      = new self();
			$objeto->id                  = isset($resultado['id']) ? (int)$resultado['id'] : 0;
			$objeto->descripcion         = $resultado['descripcion'] ?? null;
			$objeto->id_centro_costo     = isset($resultado['id_centro_costo']) ? (int)$resultado['id_centro_costo'] : null;
			$objeto->estado              = isset($resultado['estado']) ? (int)$resultado['estado'] : 1;
			$objeto->nombre_centro_costo = $resultado['nombre_centro_costo'] ?? null;
			return $objeto;
		} catch (Exception $e) {
			// Considera loggear el error aquí
			throw new Exception("Error al construir Puesto: " . $e->getMessage());
		}
	}

	// --- Métodos de Acceso a Datos (Estáticos) ---

	/**
	 * Obtiene un puesto por su ID.
	 *
	 * @param int $id       ID del puesto.
	 * @param PDO $conexion Conexión PDO.
	 *
	 * @return self|null Objeto Puesto o null si no se encuentra.
	 * @throws Exception Si hay error en DB.
	 */
	public static function get(int $id, PDO $conexion): ?self
	{
		try {
			// Consulta para obtener puesto por ID (Usando Heredoc)
			$query = <<<SQL
            SELECT            
            	p.*,
            	cc.nombre AS nombre_centro_costo
            FROM puestos p
            LEFT JOIN centros_costos cc ON p.id_centro_costo = cc.id
            WHERE
            	p.id = :id
            LIMIT 1
            SQL;

			$statement = $conexion->prepare($query);
			$statement->bindValue(":id", $id, PDO::PARAM_INT);
			$statement->execute();
			$resultado = $statement->fetch(PDO::FETCH_ASSOC);

			return $resultado ? self::construct($resultado) : null;

		} catch (PDOException $e) {
			throw new Exception("Error al obtener Puesto (ID: $id): " . $e->getMessage());
		}
	}

	/**
	 * Obtiene una lista de puestos activos.
	 *
	 * @param PDO $conexion Conexión PDO.
	 *
	 * @return array Array de objetos Puesto.
	 * @throws Exception Si hay error en DB.
	 */
	public static function get_list(PDO $conexion): array
	{
		try {
			// Consulta para obtener lista de puestos activos (Usando Heredoc)
			$query = <<<SQL
            SELECT            
            	p.*,
            	cc.nombre AS nombre_centro_costo
            FROM puestos p
            LEFT JOIN centros_costos cc ON p.id_centro_costo = cc.id
            WHERE
            	p.estado = 1
            ORDER BY
            	p.descripcion
            SQL;

			$statement = $conexion->prepare($query);
			$statement->execute();
			$resultados = $statement->fetchAll(PDO::FETCH_ASSOC);

			$listado = [];
			foreach ($resultados as $resultado) {
				$listado[] = self::construct($resultado);
			}
			return $listado;

		} catch (PDOException $e) {
			throw new Exception("Error al obtener lista de Puestos: " . $e->getMessage());
		}
	}

	/**
	 * Crea un nuevo puesto en la base de datos a partir de un objeto Puesto.
	 * El objeto Puesto debe tener la descripción establecida.
	 *
	 * @param PDO $conexion Conexión PDO.
	 *
	 * @return int|false El ID del nuevo puesto creado o false en caso de error.
	 * @throws Exception Si los datos requeridos en el objeto están vacíos o hay error en DB.
	 */
	function crear(PDO $conexion): int|false
	{
		// Validaciones básicas sobre el objeto
		if (empty($this->getDescripcion())) {
			throw new Exception("La descripción es requerida para crear un puesto.");
		}
		if ($this->getId_centro_costo() !== null && (!is_int($this->getId_centro_costo()) || $this->getId_centro_costo() <= 0)) {
			throw new Exception("ID de centro de costo inválido.");
		}

		try {
			// Preparar la consulta INSERT usando Heredoc
			$query = <<<SQL
            INSERT INTO puestos (
            	 descripcion,
            	 id_centro_costo
            ) VALUES (
            	 :descripcion,
            	 :id_centro_costo
            )
            SQL;

			$statement = $conexion->prepare($query);

			// Bind de parámetros desde el objeto
			$statement->bindValue(':descripcion', $this->getDescripcion(), PDO::PARAM_STR);			
			$statement->bindValue(
				':id_centro_costo',
				$this->getId_centro_costo(),
				$this->getId_centro_costo() === null ? PDO::PARAM_NULL : PDO::PARAM_INT
			);

			// Ejecutar la consulta
			$success = $statement->execute();

			if ($success) {
				// Devolver el ID del puesto recién creado
				return (int)$conexion->lastInsertId();
			} else {
				return false; // Error en la ejecución
			}

		} catch (PDOException $e) {
			throw new Exception("Error de base de datos al crear puesto: " . $e->getMessage());
		} catch (Exception $e) {
			// Capturar otros errores
			throw new Exception("Error al crear puesto: " . $e->getMessage());
		}
	}

	/**
	 * Modifica un puesto existente.
	 *
	 * @param PDO $conexion Conexión PDO.
	 *
	 * @return bool True si la modificación fue exitosa, False en caso contrario.
	 * @throws Exception Si la descripción está vacía o si ocurre un error de base de datos.
	 */
	function modificar(PDO $conexion): bool
	{
		if (empty(trim($this->getDescripcion()))) {
			throw new Exception("La descripción no puede estar vacía.");
		}

		if ($this->getId() <= 0) {
			throw new Exception("El ID del puesto debe ser un valor válido mayor que cero.");
		}
		if ($this->getId_centro_costo() !== null && (!is_int($this->getId_centro_costo()) || $this->getId_centro_costo() <= 0)) {
			throw new Exception("ID de centro de costo inválido.");
		}

		try {
			// Consulta para actualizar la descripción
			$query = <<<SQL
            UPDATE puestos SET
                descripcion = :descripcion,
                id_centro_costo = :id_centro_costo
            WHERE
                id = :id
            SQL;

			$statement = $conexion->prepare($query);
			$statement->bindValue(':descripcion', trim($this->getDescripcion()), PDO::PARAM_STR);
			$statement->bindValue(
				':id_centro_costo',
				$this->getId_centro_costo(),
				$this->getId_centro_costo() === null ? PDO::PARAM_NULL : PDO::PARAM_INT
			);
			$statement->bindValue(':id', $this->getId(), PDO::PARAM_INT);

			// execute() devuelve true en éxito, false en error.
			return $statement->execute();

		} catch (PDOException $e) {
			// Error de base de datos
			throw new Exception("Error de base de datos al modificar puesto (ID: {$this->getId()}): " . $e->getMessage());
		}
	}

	/**
	 * Desactiva un puesto estableciendo su estado a 0.
	 *
	 * @param int $id       ID del puesto a desactivar.
	 * @param PDO $conexion Conexión PDO.
	 *
	 * @return bool True si la desactivación fue exitosa, False en caso contrario.
	 * @throws Exception Si ocurre un error de base de datos.
	 */
	public static function desactivar(int $id, PDO $conexion): bool
	{
		try {
			// Consulta para actualizar el estado a 0 (inactivo)
			$query = <<<SQL
            UPDATE puestos SET
            	estado = 0
            WHERE
            	id = :id
            SQL;

			$statement = $conexion->prepare($query);
			$statement->bindValue(':id', $id, PDO::PARAM_INT);

			// execute() devuelve true en éxito, false en error.
			return $statement->execute();

		} catch (PDOException $e) {
			// Error de base de datos
			throw new Exception("Error de base de datos al desactivar puesto (ID: $id): " . $e->getMessage());
		}
	}

	// --- Getters y Setters ---

	public function getId(): ?int
	{
		return $this->id;
	}

	public function setId(?int $id): self
	{
		$this->id = $id;
		return $this;
	}

	public function getDescripcion(): ?string
	{
		return $this->descripcion;
	}

	public function setDescripcion(?string $descripcion): self
	{
		$this->descripcion = $descripcion;
		return $this;
	}

	public function getEstado(): ?int
	{
		return $this->estado;
	}

	public function setEstado(?int $estado): self
	{
		$this->estado = $estado;
		return $this;
	}

	public function getId_centro_costo(): ?int
	{
		return $this->id_centro_costo;
	}

	public function setId_centro_costo(?int $id_centro_costo): self
	{
		$this->id_centro_costo = $id_centro_costo;
		return $this;
	}

	public function getNombre_centro_costo(): ?string
	{
		return $this->nombre_centro_costo;
	}

	public function setNombre_centro_costo(?string $nombre_centro_costo): self
	{
		$this->nombre_centro_costo = $nombre_centro_costo;
		return $this;
	}

	// --- Métodos adicionales ---

	/**
	 * Verifica si el puesto está activo.
	 * @return bool
	 */
	public function isActivo(): bool
	{
		// Asegúrate de que el estado no sea null antes de comparar
		return $this->estado === 1;
	}
}
