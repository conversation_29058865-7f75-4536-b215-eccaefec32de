<?php

declare(strict_types=1);

namespace App\classes;

use Exception;
use PDO;
use PDOException;

class MetodoPago
{
	// --- Atributos ---
	private ?int    $id                    = null;
	private ?string $descripcion           = null;
	private ?int    $es_metodo_electronico = null;
	private ?int    $estado                = null;

	/**
	 * Constructor: Inicializa las propiedades del objeto MetodoPago.
	 */
	public function __construct()
	{
		$this->id                    = 0;
		$this->descripcion           = null;
		$this->es_metodo_electronico = 0;     // Default to false (0)
		$this->estado                = 1;     // Estado activo por defecto
	}

	/**
	 * Método estático para construir un objeto MetodoPago desde un array (ej. fila de DB).
	 *
	 * @param array $resultado Array asociativo con los datos del método de pago.
	 *
	 * @return self Instancia de MetodoPago.
	 * @throws Exception Si ocurre un error durante la construcción.
	 */
	public static function construct(array $resultado = []): self
	{
		try {
			$objeto                        = new self();
			$objeto->id                    = isset($resultado['id']) ? (int)$resultado['id'] : 0;
			$objeto->descripcion           = $resultado['descripcion'] ?? null;
			$objeto->es_metodo_electronico = isset($resultado['es_metodo_electronico']) ? (int)$resultado['es_metodo_electronico'] : 0;
			$objeto->estado                = isset($resultado['estado']) ? (int)$resultado['estado'] : 1;
			return $objeto;
		} catch (Exception $e) {
			// Considera loggear el error aquí
			throw new Exception("Error al construir MetodoPago: " . $e->getMessage());
		}
	}

	// --- Métodos de Acceso a Datos (Estáticos) ---

	/**
	 * Obtiene un método de pago por su ID.
	 *
	 * @param int $id       ID del método de pago.
	 * @param PDO $conexion Conexión PDO.
	 *
	 * @return self|null Objeto MetodoPago o null si no se encuentra.
	 * @throws Exception Si hay error en DB.
	 */
	public static function get(int $id, PDO $conexion): ?self
	{
		try {
			// Consulta para obtener método de pago por ID (Usando Heredoc)
			$query = <<<SQL
            SELECT
            	*
            FROM metodos_pagos
            WHERE
            	id = :id
            LIMIT 1
            SQL;

			$statement = $conexion->prepare($query);
			$statement->bindValue(":id", $id, PDO::PARAM_INT);
			$statement->execute();
			$resultado = $statement->fetch(PDO::FETCH_ASSOC);

			return $resultado ? self::construct($resultado) : null;

		} catch (PDOException $e) {
			throw new Exception("Error al obtener MetodoPago (ID: $id): " . $e->getMessage());
		}
	}

	/**
	 * Obtiene una lista de métodos de pago activos.
	 *
	 * @param PDO $conexion Conexión PDO.
	 *
	 * @return array Array de objetos MetodoPago.
	 * @throws Exception Si hay error en DB.
	 */
	public static function get_list(PDO $conexion): array
	{
		try {
			// Consulta para obtener lista de métodos de pago activos (Usando Heredoc)
			$query = <<<SQL
            SELECT
            	*
            FROM metodos_pagos
            WHERE
            	estado = 1
            ORDER BY
            	descripcion
            SQL;

			$statement = $conexion->prepare($query);
			$statement->execute();
			$resultados = $statement->fetchAll(PDO::FETCH_ASSOC);

			$listado = [];
			foreach ($resultados as $resultado) {
				$listado[] = self::construct($resultado);
			}
			return $listado;

		} catch (PDOException $e) {
			throw new Exception("Error al obtener lista de Métodos de Pago: " . $e->getMessage());
		}
	}

	/**
	 * Crea un nuevo método de pago en la base de datos a partir de un objeto MetodoPago.
	 *
	 * @param PDO $conexion Conexión PDO.
	 *
	 * @return int|false El ID del nuevo método de pago creado o false en caso de error.
	 * @throws Exception Si los datos requeridos en el objeto están vacíos o hay error en DB.
	 */
	function crear(PDO $conexion): int|false
	{
		// Validaciones básicas sobre el objeto
		if (empty($this->getDescripcion())) {
			throw new Exception("La descripción es requerida para crear un método de pago.");
		}

		try {
			// Preparar la consulta INSERT usando Heredoc
			$query = <<<SQL
            INSERT INTO metodos_pagos (
            	 descripcion,
            	 es_metodo_electronico
            ) VALUES (
            	 :descripcion,
            	 :es_metodo_electronico
            )
            SQL;

			$statement = $conexion->prepare($query);
			// Bind de parámetros desde el objeto
			$statement->bindValue(':descripcion', $this->getDescripcion(), PDO::PARAM_STR);
			$statement->bindValue(':es_metodo_electronico', $this->getEs_metodo_electronico(), PDO::PARAM_INT);

			// Ejecutar la consulta
			$success = $statement->execute();

			if ($success) {
				// Devolver el ID del método de pago recién creado
				return (int)$conexion->lastInsertId();
			} else {
				return false; // Error en la ejecución
			}

		} catch (PDOException $e) {
			throw new Exception("Error de base de datos al crear método de pago: " . $e->getMessage());
		} catch (Exception $e) {
			// Capturar otros errores
			throw new Exception("Error al crear método de pago: " . $e->getMessage());
		}
	}

	/**
	 * Modifica un método de pago existente.
	 *
	 * @param PDO $conexion Conexión PDO.
	 *
	 * @return bool True si la modificación fue exitosa, False en caso contrario.
	 * @throws Exception Si la descripción está vacía o si ocurre un error de base de datos.
	 */
	function modificar(PDO $conexion): bool
	{
		if (empty(trim($this->getDescripcion()))) {
			throw new Exception("La descripción no puede estar vacía.");
		}

		if ($this->getId() <= 0) {
			throw new Exception("El ID del método de pago debe ser un valor válido mayor que cero.");
		}

		try {
			// Consulta para actualizar la descripción
			$query = <<<SQL
            UPDATE metodos_pagos SET
                descripcion = :descripcion,
                es_metodo_electronico = :es_metodo_electronico
            WHERE
                id = :id
            SQL;

			$statement = $conexion->prepare($query);
			$statement->bindValue(':descripcion', trim($this->getDescripcion()), PDO::PARAM_STR);
			$statement->bindValue(':es_metodo_electronico', $this->getEs_metodo_electronico(), PDO::PARAM_INT);
			$statement->bindValue(':id', $this->getId(), PDO::PARAM_INT);

			// execute() devuelve true en éxito, false en error.
			return $statement->execute();

		} catch (PDOException $e) {
			// Error de base de datos
			throw new Exception("Error de base de datos al modificar método de pago (ID: {$this->getId()}): " . $e->getMessage());
		}
	}

	/**
	 * Desactiva un método de pago estableciendo su estado a 0.
	 *
	 * @param int $id       ID del método de pago a desactivar.
	 * @param PDO $conexion Conexión PDO.
	 *
	 * @return bool True si la desactivación fue exitosa, False en caso contrario.
	 * @throws Exception Si ocurre un error de base de datos.
	 */
	public static function desactivar(int $id, PDO $conexion): bool
	{
		try {
			// Consulta para actualizar el estado a 0 (inactivo)
			$query = <<<SQL
            UPDATE metodos_pagos SET
            	estado = 0
            WHERE
            	id = :id
            SQL;

			$statement = $conexion->prepare($query);
			$statement->bindValue(':id', $id, PDO::PARAM_INT);

			// execute() devuelve true en éxito, false en error.
			return $statement->execute();

		} catch (PDOException $e) {
			// Error de base de datos
			throw new Exception("Error de base de datos al desactivar método de pago (ID: $id): " . $e->getMessage());
		}
	}

	// --- Getters y Setters ---

	public function getId(): ?int
	{
		return $this->id;
	}

	public function setId(?int $id): self
	{
		$this->id = $id;
		return $this;
	}

	public function getDescripcion(): ?string
	{
		return $this->descripcion;
	}

	public function setDescripcion(?string $descripcion): self
	{
		$this->descripcion = $descripcion;
		return $this;
	}

	public function getEs_metodo_electronico(): ?int
	{
		return $this->es_metodo_electronico;
	}

	public function setEs_metodo_electronico(?int $es_metodo_electronico): self
	{
		$this->es_metodo_electronico = ($es_metodo_electronico === 1) ? 1 : 0;
		return $this;
	}


	public function getEstado(): ?int
	{
		return $this->estado;
	}

	public function setEstado(?int $estado): self
	{
		$this->estado = $estado;
		return $this;
	}

	/**
	 * Verifica si el método de pago está activo.
	 * @return bool
	 */
	public function isActivo(): bool
	{
		// Asegúrate de que el estado no sea null antes de comparar
		return $this->estado === 1;
	}

	/**
	 * Verifica si el método de pago es electrónico.
	 * @return bool
	 */
	public function isMetodoElectronico(): bool
	{
		return $this->es_metodo_electronico === 1;
	}
}
