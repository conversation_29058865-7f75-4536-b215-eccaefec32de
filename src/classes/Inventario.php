<?php

declare(strict_types=1);

namespace App\classes;

use Exception;
use PDO;
use PDOException;

class Inventario
{
	// --- Atributos ---
	private ?int    $id                   = null;
	private ?int    $id_centro_costo      = null;
	private ?int    $id_producto          = null;
	private ?int    $cantidad             = null;
	private ?string $nombre_centro_costo  = null;  // Nombre del centro de costo asociado
	private ?string $descripcion_producto = null;  // Descripción del producto asociado
	private ?float  $valor_producto       = null;  // Valor del producto asociado
	private ?int    $estado_producto      = null;  // Estado del producto asociado

	/**
	 * Constructor: Inicializa las propiedades del objeto Inventario.
	 */
	public function __construct()
	{
		$this->id                   = 0;
		$this->id_centro_costo      = null;
		$this->id_producto          = null;
		$this->cantidad             = 0;     // Cantidad por defecto
		$this->nombre_centro_costo  = null;
		$this->descripcion_producto = null;
		$this->valor_producto       = null;
		$this->estado_producto      = null;
	}

	/**
	 * Método estático para construir un objeto Inventario desde un array (ej. fila de DB).
	 *
	 * @param array $resultado Array asociativo con los datos del inventario.
	 *
	 * @return self Instancia de Inventario.
	 * @throws Exception Si ocurre un error durante la construcción.
	 */
	public static function construct(array $resultado = []): self
	{
		try {
			$objeto                      = new self();
			$objeto->id                  = isset($resultado['id']) ? (int)$resultado['id'] : 0;
			$objeto->id_centro_costo     = isset($resultado['id_centro_costo']) ? (int)$resultado['id_centro_costo'] : null;
			$objeto->id_producto         = isset($resultado['id_producto']) ? (int)$resultado['id_producto'] : null;
			$objeto->cantidad            = isset($resultado['cantidad']) ? (int)$resultado['cantidad'] : 0;
			$objeto->nombre_centro_costo = $resultado['nombre_centro_costo'] ?? null;
			$objeto->descripcion_producto = $resultado['descripcion_producto'] ?? null;
			$objeto->valor_producto      = isset($resultado['valor_producto']) ? (float)$resultado['valor_producto'] : null;
			$objeto->estado_producto     = isset($resultado['estado_producto']) ? (int)$resultado['estado_producto'] : null;
			return $objeto;
		} catch (Exception $e) {
			// Considera loggear el error aquí
			throw new Exception("Error al construir Inventario: " . $e->getMessage());
		}
	}

	// --- Métodos de Acceso a Datos (Estáticos) ---

	/**
	 * Obtiene un registro de inventario por su ID.
	 *
	 * @param int $id       ID del inventario.
	 * @param PDO $conexion Conexión PDO.
	 *
	 * @return self|null Objeto Inventario o null si no se encuentra.
	 * @throws Exception Si hay error en DB.
	 */
	public static function get(int $id, PDO $conexion): ?self
	{
		try {
			// Consulta para obtener inventario por ID con datos relacionados (Usando Heredoc)
			$query = <<<SQL
            SELECT
            	i.*,
            	cc.nombre AS nombre_centro_costo,
            	p.descripcion AS descripcion_producto,
            	p.valor AS valor_producto
            FROM inventario i
            LEFT JOIN centros_costos cc ON i.id_centro_costo = cc.id
            LEFT JOIN productos p ON i.id_producto = p.id
            WHERE
            	i.id = :id
            LIMIT 1
            SQL;

			$statement = $conexion->prepare($query);
			$statement->bindValue(':id', $id, PDO::PARAM_INT);
			$statement->execute();
			$resultado = $statement->fetch(PDO::FETCH_ASSOC);

			return $resultado ? self::construct($resultado) : null;

		} catch (PDOException $e) {
			throw new Exception("Error al obtener Inventario (ID: $id): " . $e->getMessage());
		}
	}

	/**
	 * Obtiene una lista de registros de inventario.
	 *
	 * @param PDO $conexion Conexión PDO.
	 *
	 * @return array Array de objetos Inventario.
	 * @throws Exception Si hay error en DB.
	 */
	public static function get_list(PDO $conexion): array
	{
		try {
			// Consulta para obtener lista de inventario con datos relacionados (Usando Heredoc)
			$query = <<<SQL
            SELECT
            	i.*,
            	cc.nombre AS nombre_centro_costo,
            	p.descripcion AS descripcion_producto,
            	p.valor AS valor_producto
            FROM inventario i
            LEFT JOIN centros_costos cc ON i.id_centro_costo = cc.id
            LEFT JOIN productos p ON i.id_producto = p.id
            ORDER BY
            	cc.nombre, p.descripcion
            SQL;

			$statement = $conexion->prepare($query);
			$statement->execute();
			$resultados = $statement->fetchAll(PDO::FETCH_ASSOC);

			$listado = [];
			foreach ($resultados as $resultado) {
				$listado[] = self::construct($resultado);
			}
			return $listado;

		} catch (PDOException $e) {
			throw new Exception("Error al obtener lista de Inventario: " . $e->getMessage());
		}
	}

	/**
	 * Obtiene inventario por centro de costo.
	 *
	 * @param int $id_centro_costo ID del centro de costo.
	 * @param PDO $conexion        Conexión PDO.
	 *
	 * @return array Array de objetos Inventario.
	 * @throws Exception Si hay error en DB.
	 */
	public static function get_by_centro_costo(int $id_centro_costo, PDO $conexion): array
	{
		try {
			$query = <<<SQL
            SELECT
            	i.*,
            	cc.nombre AS nombre_centro_costo,
            	p.descripcion AS descripcion_producto,
            	p.valor AS valor_producto
            FROM inventario i
            LEFT JOIN centros_costos cc ON i.id_centro_costo = cc.id
            LEFT JOIN productos p ON i.id_producto = p.id
            WHERE
            	i.id_centro_costo = :id_centro_costo
            ORDER BY
            	p.descripcion
            SQL;

			$statement = $conexion->prepare($query);
			$statement->bindValue(':id_centro_costo', $id_centro_costo, PDO::PARAM_INT);
			$statement->execute();
			$resultados = $statement->fetchAll(PDO::FETCH_ASSOC);

			$listado = [];
			foreach ($resultados as $resultado) {
				$listado[] = self::construct($resultado);
			}
			return $listado;

		} catch (PDOException $e) {
			throw new Exception("Error al obtener inventario por centro de costo: " . $e->getMessage());
		}
	}

	/**
	 * Obtiene inventario específico por centro de costo y producto.
	 *
	 * @param int $id_centro_costo ID del centro de costo.
	 * @param int $id_producto     ID del producto.
	 * @param PDO $conexion        Conexión PDO.
	 *
	 * @return self|null Objeto Inventario o null si no se encuentra.
	 * @throws Exception Si hay error en DB.
	 */
	public static function get_by_centro_producto(int $id_centro_costo, int $id_producto, PDO $conexion): ?self
	{
		try {
			$query = <<<SQL
            SELECT
            	i.*,
            	cc.nombre AS nombre_centro_costo,
            	p.descripcion AS descripcion_producto,
            	p.valor AS valor_producto
            FROM inventario i
            LEFT JOIN centros_costos cc ON i.id_centro_costo = cc.id
            LEFT JOIN productos p ON i.id_producto = p.id
            WHERE
            	i.id_centro_costo = :id_centro_costo
            	AND i.id_producto = :id_producto
            LIMIT 1
            SQL;

			$statement = $conexion->prepare($query);
			$statement->bindValue(':id_centro_costo', $id_centro_costo, PDO::PARAM_INT);
			$statement->bindValue(':id_producto', $id_producto, PDO::PARAM_INT);
			$statement->execute();
			$resultado = $statement->fetch(PDO::FETCH_ASSOC);

			return $resultado ? self::construct($resultado) : null;

		} catch (PDOException $e) {
			throw new Exception("Error al obtener inventario por centro de costo y producto: " . $e->getMessage());
		}
	}

	/**
	 * Crea un nuevo registro de inventario en la base de datos a partir de un objeto Inventario.
	 * El objeto Inventario debe estar completamente poblado con los datos requeridos.
	 *
	 * @param PDO $conexion Conexión PDO.
	 *
	 * @return int|false El ID del nuevo inventario creado o false en caso de error.
	 * @throws Exception Si los datos requeridos en el objeto están vacíos o hay error en DB.
	 */
	function crear(PDO $conexion): int|false
	{
		// Validaciones básicas sobre el objeto
		if ($this->getId_centro_costo() === null || $this->getId_producto() === null) {
			throw new Exception("ID de centro de costo e ID de producto son requeridos para crear un inventario.");
		}

		if ($this->getCantidad() < 0) {
			throw new Exception("La cantidad no puede ser negativa.");
		}

		try {
			// Preparar la consulta INSERT usando Heredoc
			$query = <<<SQL
            INSERT INTO inventario (
            	 id_centro_costo
            	,id_producto
            	,cantidad
            ) VALUES (
            	 :id_centro_costo
            	,:id_producto
            	,:cantidad
            )
            SQL;

			$statement = $conexion->prepare($query);

			// Bind de parámetros desde el objeto
			$statement->bindValue(':id_centro_costo', $this->getId_centro_costo(), PDO::PARAM_INT);
			$statement->bindValue(':id_producto', $this->getId_producto(), PDO::PARAM_INT);
			$statement->bindValue(':cantidad', $this->getCantidad(), PDO::PARAM_INT);

			// Ejecutar la consulta
			$success = $statement->execute();

			if ($success) {
				// Devolver el ID del inventario recién creado
				return (int)$conexion->lastInsertId();
			} else {
				return false; // Error en la ejecución
			}

		} catch (PDOException $e) {
			// Manejar errores específicos de DB (ej. violación de UNIQUE constraint)
			if ($e->getCode() == 23000 || $e->getCode() == 1062) {
				throw new Exception("Error al crear inventario: Ya existe un registro para este centro de costo y producto.");
			} else {
				throw new Exception("Error de base de datos al crear inventario: " . $e->getMessage());
			}
		} catch (Exception $e) {
			// Capturar otros errores
			throw new Exception("Error al crear inventario: " . $e->getMessage());
		}
	}

	/**
	 * Modifica un registro de inventario existente.
	 *
	 * @param PDO $conexion Conexión PDO.
	 *
	 * @return bool True si la modificación fue exitosa, False en caso contrario.
	 * @throws Exception Si los datos son inválidos o si ocurre un error de base de datos.
	 */
	function modificar(PDO $conexion): bool
	{
		if ($this->getId_centro_costo() === null || $this->getId_producto() === null) {
			throw new Exception("ID de centro de costo e ID de producto son requeridos.");
		}

		if ($this->getCantidad() < 0) {
			throw new Exception("La cantidad no puede ser negativa.");
		}

		if ($this->getId() <= 0) {
			throw new Exception("ID de inventario inválido para modificar.");
		}

		try {
			// Consulta para actualizar el inventario
			$query = <<<SQL
            UPDATE inventario SET
                id_centro_costo = :id_centro_costo,
                id_producto = :id_producto,
                cantidad = :cantidad
            WHERE
                id = :id
            SQL;

			$statement = $conexion->prepare($query);
			$statement->bindValue(':id_centro_costo', $this->getId_centro_costo(), PDO::PARAM_INT);
			$statement->bindValue(':id_producto', $this->getId_producto(), PDO::PARAM_INT);
			$statement->bindValue(':cantidad', $this->getCantidad(), PDO::PARAM_INT);
			$statement->bindValue(':id', $this->getId(), PDO::PARAM_INT);

			return $statement->execute();

		} catch (PDOException $e) {
			// Manejar errores específicos de DB (ej. violación de UNIQUE constraint)
			if ($e->getCode() == 23000 || $e->getCode() == 1062) {
				throw new Exception("Error al modificar inventario: Ya existe un registro para este centro de costo y producto.");
			} else {
				throw new Exception("Error de base de datos al modificar inventario (ID: " . $this->getId() . "): " . $e->getMessage());
			}
		}
	}

	/**
	 * Actualiza la cantidad de un registro de inventario específico.
	 *
	 * @param int $id       ID del inventario.
	 * @param int $cantidad Nueva cantidad.
	 * @param PDO $conexion Conexión PDO.
	 *
	 * @return bool True si la actualización fue exitosa, False en caso contrario.
	 * @throws Exception Si la cantidad es negativa o si ocurre un error de base de datos.
	 */
	public static function actualizar_cantidad(int $id, int $cantidad, PDO $conexion): bool
	{
		if ($cantidad < 0) {
			throw new Exception("La cantidad no puede ser negativa.");
		}

		try {
			// Consulta para actualizar solo la cantidad
			$query = <<<SQL
            UPDATE inventario SET
                cantidad = :cantidad
            WHERE
                id = :id
            SQL;

			$statement = $conexion->prepare($query);
			$statement->bindValue(':cantidad', $cantidad, PDO::PARAM_INT);
			$statement->bindValue(':id', $id, PDO::PARAM_INT);

			return $statement->execute();

		} catch (PDOException $e) {
			throw new Exception("Error de base de datos al actualizar cantidad de inventario (ID: $id): " . $e->getMessage());
		}
	}

	/**
	 * Elimina un registro de inventario.
	 *
	 * @param int $id       ID del inventario a eliminar.
	 * @param PDO $conexion Conexión PDO.
	 *
	 * @return bool True si la eliminación fue exitosa, False en caso contrario.
	 * @throws Exception Si ocurre un error de base de datos.
	 */
	public static function eliminar(int $id, PDO $conexion): bool
	{
		try {
			// Consulta para eliminar el registro de inventario
			$query = <<<SQL
            DELETE FROM inventario
            WHERE
                id = :id
            SQL;

			$statement = $conexion->prepare($query);
			$statement->bindValue(':id', $id, PDO::PARAM_INT);

			return $statement->execute();

		} catch (PDOException $e) {
			throw new Exception("Error de base de datos al eliminar inventario (ID: $id): " . $e->getMessage());
		}
	}

	// --- Getters y Setters ---

	public function getId(): ?int
	{
		return $this->id;
	}

	public function setId(?int $id): self
	{
		$this->id = $id;
		return $this;
	}

	public function getId_centro_costo(): ?int
	{
		return $this->id_centro_costo;
	}

	public function setId_centro_costo(?int $id_centro_costo): self
	{
		$this->id_centro_costo = $id_centro_costo;
		return $this;
	}

	public function getId_producto(): ?int
	{
		return $this->id_producto;
	}

	public function setId_producto(?int $id_producto): self
	{
		$this->id_producto = $id_producto;
		return $this;
	}

	public function getCantidad(): ?int
	{
		return $this->cantidad;
	}

	public function setCantidad(?int $cantidad): self
	{
		$this->cantidad = $cantidad;
		return $this;
	}

	public function getNombre_centro_costo(): ?string
	{
		return $this->nombre_centro_costo;
	}

	public function setNombre_centro_costo(?string $nombre_centro_costo): self
	{
		$this->nombre_centro_costo = $nombre_centro_costo;
		return $this;
	}

	public function getDescripcion_producto(): ?string
	{
		return $this->descripcion_producto;
	}

	public function setDescripcion_producto(?string $descripcion_producto): self
	{
		$this->descripcion_producto = $descripcion_producto;
		return $this;
	}

	public function getValor_producto(): ?float
	{
		return $this->valor_producto;
	}

	public function setValor_producto(?float $valor_producto): self
	{
		$this->valor_producto = $valor_producto;
		return $this;
	}

	public function getEstado_producto(): ?int
	{
		return $this->estado_producto;
	}

	public function setEstado_producto(?int $estado_producto): self
	{
		$this->estado_producto = $estado_producto;
		return $this;
	}

	// --- Métodos adicionales ---

	/**
	 * Verifica si hay stock disponible (cantidad > 0).
	 * @return bool
	 */
	public function tieneStock(): bool
	{
		return $this->cantidad > 0;
	}

	/**
	 * Calcula el valor total del inventario (cantidad * valor del producto).
	 * @return float
	 */
	public function getValorTotal(): float
	{
		if ($this->valor_producto === null || $this->cantidad === null) {
			return 0.0;
		}
		return $this->cantidad * $this->valor_producto;
	}

	/**
	 * Obtiene la cantidad formateada con separadores de miles.
	 * @return string
	 */
	public function getCantidadFormateada(): string
	{
		if ($this->cantidad === null) {
			return '0';
		}
		return number_format($this->cantidad, 0, ',', '.');
	}

	/**
	 * Obtiene inventario por centro de costo con filtro opcional de descripción de producto.
	 *
	 * @param PDO $conexion Conexión PDO.
	 * @param int $id_centro_costo ID del centro de costo.
	 * @param string $descripcion_producto Filtro opcional por descripción de producto.
	 *
	 * @return array Array de objetos Inventario.
	 * @throws Exception Si hay error en DB.
	 */
	public static function obtenerPorCentroCostoConFiltro(PDO $conexion, int $id_centro_costo, string $descripcion_producto = ''): array
	{
		try {
			$where_conditions = ['i.id_centro_costo = :id_centro_costo'];
			$params = [':id_centro_costo' => $id_centro_costo];

			// Add product description filter if provided
			if (!empty(trim($descripcion_producto))) {
				$where_conditions[] = 'p.descripcion LIKE :descripcion_producto';
				$params[':descripcion_producto'] = '%' . trim($descripcion_producto) . '%';
			}

			// Only show active products (exclude inactive products with estado = 0)
			$where_conditions[] = 'p.estado = 1';

			$where_clause = implode(' AND ', $where_conditions);

			$query = <<<SQL
            SELECT
            	i.*,
            	cc.nombre AS nombre_centro_costo,
            	p.descripcion AS descripcion_producto,
            	p.valor AS valor_producto,
            	p.estado AS estado_producto
            FROM inventario i
            LEFT JOIN centros_costos cc ON i.id_centro_costo = cc.id
            LEFT JOIN productos p ON i.id_producto = p.id
            WHERE {$where_clause}
            ORDER BY p.descripcion
            SQL;

			$statement = $conexion->prepare($query);

			foreach ($params as $param => $value) {
				$statement->bindValue($param, $value);
			}

			$statement->execute();
			$resultados = $statement->fetchAll(PDO::FETCH_ASSOC);

			$listado = [];
			foreach ($resultados as $resultado) {
				$listado[] = self::construct($resultado);
			}
			return $listado;

		} catch (PDOException $e) {
			throw new Exception("Error al obtener inventario por centro de costo con filtro: " . $e->getMessage());
		}
	}

	/**
	 * Obtiene inventario por ID de producto.
	 *
	 * @param int $id_producto ID del producto.
	 * @param PDO $conexion Conexión PDO.
	 *
	 * @return array Array de objetos Inventario.
	 * @throws Exception Si hay error en DB.
	 */
	public static function obtenerPorProducto(int $id_producto, PDO $conexion): array
	{
		try {
			$query = <<<SQL
            SELECT
            	i.*,
            	cc.nombre AS nombre_centro_costo,
            	p.descripcion AS descripcion_producto,
            	p.valor AS valor_producto
            FROM inventario i
            LEFT JOIN centros_costos cc ON i.id_centro_costo = cc.id
            LEFT JOIN productos p ON i.id_producto = p.id
            WHERE i.id_producto = :id_producto
            ORDER BY cc.nombre
            SQL;

			$statement = $conexion->prepare($query);
			$statement->bindValue(':id_producto', $id_producto, PDO::PARAM_INT);
			$statement->execute();
			$resultados = $statement->fetchAll(PDO::FETCH_ASSOC);

			$listado = [];
			foreach ($resultados as $resultado) {
				$listado[] = self::construct($resultado);
			}
			return $listado;

		} catch (PDOException $e) {
			throw new Exception("Error al obtener inventario por producto (ID: $id_producto): " . $e->getMessage());
		}
	}

	/**
	 * Obtiene inventario agrupado por producto con cantidades por centro de costo.
	 *
	 * @param PDO $conexion Conexión PDO.
	 *
	 * @return array Array con información de inventario agrupada por producto.
	 * @throws Exception Si hay error en DB.
	 */
	public static function obtenerInventarioAgrupadoPorProducto(PDO $conexion): array
	{
		try {
			$query = <<<SQL
            SELECT
            	p.id as producto_id,
            	p.descripcion as producto_descripcion,
            	cc.id as centro_costo_id,
            	cc.nombre as centro_costo_nombre,
            	COALESCE(i.cantidad, 0) as cantidad
            FROM productos p
            CROSS JOIN centros_costos cc
            LEFT JOIN inventario i ON p.id = i.id_producto AND cc.id = i.id_centro_costo
            WHERE p.estado = 1 AND cc.estado = 1
            ORDER BY p.descripcion, cc.nombre
            SQL;

			$statement = $conexion->prepare($query);
			$statement->execute();
			$resultados = $statement->fetchAll(PDO::FETCH_ASSOC);

			// Group results by product
			$productos_inventario = [];
			foreach ($resultados as $resultado) {
				$producto_id = $resultado['producto_id'];

				if (!isset($productos_inventario[$producto_id])) {
					$productos_inventario[$producto_id] = [
						'id' => $producto_id,
						'descripcion' => $resultado['producto_descripcion'],
						'centros_costo' => [],
						'tiene_inventario' => false
					];
				}

				$cantidad = (int)$resultado['cantidad'];
				$productos_inventario[$producto_id]['centros_costo'][] = [
					'id' => $resultado['centro_costo_id'],
					'nombre' => $resultado['centro_costo_nombre'],
					'cantidad' => $cantidad
				];

				if ($cantidad > 0) {
					$productos_inventario[$producto_id]['tiene_inventario'] = true;
				}
			}

			return array_values($productos_inventario);

		} catch (PDOException $e) {
			throw new Exception("Error al obtener inventario agrupado por producto: " . $e->getMessage());
		}
	}
}
