<?php

declare(strict_types=1);

namespace App\classes;

use Exception;
use PDO;
use PDOException;

class Servicio
{
	// --- Atributos ---
	private ?int    $id                  = null;
	private ?string $descripcion         = null;
	private ?float  $valor               = null;
	private ?int    $duracion            = null; // Duración del servicio en minutos
	private ?int    $estado              = null;
	private ?int    $id_empleado_servicio = null; // ID de la relación en la tabla empleados_servicios

	/**
	 * Constructor: Inicializa las propiedades del objeto Servicio.
	 */
	public function __construct()
	{
		$this->id          = 0;
		$this->descripcion = null;
		$this->valor       = null;
		$this->duracion    = null;
		$this->estado      = 1; // Estado activo por defecto
	}

	/**
	 * Método estático para construir un objeto Servicio desde un array (ej. fila de DB).
	 *
	 * @param array $resultado Array asociativo con los datos del servicio.
	 *
	 * @return self Instancia de Servicio.
	 * @throws Exception Si ocurre un error durante la construcción.
	 */
	public static function construct(array $resultado = []): self
	{
		try {
			$objeto                      = new self();
			$objeto->id                  = isset($resultado['id']) ? (int)$resultado['id'] : 0;
			$objeto->descripcion         = $resultado['descripcion'] ?? null;
			$objeto->valor               = isset($resultado['valor']) ? (float)$resultado['valor'] : null;
			$objeto->duracion            = isset($resultado['duracion']) ? (int)$resultado['duracion'] : null;
			$objeto->estado              = isset($resultado['estado']) ? (int)$resultado['estado'] : 1;
			$objeto->id_empleado_servicio = isset($resultado['id_empleado_servicio']) ? (int)$resultado['id_empleado_servicio'] : null;
			return $objeto;
		} catch (Exception $e) {
			// Considera loggear el error aquí
			throw new Exception("Error al construir Servicio: " . $e->getMessage());
		}
	}

	// --- Métodos de Acceso a Datos ---

	/**
	 * Obtiene un servicio por su ID.
	 *
	 * @param int $id       ID del servicio.
	 * @param PDO $conexion Conexión PDO.
	 *
	 * @return self|null Objeto Servicio o null si no se encuentra.
	 * @throws Exception Si hay error en DB.
	 */
	public static function get(int $id, PDO $conexion): ?self
	{
		try {
			// Consulta para obtener servicio por ID (Usando Heredoc)
			$query = <<<SQL
            SELECT
            	*
            FROM servicios
            WHERE
            	id = :id
            LIMIT 1
            SQL;

			$statement = $conexion->prepare($query);
			$statement->bindValue(":id", $id, PDO::PARAM_INT);
			$statement->execute();
			$resultado = $statement->fetch(PDO::FETCH_ASSOC);

			return $resultado ? self::construct($resultado) : null;

		} catch (PDOException $e) {
			throw new Exception("Error al obtener Servicio (ID: $id): " . $e->getMessage());
		}
	}

	/**
	 * Obtiene una lista de servicios activos.
	 *
	 * @param PDO $conexion Conexión PDO.
	 *
	 * @return array Array de objetos Servicio.
	 * @throws Exception Si hay error en DB.
	 */
	public static function get_list(PDO $conexion): array
	{
		try {
			// Consulta para obtener lista de servicios activos (Usando Heredoc)
			$query = <<<SQL
            SELECT
            	*
            FROM servicios
            WHERE
            	estado = 1
            ORDER BY
            	descripcion
            SQL;

			$statement = $conexion->prepare($query);
			$statement->execute();
			$resultados = $statement->fetchAll(PDO::FETCH_ASSOC);

			$listado = [];
			foreach ($resultados as $resultado) {
				$listado[] = self::construct($resultado);
			}
			return $listado;

		} catch (PDOException $e) {
			throw new Exception("Error al obtener lista de Servicios: " . $e->getMessage());
		}
	}

	/**
	 * Crea un nuevo servicio en la base de datos a partir de un objeto Servicio.
	 * El objeto Servicio debe estar completamente poblado con los datos requeridos.
	 *
	 * @param PDO $conexion Conexión PDO.
	 *
	 * @return int|false El ID del nuevo servicio creado o false en caso de error.
	 * @throws Exception Si los datos requeridos en el objeto están vacíos o hay error en DB.
	 */
	function crear(PDO $conexion): int|false
	{
		// Validaciones básicas sobre el objeto
		if (empty($this->getDescripcion()) || $this->getValor() === null) {
			throw new Exception("Descripción y valor son requeridos para crear un servicio.");
		}

		if ($this->getDuracion() !== null && $this->getDuracion() <= 0) {
			throw new Exception("La duración debe ser un número positivo de minutos.");
		}

		try {
			// Preparar la consulta INSERT usando Heredoc
			$query = <<<SQL
            INSERT INTO servicios (
            	 descripcion
            	,valor
            	,duracion
            ) VALUES (
            	 :descripcion
            	,:valor
            	,:duracion
            )
            SQL;

			$statement = $conexion->prepare($query);

			// Bind de parámetros desde el objeto
			$statement->bindValue(':descripcion', $this->getDescripcion(), PDO::PARAM_STR);
			$statement->bindValue(':valor', $this->getValor(), PDO::PARAM_STR);
			$statement->bindValue(':duracion', $this->getDuracion(), PDO::PARAM_INT);

			// Ejecutar la consulta
			$success = $statement->execute();

			if ($success) {
				// Devolver el ID del servicio recién creado
				return (int)$conexion->lastInsertId();
			} else {
				return false; // Error en la ejecución
			}

		} catch (PDOException $e) {
			throw new Exception("Error de base de datos al crear servicio: " . $e->getMessage());
		} catch (Exception $e) {
			// Capturar otros errores
			throw new Exception("Error al crear servicio: " . $e->getMessage());
		}
	}

	/**
	 * Modifica un servicio existente.
	 *
	 * @param PDO $conexion Conexión PDO.
	 *
	 * @return bool True si la modificación fue exitosa, False en caso contrario.
	 * @throws Exception Si la descripción está vacía, el valor es nulo o si ocurre un error de base de datos.
	 */
	function modificar(PDO $conexion): bool
	{
		if (empty(trim($this->getDescripcion()))) {
			throw new Exception("La descripción no puede estar vacía.");
		}

		if ($this->getValor() === null || $this->getValor() < 0) {
			throw new Exception("El valor debe ser un número válido mayor o igual a cero.");
		}

		if ($this->getDuracion() !== null && $this->getDuracion() <= 0) {
			throw new Exception("La duración debe ser un número positivo de minutos.");
		}

		if ($this->getId() === null || $this->getId() <= 0) {
			throw new Exception("ID de servicio inválido para modificar.");
		}

		try {
			// Consulta para actualizar la descripción, el valor y la duración
			$query = <<<SQL
            UPDATE servicios SET
                descripcion = :descripcion,
                valor = :valor,
                duracion = :duracion
            WHERE
                id = :id
            SQL;

			$statement = $conexion->prepare($query);
			$statement->bindValue(':descripcion', trim($this->getDescripcion()), PDO::PARAM_STR);
			$statement->bindValue(':valor', $this->getValor(), PDO::PARAM_STR);
			$statement->bindValue(':duracion', $this->getDuracion(), PDO::PARAM_INT);
			$statement->bindValue(':id', $this->getId(), PDO::PARAM_INT);

			return $statement->execute();

		} catch (PDOException $e) {
			throw new Exception("Error de base de datos al modificar servicio (ID: {$this->getId()}): " . $e->getMessage());
		}
	}

	/**
	 * Desactiva un servicio estableciendo su estado a 0.
	 *
	 * @param int $id       ID del servicio a desactivar.
	 * @param PDO $conexion Conexión PDO.
	 *
	 * @return bool True si la desactivación fue exitosa, False en caso contrario.
	 * @throws Exception Si ocurre un error de base de datos.
	 */
	public static function desactivar(int $id, PDO $conexion): bool
	{
		try {
			// Consulta para actualizar el estado a 0 (inactivo)
			$query = <<<SQL
            UPDATE servicios SET
            	estado = 0
            WHERE
            	id = :id
            SQL;

			$statement = $conexion->prepare($query);
			$statement->bindValue(':id', $id, PDO::PARAM_INT);

			return $statement->execute();

		} catch (PDOException $e) {
			throw new Exception("Error de base de datos al desactivar servicio (ID: $id): " . $e->getMessage());
		}
	}

	// --- Getters y Setters ---

	public function getId(): ?int
	{
		return $this->id;
	}

	public function setId(?int $id): self
	{
		$this->id = $id;
		return $this;
	}

	public function getDescripcion(): ?string
	{
		return $this->descripcion;
	}

	public function setDescripcion(?string $descripcion): self
	{
		$this->descripcion = $descripcion;
		return $this;
	}

	public function getValor(): ?float
	{
		return $this->valor;
	}

	public function setValor(?float $valor): self
	{
		$this->valor = $valor;
		return $this;
	}

	public function getEstado(): ?int
	{
		return $this->estado;
	}

	public function setEstado(?int $estado): self
	{
		$this->estado = $estado;
		return $this;
	}

	/**
	 * Verifica si el servicio está activo.
	 * @return bool
	 */
	public function isActivo(): bool
	{
		return $this->estado === 1;
	}

	/**
	 * Obtiene el ID de la relación en la tabla empleados_servicios.
	 * @return int|null
	 */
	public function getId_empleado_servicio(): ?int
	{
		return $this->id_empleado_servicio;
	}

	/**
	 * Establece el ID de la relación en la tabla empleados_servicios.
	 * @param int|null $id_empleado_servicio
	 * @return self
	 */
	public function setId_empleado_servicio(?int $id_empleado_servicio): self
	{
		$this->id_empleado_servicio = $id_empleado_servicio;
		return $this;
	}

	/**
	 * Obtiene la duración del servicio en minutos.
	 * @return int|null
	 */
	public function getDuracion(): ?int
	{
		return $this->duracion;
	}

	/**
	 * Establece la duración del servicio en minutos.
	 * @param int|null $duracion
	 * @return self
	 */
	public function setDuracion(?int $duracion): self
	{
		$this->duracion = $duracion;
		return $this;
	}
}
