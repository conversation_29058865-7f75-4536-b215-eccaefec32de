<?php

declare(strict_types=1);

namespace App\classes;

use Exception;
use PDO;
use PDOException;

class ControlBaseMovimiento
{
	// --- Atributos ---
	private ?int    $id              = null;
	private ?int    $id_control_base = null;
	private ?string $tipo            = null;
	private ?string $nota            = null;
	private ?float  $valor           = null;
	private ?string $fecha           = null;

	/**
	 * Constructor: Inicializa las propiedades del objeto ControlBaseMovimiento.
	 *
	 * @param int|null    $id_control_base ID del control base (obligatorio)
	 * @param string|null $tipo            Tipo de movimiento: 'ingreso' o 'egreso' (obligatorio)
	 * @param float|null  $valor           Valor del movimiento (obligatorio, debe ser > 0)
	 * @param string|null $nota            Nota descriptiva del movimiento (opcional)
	 */
	public function __construct(?int $id_control_base = null, ?string $tipo = null, ?float $valor = null, ?string $nota = null)
	{
		// Establecer la zona horaria para Colombia
		date_default_timezone_set('America/Bogota');

		$this->id              = 0;
		$this->id_control_base = $id_control_base;
		$this->tipo            = $tipo;
		$this->nota            = $nota;
		$this->valor           = $valor;
		$this->fecha           = date('Y-m-d H:i:s'); // Timestamp actual automático
	}

	/**
	 * Método estático para construir un objeto ControlBaseMovimiento desde un array (ej. fila de DB).
	 *
	 * @param array $resultado Array asociativo con los datos del movimiento.
	 *
	 * @return self Instancia de ControlBaseMovimiento.
	 * @throws Exception Si ocurre un error durante la construcción.
	 */
	public static function construct(array $resultado = []): self
	{
		try {
			$objeto                    = new self();
			$objeto->id                = isset($resultado['id']) ? (int)$resultado['id'] : 0;
			$objeto->id_control_base   = isset($resultado['id_control_base']) ? (int)$resultado['id_control_base'] : null;
			$objeto->tipo              = $resultado['tipo'] ?? null;
			$objeto->nota              = $resultado['nota'] ?? null;
			$objeto->valor             = isset($resultado['valor']) ? (float)$resultado['valor'] : null;
			$objeto->fecha             = $resultado['fecha'] ?? null;
			return $objeto;
		} catch (Exception $e) {
			// Considera loggear el error aquí
			throw new Exception("Error al construir ControlBaseMovimiento: " . $e->getMessage());
		}
	}

	// --- Métodos de Acceso a Datos (Estáticos) ---

	/**
	 * Obtiene un movimiento por su ID.
	 *
	 * @param int $id       ID del movimiento.
	 * @param PDO $conexion Conexión PDO.
	 *
	 * @return self|null Objeto ControlBaseMovimiento o null si no se encuentra.
	 * @throws Exception Si hay error en DB.
	 */
	public static function get(int $id, PDO $conexion): ?self
	{
		try {
			// Consulta para obtener movimiento por ID (Usando Heredoc)
			$query = <<<SQL
            SELECT
            	cbm.*
            FROM control_bases_movimientos cbm
            WHERE
            	cbm.id = :id
            LIMIT 1
            SQL;

			$statement = $conexion->prepare($query);
			$statement->bindValue(":id", $id, PDO::PARAM_INT);
			$statement->execute();
			$resultado = $statement->fetch(PDO::FETCH_ASSOC);

			return $resultado ? self::construct($resultado) : null;

		} catch (PDOException $e) {
			throw new Exception("Error al obtener ControlBaseMovimiento (ID: $id): " . $e->getMessage());
		}
	}

	/**
	 * Obtiene una lista de todos los movimientos.
	 *
	 * @param PDO $conexion Conexión PDO.
	 *
	 * @return array Array de objetos ControlBaseMovimiento.
	 * @throws Exception Si hay error en DB.
	 */
	public static function get_list(PDO $conexion): array
	{
		try {
			// Consulta para obtener lista de movimientos (Usando Heredoc)
			$query = <<<SQL
            SELECT
            	cbm.*
            FROM control_bases_movimientos cbm
            ORDER BY
            	cbm.fecha DESC
            SQL;

			$statement = $conexion->prepare($query);
			$statement->execute();
			$resultados = $statement->fetchAll(PDO::FETCH_ASSOC);

			$listado = [];
			foreach ($resultados as $resultado) {
				$listado[] = self::construct($resultado);
			}
			return $listado;

		} catch (PDOException $e) {
			throw new Exception("Error al obtener lista de ControlBaseMovimiento: " . $e->getMessage());
		}
	}

	/**
	 * Obtiene movimientos por ID de control base.
	 *
	 * @param int $id_control_base ID del control base.
	 * @param PDO $conexion        Conexión PDO.
	 *
	 * @return array Array de objetos ControlBaseMovimiento.
	 * @throws Exception Si hay error en DB.
	 */
	public static function obtenerPorControlBase(int $id_control_base, PDO $conexion): array
	{
		try {
			// Consulta para obtener movimientos por ID de control base (Usando Heredoc)
			$query = <<<SQL
            SELECT
            	cbm.*
            FROM control_bases_movimientos cbm
            WHERE
            	cbm.id_control_base = :id_control_base
            ORDER BY
            	cbm.fecha DESC
            SQL;

			$statement = $conexion->prepare($query);
			$statement->bindValue(":id_control_base", $id_control_base, PDO::PARAM_INT);
			$statement->execute();
			$resultados = $statement->fetchAll(PDO::FETCH_ASSOC);

			$listado = [];
			foreach ($resultados as $resultado) {
				$listado[] = self::construct($resultado);
			}
			return $listado;

		} catch (PDOException $e) {
			throw new Exception("Error al obtener ControlBaseMovimiento por control base (ID: $id_control_base): " . $e->getMessage());
		}
	}

	/**
	 * Obtiene movimientos por tipo ('ingreso' o 'egreso').
	 *
	 * @param string $tipo     Tipo de movimiento ('ingreso' o 'egreso').
	 * @param PDO    $conexion Conexión PDO.
	 *
	 * @return array Array de objetos ControlBaseMovimiento.
	 * @throws Exception Si hay error en DB.
	 */
	public static function obtenerPorTipo(string $tipo, PDO $conexion): array
	{
		try {
			// Consulta para obtener movimientos por tipo (Usando Heredoc)
			$query = <<<SQL
            SELECT
            	cbm.*
            FROM control_bases_movimientos cbm
            WHERE
            	cbm.tipo = :tipo
            ORDER BY
            	cbm.fecha DESC
            SQL;

			$statement = $conexion->prepare($query);
			$statement->bindValue(":tipo", $tipo, PDO::PARAM_STR);
			$statement->execute();
			$resultados = $statement->fetchAll(PDO::FETCH_ASSOC);

			$listado = [];
			foreach ($resultados as $resultado) {
				$listado[] = self::construct($resultado);
			}
			return $listado;

		} catch (PDOException $e) {
			throw new Exception("Error al obtener ControlBaseMovimiento por tipo ($tipo): " . $e->getMessage());
		}
	}

	/**
	 * Crea un nuevo movimiento en la base de datos a partir de un objeto ControlBaseMovimiento.
	 * El objeto ControlBaseMovimiento debe estar completamente poblado.
	 *
	 * @param PDO $conexion Conexión PDO.
	 *
	 * @return int|false El ID del nuevo movimiento creado o false en caso de error.
	 * @throws Exception Si los datos requeridos en el objeto están vacíos o hay error en DB.
	 */
	function crear(PDO $conexion): int|false
	{
		// Validaciones básicas sobre el objeto
		if ($this->getId_control_base() === null || empty($this->getTipo()) || $this->getValor() === null) {
			throw new Exception("ID control base, tipo y valor son requeridos en el objeto ControlBaseMovimiento para crearlo.");
		}

		// Validar tipo enum
		if (!$this->validarTipo($this->getTipo())) {
			throw new Exception("El tipo debe ser 'ingreso' o 'egreso'.");
		}

		// Validar valor positivo
		if (!$this->validarValorPositivo($this->getValor())) {
			throw new Exception("El valor debe ser un número positivo mayor que cero.");
		}

		// Validar que el control base existe
		if (!$this->validarControlBaseExiste($this->getId_control_base(), $conexion)) {
			throw new Exception("El control base especificado no existe.");
		}

		try {
			// Establecer la zona horaria para Colombia
			date_default_timezone_set('America/Bogota');

			// Si fecha no está establecida, usar timestamp actual
			if (empty($this->getFecha())) {
				$this->setFecha(date('Y-m-d H:i:s'));
			}

			return $this->_insert($conexion);

		} catch (Exception $e) {
			// Capturar otros errores
			throw new Exception("Error al crear movimiento: " . $e->getMessage());
		}
	}

	/**
	 * Actualiza un movimiento existente.
	 *
	 * @param PDO $conexion Conexión PDO.
	 *
	 * @return bool True si la actualización fue exitosa, false en caso contrario.
	 * @throws Exception Si hay error en DB.
	 */
	function modificar(PDO $conexion): bool
	{
		// Validar que el ID exista
		if (empty($this->getId())) {
			throw new Exception("ID es requerido para actualizar un ControlBaseMovimiento.");
		}

		// Validaciones básicas sobre el objeto
		if ($this->getId_control_base() === null || empty($this->getTipo()) || $this->getValor() === null) {
			throw new Exception("ID control base, tipo y valor son requeridos en el objeto ControlBaseMovimiento para modificarlo.");
		}

		// Validar tipo enum
		if (!$this->validarTipo($this->getTipo())) {
			throw new Exception("El tipo debe ser 'ingreso' o 'egreso'.");
		}

		// Validar valor positivo
		if (!$this->validarValorPositivo($this->getValor())) {
			throw new Exception("El valor debe ser un número positivo mayor que cero.");
		}

		// Validar que el control base existe
		if (!$this->validarControlBaseExiste($this->getId_control_base(), $conexion)) {
			throw new Exception("El control base especificado no existe.");
		}

		try {
			// Establecer la zona horaria para Colombia
			date_default_timezone_set('America/Bogota');

			return $this->_update($conexion);

		} catch (Exception $e) {
			throw new Exception("Error al modificar movimiento: " . $e->getMessage());
		}
	}

	/**
	 * Método privado para insertar un nuevo movimiento en la base de datos.
	 *
	 * @param PDO $conexion Conexión PDO.
	 *
	 * @return int|false El ID del nuevo movimiento creado o false en caso de error.
	 * @throws Exception Si hay error en DB.
	 */
	private function _insert(PDO $conexion): int|false
	{
		try {
			// Preparar la consulta INSERT usando Heredoc
			$query = <<<SQL
            INSERT INTO control_bases_movimientos (
            	 id_control_base
            	,tipo
            	,nota
            	,valor
            	,fecha
            ) VALUES (
            	 :id_control_base
            	,:tipo
            	,:nota
            	,:valor
            	,:fecha
            )
            SQL;

			$statement = $conexion->prepare($query);

			// Bind de parámetros desde el objeto
			$statement->bindValue(':id_control_base', $this->getId_control_base(), PDO::PARAM_INT);
			$statement->bindValue(':tipo', $this->getTipo(), PDO::PARAM_STR);
			$statement->bindValue(':nota', $this->getNota(), PDO::PARAM_STR);
			$statement->bindValue(':valor', $this->getValor(), PDO::PARAM_STR);
			$statement->bindValue(':fecha', $this->getFecha(), PDO::PARAM_STR);

			// Ejecutar la consulta
			$success = $statement->execute();

			if ($success) {
				// Devolver el ID del movimiento recién creado
				return (int)$conexion->lastInsertId();
			} else {
				return false; // Error en la ejecución
			}

		} catch (PDOException $e) {
			// Manejar errores específicos de DB
			throw new Exception("Error de base de datos al insertar movimiento: " . $e->getMessage());
		} catch (Exception $e) {
			// Capturar otros errores
			throw new Exception("Error al insertar movimiento: " . $e->getMessage());
		}
	}

	/**
	 * Método privado para actualizar un movimiento existente en la base de datos.
	 *
	 * @param PDO $conexion Conexión PDO.
	 *
	 * @return bool True si la actualización fue exitosa, False en caso contrario.
	 * @throws Exception Si hay error en DB.
	 */
	private function _update(PDO $conexion): bool
	{
		try {
			// Consulta para actualizar el movimiento
			$query = <<<SQL
            UPDATE control_bases_movimientos SET
                id_control_base = :id_control_base,
                tipo = :tipo,
                nota = :nota,
                valor = :valor,
                fecha = :fecha
            WHERE
                id = :id
            SQL;

			$statement = $conexion->prepare($query);
			$statement->bindValue(':id_control_base', $this->getId_control_base(), PDO::PARAM_INT);
			$statement->bindValue(':tipo', $this->getTipo(), PDO::PARAM_STR);
			$statement->bindValue(':nota', $this->getNota(), PDO::PARAM_STR);
			$statement->bindValue(':valor', $this->getValor(), PDO::PARAM_STR);
			$statement->bindValue(':fecha', $this->getFecha(), PDO::PARAM_STR);
			$statement->bindValue(':id', $this->getId(), PDO::PARAM_INT);

			// execute() devuelve true en éxito, false en error.
			return $statement->execute();

		} catch (PDOException $e) {
			// Manejar errores específicos de DB
			throw new Exception("Error de base de datos al actualizar movimiento (ID: {$this->getId()}): " . $e->getMessage());
		}
	}

	/**
	 * Elimina un movimiento por su ID (eliminación física).
	 *
	 * @param int $id       ID del movimiento a eliminar.
	 * @param PDO $conexion Conexión PDO.
	 *
	 * @return bool True si la eliminación fue exitosa, False en caso contrario.
	 * @throws Exception Si hay error en DB.
	 */
	public static function eliminar(int $id, PDO $conexion): bool
	{
		try {
			// Consulta para eliminar físicamente el movimiento
			$query = <<<SQL
            DELETE FROM control_bases_movimientos
            WHERE
                id = :id
            SQL;

			$statement = $conexion->prepare($query);
			$statement->bindValue(':id', $id, PDO::PARAM_INT);

			// execute() devuelve true en éxito, false en error.
			return $statement->execute();

		} catch (PDOException $e) {
			// Error de base de datos
			throw new Exception("Error de base de datos al eliminar movimiento (ID: $id): " . $e->getMessage());
		}
	}

	// --- Métodos de Validación ---

	/**
	 * Valida que el tipo sea 'ingreso' o 'egreso'.
	 *
	 * @param string|null $tipo Tipo a validar.
	 *
	 * @return bool True si el tipo es válido, False en caso contrario.
	 */
	private function validarTipo(?string $tipo): bool
	{
		return $tipo === 'ingreso' || $tipo === 'egreso';
	}

	/**
	 * Valida que un valor sea positivo (mayor que cero).
	 *
	 * @param float|null $valor Valor a validar.
	 *
	 * @return bool True si el valor es válido, False en caso contrario.
	 */
	private function validarValorPositivo(?float $valor): bool
	{
		return $valor !== null && is_numeric($valor) && $valor > 0;
	}

	/**
	 * Valida que un control base exista en la base de datos.
	 *
	 * @param int|null $id_control_base ID del control base a validar.
	 * @param PDO      $conexion        Conexión PDO.
	 *
	 * @return bool True si el control base existe, False en caso contrario.
	 */
	private function validarControlBaseExiste(?int $id_control_base, PDO $conexion): bool
	{
		if ($id_control_base === null) {
			return false;
		}

		try {
			$query = <<<SQL
            SELECT COUNT(*) as count
            FROM control_bases
            WHERE id = :id AND estado = 1
            SQL;

			$statement = $conexion->prepare($query);
			$statement->bindValue(':id', $id_control_base, PDO::PARAM_INT);
			$statement->execute();
			$resultado = $statement->fetch(PDO::FETCH_ASSOC);

			return $resultado && $resultado['count'] > 0;

		} catch (PDOException $e) {
			// En caso de error, asumir que no existe
			return false;
		}
	}

	/**
	 * Valida que una fecha tenga el formato correcto (YYYY-MM-DD HH:MM:SS).
	 *
	 * @param string|null $fecha Fecha a validar.
	 *
	 * @return bool True si la fecha es válida, False en caso contrario.
	 */
	private function validarFecha(?string $fecha): bool
	{
		if (empty($fecha)) {
			return false;
		}

		// Validar formato YYYY-MM-DD HH:MM:SS usando DateTime
		$dateTime = \DateTime::createFromFormat('Y-m-d H:i:s', $fecha);
		return $dateTime && $dateTime->format('Y-m-d H:i:s') === $fecha;
	}

	// --- Getters y Setters ---

	/**
	 * Obtiene el ID del movimiento.
	 *
	 * @return int|null ID del movimiento.
	 */
	public function getId(): ?int
	{
		return $this->id;
	}

	/**
	 * Establece el ID del movimiento.
	 *
	 * @param int|null $id ID del movimiento.
	 *
	 * @return self
	 */
	public function setId(?int $id): self
	{
		$this->id = $id;
		return $this;
	}

	/**
	 * Obtiene el ID del control base.
	 *
	 * @return int|null ID del control base.
	 */
	public function getId_control_base(): ?int
	{
		return $this->id_control_base;
	}

	/**
	 * Establece el ID del control base.
	 *
	 * @param int|null $id_control_base ID del control base.
	 *
	 * @return self
	 * @throws Exception Si el ID no es válido.
	 */
	public function setId_control_base(?int $id_control_base): self
	{
		if ($id_control_base !== null && $id_control_base <= 0) {
			throw new Exception("El ID del control base debe ser un número entero positivo.");
		}
		$this->id_control_base = $id_control_base;
		return $this;
	}

	/**
	 * Obtiene el tipo del movimiento.
	 *
	 * @return string|null Tipo del movimiento ('ingreso' o 'egreso').
	 */
	public function getTipo(): ?string
	{
		return $this->tipo;
	}

	/**
	 * Establece el tipo del movimiento.
	 *
	 * @param string|null $tipo Tipo del movimiento ('ingreso' o 'egreso').
	 *
	 * @return self
	 * @throws Exception Si el tipo no es válido.
	 */
	public function setTipo(?string $tipo): self
	{
		if ($tipo !== null && !$this->validarTipo($tipo)) {
			throw new Exception("El tipo debe ser 'ingreso' o 'egreso'.");
		}
		$this->tipo = $tipo;
		return $this;
	}

	/**
	 * Obtiene la nota del movimiento.
	 *
	 * @return string|null Nota del movimiento.
	 */
	public function getNota(): ?string
	{
		return $this->nota;
	}

	/**
	 * Establece la nota del movimiento.
	 *
	 * @param string|null $nota Nota del movimiento.
	 *
	 * @return self
	 */
	public function setNota(?string $nota): self
	{
		$this->nota = $nota;
		return $this;
	}

	/**
	 * Obtiene el valor del movimiento.
	 *
	 * @return float|null Valor del movimiento.
	 */
	public function getValor(): ?float
	{
		return $this->valor;
	}

	/**
	 * Establece el valor del movimiento.
	 *
	 * @param float|null $valor Valor del movimiento.
	 *
	 * @return self
	 * @throws Exception Si el valor no es válido.
	 */
	public function setValor(?float $valor): self
	{
		if ($valor !== null && !$this->validarValorPositivo($valor)) {
			throw new Exception("El valor debe ser un número positivo mayor que cero.");
		}
		$this->valor = $valor;
		return $this;
	}

	/**
	 * Obtiene la fecha del movimiento.
	 *
	 * @return string|null Fecha del movimiento.
	 */
	public function getFecha(): ?string
	{
		return $this->fecha;
	}

	/**
	 * Establece la fecha del movimiento.
	 *
	 * @param string|null $fecha Fecha del movimiento (formato YYYY-MM-DD HH:MM:SS).
	 *
	 * @return self
	 * @throws Exception Si la fecha no tiene un formato válido.
	 */
	public function setFecha(?string $fecha): self
	{
		if ($fecha !== null && !$this->validarFecha($fecha)) {
			throw new Exception("La fecha debe tener un formato válido (YYYY-MM-DD HH:MM:SS).");
		}
		$this->fecha = $fecha;
		return $this;
	}

	// --- Métodos adicionales ---

	/**
	 * Verifica si el movimiento es de tipo 'ingreso'.
	 * @return bool
	 */
	public function isIngreso(): bool
	{
		return $this->tipo === 'ingreso';
	}

	/**
	 * Verifica si el movimiento es de tipo 'egreso'.
	 * @return bool
	 */
	public function isEgreso(): bool
	{
		return $this->tipo === 'egreso';
	}

	/**
	 * Obtiene el valor formateado en pesos colombianos.
	 * @return string
	 */
	public function getValorFormateado(): string
	{
		if ($this->valor === null) {
			return '$0';
		}
		return '$' . number_format($this->valor, 0, ',', '.');
	}

	/**
	 * Obtiene la fecha formateada para mostrar.
	 * @return string
	 */
	public function getFechaFormateada(): string
	{
		if (empty($this->fecha)) {
			return '';
		}

		try {
			$dateTime = new \DateTime($this->fecha);
			return $dateTime->format('d/m/Y H:i:s');
		} catch (Exception $e) {
			return $this->fecha;
		}
	}

	/**
	 * Obtiene una descripción del tipo de movimiento.
	 * @return string
	 */
	public function getTipoDescripcion(): string
	{
		return match($this->tipo) {
			'ingreso' => 'Ingreso',
			'egreso' => 'Egreso',
			default => 'Desconocido'
		};
	}

	/**
	 * Calcula el impacto del movimiento en el control base.
	 * Los ingresos suman, los egresos restan.
	 * @return float
	 */
	public function calcularImpacto(): float
	{
		if ($this->valor === null) {
			return 0.0;
		}

		return $this->isIngreso() ? $this->valor : -$this->valor;
	}

	/**
	 * Obtiene un resumen del movimiento para mostrar.
	 * @return string
	 */
	public function getResumen(): string
	{
		$tipo = $this->getTipoDescripcion();
		$valor = $this->getValorFormateado();
		$fecha = $this->getFechaFormateada();

		$resumen = "$tipo: $valor";
		if (!empty($fecha)) {
			$resumen .= " ($fecha)";
		}
		if (!empty($this->nota)) {
			$resumen .= " - " . substr($this->nota, 0, 50);
			if (strlen($this->nota) > 50) {
				$resumen .= "...";
			}
		}

		return $resumen;
	}
}
