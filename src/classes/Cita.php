<?php

declare(strict_types=1);

namespace App\classes;

use Exception;
use PDO;
use PDOException;
use App\classes\Servicio;
use App\classes\EmpleadoTurno;
use App\classes\Empleado;

class Cita
{
	// --- Atributos ---
	private ?int    $id                      = null;
	private ?int    $id_empleado_turno       = null;
	private ?int    $id_metodo_pago          = null;
	private ?string $fecha_inicio            = null;
	private ?string $fecha_fin               = null;
	private ?int    $estado                  = null;
	private ?string $razon_cancelacion       = null;
	private ?float  $valor_comision_empleado = null;
	private ?string $fecha_cancelacion       = null;
	private ?int    $id_cierre               = null;

	// Atributos adicionales para información relacionada
	private ?string $nombre_empleado      = null; // Nombre del empleado asociado al turno
	private ?string $nombre_metodo_pago   = null; // Nombre del método de pago
	private ?string $descripcion_puesto   = null; // Descripción del puesto asociado al turno
	private ?string $nombre_centro_costo  = null; // Nombre del centro de costo
	private ?float  $total_valor_servicios = null; // Total del valor de servicios de la cita

	/**
	 * Constructor: Inicializa las propiedades del objeto Cita.
	 */
	public function __construct()
	{
		$this->id                      = 0;     // O null si prefieres no usar 0 por defecto
		$this->id_empleado_turno       = null;
		$this->id_metodo_pago          = null;
		$this->fecha_inicio            = null;
		$this->fecha_fin               = null;
		$this->estado                  = 1;     // Estado activo por defecto
		$this->razon_cancelacion       = null;
		$this->valor_comision_empleado = 0.0;   // Default to 0.0
		$this->fecha_cancelacion       = null;
		$this->id_cierre               = null;  // Referencia opcional a la tabla cierres
		$this->nombre_empleado         = null;
		$this->nombre_metodo_pago      = null;
		$this->descripcion_puesto      = null;
		$this->nombre_centro_costo     = null;
		$this->total_valor_servicios   = null;
	}

	/**
	 * Método estático para construir un objeto Cita desde un array (ej. fila de DB).
	 *
	 * @param array $resultado Array asociativo con los datos de la cita.
	 *
	 * @return self Instancia de Cita.
	 * @throws Exception Si ocurre un error durante la construcción.
	 */
	public static function construct(array $resultado = []): self
	{
		try {
			$objeto                     = new self();
			$objeto->id                 = isset($resultado['id']) ? (int)$resultado['id'] : 0;
			$objeto->id_metodo_pago     = isset($resultado['id_metodo_pago']) ? (int)$resultado['id_metodo_pago'] : null;
			$objeto->id_empleado_turno  = isset($resultado['id_empleado_turno']) ? (int)$resultado['id_empleado_turno'] : null;
			$objeto->fecha_inicio       = $resultado['fecha_inicio'] ?? null;
			$objeto->fecha_fin          = $resultado['fecha_fin'] ?? null;
			$objeto->estado             = isset($resultado['estado']) ? (int)$resultado['estado'] : 1;
			$objeto->razon_cancelacion  = $resultado['razon_cancelacion'] ?? null;
			$objeto->valor_comision_empleado = isset($resultado['valor_comision_empleado']) ? (float)$resultado['valor_comision_empleado'] : 0.0;
			$objeto->fecha_cancelacion  = $resultado['fecha_cancelacion'] ?? null;
			$objeto->id_cierre          = isset($resultado['id_cierre']) ? (int)$resultado['id_cierre'] : null;
			$objeto->nombre_metodo_pago   = $resultado['nombre_metodo_pago'] ?? null;
			$objeto->nombre_empleado      = $resultado['nombre_empleado'] ?? null;
			$objeto->descripcion_puesto   = $resultado['descripcion_puesto'] ?? null;
			$objeto->nombre_centro_costo  = $resultado['nombre_centro_costo'] ?? null;
			$objeto->total_valor_servicios = isset($resultado['total_valor_servicios']) ? (float)$resultado['total_valor_servicios'] : null;
			return $objeto;
		} catch (Exception $e) {
			// Considera loggear el error aquí
			throw new Exception("Error al construir Cita: " . $e->getMessage());
		}
	}

	// --- Métodos de Acceso a Datos (Estáticos) ---

	/**
	 * Obtiene una cita por su ID.
	 *
	 * @param int $id       ID de la cita.
	 * @param PDO $conexion Conexión PDO.
	 * @param bool $incluir_canceladas Si es true, incluye citas canceladas (estado=0).
	 *
	 * @return self|null Objeto Cita o null si no se encuentra.
	 * @throws Exception Si hay error en DB.
	 */
	public static function get(int $id, PDO $conexion, bool $incluir_canceladas = false): ?self
	{
		try {
			// Consulta para obtener cita por ID (Usando Heredoc)
			$query = <<<SQL
            SELECT
            	c.*,
            	mp.descripcion AS nombre_metodo_pago,
            	e.nombre AS nombre_empleado,
            	p.descripcion AS descripcion_puesto
            FROM citas c
            JOIN empleados_turnos et ON c.id_empleado_turno = et.id
            LEFT JOIN empleados e ON et.id_empleado = e.id
            LEFT JOIN puestos p ON et.id_puesto = p.id
            LEFT JOIN metodos_pagos mp ON c.id_metodo_pago = mp.id
            WHERE
            	c.id = :id
            SQL;

			// Si no se incluyen canceladas, agregar filtro de estado
			if (!$incluir_canceladas) {
				$query .= " AND c.estado = 1";
			}

			$query .= " LIMIT 1";

			$statement = $conexion->prepare($query);
			$statement->bindValue(":id", $id, PDO::PARAM_INT);
			$statement->execute();
			$resultado = $statement->fetch(PDO::FETCH_ASSOC);

			return $resultado ? self::construct($resultado) : null;

		} catch (PDOException $e) {
			throw new Exception("Error al obtener Cita (ID: $id): " . $e->getMessage());
		}
	}

	/**
	 * Obtiene una lista de todas las citas.
	 *
	 * @param PDO $conexion Conexión PDO.
	 * @param bool $incluir_canceladas Si es true, incluye citas canceladas (estado=0).
	 *
	 * @return array Array de objetos Cita.
	 * @throws Exception Si hay error en DB.
	 */
	public static function get_list(PDO $conexion, bool $incluir_canceladas = false): array
	{
		try {
			// Consulta para obtener lista de citas (Usando Heredoc)
			$query = <<<SQL
            SELECT
            	c.*,
            	mp.descripcion AS nombre_metodo_pago,
            	e.nombre AS nombre_empleado,
            	p.descripcion AS descripcion_puesto
            FROM citas c
            JOIN empleados_turnos et ON c.id_empleado_turno = et.id
            LEFT JOIN empleados e ON et.id_empleado = e.id
            LEFT JOIN puestos p ON et.id_puesto = p.id
            LEFT JOIN metodos_pagos mp ON c.id_metodo_pago = mp.id
            SQL;

			// Si no se incluyen canceladas, agregar filtro de estado
			if (!$incluir_canceladas) {
				$query .= " WHERE c.estado = 1";
			}

			$query .= " ORDER BY c.fecha_inicio DESC";

			$statement = $conexion->prepare($query);
			$statement->execute();
			$resultados = $statement->fetchAll(PDO::FETCH_ASSOC);

			$listado = [];
			foreach ($resultados as $resultado) {
				$listado[] = self::construct($resultado);
			}
			return $listado;

		} catch (PDOException $e) {
			throw new Exception("Error al obtener lista de Citas: " . $e->getMessage());
		}
	}

	/**
	 * Obtiene las citas para un turno específico.
	 *
	 * @param int $id_empleado_turno ID del turno de empleado.
	 * @param PDO $conexion          Conexión PDO.
	 * @param bool $incluir_canceladas Si es true, incluye citas canceladas (estado=0).
	 *
	 * @return array Array de objetos Cita.
	 * @throws Exception Si hay error en DB.
	 */
	public static function get_by_turno(int $id_empleado_turno, PDO $conexion, bool $incluir_canceladas = false): array
	{
		try {
			// Consulta para obtener citas por turno (Usando Heredoc)
			$query = <<<SQL
            SELECT
            	c.*,
            	mp.descripcion AS nombre_metodo_pago,
            	e.nombre AS nombre_empleado,
            	p.descripcion AS descripcion_puesto
            FROM citas c
            JOIN empleados_turnos et ON c.id_empleado_turno = et.id
            LEFT JOIN empleados e ON et.id_empleado = e.id
            LEFT JOIN puestos p ON et.id_puesto = p.id
            LEFT JOIN metodos_pagos mp ON c.id_metodo_pago = mp.id
            WHERE
            	c.id_empleado_turno = :id_empleado_turno
            SQL;

			// Si no se incluyen canceladas, agregar filtro de estado
			if (!$incluir_canceladas) {
				$query .= " AND c.estado = 1";
			}

			$query .= " ORDER BY c.fecha_inicio";

			$statement = $conexion->prepare($query);
			$statement->bindValue(":id_empleado_turno", $id_empleado_turno, PDO::PARAM_INT);
			$statement->execute();
			$resultados = $statement->fetchAll(PDO::FETCH_ASSOC);

			$listado = [];
			foreach ($resultados as $resultado) {
				$listado[] = self::construct($resultado);
			}
			return $listado;

		} catch (PDOException $e) {
			throw new Exception("Error al obtener citas del turno (ID: $id_empleado_turno): " . $e->getMessage());
		}
	}

	/**
	 * Obtiene las citas para un rango de fechas.
	 *
	 * @param string $fecha_desde Fecha de inicio del rango.
	 * @param string $fecha_hasta Fecha de fin del rango.
	 * @param PDO    $conexion    Conexión PDO.
	 * @param bool $incluir_canceladas Si es true, incluye citas canceladas (estado=0).
	 *
	 * @return array Array de objetos Cita.
	 * @throws Exception Si hay error en DB.
	 */
	public static function get_by_fecha_rango(string $fecha_desde, string $fecha_hasta, PDO $conexion, bool $incluir_canceladas = false): array
	{
		try {
			// Consulta para obtener citas por rango de fechas (Usando Heredoc)
			$query = <<<SQL
            SELECT
            	c.*,
            	mp.descripcion AS nombre_metodo_pago,
            	e.nombre AS nombre_empleado,
            	p.descripcion AS descripcion_puesto
            FROM citas c
            JOIN empleados_turnos et ON c.id_empleado_turno = et.id
            LEFT JOIN empleados e ON et.id_empleado = e.id
            LEFT JOIN puestos p ON et.id_puesto = p.id
            LEFT JOIN metodos_pagos mp ON c.id_metodo_pago = mp.id
            WHERE
            	c.fecha_inicio >= :fecha_desde
            	AND c.fecha_inicio <= :fecha_hasta
            SQL;

			// Si no se incluyen canceladas, agregar filtro de estado
			if (!$incluir_canceladas) {
				$query .= " AND c.estado = 1";
			}

			$query .= " ORDER BY c.fecha_inicio";

			$statement = $conexion->prepare($query);
			$statement->bindValue(":fecha_desde", $fecha_desde, PDO::PARAM_STR);
			$statement->bindValue(":fecha_hasta", $fecha_hasta, PDO::PARAM_STR);
			$statement->execute();
			$resultados = $statement->fetchAll(PDO::FETCH_ASSOC);

			$listado = [];
			foreach ($resultados as $resultado) {
				$listado[] = self::construct($resultado);
			}
			return $listado;

		} catch (PDOException $e) {
			throw new Exception("Error al obtener citas por rango de fechas: " . $e->getMessage());
		}
	}

	/**
	 * Cuenta las citas activas para un centro de costo específico que pueden impedir el cierre de caja.
	 *
	 * Filtros aplicados:
	 * - fecha_fin IS NULL (citas no finalizadas)
	 * - estado = 1 (citas activas, no canceladas)
	 * - id_cierre IS NULL (citas no incluidas en cierres anteriores)
	 * - centro de costo específico
	 *
	 * @param int $id_centro_costo ID del centro de costo.
	 * @param PDO $conexion Conexión PDO.
	 *
	 * @return int Número de citas activas encontradas.
	 * @throws Exception Si hay error en DB.
	 */
	public static function countActiveCitasByCentroCosto(int $id_centro_costo, PDO $conexion): int
	{
		try {
			// Consulta optimizada para contar citas activas por centro de costo (Usando Heredoc)
			// Incluye filtros para performance y lógica de negocio correcta
			$query = <<<SQL
			SELECT COUNT(*) as total_citas_activas
			FROM citas c
			INNER JOIN empleados_turnos et ON c.id_empleado_turno = et.id
			INNER JOIN puestos p ON et.id_puesto = p.id
			WHERE c.fecha_fin IS NULL
			AND c.estado = 1
			AND c.id_cierre IS NULL
			AND p.id_centro_costo = :id_centro_costo
			SQL;

			$statement = $conexion->prepare($query);
			$statement->bindValue(':id_centro_costo', $id_centro_costo, PDO::PARAM_INT);
			$statement->execute();
			$resultado = $statement->fetch(PDO::FETCH_ASSOC);

			return (int)($resultado['total_citas_activas'] ?? 0);

		} catch (PDOException $e) {
			throw new Exception("Error al contar citas activas por centro de costo: " . $e->getMessage());
		}
	}

	/**
	 * Obtiene las citas finalizadas listas para cierre de caja por centro de costo.
	 *
	 * Filtros aplicados:
	 * - estado = 1 (citas activas, no canceladas)
	 * - fecha_fin IS NOT NULL (citas finalizadas)
	 * - id_cierre IS NULL (no incluidas en cierres anteriores)
	 * - centro de costo específico
	 *
	 * @param int $id_centro_costo ID del centro de costo.
	 * @param PDO $conexion Conexión PDO.
	 *
	 * @return array Array de objetos Cita con información completa.
	 * @throws Exception Si hay error en DB.
	 */
	public static function getCitasParaCierreByCentroCosto(int $id_centro_costo, PDO $conexion): array
	{
		try {
			// Consulta para obtener citas listas para cierre con información completa
			$query = <<<SQL
			SELECT
				c.*,
				mp.descripcion AS nombre_metodo_pago,
				e.nombre AS nombre_empleado,
				p.descripcion AS descripcion_puesto,
				cc.nombre AS nombre_centro_costo,
				(
					SELECT SUM(cs.valor)
					FROM citas_servicios cs
					WHERE cs.id_cita = c.id
				) as total_valor_servicios
			FROM citas c
			INNER JOIN empleados_turnos et ON c.id_empleado_turno = et.id
			INNER JOIN empleados e ON et.id_empleado = e.id
			INNER JOIN puestos p ON et.id_puesto = p.id
			INNER JOIN centros_costos cc ON p.id_centro_costo = cc.id
			LEFT JOIN metodos_pagos mp ON c.id_metodo_pago = mp.id
			WHERE c.estado = 1
			AND c.fecha_fin IS NOT NULL
			AND c.id_cierre IS NULL
			AND p.id_centro_costo = :id_centro_costo
			ORDER BY c.fecha_fin DESC, c.fecha_inicio DESC
			SQL;

			$statement = $conexion->prepare($query);
			$statement->bindValue(':id_centro_costo', $id_centro_costo, PDO::PARAM_INT);
			$statement->execute();
			$resultados = $statement->fetchAll(PDO::FETCH_ASSOC);

			$listado = [];
			foreach ($resultados as $resultado) {
				$cita = self::construct($resultado);
				$listado[] = $cita;
			}

			return $listado;

		} catch (PDOException $e) {
			throw new Exception("Error al obtener citas para cierre por centro de costo: " . $e->getMessage());
		}
	}

	/**
	 * Obtiene las citas finalizadas para consulta histórica por rango de fechas de finalización y centro de costo.
	 *
	 * @param string $fecha_desde Fecha de inicio del rango (fecha_fin).
	 * @param string $fecha_hasta Fecha de fin del rango (fecha_fin).
	 * @param int|null $id_centro_costo ID del centro de costo (opcional).
	 * @param PDO $conexion Conexión PDO.
	 * @param bool $incluir_canceladas Si es true, incluye citas canceladas (estado=0).
	 * @param array $filtros_adicionales Array con filtros opcionales: id_empleado, id_puesto, id_metodo_pago.
	 *
	 * @return array Array con datos formateados para consulta histórica.
	 * @throws Exception Si hay error en DB.
	 */
	public static function get_historico_by_fecha_fin_rango(string $fecha_desde, string $fecha_hasta, ?int $id_centro_costo, PDO $conexion, bool $incluir_canceladas = true, array $filtros_adicionales = []): array
	{
		try {
			// Verificar si existe la columna id_centro_costo en la tabla puestos
			$check_query = "SHOW COLUMNS FROM puestos LIKE 'id_centro_costo'";
			$check_stmt = $conexion->prepare($check_query);
			$check_stmt->execute();
			$column_exists = $check_stmt->rowCount() > 0;

			if ($column_exists && $id_centro_costo !== null) {
				// Si existe la columna id_centro_costo en puestos, usar la consulta completa
				$query = <<<SQL
				SELECT
					c.*,
					mp.descripcion AS nombre_metodo_pago,
					e.nombre AS nombre_empleado,
					p.descripcion AS descripcion_puesto,
					cc.nombre AS nombre_centro_costo,
					(
						SELECT SUM(cs.valor)
						FROM citas_servicios cs
						WHERE cs.id_cita = c.id
					) as total_valor_servicios
				FROM citas c
				JOIN empleados_turnos et ON c.id_empleado_turno = et.id
				LEFT JOIN empleados e ON et.id_empleado = e.id
				LEFT JOIN puestos p ON et.id_puesto = p.id
				LEFT JOIN centros_costos cc ON p.id_centro_costo = cc.id
				LEFT JOIN metodos_pagos mp ON c.id_metodo_pago = mp.id
				WHERE
					c.fecha_fin IS NOT NULL
					AND DATE(c.fecha_fin) >= :fecha_desde
					AND DATE(c.fecha_fin) <= :fecha_hasta
					AND p.id_centro_costo = :id_centro_costo
				SQL;

				// Agregar filtros adicionales
				if (!empty($filtros_adicionales['id_empleado'])) {
					$query .= " AND e.id = :id_empleado";
				}
				if (!empty($filtros_adicionales['id_puesto'])) {
					$query .= " AND p.id = :id_puesto";
				}
				if (!empty($filtros_adicionales['id_metodo_pago'])) {
					$query .= " AND c.id_metodo_pago = :id_metodo_pago";
				}

				// Si no se incluyen canceladas, agregar filtro de estado
				if (!$incluir_canceladas) {
					$query .= " AND c.estado = 1";
				}

				$query .= " ORDER BY c.fecha_fin DESC, c.fecha_inicio DESC";

				$stmt = $conexion->prepare($query);
				$stmt->bindValue(':fecha_desde', $fecha_desde, PDO::PARAM_STR);
				$stmt->bindValue(':fecha_hasta', $fecha_hasta, PDO::PARAM_STR);
				$stmt->bindValue(':id_centro_costo', $id_centro_costo, PDO::PARAM_INT);

				// Bind filtros adicionales
				if (!empty($filtros_adicionales['id_empleado'])) {
					$stmt->bindValue(':id_empleado', $filtros_adicionales['id_empleado'], PDO::PARAM_INT);
				}
				if (!empty($filtros_adicionales['id_puesto'])) {
					$stmt->bindValue(':id_puesto', $filtros_adicionales['id_puesto'], PDO::PARAM_INT);
				}
				if (!empty($filtros_adicionales['id_metodo_pago'])) {
					$stmt->bindValue(':id_metodo_pago', $filtros_adicionales['id_metodo_pago'], PDO::PARAM_INT);
				}
			} else {
				// Si no existe la columna, usar consulta sin filtro de centro de costo
				$query = <<<SQL
            SELECT
                c.*,
                mp.descripcion AS nombre_metodo_pago,
                e.nombre AS nombre_empleado,
                p.descripcion AS descripcion_puesto,
                cc.nombre AS nombre_centro_costo,
                (
                    SELECT SUM(cs.valor)
                    FROM citas_servicios cs
                    WHERE cs.id_cita = c.id
                ) as total_valor_servicios
            FROM citas c
            JOIN empleados_turnos et ON c.id_empleado_turno = et.id
            LEFT JOIN empleados e ON et.id_empleado = e.id
            LEFT JOIN puestos p ON et.id_puesto = p.id
            LEFT JOIN centros_costos cc ON p.id_centro_costo = cc.id
            LEFT JOIN metodos_pagos mp ON c.id_metodo_pago = mp.id
            WHERE
                c.fecha_fin IS NOT NULL
                AND DATE(c.fecha_fin) >= :fecha_desde
                AND DATE(c.fecha_fin) <= :fecha_hasta
            SQL;

				// Agregar filtros adicionales
				if (!empty($filtros_adicionales['id_empleado'])) {
					$query .= " AND e.id = :id_empleado";
				}
				if (!empty($filtros_adicionales['id_puesto'])) {
					$query .= " AND p.id = :id_puesto";
				}
				if (!empty($filtros_adicionales['id_metodo_pago'])) {
					$query .= " AND c.id_metodo_pago = :id_metodo_pago";
				}

				// Si no se incluyen canceladas, agregar filtro de estado
				if (!$incluir_canceladas) {
					$query .= " AND c.estado = 1";
				}

				$query .= " ORDER BY c.fecha_fin DESC, c.fecha_inicio DESC";

				$stmt = $conexion->prepare($query);
				$stmt->bindValue(':fecha_desde', $fecha_desde, PDO::PARAM_STR);
				$stmt->bindValue(':fecha_hasta', $fecha_hasta, PDO::PARAM_STR);

				// Bind filtros adicionales
				if (!empty($filtros_adicionales['id_empleado'])) {
					$stmt->bindValue(':id_empleado', $filtros_adicionales['id_empleado'], PDO::PARAM_INT);
				}
				if (!empty($filtros_adicionales['id_puesto'])) {
					$stmt->bindValue(':id_puesto', $filtros_adicionales['id_puesto'], PDO::PARAM_INT);
				}
				if (!empty($filtros_adicionales['id_metodo_pago'])) {
					$stmt->bindValue(':id_metodo_pago', $filtros_adicionales['id_metodo_pago'], PDO::PARAM_INT);
				}
			}

			$stmt->execute();
			$resultados = $stmt->fetchAll(PDO::FETCH_ASSOC);

			// Formatear los resultados
			$citas = [];
			foreach ($resultados as $resultado) {
				$citas[] = [
					'id' => (int)$resultado['id'],
					'descripcion_puesto' => $resultado['descripcion_puesto'],
					'turno_empleado_nombre' => $resultado['nombre_empleado'],
					'cita_empleado_nombre' => $resultado['nombre_empleado'], // Same as turno employee for now
					'nombre_metodo_pago' => $resultado['nombre_metodo_pago'],
					'centro_costo_nombre' => $resultado['nombre_centro_costo'] ?? 'N/A',
					'fecha_inicio' => $resultado['fecha_inicio'],
					'fecha_fin' => $resultado['fecha_fin'],
					'fecha_inicio_formateada' => self::formatearFechaHora($resultado['fecha_inicio']),
					'fecha_fin_formateada' => self::formatearFechaHora($resultado['fecha_fin']),
					'estado' => (int)$resultado['estado'],
					'estado_texto' => (int)$resultado['estado'] === 1 ? 'Terminada' : 'Cancelada',
					'razon_cancelacion' => $resultado['razon_cancelacion'],
					'total_valor_servicios' => (float)($resultado['total_valor_servicios'] ?? 0),
					'total_valor_formateado' => '$' . number_format((float)($resultado['total_valor_servicios'] ?? 0), 0, ',', '.'),
					'valor_comision_empleado' => (float)($resultado['valor_comision_empleado'] ?? 0),
					'valor_comision_formateado' => '$' . number_format((float)($resultado['valor_comision_empleado'] ?? 0), 0, ',', '.')
				];
			}

			return [
				'citas' => $citas,
				'schema_warning' => !$column_exists || $id_centro_costo === null
			];

		} catch (PDOException $e) {
			throw new Exception("Error de base de datos al generar la consulta histórica: " . $e->getMessage());
		}
	}

	/**
	 * Obtiene citas de un empleado específico en un rango de fechas (incluye citas en progreso y completadas)
	 *
	 * @param string $fecha_desde Fecha de inicio del rango (Y-m-d).
	 * @param string $fecha_hasta Fecha de fin del rango (Y-m-d).
	 * @param int $id_empleado ID del empleado.
	 * @param int|null $id_centro_costo ID del centro de costo para filtrar.
	 * @param PDO $conexion Conexión PDO.
	 * @param bool $incluir_canceladas Si incluir citas canceladas.
	 *
	 * @return array Array con datos formateados para consulta de empleado.
	 * @throws Exception Si hay error en DB.
	 */
	public static function get_citas_empleado_by_fecha_rango(string $fecha_desde, string $fecha_hasta, int $id_empleado, ?int $id_centro_costo, PDO $conexion, bool $incluir_canceladas = true): array
	{
		try {
			// Verificar si existe la columna id_centro_costo en la tabla puestos
			$check_query = "SHOW COLUMNS FROM puestos LIKE 'id_centro_costo'";
			$check_stmt = $conexion->prepare($check_query);
			$check_stmt->execute();
			$column_exists = $check_stmt->rowCount() > 0;

			if ($column_exists && $id_centro_costo !== null) {
				// Si existe la columna id_centro_costo en puestos, usar la consulta completa
				$query = <<<SQL
				SELECT
					c.*,
					mp.descripcion AS nombre_metodo_pago,
					e.nombre AS nombre_empleado,
					p.descripcion AS descripcion_puesto,
					cc.nombre AS nombre_centro_costo,
					(
						SELECT SUM(cs.valor)
						FROM citas_servicios cs
						WHERE cs.id_cita = c.id
					) as total_valor_servicios
				FROM citas c
				JOIN empleados_turnos et ON c.id_empleado_turno = et.id
				LEFT JOIN empleados e ON et.id_empleado = e.id
				LEFT JOIN puestos p ON et.id_puesto = p.id
				LEFT JOIN centros_costos cc ON p.id_centro_costo = cc.id
				LEFT JOIN metodos_pagos mp ON c.id_metodo_pago = mp.id
				WHERE
					DATE(c.fecha_inicio) >= :fecha_desde
					AND DATE(c.fecha_inicio) <= :fecha_hasta
					AND e.id = :id_empleado
					AND p.id_centro_costo = :id_centro_costo
				SQL;

				// Si no se incluyen canceladas, agregar filtro de estado
				if (!$incluir_canceladas) {
					$query .= " AND c.estado = 1";
				}

				$query .= " ORDER BY c.fecha_inicio DESC";

				$stmt = $conexion->prepare($query);
				$stmt->bindValue(':fecha_desde', $fecha_desde, PDO::PARAM_STR);
				$stmt->bindValue(':fecha_hasta', $fecha_hasta, PDO::PARAM_STR);
				$stmt->bindValue(':id_empleado', $id_empleado, PDO::PARAM_INT);
				$stmt->bindValue(':id_centro_costo', $id_centro_costo, PDO::PARAM_INT);
			} else {
				// Si no existe la columna, usar consulta sin filtro de centro de costo
				$query = <<<SQL
				SELECT
					c.*,
					mp.descripcion AS nombre_metodo_pago,
					e.nombre AS nombre_empleado,
					p.descripcion AS descripcion_puesto,
					cc.nombre AS nombre_centro_costo,
					(
						SELECT SUM(cs.valor)
						FROM citas_servicios cs
						WHERE cs.id_cita = c.id
					) as total_valor_servicios
				FROM citas c
				JOIN empleados_turnos et ON c.id_empleado_turno = et.id
				LEFT JOIN empleados e ON et.id_empleado = e.id
				LEFT JOIN puestos p ON et.id_puesto = p.id
				LEFT JOIN centros_costos cc ON p.id_centro_costo = cc.id
				LEFT JOIN metodos_pagos mp ON c.id_metodo_pago = mp.id
				WHERE
					DATE(c.fecha_inicio) >= :fecha_desde
					AND DATE(c.fecha_inicio) <= :fecha_hasta
					AND e.id = :id_empleado
				SQL;

				// Si no se incluyen canceladas, agregar filtro de estado
				if (!$incluir_canceladas) {
					$query .= " AND c.estado = 1";
				}

				$query .= " ORDER BY c.fecha_inicio DESC";

				$stmt = $conexion->prepare($query);
				$stmt->bindValue(':fecha_desde', $fecha_desde, PDO::PARAM_STR);
				$stmt->bindValue(':fecha_hasta', $fecha_hasta, PDO::PARAM_STR);
				$stmt->bindValue(':id_empleado', $id_empleado, PDO::PARAM_INT);
			}

			$stmt->execute();
			$resultados = $stmt->fetchAll(PDO::FETCH_ASSOC);

			// Formatear los resultados
			$citas = [];
			foreach ($resultados as $resultado) {
				// Determinar estado de la cita
				$estado = (int)$resultado['estado'];
				$estado_texto = '';
				if ($estado === 1) {
					if ($resultado['fecha_fin'] !== null) {
						$estado_texto = 'Terminada';
					} else {
						$estado_texto = 'En Progreso';
					}
				} else {
					$estado_texto = 'Cancelada';
				}

				$citas[] = [
					'id' => (int)$resultado['id'],
					'descripcion_puesto' => $resultado['descripcion_puesto'],
					'turno_empleado_nombre' => $resultado['nombre_empleado'],
					'cita_empleado_nombre' => $resultado['nombre_empleado'],
					'nombre_metodo_pago' => $resultado['nombre_metodo_pago'],
					'centro_costo_nombre' => $resultado['nombre_centro_costo'] ?? 'N/A',
					'fecha_inicio' => $resultado['fecha_inicio'],
					'fecha_fin' => $resultado['fecha_fin'],
					'fecha_inicio_formateada' => self::formatearFechaHora($resultado['fecha_inicio']),
					'fecha_fin_formateada' => self::formatearFechaHora($resultado['fecha_fin']),
					'estado' => $estado,
					'estado_texto' => $estado_texto,
					'razon_cancelacion' => $resultado['razon_cancelacion'],
					'total_valor_servicios' => (float)($resultado['total_valor_servicios'] ?? 0),
					'total_valor_formateado' => '$' . number_format((float)($resultado['total_valor_servicios'] ?? 0), 0, ',', '.'),
					'valor_comision_empleado' => (float)($resultado['valor_comision_empleado'] ?? 0),
					'valor_comision_formateado' => '$' . number_format((float)($resultado['valor_comision_empleado'] ?? 0), 0, ',', '.')
				];
			}

			return [
				'citas' => $citas,
				'schema_warning' => !$column_exists || $id_centro_costo === null
			];

		} catch (PDOException $e) {
			throw new Exception("Error de base de datos al generar la consulta de citas del empleado: " . $e->getMessage());
		}
	}

	/**
	 * Formatea una fecha y hora para mostrar en formato legible
	 *
	 * @param string|null $fecha_hora Fecha y hora en formato Y-m-d H:i:s
	 * @return string Fecha y hora formateada
	 */
	private static function formatearFechaHora(?string $fecha_hora): string
	{
		if (empty($fecha_hora)) {
			return 'N/A';
		}

		try {
			$fecha = new \DateTime($fecha_hora, new \DateTimeZone('America/Bogota'));
			return $fecha->format('Y-m-d g:i A');
		} catch (Exception) {
			return $fecha_hora;
		}
	}

	/**
	 * Crea una nueva cita en la base de datos a partir de un objeto Cita.
	 * El objeto Cita debe estar completamente poblado con los datos requeridos.
	 *
	 * @param PDO $conexion Conexión PDO.
	 * @param array $serviciosIds Array opcional de IDs de servicios para validar que el empleado puede realizarlos.
	 *
	 * @return int|false El ID de la nueva cita creada o false en caso de error.
	 * @throws Exception Si los datos requeridos en el objeto están vacíos o hay error en DB.
	 */
	function crear(PDO $conexion, array $serviciosIds = []): int|false
	{
		// Validaciones básicas sobre el objeto
		if ($this->getId_empleado_turno() === null || empty($this->getFecha_inicio())) {
			throw new Exception("ID de turno de empleado y fecha de inicio son requeridos para crear una cita.");
		}

		// Validar servicios del empleado si se proporcionan
		if (!empty($serviciosIds)) {
			$this->validarServiciosEmpleado($conexion, $serviciosIds);
		}

		// Verificar que el turno del empleado esté activo (no ha finalizado)
		$turno = EmpleadoTurno::get($this->getId_empleado_turno(), $conexion);
		if (!$turno) {
			throw new Exception("El turno de empleado especificado no existe.");
		}

		if ($turno->getFecha_fin() !== null) {
			throw new Exception("No se puede crear una cita para un turno que ya ha finalizado.");
		}

		try {
			return $this->_insert($conexion);
		} catch (Exception $e) {
			// Capturar otros errores
			throw new Exception("Error al crear cita: " . $e->getMessage());
		}
	}

	/**
	 * Método privado para insertar una nueva cita en la base de datos.
	 *
	 * @param PDO $conexion Conexión PDO.
	 *
	 * @return int|false El ID de la nueva cita creada o false en caso de error.
	 * @throws Exception Si hay error en DB.
	 */
	private function _insert(PDO $conexion): int|false
	{
		try {
			// Establecer la zona horaria para Colombia
			date_default_timezone_set('America/Bogota');

			// Preparar la consulta INSERT usando Heredoc
			$query = <<<SQL
            INSERT INTO citas (
            	 id_empleado_turno
            	,fecha_inicio
            	,fecha_fin
            	,estado
            	,razon_cancelacion
            	,fecha_cancelacion
            	,id_metodo_pago
            	,valor_comision_empleado
            	,id_cierre
            ) VALUES (
            	 :id_empleado_turno
            	,:fecha_inicio
            	,:fecha_fin
            	,:estado
            	,:razon_cancelacion
            	,:fecha_cancelacion
            	,:id_metodo_pago
            	,:valor_comision_empleado
            	,:id_cierre
            )
            SQL;

			$statement = $conexion->prepare($query);

			// Bind de parámetros desde el objeto
			$statement->bindValue(':id_empleado_turno', $this->getId_empleado_turno(), PDO::PARAM_INT);
			$statement->bindValue(':fecha_inicio', $this->getFecha_inicio(), PDO::PARAM_STR);
			$statement->bindValue(':fecha_fin', $this->getFecha_fin(), $this->getFecha_fin() === null ? PDO::PARAM_NULL : PDO::PARAM_STR);
			$statement->bindValue(':estado', $this->getEstado(), PDO::PARAM_INT);
			$statement->bindValue(':razon_cancelacion', $this->getRazon_cancelacion(), $this->getRazon_cancelacion() === null ? PDO::PARAM_NULL : PDO::PARAM_STR);
			$statement->bindValue(':fecha_cancelacion', $this->getFecha_cancelacion(), $this->getFecha_cancelacion() === null ? PDO::PARAM_NULL : PDO::PARAM_STR);
			$statement->bindValue(':id_metodo_pago', $this->getId_metodo_pago(), $this->getId_metodo_pago() === null ? PDO::PARAM_NULL : PDO::PARAM_INT);
			$statement->bindValue(':valor_comision_empleado', $this->getValor_comision_empleado(), PDO::PARAM_STR);

			// Handle null values for id_cierre
			$id_cierre = $this->getId_cierre();
			$statement->bindValue(':id_cierre', $id_cierre, $id_cierre === null ? PDO::PARAM_NULL : PDO::PARAM_INT);

			// Ejecutar la consulta
			$success = $statement->execute();

			if ($success) {
				// Devolver el ID de la cita recién creada
				return (int)$conexion->lastInsertId();
			} else {
				return false; // Error en la ejecución
			}

		} catch (PDOException $e) {
			throw new Exception("Error de base de datos al crear cita: " . $e->getMessage());
		}
	}

	/**
	 * Modifica una cita existente.
	 *
	 * @param PDO $conexion Conexión PDO.
	 * @param array $serviciosIds Array opcional de IDs de servicios para validar que el empleado puede realizarlos.
	 *
	 * @return bool True si la modificación fue exitosa, False en caso contrario.
	 * @throws Exception Si los datos requeridos en el objeto están vacíos o hay error en DB.
	 */
	function modificar(PDO $conexion, array $serviciosIds = []): bool
	{
		// Validaciones básicas sobre el objeto
		if ($this->getId() <= 0 || $this->getId_empleado_turno() === null || empty($this->getFecha_inicio())) {
			throw new Exception("ID, ID de turno de empleado y fecha de inicio son requeridos para modificar una cita.");
		}

		// Validar servicios del empleado si se proporcionan
		if (!empty($serviciosIds)) {
			$this->validarServiciosEmpleado($conexion, $serviciosIds);
		}

		// Verificar que la fecha de fin sea posterior a la fecha de inicio si no es NULL
		if ($this->getFecha_fin() !== null && strtotime($this->getFecha_fin()) <= strtotime($this->getFecha_inicio())) {
			throw new Exception("La fecha de fin debe ser posterior a la fecha de inicio.");
		}

		// Verificar que el turno del empleado esté activo (no ha finalizado)
		$turno = EmpleadoTurno::get($this->getId_empleado_turno(), $conexion);
		if (!$turno) {
			throw new Exception("El turno de empleado especificado no existe.");
		}

		if ($turno->getFecha_fin() !== null) {
			throw new Exception("No se puede modificar una cita para un turno que ya ha finalizado.");
		}

		try {
			return $this->_update($conexion);
		} catch (Exception $e) {
			// Capturar otros errores
			throw new Exception("Error al modificar cita: " . $e->getMessage());
		}
	}

	/**
	 * Método privado para actualizar una cita existente en la base de datos.
	 *
	 * @param PDO $conexion Conexión PDO.
	 *
	 * @return bool True si la actualización fue exitosa, False en caso contrario.
	 * @throws Exception Si hay error en DB.
	 */
	private function _update(PDO $conexion): bool
	{
		try {
			// Establecer la zona horaria para Colombia
			date_default_timezone_set('America/Bogota');

			// Consulta para actualizar la cita
			$query = <<<SQL
            UPDATE citas SET
                id_empleado_turno = :id_empleado_turno,
                fecha_inicio = :fecha_inicio,
                fecha_fin = :fecha_fin,
                estado = :estado,
                razon_cancelacion = :razon_cancelacion,
                fecha_cancelacion = :fecha_cancelacion,
                id_metodo_pago = :id_metodo_pago,
                valor_comision_empleado = :valor_comision_empleado,
                id_cierre = :id_cierre
            WHERE
                id = :id
            SQL;

			$statement = $conexion->prepare($query);
			$statement->bindValue(':id_empleado_turno', $this->getId_empleado_turno(), PDO::PARAM_INT);
			$statement->bindValue(':fecha_inicio', $this->getFecha_inicio(), PDO::PARAM_STR);
			$statement->bindValue(':fecha_fin', $this->getFecha_fin(), $this->getFecha_fin() === null ? PDO::PARAM_NULL : PDO::PARAM_STR);
			$statement->bindValue(':estado', $this->getEstado(), PDO::PARAM_INT);
			$statement->bindValue(':razon_cancelacion', $this->getRazon_cancelacion(), $this->getRazon_cancelacion() === null ? PDO::PARAM_NULL : PDO::PARAM_STR);
			$statement->bindValue(':fecha_cancelacion', $this->getFecha_cancelacion(), $this->getFecha_cancelacion() === null ? PDO::PARAM_NULL : PDO::PARAM_STR);
			$statement->bindValue(':id_metodo_pago', $this->getId_metodo_pago(), $this->getId_metodo_pago() === null ? PDO::PARAM_NULL : PDO::PARAM_INT);
			$statement->bindValue(':valor_comision_empleado', $this->getValor_comision_empleado(), PDO::PARAM_STR);

			// Handle null values for id_cierre
			$id_cierre = $this->getId_cierre();
			$statement->bindValue(':id_cierre', $id_cierre, $id_cierre === null ? PDO::PARAM_NULL : PDO::PARAM_INT);
			$statement->bindValue(':id', $this->getId(), PDO::PARAM_INT);

			// execute() devuelve true en éxito, false en error.
			return $statement->execute();

		} catch (PDOException $e) {
			// Error de base de datos
			throw new Exception("Error de base de datos al modificar cita (ID: {$this->getId()}): " . $e->getMessage());
		}
	}

	/**
	 * Elimina una cita de la base de datos.
	 *
	 * @param int $id       ID de la cita a eliminar.
	 * @param PDO $conexion Conexión PDO.
	 *
	 * @return bool True si la eliminación fue exitosa, False en caso contrario.
	 * @throws Exception Si ocurre un error de base de datos.
	 */
	public static function eliminar(int $id, PDO $conexion): bool
	{
		try {
			// Primero eliminar las relaciones en la tabla citas_servicios
			$query = <<<SQL
            DELETE FROM citas_servicios
            WHERE
            	id_cita = :id_cita
            SQL;

			$statement = $conexion->prepare($query);
			$statement->bindValue(':id_cita', $id, PDO::PARAM_INT);
			$statement->execute();

			// Luego eliminar la cita
			$query = <<<SQL
            DELETE FROM citas
            WHERE
            	id = :id
            SQL;

			$statement = $conexion->prepare($query);
			$statement->bindValue(':id', $id, PDO::PARAM_INT);

			// execute() devuelve true en éxito, false en error.
			return $statement->execute();

		} catch (PDOException $e) {
			// Error de base de datos
			throw new Exception("Error de base de datos al eliminar cita (ID: $id): " . $e->getMessage());
		}
	}

	// --- Getters y Setters ---

	public function getId(): ?int
	{
		return $this->id;
	}

	public function setId(?int $id): self
	{
		$this->id = $id;
		return $this;
	}

	public function getId_empleado_turno(): ?int
	{
		return $this->id_empleado_turno;
	}

	public function setId_empleado_turno(?int $id_empleado_turno): self
	{
		$this->id_empleado_turno = $id_empleado_turno;
		return $this;
	}

	public function getId_metodo_pago(): ?int
	{
		return $this->id_metodo_pago;
	}

	public function setId_metodo_pago(?int $id_metodo_pago): self
	{
		$this->id_metodo_pago = $id_metodo_pago;
		return $this;
	}

	public function getNombre_metodo_pago(): ?string
	{
		return $this->nombre_metodo_pago;
	}

	public function setNombre_metodo_pago(?string $nombre_metodo_pago): self
	{
		$this->nombre_metodo_pago = $nombre_metodo_pago;
		return $this;
	}

	public function getFecha_inicio(): ?string
	{
		return $this->fecha_inicio;
	}

	public function setFecha_inicio(?string $fecha_inicio): self
	{
		$this->fecha_inicio = $fecha_inicio;
		return $this;
	}

	public function getFecha_fin(): ?string
	{
		return $this->fecha_fin;
	}

	public function setFecha_fin(?string $fecha_fin): self
	{
		$this->fecha_fin = $fecha_fin;
		return $this;
	}

	public function getNombre_empleado(): ?string
	{
		return $this->nombre_empleado;
	}

	public function setNombre_empleado(?string $nombre_empleado): self
	{
		$this->nombre_empleado = $nombre_empleado;
		return $this;
	}

	public function getDescripcion_puesto(): ?string
	{
		return $this->descripcion_puesto;
	}

	public function setDescripcion_puesto(?string $descripcion_puesto): self
	{
		$this->descripcion_puesto = $descripcion_puesto;
		return $this;
	}

	public function getEstado(): ?int
	{
		return $this->estado;
	}

	public function setEstado(?int $estado): self
	{
		$this->estado = $estado;
		return $this;
	}

	public function getRazon_cancelacion(): ?string
	{
		return $this->razon_cancelacion;
	}

	public function setRazon_cancelacion(?string $razon_cancelacion): self
	{
		$this->razon_cancelacion = $razon_cancelacion;
		return $this;
	}

	public function getFecha_cancelacion(): ?string
	{
		return $this->fecha_cancelacion;
	}

	public function setFecha_cancelacion(?string $fecha_cancelacion): self
	{
		$this->fecha_cancelacion = $fecha_cancelacion;
		return $this;
	}

	public function getValor_comision_empleado(): ?float
	{
		return $this->valor_comision_empleado;
	}

	public function setValor_comision_empleado(?float $valor_comision_empleado): self
	{
		// Ensure it's a float or null, default to 0.0 if needed
		$this->valor_comision_empleado = $valor_comision_empleado ?? 0.0;
		return $this;
	}

	public function getId_cierre(): ?int
	{
		return $this->id_cierre;
	}

	public function setId_cierre(?int $id_cierre): self
	{
		$this->id_cierre = $id_cierre;
		return $this;
	}

	public function getNombre_centro_costo(): ?string
	{
		return $this->nombre_centro_costo;
	}

	public function setNombre_centro_costo(?string $nombre_centro_costo): self
	{
		$this->nombre_centro_costo = $nombre_centro_costo;
		return $this;
	}

	public function getTotal_valor_servicios(): ?float
	{
		return $this->total_valor_servicios;
	}

	public function setTotal_valor_servicios(?float $total_valor_servicios): self
	{
		$this->total_valor_servicios = $total_valor_servicios;
		return $this;
	}

	// --- Métodos para gestionar la relación con Servicios ---

	/**
	 * Agrega un servicio a la cita con un valor específico.
	 *
	 * @param int   $id_servicio ID del servicio a agregar.
	 * @param float $valor       Valor del servicio para esta cita.
	 * @param PDO   $conexion    Conexión PDO.
	 *
	 * @return bool True si la operación fue exitosa, False en caso contrario.
	 * @throws Exception Si el ID de la cita no es válido, el ID del servicio no es válido, el valor es negativo o si ocurre un error de base de datos.
	 */
	public function agregarServicio(int $id_servicio, float $valor, PDO $conexion): bool
	{
		// Validaciones básicas
		if ($this->getId() <= 0) {
			throw new Exception("La cita debe estar guardada en la base de datos antes de agregar servicios.");
		}

		if ($id_servicio <= 0) {
			throw new Exception("El ID del servicio debe ser un valor válido mayor que cero.");
		}

		if ($valor < 0) {
			throw new Exception("El valor del servicio no puede ser negativo.");
		}

		try {
			// Verificar si el servicio existe
			$servicio = Servicio::get($id_servicio, $conexion);
			if (!$servicio) {
				throw new Exception("El servicio con ID $id_servicio no existe.");
			}

			// Verificar si ya existe la relación
			$query = <<<SQL
            SELECT COUNT(*) as total
            FROM citas_servicios
            WHERE
            	id_cita = :id_cita AND id_servicio = :id_servicio
            SQL;

			$statement = $conexion->prepare($query);
			$statement->bindValue(':id_cita', $this->getId(), PDO::PARAM_INT);
			$statement->bindValue(':id_servicio', $id_servicio, PDO::PARAM_INT);
			$statement->execute();
			$resultado = $statement->fetch(PDO::FETCH_ASSOC);

			if ($resultado['total'] > 0) {
				// Ya existe la relación, actualizar el valor
				$query = <<<SQL
                UPDATE citas_servicios SET
                	valor = :valor
                WHERE
                	id_cita = :id_cita AND id_servicio = :id_servicio
                SQL;

				$statement = $conexion->prepare($query);
				$statement->bindValue(':valor', $valor, PDO::PARAM_STR);
				$statement->bindValue(':id_cita', $this->getId(), PDO::PARAM_INT);
				$statement->bindValue(':id_servicio', $id_servicio, PDO::PARAM_INT);

				return $statement->execute();
			} else {
				// No existe la relación, crear una nueva
				$query = <<<SQL
                INSERT INTO citas_servicios (
                	 id_cita
                	,id_servicio
                	,valor
                ) VALUES (
                	 :id_cita
                	,:id_servicio
                	,:valor
                )
                SQL;

				$statement = $conexion->prepare($query);
				$statement->bindValue(':id_cita', $this->getId(), PDO::PARAM_INT);
				$statement->bindValue(':id_servicio', $id_servicio, PDO::PARAM_INT);
				$statement->bindValue(':valor', $valor, PDO::PARAM_STR);

				return $statement->execute();
			}

		} catch (PDOException $e) {
			throw new Exception("Error de base de datos al agregar servicio a la cita: " . $e->getMessage());
		}
	}

	/**
	 * Elimina la asociación entre un servicio y la cita actual.
	 *
	 * @param int $id_servicio ID del servicio a eliminar de la cita.
	 * @param PDO $conexion    Conexión PDO.
	 *
	 * @return bool True si la operación fue exitosa, False en caso contrario.
	 * @throws Exception Si el ID de la cita no es válido, el ID del servicio no es válido o si ocurre un error de base de datos.
	 */
	public function eliminarServicio(int $id_servicio, PDO $conexion): bool
	{
		// Validaciones básicas
		if ($this->getId() <= 0) {
			throw new Exception("La cita debe estar guardada en la base de datos antes de eliminar servicios.");
		}

		if ($id_servicio <= 0) {
			throw new Exception("El ID del servicio debe ser un valor válido mayor que cero.");
		}

		try {
			// Eliminar la relación
			$query = <<<SQL
            DELETE FROM citas_servicios
            WHERE
            	id_cita = :id_cita AND id_servicio = :id_servicio
            SQL;

			$statement = $conexion->prepare($query);
			$statement->bindValue(':id_cita', $this->getId(), PDO::PARAM_INT);
			$statement->bindValue(':id_servicio', $id_servicio, PDO::PARAM_INT);

			return $statement->execute();

		} catch (PDOException $e) {
			throw new Exception("Error de base de datos al eliminar servicio de la cita: " . $e->getMessage());
		}
	}

	/**
	 * Obtiene todos los servicios asociados a la cita actual.
	 *
	 * @param PDO $conexion Conexión PDO.
	 *
	 * @return array Array de objetos Servicio con información adicional del valor específico para esta cita.
	 * @throws Exception Si el ID de la cita no es válido o si ocurre un error de base de datos.
	 */
	public function getServicios(PDO $conexion): array
	{
		// Validaciones básicas
		if ($this->getId() <= 0) {
			throw new Exception("La cita debe estar guardada en la base de datos para obtener sus servicios.");
		}

		try {
			// Consulta para obtener los servicios asociados a la cita
			$query = <<<SQL
            SELECT
            	s.*,
            	cs.valor as valor_cita,
            	cs.id as id_cita_servicio
            FROM citas_servicios cs
            JOIN servicios s ON cs.id_servicio = s.id
            WHERE
            	cs.id_cita = :id_cita
            ORDER BY
            	s.descripcion
            SQL;

			$statement = $conexion->prepare($query);
			$statement->bindValue(':id_cita', $this->getId(), PDO::PARAM_INT);
			$statement->execute();
			$resultados = $statement->fetchAll(PDO::FETCH_ASSOC);

			$servicios = [];
			foreach ($resultados as $resultado) {
				$servicio = Servicio::construct($resultado);
				// Agregar el valor específico para esta cita
				$servicio->setValor((float)$resultado['valor_cita']);
				$servicios[] = $servicio;
			}

			return $servicios;

		} catch (PDOException $e) {
			throw new Exception("Error de base de datos al obtener servicios de la cita: " . $e->getMessage());
		}
	}

	/**
	 * Calcula el valor total de todos los servicios asociados a la cita.
	 *
	 * @param PDO $conexion Conexión PDO.
	 *
	 * @return float Valor total de los servicios.
	 * @throws Exception Si el ID de la cita no es válido o si ocurre un error de base de datos.
	 */
	public function getTotalValorServicios(PDO $conexion): float
	{
		// Validaciones básicas
		if ($this->getId() <= 0) {
			throw new Exception("La cita debe estar guardada en la base de datos para calcular el total.");
		}

		try {
			// Consulta para obtener la suma de los valores de los servicios
			$query = <<<SQL
            SELECT
            	SUM(valor) as total
            FROM citas_servicios
            WHERE
            	id_cita = :id_cita
            SQL;

			$statement = $conexion->prepare($query);
			$statement->bindValue(':id_cita', $this->getId(), PDO::PARAM_INT);
			$statement->execute();
			$resultado = $statement->fetch(PDO::FETCH_ASSOC);

			return $resultado['total'] ? (float)$resultado['total'] : 0.0;

		} catch (PDOException $e) {
			throw new Exception("Error de base de datos al calcular el total de servicios: " . $e->getMessage());
		}
	}

	/**
	 * Finaliza una cita estableciendo la fecha_fin, el método de pago y calculando la comisión del empleado.
	 *
	 * @param int    $id             ID de la cita a finalizar.
	 * @param string $fecha_fin      Fecha de finalización de la cita.
	 * @param int    $id_metodo_pago ID del método de pago utilizado.
	 * @param PDO    $conexion       Conexión PDO.
	 *
	 * @return bool True si la finalización fue exitosa, False en caso contrario.
	 * @throws Exception Si ocurre un error de base de datos.
	 */
	public static function finalizar(int $id, string $fecha_fin, int $id_metodo_pago, PDO $conexion): bool
	{
		try {
			// Establecer la zona horaria para Colombia
			date_default_timezone_set('America/Bogota');

			// Verificar que la cita exista
			$cita = self::get($id, $conexion, true); // Incluir canceladas para validación completa
			if (!$cita) {
				throw new Exception("La cita con ID $id no existe.");
			}

			// Verificar que la cita no esté cancelada
			if ($cita->getEstado() === 0) {
				throw new Exception("No se puede finalizar una cita que ha sido cancelada.");
			}

			// Verificar que la cita no esté ya finalizada
			if ($cita->getFecha_fin() !== null) {
				throw new Exception("La cita ya ha sido finalizada.");
			}

			// Verificar que la fecha de fin sea posterior a la fecha de inicio
			if (strtotime($fecha_fin) <= strtotime($cita->getFecha_inicio())) {
				throw new Exception("La fecha de finalización debe ser posterior a la fecha de inicio de la cita.");
			}

			// Calcular la comisión del empleado
			$valorComision = 0.0;
			try {
				$valorComision = $cita->calcularComisionEmpleado($conexion);
			} catch (Exception $e) {
				// Si hay error calculando la comisión, continuar con valor 0 pero registrar el error
				error_log("Error calculando comisión para cita ID $id: " . $e->getMessage());
			}

			// Consulta para actualizar la fecha_fin, el método de pago y la comisión
			$query = <<<SQL
            UPDATE citas SET
            	fecha_fin = :fecha_fin,
            	id_metodo_pago = :id_metodo_pago,
            	valor_comision_empleado = :valor_comision_empleado
            WHERE
            	id = :id
            SQL;

			$statement = $conexion->prepare($query);
			$statement->bindValue(':fecha_fin', $fecha_fin, PDO::PARAM_STR);
			$statement->bindValue(':id_metodo_pago', $id_metodo_pago, PDO::PARAM_INT);
			$statement->bindValue(':valor_comision_empleado', $valorComision, PDO::PARAM_STR);
			$statement->bindValue(':id', $id, PDO::PARAM_INT);

			// execute() devuelve true en éxito, false en error.
			return $statement->execute();

		} catch (PDOException $e) {
			// Error de base de datos
			throw new Exception("Error de base de datos al finalizar cita (ID: $id): " . $e->getMessage());
		}
	}

	/**
	 * Cancela una cita estableciendo el estado a 0, la fecha_cancelacion al timestamp actual
	 * y guardando la razón de cancelación proporcionada.
	 *
	 * @param int    $id                ID de la cita a cancelar.
	 * @param string $razon_cancelacion Razón por la que se cancela la cita (obligatorio).
	 * @param PDO    $conexion          Conexión PDO.
	 *
	 * @return bool True si la cancelación fue exitosa, False en caso contrario.
	 * @throws Exception Si ocurre un error de base de datos o si faltan datos requeridos.
	 */
	public static function cancelar(int $id, string $razon_cancelacion, PDO $conexion): bool
	{
		// Validar que la razón de cancelación no esté vacía
		if (empty($razon_cancelacion)) {
			throw new Exception("La razón de cancelación es obligatoria para cancelar una cita.");
		}

		try {
			// Establecer la zona horaria para Colombia
			date_default_timezone_set('America/Bogota');
			$fecha_cancelacion = date('Y-m-d H:i:s');

			// Verificar que la cita exista
			$cita = self::get($id, $conexion, true); // Incluir canceladas para validación completa
			if (!$cita) {
				throw new Exception("La cita con ID $id no existe.");
			}

			// Verificar que la cita no esté ya cancelada
			if ($cita->getEstado() === 0) {
				throw new Exception("La cita ya ha sido cancelada.");
			}

			// Verificar que la cita no esté finalizada
			if ($cita->getFecha_fin() !== null) {
				throw new Exception("No se puede cancelar una cita que ya ha sido finalizada.");
			}

			// Consulta para actualizar el estado, fecha_cancelacion y razon_cancelacion
			$query = <<<SQL
            UPDATE citas SET
            	estado = 0,
            	fecha_cancelacion = :fecha_cancelacion,
            	razon_cancelacion = :razon_cancelacion
            WHERE
            	id = :id
            SQL;

			$statement = $conexion->prepare($query);
			$statement->bindValue(':fecha_cancelacion', $fecha_cancelacion, PDO::PARAM_STR);
			$statement->bindValue(':razon_cancelacion', $razon_cancelacion, PDO::PARAM_STR);
			$statement->bindValue(':id', $id, PDO::PARAM_INT);

			// execute() devuelve true en éxito, false en error.
			return $statement->execute();

		} catch (PDOException $e) {
			// Error de base de datos
			throw new Exception("Error de base de datos al cancelar cita (ID: $id): " . $e->getMessage());
		}
	}

	// --- Métodos adicionales ---

	/**
	 * Calcula la duración de la cita en minutos.
	 *
	 * @return int|null Duración en minutos o null si no hay fechas definidas.
	 */
	public function getDuracionMinutos(): ?int
	{
		if (empty($this->fecha_inicio) || empty($this->fecha_fin)) {
			return null;
		}

		$inicio = strtotime($this->fecha_inicio);
		$fin    = strtotime($this->fecha_fin);

		if ($inicio === false || $fin === false) {
			return null;
		}

		return (int)(($fin - $inicio) / 60);
	}

	/**
	 * Obtiene el porcentaje de comisión del empleado asociado a esta cita.
	 *
	 * @param PDO $conexion Conexión PDO.
	 *
	 * @return float Porcentaje de comisión del empleado (0.0 si no se encuentra).
	 * @throws Exception Si el ID de la cita no es válido o si ocurre un error de base de datos.
	 */
	public function getPorcentajeComisionEmpleado(PDO $conexion): float
	{
		// Validaciones básicas
		if ($this->getId() <= 0) {
			throw new Exception("La cita debe estar guardada en la base de datos para obtener la comisión del empleado.");
		}

		if ($this->getId_empleado_turno() === null) {
			throw new Exception("La cita debe tener un turno de empleado asociado.");
		}

		try {
			// Consulta para obtener el porcentaje de comisión del empleado
			$query = <<<SQL
            SELECT
                e.porc_comision
            FROM empleados_turnos et
            JOIN empleados e ON et.id_empleado = e.id
            WHERE
                et.id = :id_empleado_turno
            LIMIT 1
            SQL;

			$statement = $conexion->prepare($query);
			$statement->bindValue(':id_empleado_turno', $this->getId_empleado_turno(), PDO::PARAM_INT);
			$statement->execute();
			$resultado = $statement->fetch(PDO::FETCH_ASSOC);

			return $resultado ? (float)$resultado['porc_comision'] : 0.0;

		} catch (PDOException $e) {
			throw new Exception("Error de base de datos al obtener porcentaje de comisión: " . $e->getMessage());
		}
	}

	/**
	 * Calcula y establece la comisión del empleado basada en el valor total de los servicios.
	 * Fórmula: round(total_value_of_cita * (Empleado.porc_comision / 100), 0)
	 *
	 * @param PDO $conexion Conexión PDO.
	 *
	 * @return float El valor de la comisión calculada.
	 * @throws Exception Si el ID de la cita no es válido o si ocurre un error de base de datos.
	 */
	public function calcularComisionEmpleado(PDO $conexion): float
	{
		// Validaciones básicas
		if ($this->getId() <= 0) {
			throw new Exception("La cita debe estar guardada en la base de datos para calcular la comisión.");
		}

		try {
			// Obtener el valor total de los servicios
			$valorTotal = $this->getTotalValorServicios($conexion);

			// Obtener el porcentaje de comisión del empleado
			$porcentajeComision = $this->getPorcentajeComisionEmpleado($conexion);

			// Calcular la comisión: round(total_value * (porc_comision / 100), 0)
			$valorComision = round($valorTotal * ($porcentajeComision / 100), 0);

			// Establecer el valor calculado en el objeto
			$this->setValor_comision_empleado($valorComision);

			return $valorComision;

		} catch (Exception $e) {
			throw new Exception("Error al calcular comisión del empleado: " . $e->getMessage());
		}
	}

	/**
	 * Actualiza el id_cierre para múltiples citas en una sola operación.
	 *
	 * @param array $ids_citas Array de IDs de citas a actualizar.
	 * @param int   $id_cierre ID del cierre a asignar.
	 * @param PDO   $conexion  Conexión PDO.
	 *
	 * @return bool True si la actualización fue exitosa, False en caso contrario.
	 * @throws Exception Si hay error en DB.
	 */
	public static function actualizarIdCierreBulk(array $ids_citas, int $id_cierre, PDO $conexion): bool
	{
		if (empty($ids_citas)) {
			return true; // No hay nada que actualizar
		}

		try {
			// Validar y sanitizar todos los IDs para prevenir inyección SQL
			$ids_validados = [];
			foreach ($ids_citas as $id) {
				$id_int = filter_var($id, FILTER_VALIDATE_INT, [
					'options' => ['min_range' => 1]
				]);
				if ($id_int === false) {
					throw new Exception("ID de cita inválido detectado: " . var_export($id, true));
				}
				$ids_validados[] = $id_int;
			}

			// Crear placeholders seguros para los IDs validados
			$placeholders = str_repeat('?,', count($ids_validados) - 1) . '?';

			// Consulta para actualizar el id_cierre en múltiples citas
			$query = <<<SQL
            UPDATE citas SET
                id_cierre = ?
            WHERE
                id IN ($placeholders)
                AND estado = 1
            SQL;

			$statement = $conexion->prepare($query);

			// Bind del id_cierre primero, luego los IDs validados
			$params = [$id_cierre];
			$params = array_merge($params, $ids_validados);

			return $statement->execute($params);

		} catch (PDOException $e) {
			throw new Exception("Error de base de datos al actualizar id_cierre en citas: " . $e->getMessage());
		}
	}

	/**
	 * Obtiene las citas asociadas a un cierre específico.
	 *
	 * @param int $id_cierre ID del cierre.
	 * @param PDO $conexion  Conexión PDO.
	 *
	 * @return array Array de objetos Cita con información completa.
	 * @throws Exception Si hay error en DB.
	 */
	public static function getCitasByCierre(int $id_cierre, PDO $conexion): array
	{
		try {
			// Consulta para obtener citas por cierre con información completa
			$query = <<<SQL
			SELECT
				c.*,
				mp.descripcion AS nombre_metodo_pago,
				e.nombre AS nombre_empleado,
				p.descripcion AS descripcion_puesto,
				(
					SELECT SUM(cs.valor)
					FROM citas_servicios cs
					WHERE cs.id_cita = c.id
				) as total_valor_servicios
			FROM citas c
			LEFT JOIN metodos_pagos mp ON c.id_metodo_pago = mp.id
			LEFT JOIN empleados_turnos et ON c.id_empleado_turno = et.id
			LEFT JOIN empleados e ON et.id_empleado = e.id
			LEFT JOIN puestos p ON et.id_puesto = p.id
			WHERE c.id_cierre = :id_cierre
			ORDER BY c.fecha_inicio DESC
			SQL;

			$statement = $conexion->prepare($query);
			$statement->bindValue(':id_cierre', $id_cierre, PDO::PARAM_INT);
			$statement->execute();
			$resultados = $statement->fetchAll(PDO::FETCH_ASSOC);

			$listado = [];
			foreach ($resultados as $resultado) {
				$cita = self::construct($resultado);
				$listado[] = $cita;
			}

			return $listado;

		} catch (PDOException $e) {
			throw new Exception("Error al obtener citas por cierre: " . $e->getMessage());
		}
	}

	/**
	 * Calcula los ingresos totales de citas para una fecha específica y centro de costo.
	 * Solo incluye citas finalizadas (con cierre asignado).
	 *
	 * @param string $fecha           Fecha a consultar (formato YYYY-MM-DD).
	 * @param int    $id_centro_costo ID del centro de costo.
	 * @param PDO    $conexion        Conexión PDO.
	 *
	 * @return float Total de ingresos de citas para la fecha y centro de costo.
	 * @throws Exception Si hay error en DB.
	 */
	public static function calcularIngresosPorFecha(string $fecha, int $id_centro_costo, PDO $conexion): float
	{
		try {
			$query = <<<SQL
			SELECT COALESCE(SUM(cs.valor), 0) as total
			FROM citas c
			INNER JOIN empleados_turnos et ON c.id_empleado_turno = et.id
			INNER JOIN puestos p ON et.id_puesto = p.id
			INNER JOIN citas_servicios cs ON c.id = cs.id_cita
			WHERE DATE(c.fecha_fin) = :fecha
			AND c.estado = 1
			AND c.id_cierre IS NOT NULL
			AND p.id_centro_costo = :id_centro_costo
			SQL;

			$statement = $conexion->prepare($query);
			$statement->bindValue(':fecha', $fecha, PDO::PARAM_STR);
			$statement->bindValue(':id_centro_costo', $id_centro_costo, PDO::PARAM_INT);
			$statement->execute();
			$resultado = $statement->fetch(PDO::FETCH_ASSOC);

			return (float)($resultado['total'] ?? 0);

		} catch (PDOException $e) {
			throw new Exception("Error al calcular ingresos de citas por fecha ($fecha): " . $e->getMessage());
		}
	}

	/**
	 * Obtiene los registros detallados de citas para una fecha específica y centro de costo.
	 * Solo incluye citas finalizadas (con cierre asignado).
	 *
	 * @param string $fecha           Fecha a consultar (formato YYYY-MM-DD).
	 * @param int    $id_centro_costo ID del centro de costo.
	 * @param PDO    $conexion        Conexión PDO.
	 *
	 * @return array Array de registros de citas con detalles.
	 * @throws Exception Si hay error en DB.
	 */
	public static function obtenerRegistrosPorFecha(string $fecha, int $id_centro_costo, PDO $conexion): array
	{
		try {
			$query = <<<SQL
			SELECT
				c.id,
				c.fecha_inicio,
				c.fecha_fin,
				e.nombre AS nombre_empleado,
				mp.descripcion AS metodo_pago,
				COALESCE(SUM(cs.valor), 0) as valor_total
			FROM citas c
			INNER JOIN empleados_turnos et ON c.id_empleado_turno = et.id
			INNER JOIN empleados e ON et.id_empleado = e.id
			INNER JOIN puestos p ON et.id_puesto = p.id
			LEFT JOIN metodos_pagos mp ON c.id_metodo_pago = mp.id
			LEFT JOIN citas_servicios cs ON c.id = cs.id_cita
			WHERE DATE(c.fecha_fin) = :fecha
			AND c.estado = 1
			AND c.id_cierre IS NOT NULL
			AND p.id_centro_costo = :id_centro_costo
			GROUP BY c.id, c.fecha_inicio, c.fecha_fin, e.nombre, mp.descripcion
			ORDER BY c.fecha_fin DESC
			SQL;

			$statement = $conexion->prepare($query);
			$statement->bindValue(':fecha', $fecha, PDO::PARAM_STR);
			$statement->bindValue(':id_centro_costo', $id_centro_costo, PDO::PARAM_INT);
			$statement->execute();
			$resultados = $statement->fetchAll(PDO::FETCH_ASSOC);

			// Format results
			$registros_formateados = [];
			foreach ($resultados as $resultado) {
				$registros_formateados[] = [
					'id' => $resultado['id'],
					'fecha_inicio' => $resultado['fecha_inicio'],
					'fecha_fin' => $resultado['fecha_fin'],
					'nombre_empleado' => $resultado['nombre_empleado'],
					'metodo_pago' => $resultado['metodo_pago'] ?? 'No especificado',
					'valor_total' => (float)$resultado['valor_total'],
					'valor_total_formateado' => '$' . number_format((float)$resultado['valor_total'], 0, ',', '.')
				];
			}

			return $registros_formateados;

		} catch (PDOException $e) {
			throw new Exception("Error al obtener registros de citas por fecha ($fecha): " . $e->getMessage());
		}
	}

	/**
	 * Valida que el empleado asociado a esta cita pueda realizar todos los servicios especificados.
	 * Método privado para validación interna de la clase.
	 *
	 * @param PDO   $conexion     Conexión PDO.
	 * @param array $serviciosIds Array de IDs de servicios a validar.
	 *
	 * @throws Exception Si el empleado no puede realizar alguno de los servicios.
	 */
	private function validarServiciosEmpleado(PDO $conexion, array $serviciosIds): void
	{
		if (empty($serviciosIds)) {
			return; // No hay servicios que validar
		}

		try {
			// Obtener el empleado asociado al turno
			$empleadoTurno = EmpleadoTurno::get($this->getId_empleado_turno(), $conexion);
			if (!$empleadoTurno) {
				throw new Exception("No se pudo obtener información del turno del empleado.");
			}

			$empleado = Empleado::get($empleadoTurno->getId_empleado(), $conexion);
			if (!$empleado) {
				throw new Exception("No se pudo obtener información del empleado.");
			}

			// Validar que el empleado puede realizar todos los servicios
			$validacion = $empleado->validarServiciosAsociados($serviciosIds, $conexion);

			if (!$validacion['valido']) {
				// Obtener nombres de los servicios faltantes para el mensaje de error
				$serviciosFaltantes = $validacion['servicios_faltantes'];
				$nombresServicios = Empleado::obtenerNombresServicios($serviciosFaltantes, $conexion);

				$serviciosTexto = [];
				foreach ($serviciosFaltantes as $idServicio) {
					$serviciosTexto[] = $nombresServicios[$idServicio] ?? "Servicio ID: $idServicio";
				}

				$mensaje = "El empleado '{$empleado->getNombre()}' no puede realizar " .
						  (count($serviciosTexto) === 1 ? "el servicio" : "los servicios") . ": " .
						  implode(", ", $serviciosTexto) . ". " .
						  "Por favor, seleccione un empleado diferente o actualice los servicios del empleado.";

				throw new Exception($mensaje);
			}

		} catch (Exception $e) {
			// Re-lanzar la excepción para que sea manejada por el código que llama
			throw $e;
		}
	}
}
