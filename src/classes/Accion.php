<?php

declare(strict_types=1);

namespace App\classes;

use Exception;
use PDO;
use PDOException;

class Accion
{
	// --- Atributos ---
	private ?int    $id         = null;
	private ?string $nombre     = null;
	private ?string $grupo      = null;
	private ?int    $id_menu    = null;
	private ?int    $id_submenu = null;
	private ?int    $estado     = null;

	// --- Propiedades adicionales para la vista (no se almacenan en la base de datos) ---
	private ?string $nombre_menu    = null;
	private ?string $nombre_submenu = null;
	private ?int    $id_perfil_accion = null; // ID de la relación en la tabla perfiles_acciones

	/**
	 * Constructor: Inicializa las propiedades del objeto Accion.
	 */
	public function __construct()
	{
		$this->id         = 0;
		$this->nombre     = null;
		$this->grupo      = null;
		$this->id_menu    = null;
		$this->id_submenu = null;
		$this->estado     = 1; // Estado activo por defecto
	}

	/**
	 * Método estático para construir un objeto Accion desde un array (ej. fila de DB).
	 *
	 * @param array $resultado Array asociativo con los datos de la acción.
	 *
	 * @return self Instancia de Accion.
	 * @throws Exception Si ocurre un error durante la construcción.
	 */
	public static function construct(array $resultado = []): self
	{
		try {
			$objeto                 = new self();
			$objeto->id             = isset($resultado['id']) ? (int)$resultado['id'] : 0;
			$objeto->nombre         = $resultado['nombre'] ?? null;
			$objeto->grupo          = $resultado['grupo'] ?? null;
			$objeto->id_menu        = isset($resultado['id_menu']) ? (int)$resultado['id_menu'] : null;
			$objeto->id_submenu     = isset($resultado['id_submenu']) ? (int)$resultado['id_submenu'] : null;
			$objeto->estado         = isset($resultado['estado']) ? (int)$resultado['estado'] : 1;
			$objeto->setNombreMenu($resultado['nombre_menu'] ?? null);
			$objeto->setNombreSubmenu($resultado['nombre_submenu'] ?? null);

			return $objeto;
		} catch (Exception $e) {
			throw new Exception("Error al construir objeto Accion: " . $e->getMessage());
		}
	}

	/**
	 * Obtiene una acción por su ID.
	 *
	 * @param int $id       ID de la acción.
	 * @param PDO $conexion Conexión PDO.
	 *
	 * @return self|null Objeto Accion o null si no se encuentra.
	 * @throws Exception Si hay error en DB.
	 */
	public static function get(int $id, PDO $conexion): ?self
	{
		try {
			// Consulta para obtener acción por ID (Usando Heredoc)
			$query = <<<SQL
            SELECT
            	*
            FROM acciones
            WHERE
            	id = :id
            LIMIT 1
            SQL;

			$statement = $conexion->prepare($query);
			$statement->bindValue(":id", $id, PDO::PARAM_INT);
			$statement->execute();
			$resultado = $statement->fetch(PDO::FETCH_ASSOC);

			return $resultado ? self::construct($resultado) : null;

		} catch (PDOException $e) {
			throw new Exception("Error al obtener Accion: " . $e->getMessage());
		}
	}

	/**
	 * Obtiene una lista de acciones activas.
	 *
	 * @param PDO   $conexion   Conexión PDO.
	 * @param array $parametros Parámetros opcionales para filtrar la consulta.
	 *
	 * @return array Array de objetos Accion.
	 * @throws Exception Si hay error en DB.
	 */
	public static function get_list(PDO $conexion, array $parametros = []): array
	{
		try {
			$id_menu           = $parametros['id_menu'] ?? null;
			$id_submenu        = $parametros['id_submenu'] ?? null;
			$grupo             = $parametros['grupo'] ?? null;
			$incluir_inactivas = $parametros['incluir_inactivas'] ?? false;

			// Consulta base para obtener lista de acciones
			$query = "SELECT a.*, m.texto AS nombre_menu, s.texto AS nombre_submenu
					FROM acciones a
					LEFT JOIN menus m ON a.id_menu = m.id
					LEFT JOIN submenus s ON a.id_submenu = s.id";

			// Condición de estado
			if (!$incluir_inactivas) {
				$query .= " WHERE a.estado = 1";
			} else {
				$query .= " WHERE 1=1";
			}
			$params = [];

			// Si se proporciona un id_menu, filtrar por él
			if ($id_menu !== null) {
				$query              .= " AND a.id_menu = :id_menu";
				$params[':id_menu'] = $id_menu;
			}

			// Si se proporciona un id_submenu, filtrar por él
			if ($id_submenu !== null) {
				$query                 .= " AND a.id_submenu = :id_submenu";
				$params[':id_submenu'] = $id_submenu;
			}

			// Si se proporciona un grupo, filtrar por él
			if ($grupo !== null) {
				$query            .= " AND a.grupo = :grupo";
				$params[':grupo'] = $grupo;
			}

			// Ordenar por nombre
			$query .= " ORDER BY a.grupo, a.nombre";

			$statement = $conexion->prepare($query);

			// Bind de parámetros
			foreach ($params as $param => $value) {
				$type = is_int($value) ? PDO::PARAM_INT : PDO::PARAM_STR;
				$statement->bindValue($param, $value, $type);
			}

			$statement->execute();
			$resultados = $statement->fetchAll(PDO::FETCH_ASSOC);

			$listado = [];
			foreach ($resultados as $resultado) {
				$accion    = self::construct($resultado);
				$listado[] = $accion;
			}
			return $listado;

		} catch (PDOException $e) {
			throw new Exception("Error al obtener lista de Acciones: " . $e->getMessage());
		}
	}

	/**
	 * Crea una nueva acción en la base de datos a partir de un objeto Accion.
	 *
	 * @param PDO $conexion Conexión PDO.
	 *
	 * @return int|false El ID de la nueva acción creada o false en caso de error.
	 * @throws Exception Si los datos requeridos en el objeto están vacíos o hay error en DB.
	 */
	function crear(PDO $conexion): int|false
	{
		// Validaciones básicas sobre el objeto
		if (empty($this->getNombre())) {
			throw new Exception("Nombre es requerido en el objeto Accion para crearlo.");
		}

		try {
			// Preparar la consulta INSERT usando Heredoc
			$query = <<<SQL
            INSERT INTO acciones (
            	 nombre
            	,grupo
            	,id_menu
            	,id_submenu
            	,estado
            ) VALUES (
            	 :nombre
            	,:grupo
            	,:id_menu
            	,:id_submenu
            	,:estado
            )
            SQL;

			$statement = $conexion->prepare($query);

			// Bind de parámetros desde el objeto
			$statement->bindValue(':nombre', $this->getNombre(), PDO::PARAM_STR);
			$statement->bindValue(':grupo', $this->getGrupo(), PDO::PARAM_STR);
			$statement->bindValue(':id_menu', $this->getIdMenu(), $this->getIdMenu() === null ? PDO::PARAM_NULL : PDO::PARAM_INT);
			$statement->bindValue(':id_submenu', $this->getIdSubmenu(), $this->getIdSubmenu() === null ? PDO::PARAM_NULL : PDO::PARAM_INT);
			$statement->bindValue(':estado', $this->getEstado(), PDO::PARAM_INT);

			// Ejecutar la consulta
			$success = $statement->execute();

			if ($success) {
				// Devolver el ID de la acción recién creada
				return (int)$conexion->lastInsertId();
			} else {
				return false; // Error en la ejecución
			}

		} catch (PDOException $e) {
			throw new Exception("Error de base de datos al crear acción: " . $e->getMessage());
		} catch (Exception $e) {
			// Capturar otros errores
			throw new Exception("Error al crear acción: " . $e->getMessage());
		}
	}

	/**
	 * Actualiza una acción existente.
	 *
	 * @param PDO $conexion Conexión PDO.
	 *
	 * @return bool True si la actualización fue exitosa, false en caso contrario.
	 * @throws Exception Si hay error en DB.
	 */
	function modificar(PDO $conexion): bool
	{
		// Validar que el ID exista
		if (empty($this->getId())) {
			throw new Exception("ID es requerido para actualizar una Accion.");
		}

		// Validar que el nombre no esté vacío
		if (empty($this->getNombre())) {
			throw new Exception("Nombre es requerido para actualizar una Accion.");
		}

		try {
			// Consulta para actualizar la acción
			$query = <<<SQL
            UPDATE acciones SET
                nombre = :nombre,
                grupo = :grupo,
                id_menu = :id_menu,
                id_submenu = :id_submenu,
                estado = :estado
            WHERE
                id = :id
            SQL;

			$statement = $conexion->prepare($query);
			$statement->bindValue(':nombre', $this->getNombre(), PDO::PARAM_STR);
			$statement->bindValue(':grupo', $this->getGrupo(), PDO::PARAM_STR);
			$statement->bindValue(':id_menu', $this->getIdMenu(), $this->getIdMenu() === null ? PDO::PARAM_NULL : PDO::PARAM_INT);
			$statement->bindValue(':id_submenu', $this->getIdSubmenu(), $this->getIdSubmenu() === null ? PDO::PARAM_NULL : PDO::PARAM_INT);
			$statement->bindValue(':estado', $this->getEstado(), PDO::PARAM_INT);
			$statement->bindValue(':id', $this->getId(), PDO::PARAM_INT);

			return $statement->execute();

		} catch (PDOException $e) {
			throw new Exception("Error de base de datos al modificar acción (ID: {$this->getId()}): " . $e->getMessage());
		}
	}

	/**
	 * Desactiva una acción por su ID.
	 *
	 * @param int $id       ID de la acción a desactivar.
	 * @param PDO $conexion Conexión PDO.
	 *
	 * @return bool True si la desactivación fue exitosa, false en caso contrario.
	 * @throws Exception Si hay error en DB.
	 */
	public static function desactivar(int $id, PDO $conexion): bool
	{
		try {
			// Consulta para desactivar la acción
			$query = <<<SQL
            UPDATE acciones SET
                estado = 0
            WHERE
                id = :id
            SQL;

			$statement = $conexion->prepare($query);
			$statement->bindValue(':id', $id, PDO::PARAM_INT);

			return $statement->execute();

		} catch (PDOException $e) {
			throw new Exception("Error de base de datos al desactivar acción (ID: $id): " . $e->getMessage());
		}
	}

	/**
	 * Activa una acción por su ID.
	 *
	 * @param int $id       ID de la acción a activar.
	 * @param PDO $conexion Conexión PDO.
	 *
	 * @return bool True si la activación fue exitosa, false en caso contrario.
	 * @throws Exception Si hay error en DB.
	 */
	public static function activar(int $id, PDO $conexion): bool
	{
		try {
			// Consulta para activar la acción
			$query = <<<SQL
            UPDATE acciones SET
                estado = 1
            WHERE
                id = :id
            SQL;

			$statement = $conexion->prepare($query);
			$statement->bindValue(':id', $id, PDO::PARAM_INT);

			return $statement->execute();

		} catch (PDOException $e) {
			throw new Exception("Error de base de datos al activar acción (ID: $id): " . $e->getMessage());
		}
	}

	/**
	 * Obtiene el menú al que pertenece esta acción.
	 *
	 * @param PDO $conexion Conexión PDO.
	 *
	 * @return Menu|null El objeto Menu al que pertenece esta acción, o null si no existe.
	 * @throws Exception Si hay error en DB.
	 */
	public function getMenu(PDO $conexion): ?Menu
	{
		if (empty($this->id_menu)) {
			return null;
		}

		return Menu::get($this->id_menu, $conexion);
	}

	/**
	 * Obtiene el submenú al que pertenece esta acción.
	 *
	 * @param PDO $conexion Conexión PDO.
	 *
	 * @return Submenu|null El objeto Submenu al que pertenece esta acción, o null si no existe.
	 * @throws Exception Si hay error en DB.
	 */
	public function getSubmenu(PDO $conexion): ?Submenu
	{
		if (empty($this->id_submenu)) {
			return null;
		}

		return Submenu::get($this->id_submenu, $conexion);
	}

	// --- Getters y Setters ---

	public function getId(): ?int
	{
		return $this->id;
	}

	public function setId(?int $id): self
	{
		$this->id = $id;
		return $this;
	}

	public function getNombre(): ?string
	{
		return $this->nombre;
	}

	public function setNombre(?string $nombre): self
	{
		$this->nombre = $nombre;
		return $this;
	}

	public function getGrupo(): ?string
	{
		return $this->grupo;
	}

	public function setGrupo(?string $grupo): self
	{
		$this->grupo = $grupo;
		return $this;
	}

	public function getIdMenu(): ?int
	{
		return $this->id_menu;
	}

	public function setIdMenu(?int $id_menu): self
	{
		$this->id_menu = $id_menu;
		return $this;
	}

	public function getIdSubmenu(): ?int
	{
		return $this->id_submenu;
	}

	public function setIdSubmenu(?int $id_submenu): self
	{
		$this->id_submenu = $id_submenu;
		return $this;
	}

	public function getEstado(): ?int
	{
		return $this->estado;
	}

	public function setEstado(?int $estado): self
	{
		$this->estado = $estado;
		return $this;
	}

	/**
	 * Verifica si la acción está activa.
	 * @return bool
	 */
	public function isActiva(): bool
	{
		return $this->estado === 1;
	}

	/**
	 * Obtiene el nombre del menú al que pertenece esta acción.
	 * @return string|null
	 */
	public function getNombreMenu(): ?string
	{
		return $this->nombre_menu;
	}

	/**
	 * Establece el nombre del menú al que pertenece esta acción.
	 * @param string|null $nombre_menu
	 * @return self
	 */
	public function setNombreMenu(?string $nombre_menu): self
	{
		$this->nombre_menu = $nombre_menu;
		return $this;
	}

	/**
	 * Obtiene el nombre del submenú al que pertenece esta acción.
	 * @return string|null
	 */
	public function getNombreSubmenu(): ?string
	{
		return $this->nombre_submenu;
	}

	/**
	 * Establece el nombre del submenú al que pertenece esta acción.
	 * @param string|null $nombre_submenu
	 * @return self
	 */
	public function setNombreSubmenu(?string $nombre_submenu): self
	{
		$this->nombre_submenu = $nombre_submenu;
		return $this;
	}

	/**
	 * Obtiene el ID de la relación en la tabla perfiles_acciones.
	 * @return int|null
	 */
	public function getIdPerfilAccion(): ?int
	{
		return $this->id_perfil_accion;
	}

	/**
	 * Establece el ID de la relación en la tabla perfiles_acciones.
	 * @param int|null $id_perfil_accion
	 * @return self
	 */
	public function setIdPerfilAccion(?int $id_perfil_accion): self
	{
		$this->id_perfil_accion = $id_perfil_accion;
		return $this;
	}
}
