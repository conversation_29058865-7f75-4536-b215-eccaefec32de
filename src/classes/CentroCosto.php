<?php

declare(strict_types=1);

namespace App\classes;

use Exception;
use PDO;
use PDOException;

class CentroCosto
{
	// --- Atributos ---
	private ?int    $id     = null;
	private ?string $nombre = null;
	private ?int    $estado = null;

	// --- Propiedades adicionales (no persistentes) ---
	public ?int $user_count = null;
	public ?int $employee_count = null;

	/**
	 * Constructor: Inicializa las propiedades del objeto CentroCosto.
	 */
	public function __construct()
	{
		$this->id     = 0; // O null si prefieres no usar 0 por defecto
		$this->nombre = null;
		$this->estado = 1; // Estado activo por defecto
	}

	/**
	 * Método estático para construir un objeto CentroCosto desde un array (ej. fila de DB).
	 *
	 * @param array $resultado Array asociativo con los datos del centro de costo.
	 *
	 * @return self Instancia de CentroCosto.
	 * @throws Exception Si ocurre un error durante la construcción.
	 */
	public static function construct(array $resultado = []): self
	{
		try {
			$objeto         = new self();
			$objeto->id     = isset($resultado['id']) ? (int)$resultado['id'] : 0;
			$objeto->nombre = $resultado['nombre'] ?? null;
			$objeto->estado = isset($resultado['estado']) ? (int)$resultado['estado'] : 1;
			return $objeto;
		} catch (Exception $e) {
			// Considera loggear el error aquí
			throw new Exception("Error al construir CentroCosto: " . $e->getMessage());
		}
	}

	// --- Métodos de Acceso a Datos (Estáticos) ---

	/**
	 * Obtiene un centro de costo por su ID.
	 *
	 * @param int $id       ID del centro de costo.
	 * @param PDO $conexion Conexión PDO.
	 *
	 * @return self|null Objeto CentroCosto o null si no se encuentra.
	 * @throws Exception Si hay error en DB.
	 */
	public static function get(int $id, PDO $conexion): ?self
	{
		try {
			// Consulta para obtener centro de costo por ID (Usando Heredoc)
			$query = <<<SQL
            SELECT
            	*
            FROM centros_costos
            WHERE
            	id = :id
            LIMIT 1
            SQL;

			$statement = $conexion->prepare($query);
			$statement->bindValue(":id", $id, PDO::PARAM_INT);
			$statement->execute();
			$resultado = $statement->fetch(PDO::FETCH_ASSOC);

			return $resultado ? self::construct($resultado) : null;

		} catch (PDOException $e) {
			throw new Exception("Error al obtener CentroCosto (ID: $id): " . $e->getMessage());
		}
	}

	/**
	 * Obtiene una lista de centros de costos activos.
	 *
	 * @param PDO $conexion Conexión PDO.
	 *
	 * @return array Array de objetos CentroCosto.
	 * @throws Exception Si hay error en DB.
	 */
	public static function get_list(PDO $conexion): array
	{
		try {
			// Consulta para obtener lista de centros de costos activos (Usando Heredoc)
			$query = <<<SQL
            SELECT
            	*
            FROM centros_costos
            WHERE
            	estado = 1
            ORDER BY
            	nombre
            SQL;

			$statement = $conexion->prepare($query);
			$statement->execute();
			$resultados = $statement->fetchAll(PDO::FETCH_ASSOC);

			$listado = [];
			foreach ($resultados as $resultado) {
				$listado[] = self::construct($resultado);
			}
			return $listado;

		} catch (PDOException $e) {
			throw new Exception("Error al obtener lista de Centros de Costos: " . $e->getMessage());
		}
	}

	/**
	 * Obtiene una lista de centros de costos activos con el conteo de usuarios asociados.
	 *
	 * @param PDO $conexion Conexión PDO.
	 *
	 * @return array Array de objetos CentroCosto con propiedad adicional 'user_count'.
	 * @throws Exception Si hay error en DB.
	 */
	public static function get_list_with_user_count(PDO $conexion): array
	{
		try {
			// Consulta para obtener lista de centros de costos activos con conteo de usuarios
			$query = <<<SQL
            SELECT
            	cc.*,
            	COALESCE(COUNT(ccu.id_usuario), 0) AS user_count
            FROM centros_costos cc
            LEFT JOIN centros_costos_usuarios ccu ON cc.id = ccu.id_centro_costo
            WHERE
            	cc.estado = 1
            GROUP BY
            	cc.id, cc.nombre, cc.estado
            ORDER BY
            	cc.nombre
            SQL;

			$statement = $conexion->prepare($query);
			$statement->execute();
			$resultados = $statement->fetchAll(PDO::FETCH_ASSOC);

			$listado = [];
			foreach ($resultados as $resultado) {
				$centroCosto = self::construct($resultado);
				// Add user count as a property
				$centroCosto->user_count = (int)$resultado['user_count'];
				$listado[] = $centroCosto;
			}
			return $listado;

		} catch (PDOException $e) {
			throw new Exception("Error al obtener lista de Centros de Costos con conteo de usuarios: " . $e->getMessage());
		}
	}

	/**
	 * Obtiene una lista de centros de costos activos con el conteo de usuarios y empleados asociados.
	 *
	 * @param PDO $conexion Conexión PDO.
	 *
	 * @return array Array de objetos CentroCosto con propiedades adicionales 'user_count' y 'employee_count'.
	 * @throws Exception Si hay error en DB.
	 */
	public static function get_list_with_counts(PDO $conexion): array
	{
		try {
			// Consulta para obtener lista de centros de costos activos con conteo de usuarios y empleados
			$query = <<<SQL
            SELECT
            	cc.*,
            	COALESCE(COUNT(DISTINCT ccu.id_usuario), 0) AS user_count,
            	COALESCE(COUNT(DISTINCT cce.id_empleado), 0) AS employee_count
            FROM centros_costos cc
            LEFT JOIN centros_costos_usuarios ccu ON cc.id = ccu.id_centro_costo
            LEFT JOIN centros_costos_empleados cce ON cc.id = cce.id_centro_costo
            WHERE
            	cc.estado = 1
            GROUP BY
            	cc.id, cc.nombre, cc.estado
            ORDER BY
            	cc.nombre
            SQL;

			$statement = $conexion->prepare($query);
			$statement->execute();
			$resultados = $statement->fetchAll(PDO::FETCH_ASSOC);

			$listado = [];
			foreach ($resultados as $resultado) {
				$centroCosto = self::construct($resultado);
				// Add user and employee counts as properties
				$centroCosto->user_count = (int)$resultado['user_count'];
				$centroCosto->employee_count = (int)$resultado['employee_count'];
				$listado[] = $centroCosto;
			}
			return $listado;

		} catch (PDOException $e) {
			throw new Exception("Error al obtener lista de Centros de Costos con conteos: " . $e->getMessage());
		}
	}

	/**
	 * Crea un nuevo centro de costo en la base de datos a partir de un objeto CentroCosto.
	 * El objeto CentroCosto debe estar completamente poblado.
	 *
	 * @param PDO $conexion Conexión PDO.
	 *
	 * @return int|false El ID del nuevo centro de costo creado o false en caso de error.
	 * @throws Exception Si los datos requeridos en el objeto están vacíos o hay error en DB.
	 */
	function crear(PDO $conexion): int|false
	{
		// Validaciones básicas sobre el objeto
		if (empty($this->getNombre())) {
			throw new Exception("Nombre es requerido en el objeto CentroCosto para crearlo.");
		}

		try {
			// Preparar la consulta INSERT usando Heredoc
			$query = <<<SQL
            INSERT INTO centros_costos (
            	 nombre
            ) VALUES (
            	 :nombre
            )
            SQL;

			$statement = $conexion->prepare($query);

			// Bind de parámetros desde el objeto
			$statement->bindValue(':nombre', $this->getNombre(), PDO::PARAM_STR);

			// Ejecutar la consulta
			$success = $statement->execute();

			if ($success) {
				// Devolver el ID del centro de costo recién creado
				return (int)$conexion->lastInsertId();
			} else {
				return false; // Error en la ejecución
			}

		} catch (PDOException $e) {
			// Manejar errores específicos de DB (ej. nombre duplicado)
			if ($e->getCode() == 23000 || $e->getCode() == 1062) { // Códigos comunes para violación de UNIQUE constraint
				throw new Exception("Error al crear centro de costo: El nombre ya existe.");
			} else {
				throw new Exception("Error de base de datos al crear centro de costo: " . $e->getMessage());
			}
		} catch (Exception $e) {
			// Capturar otros errores
			throw new Exception("Error al crear centro de costo: " . $e->getMessage());
		}
	}

	/**
	 * Modifica el nombre de un centro de costo existente.
	 *
	 * @param PDO $conexion Conexión PDO.
	 *
	 * @return bool True si la modificación fue exitosa, False en caso contrario.
	 * @throws Exception Si el nuevo nombre está vacío o si ocurre un error de base de datos.
	 */
	function modificar(PDO $conexion): bool
	{
		if (empty(trim($this->getNombre()))) {
			throw new Exception("El nombre no puede estar vacío.");
		}

		if ($this->getId() <= 0) {
			throw new Exception("ID de centro de costo inválido para modificar.");
		}

		try {
			// Consulta para actualizar el nombre
			$query = <<<SQL
            UPDATE centros_costos SET
                nombre = :nombre
            WHERE
                id = :id
            SQL;

			$statement = $conexion->prepare($query);
			$statement->bindValue(':nombre', trim($this->getNombre()), PDO::PARAM_STR);
			$statement->bindValue(':id', $this->getId(), PDO::PARAM_INT);

			// execute() devuelve true en éxito, false en error.
			return $statement->execute();

		} catch (PDOException $e) {
			// Error de base de datos
			throw new Exception("Error de base de datos al modificar centro de costo (ID: {$this->getId()}): " . $e->getMessage());
		}
	}

	/**
	 * Desactiva un centro de costo estableciendo su estado a 0.
	 *
	 * @param int $id       ID del centro de costo a desactivar.
	 * @param PDO $conexion Conexión PDO.
	 *
	 * @return bool True si la desactivación fue exitosa, False en caso contrario.
	 * @throws Exception Si ocurre un error de base de datos.
	 */
	public static function desactivar(int $id, PDO $conexion): bool
	{
		try {
			// Consulta para actualizar el estado a 0 (inactivo)
			$query = <<<SQL
            UPDATE centros_costos SET
            	estado = 0
            WHERE
            	id = :id
            SQL;

			$statement = $conexion->prepare($query);
			$statement->bindValue(':id', $id, PDO::PARAM_INT);

			// execute() devuelve true en éxito, false en error.
			return $statement->execute();

		} catch (PDOException $e) {
			// Error de base de datos
			throw new Exception("Error de base de datos al desactivar centro de costo (ID: $id): " . $e->getMessage());
		}
	}

	/**
	 * Obtiene los centros de costo asociados a un usuario específico.
	 *
	 * @param int $id_usuario ID del usuario.
	 * @param PDO $conexion   Conexión PDO.
	 *
	 * @return array Array de objetos CentroCosto asociados al usuario.
	 * @throws Exception Si hay error en DB.
	 */
	public static function get_by_usuario(int $id_usuario, PDO $conexion): array
	{
		try {
			// Consulta para obtener centros de costo asociados a un usuario
			$query = <<<SQL
            SELECT
            	cc.*
            FROM centros_costos cc
            INNER JOIN centros_costos_usuarios ccu ON cc.id = ccu.id_centro_costo
            WHERE
            	ccu.id_usuario = :id_usuario
            	AND cc.estado = 1
            ORDER BY
            	cc.nombre
            SQL;

			$statement = $conexion->prepare($query);
			$statement->bindValue(":id_usuario", $id_usuario, PDO::PARAM_INT);
			$statement->execute();
			$resultados = $statement->fetchAll(PDO::FETCH_ASSOC);

			$listado = [];
			foreach ($resultados as $resultado) {
				$listado[] = self::construct($resultado);
			}
			return $listado;

		} catch (PDOException $e) {
			throw new Exception("Error al obtener centros de costo del usuario (ID: $id_usuario): " . $e->getMessage());
		}
	}

	/**
	 * Obtiene los usuarios asociados a un centro de costo específico.
	 *
	 * @param int $id_centro_costo ID del centro de costo.
	 * @param PDO $conexion        Conexión PDO.
	 *
	 * @return array Array de objetos Usuario asociados al centro de costo.
	 * @throws Exception Si hay error en DB.
	 */
	public static function get_usuarios_by_centro(int $id_centro_costo, PDO $conexion): array
	{
		try {
			// Consulta para obtener usuarios asociados a un centro de costo
			$query = <<<SQL
            SELECT
            	u.*
            FROM usuarios u
            INNER JOIN centros_costos_usuarios ccu ON u.id = ccu.id_usuario
            WHERE
            	ccu.id_centro_costo = :id_centro_costo
            	AND u.estado = 1
            ORDER BY
            	u.nombre
            SQL;

			$statement = $conexion->prepare($query);
			$statement->bindValue(":id_centro_costo", $id_centro_costo, PDO::PARAM_INT);
			$statement->execute();
			$resultados = $statement->fetchAll(PDO::FETCH_ASSOC);

			$listado = [];
			foreach ($resultados as $resultado) {
				// Assuming Usuario class has a construct method similar to CentroCosto
				$listado[] = \App\classes\Usuario::construct($resultado);
			}
			return $listado;

		} catch (PDOException $e) {
			throw new Exception("Error al obtener usuarios del centro de costo (ID: $id_centro_costo): " . $e->getMessage());
		}
	}

	/**
	 * Sincroniza las asociaciones de usuarios para un centro de costo.
	 * Elimina las asociaciones existentes e inserta las nuevas.
	 *
	 * @param int   $id_centro_costo ID del centro de costo.
	 * @param array $ids_usuarios    Array de IDs de usuarios a asociar.
	 * @param PDO   $conexion        Conexión PDO.
	 *
	 * @return bool True si la operación fue exitosa.
	 * @throws Exception Si hay error en DB o validación.
	 */
	public static function sync_centro_usuario_associations(int $id_centro_costo, array $ids_usuarios, PDO $conexion): bool
	{
		try {
			// Iniciar transacción
			$conexion->beginTransaction();

			// 1. Eliminar todas las asociaciones existentes del centro de costo
			$deleteQuery = <<<SQL
            DELETE FROM centros_costos_usuarios
            WHERE id_centro_costo = :id_centro_costo
            SQL;

			$deleteStatement = $conexion->prepare($deleteQuery);
			$deleteStatement->bindValue(':id_centro_costo', $id_centro_costo, PDO::PARAM_INT);
			$deleteStatement->execute();

			// 2. Insertar las nuevas asociaciones
			if (!empty($ids_usuarios)) {
				$insertQuery = <<<SQL
                INSERT INTO centros_costos_usuarios (id_centro_costo, id_usuario)
                VALUES (:id_centro_costo, :id_usuario)
                SQL;

				$insertStatement = $conexion->prepare($insertQuery);

				foreach ($ids_usuarios as $id_usuario) {
					// Validar que el ID sea un entero positivo
					$id_usuario = (int)$id_usuario;
					if ($id_usuario <= 0) {
						throw new Exception("ID de usuario inválido: $id_usuario");
					}

					// Validar que el usuario existe y está activo
					$usuario = \App\classes\Usuario::get($id_usuario, $conexion);
					if (!$usuario || !$usuario->isActivo()) {
						throw new Exception("Usuario no encontrado o inactivo (ID: $id_usuario)");
					}

					$insertStatement->bindValue(':id_centro_costo', $id_centro_costo, PDO::PARAM_INT);
					$insertStatement->bindValue(':id_usuario', $id_usuario, PDO::PARAM_INT);
					$insertStatement->execute();
				}
			}

			// Confirmar transacción
			$conexion->commit();
			return true;

		} catch (PDOException $e) {
			// Revertir transacción en caso de error
			$conexion->rollBack();
			throw new Exception("Error de base de datos al sincronizar asociaciones de centro de costo: " . $e->getMessage());
		} catch (Exception $e) {
			// Revertir transacción en caso de error
			$conexion->rollBack();
			throw new Exception("Error al sincronizar asociaciones de centro de costo: " . $e->getMessage());
		}
	}

	/**
	 * Sincroniza las asociaciones de centros de costo para un usuario.
	 * Elimina las asociaciones existentes e inserta las nuevas.
	 *
	 * @param int   $id_usuario         ID del usuario.
	 * @param array $ids_centros_costos Array de IDs de centros de costo a asociar.
	 * @param PDO   $conexion           Conexión PDO.
	 *
	 * @return bool True si la operación fue exitosa.
	 * @throws Exception Si hay error en DB o validación.
	 */
	public static function sync_usuario_associations(int $id_usuario, array $ids_centros_costos, PDO $conexion): bool
	{
		try {
			// Iniciar transacción
			$conexion->beginTransaction();

			// 1. Eliminar todas las asociaciones existentes del usuario
			$deleteQuery = <<<SQL
            DELETE FROM centros_costos_usuarios
            WHERE id_usuario = :id_usuario
            SQL;

			$deleteStatement = $conexion->prepare($deleteQuery);
			$deleteStatement->bindValue(':id_usuario', $id_usuario, PDO::PARAM_INT);
			$deleteStatement->execute();

			// 2. Insertar las nuevas asociaciones
			if (!empty($ids_centros_costos)) {
				$insertQuery = <<<SQL
                INSERT INTO centros_costos_usuarios (id_centro_costo, id_usuario)
                VALUES (:id_centro_costo, :id_usuario)
                SQL;

				$insertStatement = $conexion->prepare($insertQuery);

				foreach ($ids_centros_costos as $id_centro_costo) {
					// Validar que el ID sea un entero positivo
					$id_centro_costo = (int)$id_centro_costo;
					if ($id_centro_costo <= 0) {
						throw new Exception("ID de centro de costo inválido: $id_centro_costo");
					}

					// Validar que el centro de costo existe y está activo
					$centro_costo = self::get($id_centro_costo, $conexion);
					if (!$centro_costo || !$centro_costo->isActivo()) {
						throw new Exception("Centro de costo no encontrado o inactivo (ID: $id_centro_costo)");
					}

					$insertStatement->bindValue(':id_centro_costo', $id_centro_costo, PDO::PARAM_INT);
					$insertStatement->bindValue(':id_usuario', $id_usuario, PDO::PARAM_INT);
					$insertStatement->execute();
				}
			}

			// Confirmar transacción
			$conexion->commit();
			return true;

		} catch (PDOException $e) {
			// Revertir transacción en caso de error
			$conexion->rollBack();
			throw new Exception("Error de base de datos al sincronizar asociaciones de usuario: " . $e->getMessage());
		} catch (Exception $e) {
			// Revertir transacción en caso de error
			$conexion->rollBack();
			throw new Exception("Error al sincronizar asociaciones de usuario: " . $e->getMessage());
		}
	}

	/**
	 * Obtiene los empleados asociados a un centro de costo específico.
	 *
	 * @param int $id_centro_costo ID del centro de costo.
	 * @param PDO $conexion        Conexión PDO.
	 *
	 * @return array Array de objetos Empleado asociados al centro de costo.
	 * @throws Exception Si hay error en DB.
	 */
	public static function get_empleados_by_centro(int $id_centro_costo, PDO $conexion): array
	{
		try {
			// Consulta para obtener empleados asociados a un centro de costo
			$query = <<<SQL
            SELECT
            	e.*
            FROM empleados e
            INNER JOIN centros_costos_empleados cce ON e.id = cce.id_empleado
            WHERE
            	cce.id_centro_costo = :id_centro_costo
            	AND e.estado = 1
            ORDER BY
            	e.nombre
            SQL;

			$statement = $conexion->prepare($query);
			$statement->bindValue(":id_centro_costo", $id_centro_costo, PDO::PARAM_INT);
			$statement->execute();
			$resultados = $statement->fetchAll(PDO::FETCH_ASSOC);

			$listado = [];
			foreach ($resultados as $resultado) {
				$listado[] = \App\classes\Empleado::construct($resultado);
			}
			return $listado;

		} catch (PDOException $e) {
			throw new Exception("Error al obtener empleados del centro de costo (ID: $id_centro_costo): " . $e->getMessage());
		}
	}

	/**
	 * Sincroniza las asociaciones de empleados para un centro de costo.
	 * Elimina las asociaciones existentes e inserta las nuevas.
	 *
	 * @param int   $id_centro_costo ID del centro de costo.
	 * @param array $ids_empleados   Array de IDs de empleados a asociar.
	 * @param PDO   $conexion        Conexión PDO.
	 *
	 * @return bool True si la operación fue exitosa.
	 * @throws Exception Si hay error en DB o validación.
	 */
	public static function sync_centro_empleado_associations(int $id_centro_costo, array $ids_empleados, PDO $conexion): bool
	{
		try {
			// Iniciar transacción
			$conexion->beginTransaction();

			// 1. Eliminar todas las asociaciones existentes del centro de costo
			$deleteQuery = <<<SQL
            DELETE FROM centros_costos_empleados
            WHERE id_centro_costo = :id_centro_costo
            SQL;

			$deleteStatement = $conexion->prepare($deleteQuery);
			$deleteStatement->bindValue(':id_centro_costo', $id_centro_costo, PDO::PARAM_INT);
			$deleteStatement->execute();

			// 2. Insertar las nuevas asociaciones
			if (!empty($ids_empleados)) {
				$insertQuery = <<<SQL
                INSERT INTO centros_costos_empleados (id_centro_costo, id_empleado)
                VALUES (:id_centro_costo, :id_empleado)
                SQL;

				$insertStatement = $conexion->prepare($insertQuery);

				foreach ($ids_empleados as $id_empleado) {
					// Validar que el ID sea un entero positivo
					$id_empleado = (int)$id_empleado;
					if ($id_empleado <= 0) {
						throw new Exception("ID de empleado inválido: $id_empleado");
					}

					// Validar que el empleado existe y está activo
					$empleado = \App\classes\Empleado::get($id_empleado, $conexion);
					if (!$empleado || !$empleado->isActivo()) {
						throw new Exception("Empleado no encontrado o inactivo (ID: $id_empleado)");
					}

					$insertStatement->bindValue(':id_centro_costo', $id_centro_costo, PDO::PARAM_INT);
					$insertStatement->bindValue(':id_empleado', $id_empleado, PDO::PARAM_INT);
					$insertStatement->execute();
				}
			}

			// Confirmar transacción
			$conexion->commit();
			return true;

		} catch (PDOException $e) {
			// Revertir transacción en caso de error
			$conexion->rollBack();
			throw new Exception("Error de base de datos al sincronizar asociaciones de empleados: " . $e->getMessage());
		} catch (Exception $e) {
			// Revertir transacción en caso de error
			$conexion->rollBack();
			throw new Exception("Error al sincronizar asociaciones de empleados: " . $e->getMessage());
		}
	}

	// --- Getters y Setters ---

	public function getId(): ?int
	{
		return $this->id;
	}

	public function setId(?int $id): self
	{
		$this->id = $id;
		return $this;
	}

	public function getNombre(): ?string
	{
		return $this->nombre;
	}

	public function setNombre(?string $nombre): self
	{
		$this->nombre = $nombre;
		return $this;
	}

	public function getEstado(): ?int
	{
		return $this->estado;
	}

	public function setEstado(?int $estado): self
	{
		$this->estado = $estado;
		return $this;
	}

	// --- Métodos adicionales ---

	/**
	 * Verifica si el centro de costo está activo.
	 * @return bool
	 */
	public function isActivo(): bool
	{
		// Asegúrate de que el estado no sea null antes de comparar
		return $this->estado === 1;
	}
}
