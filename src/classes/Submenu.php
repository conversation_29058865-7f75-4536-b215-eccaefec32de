<?php

declare(strict_types=1);

namespace App\classes;

use Exception;
use PDO;
use PDOException;

class Submenu
{
	// --- Atributos ---
	private ?int    $id        = null;
	private ?int    $id_menu   = null;
	private ?string $url       = null;
	private ?string $texto     = null;
	private ?int    $prioridad = null;
	private ?int    $estado    = null;

	/**
	 * Constructor: Inicializa las propiedades del objeto Submenu.
	 */
	public function __construct()
	{
		$this->id        = 0;
		$this->id_menu   = 0;
		$this->url       = null;
		$this->texto     = null;
		$this->prioridad = 0;
		$this->estado    = 1; // Estado activo por defecto
	}

	/**
	 * Método estático para construir un objeto Submenu desde un array (ej. fila de DB).
	 *
	 * @param array $resultado Array asociativo con los datos del submenu.
	 *
	 * @return self Instancia de Submenu.
	 * @throws Exception Si ocurre un error durante la construcción.
	 */
	public static function construct(array $resultado = []): self
	{
		try {
			$objeto             = new self();
			$objeto->id         = isset($resultado['id']) ? (int)$resultado['id'] : 0;
			$objeto->id_menu    = isset($resultado['id_menu']) ? (int)$resultado['id_menu'] : 0;
			$objeto->url        = $resultado['url'] ?? null;
			$objeto->texto      = $resultado['texto'] ?? null;
			$objeto->prioridad  = isset($resultado['prioridad']) ? (int)$resultado['prioridad'] : 0;
			$objeto->estado     = isset($resultado['estado']) ? (int)$resultado['estado'] : 1;
			return $objeto;
		} catch (Exception $e) {
			throw new Exception("Error al construir objeto Submenu: " . $e->getMessage());
		}
	}

	/**
	 * Obtiene un submenu por su ID.
	 *
	 * @param int $id       ID del submenu.
	 * @param PDO $conexion Conexión PDO.
	 *
	 * @return self|null Objeto Submenu o null si no se encuentra.
	 * @throws Exception Si hay error en DB.
	 */
	public static function get(int $id, PDO $conexion): ?self
	{
		try {
			// Consulta para obtener submenu por ID (Usando Heredoc)
			$query = <<<SQL
            SELECT
            	*
            FROM submenus
            WHERE
            	id = :id
            LIMIT 1
            SQL;

			$statement = $conexion->prepare($query);
			$statement->bindValue(":id", $id, PDO::PARAM_INT);
			$statement->execute();
			$resultado = $statement->fetch(PDO::FETCH_ASSOC);

			return $resultado ? self::construct($resultado) : null;

		} catch (PDOException $e) {
			throw new Exception("Error al obtener Submenu: " . $e->getMessage());
		}
	}

	/**
	 * Obtiene una lista de submenus activos.
	 *
	 * @param PDO $conexion Conexión PDO.
	 * @param int|null $id_menu Filtrar por ID de menú (opcional).
	 *
	 * @return array Array de objetos Submenu.
	 * @throws Exception Si hay error en DB.
	 */
	public static function get_list(PDO $conexion, ?int $id_menu = null): array
	{
		try {
			// Consulta base para obtener lista de submenus activos
			$query = "SELECT * FROM submenus WHERE estado = 1";

			// Si se proporciona un id_menu, filtrar por él
			if ($id_menu !== null) {
				$query .= " AND id_menu = :id_menu";
			}

			// Ordenar por prioridad
			$query .= " ORDER BY prioridad";

			$statement = $conexion->prepare($query);

			// Bind del parámetro id_menu si se proporcionó
			if ($id_menu !== null) {
				$statement->bindValue(':id_menu', $id_menu, PDO::PARAM_INT);
			}

			$statement->execute();
			$resultados = $statement->fetchAll(PDO::FETCH_ASSOC);

			$listado = [];
			foreach ($resultados as $resultado) {
				$listado[] = self::construct($resultado);
			}
			return $listado;

		} catch (PDOException $e) {
			throw new Exception("Error al obtener lista de Submenus: " . $e->getMessage());
		}
	}

	/**
	 * Crea un nuevo submenu en la base de datos a partir de un objeto Submenu.
	 *
	 * @param PDO $conexion Conexión PDO.
	 *
	 * @return int|false El ID del nuevo submenu creado o false en caso de error.
	 * @throws Exception Si los datos requeridos en el objeto están vacíos o hay error en DB.
	 */
	function crear(PDO $conexion): int|false
	{
		// Validaciones básicas sobre el objeto
		if (empty($this->getIdMenu())) {
			throw new Exception("ID del menú es requerido en el objeto Submenu para crearlo. Valor actual: " . var_export($this->getIdMenu(), true));
		}

		if (empty($this->getTexto())) {
			throw new Exception("Texto es requerido en el objeto Submenu para crearlo.");
		}

		// Validar que el ID del menú sea un número positivo
		if ($this->getIdMenu() <= 0) {
			throw new Exception("ID del menú debe ser un número positivo. Valor actual: " . $this->getIdMenu());
		}

		try {
			// Preparar la consulta INSERT usando Heredoc
			$query = <<<SQL
            INSERT INTO submenus (
            	 id_menu
            	,url
            	,texto
            	,prioridad
            	,estado
            ) VALUES (
            	 :id_menu
            	,:url
            	,:texto
            	,:prioridad
            	,:estado
            )
            SQL;

			$statement = $conexion->prepare($query);

			// Bind de parámetros desde el objeto
			$statement->bindValue(':id_menu', $this->getIdMenu(), PDO::PARAM_INT);
			$statement->bindValue(':url', $this->getUrl(), PDO::PARAM_STR);
			$statement->bindValue(':texto', $this->getTexto(), PDO::PARAM_STR);
			$statement->bindValue(':prioridad', $this->getPrioridad(), PDO::PARAM_INT);
			$statement->bindValue(':estado', $this->getEstado(), PDO::PARAM_INT);

			// Ejecutar la consulta
			$success = $statement->execute();

			if ($success) {
				// Obtener el ID del submenu recién creado
				$newId = (int)$conexion->lastInsertId();

				// Verificar que se obtuvo un ID válido
				if ($newId <= 0) {
					throw new Exception("Error al obtener el ID del submenu creado. LastInsertId devuelve: " . $newId);
				}

				return $newId;
			} else {
				// Obtener información sobre el error
				$errorInfo = $statement->errorInfo();
				throw new Exception("Error en la ejecución de la consulta: " . implode(" - ", $errorInfo));
			}

		} catch (PDOException $e) {
			// Registrar el error en el log
			error_log("PDO Exception al crear submenu: " . $e->getMessage());
			// Incluir más detalles sobre el error
			throw new Exception("Error de base de datos al crear submenu: " . $e->getMessage() .
				". Código: " . $e->getCode());
		} catch (Exception $e) {
			// Registrar el error en el log
			error_log("Exception al crear submenu: " . $e->getMessage());
			// Capturar otros errores
			throw new Exception("Error al crear submenu: " . $e->getMessage());
		}
	}

	/**
	 * Actualiza un submenu existente.
	 *
	 * @param PDO $conexion Conexión PDO.
	 *
	 * @return bool True si la actualización fue exitosa, false en caso contrario.
	 * @throws Exception Si hay error en DB.
	 */
	function modificar(PDO $conexion): bool
	{
		// Validar que el ID exista
		if (empty($this->getId())) {
			throw new Exception("ID es requerido para actualizar un Submenu.");
		}

		try {
			// Consulta para actualizar el submenu
			$query = <<<SQL
            UPDATE submenus SET
                id_menu = :id_menu,
                url = :url,
                texto = :texto,
                prioridad = :prioridad,
                estado = :estado
            WHERE
                id = :id
            SQL;

			$statement = $conexion->prepare($query);
			$statement->bindValue(':id_menu', $this->getIdMenu(), PDO::PARAM_INT);
			$statement->bindValue(':url', $this->getUrl(), PDO::PARAM_STR);
			$statement->bindValue(':texto', $this->getTexto(), PDO::PARAM_STR);
			$statement->bindValue(':prioridad', $this->getPrioridad(), PDO::PARAM_INT);
			$statement->bindValue(':estado', $this->getEstado(), PDO::PARAM_INT);
			$statement->bindValue(':id', $this->getId(), PDO::PARAM_INT);

			return $statement->execute();

		} catch (PDOException $e) {
			throw new Exception("Error de base de datos al actualizar submenu (ID: {$this->getId()}): " . $e->getMessage());
		}
	}

	/**
	 * Desactiva un submenu por su ID.
	 *
	 * @param int $id       ID del submenu a desactivar.
	 * @param PDO $conexion Conexión PDO.
	 *
	 * @return bool True si la desactivación fue exitosa, false en caso contrario.
	 * @throws Exception Si hay error en DB.
	 */
	public static function desactivar(int $id, PDO $conexion): bool
	{
		try {
			// Consulta para desactivar el submenu
			$query = <<<SQL
            UPDATE submenus SET
                estado = 0
            WHERE
                id = :id
            SQL;

			$statement = $conexion->prepare($query);
			$statement->bindValue(':id', $id, PDO::PARAM_INT);

			return $statement->execute();

		} catch (PDOException $e) {
			throw new Exception("Error de base de datos al desactivar submenu (ID: $id): " . $e->getMessage());
		}
	}

	/**
	 * Activa un submenu por su ID.
	 *
	 * @param int $id       ID del submenu a activar.
	 * @param PDO $conexion Conexión PDO.
	 *
	 * @return bool True si la activación fue exitosa, false en caso contrario.
	 * @throws Exception Si hay error en DB.
	 */
	public static function activar(int $id, PDO $conexion): bool
	{
		try {
			// Consulta para activar el submenu
			$query = <<<SQL
            UPDATE submenus SET
                estado = 1
            WHERE
                id = :id
            SQL;

			$statement = $conexion->prepare($query);
			$statement->bindValue(':id', $id, PDO::PARAM_INT);

			return $statement->execute();

		} catch (PDOException $e) {
			throw new Exception("Error de base de datos al activar submenu (ID: $id): " . $e->getMessage());
		}
	}

	/**
	 * Obtiene el menú al que pertenece este submenu.
	 *
	 * @param PDO $conexion Conexión PDO.
	 *
	 * @return Menu|null El objeto Menu al que pertenece este submenu, o null si no existe.
	 * @throws Exception Si hay error en DB.
	 */
	public function getMenu(PDO $conexion): ?Menu
	{
		if (empty($this->id_menu)) {
			return null;
		}

		return Menu::get($this->id_menu, $conexion);
	}

	// --- Getters y Setters ---

	public function getId(): ?int
	{
		return $this->id;
	}

	public function setId(?int $id): self
	{
		$this->id = $id;
		return $this;
	}

	public function getIdMenu(): ?int
	{
		return $this->id_menu;
	}

	public function setIdMenu(?int $id_menu): self
	{
		$this->id_menu = $id_menu;
		return $this;
	}

	public function getUrl(): ?string
	{
		return $this->url;
	}

	public function setUrl(?string $url): self
	{
		$this->url = $url;
		return $this;
	}

	public function getTexto(): ?string
	{
		return $this->texto;
	}

	public function setTexto(?string $texto): self
	{
		$this->texto = $texto;
		return $this;
	}

	public function getPrioridad(): ?int
	{
		return $this->prioridad;
	}

	public function setPrioridad(?int $prioridad): self
	{
		$this->prioridad = $prioridad;
		return $this;
	}

	public function getEstado(): ?int
	{
		return $this->estado;
	}

	public function setEstado(?int $estado): self
	{
		$this->estado = $estado;
		return $this;
	}

	/**
	 * Verifica si el submenu está activo.
	 * @return bool
	 */
	public function isActivo(): bool
	{
		return $this->estado === 1;
	}
}
