<?php

declare(strict_types=1);

namespace App\classes;

use Exception;
use PDO;
use PDOException;

#[\AllowDynamicProperties] class EmpleadoTurno
{
	// --- Atributos ---
	private ?int    $id                 = null;
	private ?string $fecha_inicio       = null;
	private ?int    $id_puesto          = null;
	private ?string $descripcion_puesto = null; // Nombre descriptivo del puesto
	private ?int    $id_empleado        = null;
	private ?string $nombre_empleado    = null; // Nombre del empleado asociado
	private ?string $fecha_fin          = null;

	/**
	 * Constructor: Inicializa las propiedades del objeto EmpleadoTurno.
	 */
	public function __construct()
	{
		$this->id                 = 0; // O null si prefieres no usar 0 por defecto
		$this->fecha_inicio       = null;
		$this->id_puesto          = null;
		$this->descripcion_puesto = null;
		$this->id_empleado        = null;
		$this->nombre_empleado    = null;
		$this->fecha_fin          = null;
	}

	/**
	 * Método estático para construir un objeto EmpleadoTurno desde un array (ej. fila de DB).
	 *
	 * @param array $resultado Array asociativo con los datos del turno.
	 *
	 * @return self Instancia de EmpleadoTurno.
	 * @throws Exception Si ocurre un error durante la construcción.
	 */
	public static function construct(array $resultado = []): self
	{
		try {
			$objeto                     = new self();
			$objeto->id                 = isset($resultado['id']) ? (int)$resultado['id'] : 0;
			$objeto->fecha_inicio       = $resultado['fecha_inicio'] ?? null;
			$objeto->id_puesto          = isset($resultado['id_puesto']) ? (int)$resultado['id_puesto'] : null;
			$objeto->descripcion_puesto = $resultado['descripcion_puesto'] ?? null;
			$objeto->id_empleado        = isset($resultado['id_empleado']) ? (int)$resultado['id_empleado'] : null;
			$objeto->nombre_empleado    = $resultado['nombre_empleado'] ?? null;
			$objeto->fecha_fin          = $resultado['fecha_fin'] ?? null;
			return $objeto;
		} catch (Exception $e) {
			// Considera loggear el error aquí
			throw new Exception("Error al construir EmpleadoTurno: " . $e->getMessage());
		}
	}

	// --- Métodos de Acceso a Datos (Estáticos) ---

	/**
	 * Obtiene un turno por su ID.
	 *
	 * @param int $id       ID del turno.
	 * @param PDO $conexion Conexión PDO.
	 *
	 * @return self|null Objeto EmpleadoTurno o null si no se encuentra.
	 * @throws Exception Si hay error en DB.
	 */
	public static function get(int $id, PDO $conexion): ?self
	{
		try {
			// Consulta para obtener turno por ID (Usando Heredoc)
			$query = <<<SQL
            SELECT
            	et.*,
            	p.descripcion AS descripcion_puesto,
            	e.nombre AS nombre_empleado
            FROM empleados_turnos et
            LEFT JOIN puestos p ON et.id_puesto = p.id
            LEFT JOIN empleados e ON et.id_empleado = e.id
            WHERE
            	et.id = :id
            LIMIT 1
            SQL;

			$statement = $conexion->prepare($query);
			$statement->bindValue(":id", $id, PDO::PARAM_INT);
			$statement->execute();
			$resultado = $statement->fetch(PDO::FETCH_ASSOC);

			return $resultado ? self::construct($resultado) : null;

		} catch (PDOException $e) {
			throw new Exception("Error al obtener EmpleadoTurno (ID: $id): " . $e->getMessage());
		}
	}

	/**
	 * Obtiene una lista de turnos activos (sin fecha_fin).
	 *
	 * @param PDO $conexion Conexión PDO.
	 *
	 * @return array Array de objetos EmpleadoTurno.
	 * @throws Exception Si hay error en DB.
	 */
	public static function get_list_activos(PDO $conexion): array
	{
		try {
			// Consulta para obtener lista de turnos activos (Usando Heredoc)
			$query = <<<SQL
            SELECT
            	et.*,
            	p.descripcion AS descripcion_puesto,
            	e.nombre AS nombre_empleado
            FROM empleados_turnos et
            LEFT JOIN puestos p ON et.id_puesto = p.id
            LEFT JOIN empleados e ON et.id_empleado = e.id
            WHERE
            	et.fecha_fin IS NULL
            ORDER BY
            	et.fecha_inicio DESC
            SQL;

			$statement = $conexion->prepare($query);
			$statement->execute();
			$resultados = $statement->fetchAll(PDO::FETCH_ASSOC);

			$listado = [];
			foreach ($resultados as $resultado) {
				$listado[] = self::construct($resultado);
			}
			return $listado;

		} catch (PDOException $e) {
			throw new Exception("Error al obtener lista de Turnos activos: " . $e->getMessage());
		}
	}

	/**
	 * Obtiene una lista de empleados disponibles (sin turnos activos).
	 *
	 * @param PDO $conexion Conexión PDO.
	 *
	 * @return array Array de objetos Empleado.
	 * @throws Exception Si hay error en DB.
	 */
	public static function get_empleados_disponibles(PDO $conexion): array
	{
		try {
			// Consulta para obtener empleados sin turnos activos (Usando Heredoc)
			$query = <<<SQL
            SELECT e.*
            FROM empleados e
            WHERE e.estado = 1
            AND e.id NOT IN (
                SELECT et.id_empleado
                FROM empleados_turnos et
                WHERE et.fecha_fin IS NULL
            )
            ORDER BY e.nombre
            SQL;

			$statement = $conexion->prepare($query);
			$statement->execute();
			$resultados = $statement->fetchAll(PDO::FETCH_ASSOC);

			$listado = [];
			foreach ($resultados as $resultado) {
				$listado[] = \App\classes\Empleado::construct($resultado);
			}
			return $listado;

		} catch (PDOException $e) {
			throw new Exception("Error al obtener lista de Empleados disponibles: " . $e->getMessage());
		}
	}

	/**
	 * Verifica si un empleado tiene un turno activo.
	 *
	 * @param int $id_empleado ID del empleado a verificar.
	 * @param PDO $conexion    Conexión PDO.
	 *
	 * @return bool True si el empleado tiene un turno activo, False en caso contrario.
	 * @throws Exception Si hay error en DB.
	 */
	public static function tiene_turno_activo(int $id_empleado, PDO $conexion): bool
	{
		try {
			// Consulta para verificar si el empleado tiene un turno activo
			$query = <<<SQL
            SELECT COUNT(*) as total
            FROM empleados_turnos
            WHERE id_empleado = :id_empleado
            AND fecha_fin IS NULL
            SQL;

			$statement = $conexion->prepare($query);
			$statement->bindValue(':id_empleado', $id_empleado, PDO::PARAM_INT);
			$statement->execute();
			$resultado = $statement->fetch(PDO::FETCH_ASSOC);

			return ($resultado['total'] > 0);

		} catch (PDOException $e) {
			throw new Exception("Error al verificar si el empleado tiene turno activo: " . $e->getMessage());
		}
	}

	/**
	 * Obtiene una lista de todos los turnos.
	 *
	 * @param PDO $conexion Conexión PDO.
	 *
	 * @return array Array de objetos EmpleadoTurno.
	 * @throws Exception Si hay error en DB.
	 */
	public static function get_list(PDO $conexion): array
	{
		try {
			// Consulta para obtener lista de todos los turnos (Usando Heredoc)
			$query = <<<SQL
            SELECT
            	et.*,
            	p.descripcion AS descripcion_puesto,
            	e.nombre AS nombre_empleado
            FROM empleados_turnos et
            LEFT JOIN puestos p ON et.id_puesto = p.id
            LEFT JOIN empleados e ON et.id_empleado = e.id
            ORDER BY
            	et.fecha_inicio DESC
            SQL;

			$statement = $conexion->prepare($query);
			$statement->execute();
			$resultados = $statement->fetchAll(PDO::FETCH_ASSOC);

			$listado = [];
			foreach ($resultados as $resultado) {
				$listado[] = self::construct($resultado);
			}
			return $listado;

		} catch (PDOException $e) {
			throw new Exception("Error al obtener lista de Turnos: " . $e->getMessage());
		}
	}

	/**
	 * Obtiene los turnos de un empleado específico.
	 *
	 * @param int $id_empleado ID del empleado.
	 * @param PDO $conexion    Conexión PDO.
	 *
	 * @return array Array de objetos EmpleadoTurno.
	 * @throws Exception Si hay error en DB.
	 */
	public static function get_by_empleado(int $id_empleado, PDO $conexion): array
	{
		try {
			// Consulta para obtener turnos por empleado (Usando Heredoc)
			$query = <<<SQL
            SELECT
            	et.*,
            	p.descripcion AS descripcion_puesto,
            	e.nombre AS nombre_empleado
            FROM empleados_turnos et
            LEFT JOIN puestos p ON et.id_puesto = p.id
            LEFT JOIN empleados e ON et.id_empleado = e.id
            WHERE
            	et.id_empleado = :id_empleado
            ORDER BY
            	et.fecha_inicio DESC
            SQL;

			$statement = $conexion->prepare($query);
			$statement->bindValue(":id_empleado", $id_empleado, PDO::PARAM_INT);
			$statement->execute();
			$resultados = $statement->fetchAll(PDO::FETCH_ASSOC);

			$listado = [];
			foreach ($resultados as $resultado) {
				$listado[] = self::construct($resultado);
			}
			return $listado;

		} catch (PDOException $e) {
			throw new Exception("Error al obtener turnos del empleado (ID: $id_empleado): " . $e->getMessage());
		}
	}

	/**
	 * Obtiene el turno activo para un puesto específico.
	 *
	 * @param int $id_puesto ID del puesto.
	 * @param PDO $conexion  Conexión PDO.
	 *
	 * @return self|null Objeto EmpleadoTurno o null si no hay turno activo para ese puesto.
	 * @throws Exception Si hay error en DB.
	 */
	public static function get_activo_by_puesto(int $id_puesto, PDO $conexion): ?self
	{
		try {
			// Consulta para obtener turno activo por puesto (Usando Heredoc)
			$query = <<<SQL
            SELECT
            	et.*,
            	p.descripcion AS descripcion_puesto,
            	e.nombre AS nombre_empleado
            FROM empleados_turnos et
            LEFT JOIN puestos p ON et.id_puesto = p.id
            LEFT JOIN empleados e ON et.id_empleado = e.id
            WHERE
            	et.id_puesto = :id_puesto
            	AND et.fecha_fin IS NULL
            ORDER BY
            	et.fecha_inicio DESC
            LIMIT 1
            SQL;

			$statement = $conexion->prepare($query);
			$statement->bindValue(":id_puesto", $id_puesto, PDO::PARAM_INT);
			$statement->execute();
			$resultado = $statement->fetch(PDO::FETCH_ASSOC);

			return $resultado ? self::construct($resultado) : null;

		} catch (PDOException $e) {
			throw new Exception("Error al obtener turno activo del puesto (ID: $id_puesto): " . $e->getMessage());
		}
	}

	/**
	 * Crea un nuevo turno en la base de datos a partir de un objeto EmpleadoTurno.
	 * El objeto EmpleadoTurno debe estar completamente poblado con los datos requeridos.
	 *
	 * @param PDO $conexion Conexión PDO.
	 *
	 * @return int|false El ID del nuevo turno creado o false en caso de error.
	 * @throws Exception Si los datos requeridos en el objeto están vacíos o hay error en DB.
	 */
	function crear(PDO $conexion): int|false
	{
		// Validaciones básicas sobre el objeto
		if (empty($this->getFecha_inicio()) || $this->getId_puesto() === null || $this->getId_empleado() === null) {
			throw new Exception("Fecha de inicio, ID de puesto e ID de empleado son requeridos para crear un turno.");
		}

		try {
			return $this->_insert($conexion);
		} catch (Exception $e) {
			// Capturar otros errores
			throw new Exception("Error al crear turno: " . $e->getMessage());
		}
	}

	/**
	 * Método privado para insertar un nuevo turno en la base de datos.
	 *
	 * @param PDO $conexion Conexión PDO.
	 *
	 * @return int|false El ID del nuevo turno creado o false en caso de error.
	 * @throws Exception Si hay error en DB.
	 */
	private function _insert(PDO $conexion): int|false
	{
		try {
			// Establecer la zona horaria para Colombia
			date_default_timezone_set('America/Bogota');

			// Preparar la consulta INSERT usando Heredoc
			$query = <<<SQL
            INSERT INTO empleados_turnos (
            	 fecha_inicio
            	,id_puesto
            	,id_empleado
            	,fecha_fin
            ) VALUES (
            	 :fecha_inicio
            	,:id_puesto
            	,:id_empleado
            	,:fecha_fin
            )
            SQL;

			$statement = $conexion->prepare($query);

			// Bind de parámetros desde el objeto
			$statement->bindValue(':fecha_inicio', $this->getFecha_inicio(), PDO::PARAM_STR);
			$statement->bindValue(':id_puesto', $this->getId_puesto(), PDO::PARAM_INT);
			$statement->bindValue(':id_empleado', $this->getId_empleado(), PDO::PARAM_INT);
			$statement->bindValue(':fecha_fin', $this->getFecha_fin(), $this->getFecha_fin() === null ? PDO::PARAM_NULL : PDO::PARAM_STR);

			// Ejecutar la consulta
			$success = $statement->execute();

			if ($success) {
				// Devolver el ID del turno recién creado
				return (int)$conexion->lastInsertId();
			} else {
				return false; // Error en la ejecución
			}

		} catch (PDOException $e) {
			throw new Exception("Error de base de datos al crear turno: " . $e->getMessage());
		}
	}

	/**
	 * Modifica un turno existente.
	 *
	 * @param PDO $conexion Conexión PDO.
	 *
	 * @return bool True si la modificación fue exitosa, False en caso contrario.
	 * @throws Exception Si los datos requeridos en el objeto están vacíos o hay error en DB.
	 */
	function modificar(PDO $conexion): bool
	{
		// Validaciones básicas sobre el objeto
		if ($this->getId() <= 0 || empty($this->getFecha_inicio()) || $this->getId_puesto() === null || $this->getId_empleado() === null) {
			throw new Exception("ID, fecha de inicio, ID de puesto e ID de empleado son requeridos para modificar un turno.");
		}

		try {
			return $this->_update($conexion);
		} catch (Exception $e) {
			// Capturar otros errores
			throw new Exception("Error al modificar turno: " . $e->getMessage());
		}
	}

	/**
	 * Método privado para actualizar un turno existente en la base de datos.
	 *
	 * @param PDO $conexion Conexión PDO.
	 *
	 * @return bool True si la actualización fue exitosa, False en caso contrario.
	 * @throws Exception Si hay error en DB.
	 */
	private function _update(PDO $conexion): bool
	{
		try {
			// Establecer la zona horaria para Colombia
			date_default_timezone_set('America/Bogota');

			// Consulta para actualizar el turno
			$query = <<<SQL
            UPDATE empleados_turnos SET
                fecha_inicio = :fecha_inicio,
                id_puesto = :id_puesto,
                id_empleado = :id_empleado,
                fecha_fin = :fecha_fin
            WHERE
                id = :id
            SQL;

			$statement = $conexion->prepare($query);
			$statement->bindValue(':fecha_inicio', $this->getFecha_inicio(), PDO::PARAM_STR);
			$statement->bindValue(':id_puesto', $this->getId_puesto(), PDO::PARAM_INT);
			$statement->bindValue(':id_empleado', $this->getId_empleado(), PDO::PARAM_INT);
			$statement->bindValue(':fecha_fin', $this->getFecha_fin(), $this->getFecha_fin() === null ? PDO::PARAM_NULL : PDO::PARAM_STR);
			$statement->bindValue(':id', $this->getId(), PDO::PARAM_INT);

			// execute() devuelve true en éxito, false en error.
			return $statement->execute();

		} catch (PDOException $e) {
			// Error de base de datos
			throw new Exception("Error de base de datos al modificar turno (ID: {$this->getId()}): " . $e->getMessage());
		}
	}

	/**
	 * Finaliza un turno estableciendo la fecha_fin.
	 *
	 * @param int    $id        ID del turno a finalizar.
	 * @param string $fecha_fin Fecha de finalización del turno.
	 * @param PDO    $conexion  Conexión PDO.
	 *
	 * @return bool True si la finalización fue exitosa, False en caso contrario.
	 * @throws Exception Si hay citas pendientes o activas asociadas al turno o si ocurre un error de base de datos.
	 */
	public static function finalizar(int $id, string $fecha_fin, PDO $conexion): bool
	{
		try {
			// Establecer la zona horaria para Colombia
			date_default_timezone_set('America/Bogota');

			// Verificar si existen citas activas para este turno (donde fecha_fin es NULL y estado es 1)
			$query = <<<SQL
            SELECT COUNT(*) as total
            FROM citas
            WHERE
            	id_empleado_turno = :id_turno
            	AND fecha_fin IS NULL
            	AND estado = 1
            SQL;

			$statement = $conexion->prepare($query);
			$statement->bindValue(':id_turno', $id, PDO::PARAM_INT);
			$statement->execute();
			$resultado = $statement->fetch(PDO::FETCH_ASSOC);

			// Si hay citas activas, no permitir finalizar el turno
			if ($resultado['total'] > 0) {
				throw new Exception("No se puede finalizar el turno mientras hay una cita activa. Finalice todas las citas antes de finalizar el turno.");
			}

			// Verificar si existen citas pendientes para este turno
			$fecha_actual = date('Y-m-d H:i:s'); // Fecha y hora actual

			// Consulta para verificar si hay citas pendientes (con fecha_fin posterior a la fecha actual)
			$query = <<<SQL
            SELECT COUNT(*) as total
            FROM citas
            WHERE
            	id_empleado_turno = :id_turno
            	AND fecha_fin > :fecha_actual
            SQL;

			$statement = $conexion->prepare($query);
			$statement->bindValue(':id_turno', $id, PDO::PARAM_INT);
			$statement->bindValue(':fecha_actual', $fecha_actual, PDO::PARAM_STR);
			$statement->execute();
			$resultado = $statement->fetch(PDO::FETCH_ASSOC);

			// Si hay citas pendientes, no permitir finalizar el turno
			if ($resultado['total'] > 0) {
				throw new Exception("No se puede finalizar el turno porque tiene citas pendientes. Finalice o reprograme todas las citas antes de finalizar el turno.");
			}

			// Consulta para actualizar la fecha_fin
			$query = <<<SQL
            UPDATE empleados_turnos SET
            	fecha_fin = :fecha_fin
            WHERE
            	id = :id
            SQL;

			$statement = $conexion->prepare($query);
			$statement->bindValue(':fecha_fin', $fecha_fin, PDO::PARAM_STR);
			$statement->bindValue(':id', $id, PDO::PARAM_INT);

			// execute() devuelve true en éxito, false en error.
			return $statement->execute();

		} catch (PDOException $e) {
			// Error de base de datos
			throw new Exception("Error de base de datos al finalizar turno (ID: $id): " . $e->getMessage());
		}
	}

	/**
	 * Elimina un turno de la base de datos.
	 *
	 * @param int $id       ID del turno a eliminar.
	 * @param PDO $conexion Conexión PDO.
	 *
	 * @return bool True si la eliminación fue exitosa, False en caso contrario.
	 * @throws Exception Si ocurre un error de base de datos.
	 */
	public static function eliminar(int $id, PDO $conexion): bool
	{
		try {
			// Consulta para eliminar el turno
			$query = <<<SQL
            DELETE FROM empleados_turnos
            WHERE
            	id = :id
            SQL;

			$statement = $conexion->prepare($query);
			$statement->bindValue(':id', $id, PDO::PARAM_INT);

			// execute() devuelve true en éxito, false en error.
			return $statement->execute();

		} catch (PDOException $e) {
			// Error de base de datos
			throw new Exception("Error de base de datos al eliminar turno (ID: $id): " . $e->getMessage());
		}
	}

	// --- Getters y Setters ---

	public function getId(): ?int
	{
		return $this->id;
	}

	public function setId(?int $id): self
	{
		$this->id = $id;
		return $this;
	}

	public function getFecha_inicio(): ?string
	{
		return $this->fecha_inicio;
	}

	public function setFecha_inicio(?string $fecha_inicio): self
	{
		$this->fecha_inicio = $fecha_inicio;
		return $this;
	}

	public function getId_puesto(): ?int
	{
		return $this->id_puesto;
	}

	public function setId_puesto(?int $id_puesto): self
	{
		$this->id_puesto = $id_puesto;
		return $this;
	}

	public function getDescripcion_puesto(): ?string
	{
		return $this->descripcion_puesto;
	}

	public function setDescripcion_puesto(?string $descripcion_puesto): self
	{
		$this->descripcion_puesto = $descripcion_puesto;
		return $this;
	}

	public function getId_empleado(): ?int
	{
		return $this->id_empleado;
	}

	public function setId_empleado(?int $id_empleado): self
	{
		$this->id_empleado = $id_empleado;
		return $this;
	}

	public function getNombre_empleado(): ?string
	{
		return $this->nombre_empleado;
	}

	public function setNombre_empleado(?string $nombre_empleado): self
	{
		$this->nombre_empleado = $nombre_empleado;
		return $this;
	}

	public function getFecha_fin(): ?string
	{
		return $this->fecha_fin;
	}

	public function setFecha_fin(?string $fecha_fin): self
	{
		$this->fecha_fin = $fecha_fin;
		return $this;
	}

	/**
	 * Verifica si el turno está activo (sin fecha_fin).
	 * @return bool
	 */
	public function isActivo(): bool
	{
		return $this->fecha_fin === null;
	}

	/**
	 * Verifica si el turno ya tiene una cita activa asociada.
	 *
	 * @param PDO $conexion Conexión PDO.
	 *
	 * @return bool True si el turno ya tiene una cita activa, False en caso contrario.
	 * @throws Exception Si hay error en DB.
	 */
	public function tieneCitaActiva(PDO $conexion): bool
	{
		try {
			// Validar que el turno tenga un ID válido
			if ($this->getId() <= 0) {
				throw new Exception("El turno debe tener un ID válido para verificar si tiene citas activas.");
			}

			// Consulta para verificar si hay citas activas para este turno
			$query = <<<SQL
            SELECT COUNT(*) as total
            FROM citas
            WHERE
            	id_empleado_turno = :id_turno
            	AND fecha_fin IS NULL
            	AND estado = 1
            SQL;

			$statement = $conexion->prepare($query);
			$statement->bindValue(':id_turno', $this->getId(), PDO::PARAM_INT);
			$statement->execute();
			$resultado = $statement->fetch(PDO::FETCH_ASSOC);

			return ($resultado['total'] > 0);

		} catch (PDOException $e) {
			throw new Exception("Error al verificar si el turno tiene citas activas: " . $e->getMessage());
		}
	}

	/**
	 * Método estático para verificar si un turno tiene una cita activa asociada.
	 *
	 * @param int $id_turno ID del turno a verificar.
	 * @param PDO $conexion Conexión PDO.
	 *
	 * @return bool True si el turno ya tiene una cita activa, False en caso contrario.
	 * @throws Exception Si hay error en DB.
	 */
	public static function tieneCitaActivaById(int $id_turno, PDO $conexion): bool
	{
		try {
			// Consulta para verificar si hay citas activas para este turno
			$query = <<<SQL
            SELECT COUNT(*) as total
            FROM citas
            WHERE
            	id_empleado_turno = :id_turno
            	AND fecha_fin IS NULL
            	AND estado = 1
            SQL;

			$statement = $conexion->prepare($query);
			$statement->bindValue(':id_turno', $id_turno, PDO::PARAM_INT);
			$statement->execute();
			$resultado = $statement->fetch(PDO::FETCH_ASSOC);

			return ($resultado['total'] > 0);

		} catch (PDOException $e) {
			throw new Exception("Error al verificar si el turno tiene citas activas: " . $e->getMessage());
		}
	}

	/**
	 * Cuenta los turnos activos para un centro de costo específico que pueden impedir el cierre de caja.
	 *
	 * Filtros aplicados:
	 * - fecha_fin IS NULL (turnos no finalizados)
	 * - centro de costo específico (a través de la relación con puestos)
	 *
	 * @param int $id_centro_costo ID del centro de costo.
	 * @param PDO $conexion Conexión PDO.
	 *
	 * @return int Número de turnos activos encontrados.
	 * @throws Exception Si hay error en DB.
	 */
	public static function countActiveShiftsByCentroCosto(int $id_centro_costo, PDO $conexion): int
	{
		try {
			// Consulta optimizada para contar turnos activos por centro de costo (Usando Heredoc)
			// Incluye filtros para performance y lógica de negocio correcta
			$query = <<<SQL
			SELECT COUNT(*) as total_turnos_activos
			FROM empleados_turnos et
			INNER JOIN puestos p ON et.id_puesto = p.id
			WHERE et.fecha_fin IS NULL
			AND p.id_centro_costo = :id_centro_costo
			SQL;

			$statement = $conexion->prepare($query);
			$statement->bindValue(':id_centro_costo', $id_centro_costo, PDO::PARAM_INT);
			$statement->execute();
			$resultado = $statement->fetch(PDO::FETCH_ASSOC);

			return (int)($resultado['total_turnos_activos'] ?? 0);

		} catch (PDOException $e) {
			throw new Exception("Error al contar turnos activos por centro de costo: " . $e->getMessage());
		}
	}
}
