<?php

declare(strict_types=1);

namespace App\classes;

use AllowDynamicProperties;
use Exception;
use PDO;
use PDOException;
use App\classes\Submenu;

#[AllowDynamicProperties] class Menu
{
	// --- Atributos ---
	private ?int    $id          = null;
	private ?string $url         = null;
	private ?string $texto       = null;
	private ?string $icono       = null;
	private ?int    $prioridad   = null;
	private ?int    $tiene_subs  = null;
	private ?int    $estado      = null;

	/**
	 * Constructor: Inicializa las propiedades del objeto Menu.
	 */
	public function __construct()
	{
		$this->id         = 0;
		$this->url        = null;
		$this->texto      = null;
		$this->icono      = null;
		$this->prioridad  = 0;
		$this->tiene_subs = 0;
		$this->estado     = 1; // Estado activo por defecto
	}

	/**
	 * Método estático para construir un objeto Menu desde un array (ej. fila de DB).
	 *
	 * @param array $resultado Array asociativo con los datos del menu.
	 *
	 * @return self Instancia de Menu.
	 * @throws Exception Si ocurre un error durante la construcción.
	 */
	public static function construct(array $resultado = []): self
	{
		try {
			$objeto              = new self();
			$objeto->id          = isset($resultado['id']) ? (int)$resultado['id'] : 0;
			$objeto->url         = $resultado['url'] ?? null;
			$objeto->texto       = $resultado['texto'] ?? null;
			$objeto->icono       = $resultado['icono'] ?? null;
			$objeto->prioridad   = isset($resultado['prioridad']) ? (int)$resultado['prioridad'] : 0;
			$objeto->tiene_subs  = isset($resultado['tiene_subs']) ? (int)$resultado['tiene_subs'] : 0;
			$objeto->estado      = isset($resultado['estado']) ? (int)$resultado['estado'] : 1;
			return $objeto;
		} catch (Exception $e) {
			throw new Exception("Error al construir objeto Menu: " . $e->getMessage());
		}
	}

	/**
	 * Obtiene un menu por su ID.
	 *
	 * @param int $id       ID del menu.
	 * @param PDO $conexion Conexión PDO.
	 *
	 * @return self|null Objeto Menu o null si no se encuentra.
	 * @throws Exception Si hay error en DB.
	 */
	public static function get(int $id, PDO $conexion): ?self
	{
		try {
			// Consulta para obtener menu por ID (Usando Heredoc)
			$query = <<<SQL
            SELECT
            	*
            FROM menus
            WHERE
            	id = :id
            LIMIT 1
            SQL;

			$statement = $conexion->prepare($query);
			$statement->bindValue(":id", $id, PDO::PARAM_INT);
			$statement->execute();
			$resultado = $statement->fetch(PDO::FETCH_ASSOC);

			return $resultado ? self::construct($resultado) : null;

		} catch (PDOException $e) {
			throw new Exception("Error al obtener Menu: " . $e->getMessage());
		}
	}

	/**
	 * Obtiene una lista de menus activos.
	 *
	 * @param PDO $conexion Conexión PDO.
	 *
	 * @return array Array de objetos Menu.
	 * @throws Exception Si hay error en DB.
	 */
	public static function get_list(PDO $conexion): array
	{
		try {
			// Consulta para obtener lista de menus activos (Usando Heredoc)
			$query = <<<SQL
            SELECT
            	*
            FROM menus
            WHERE
            	estado = 1
            ORDER BY
            	prioridad
            SQL;

			$statement = $conexion->prepare($query);
			$statement->execute();
			$resultados = $statement->fetchAll(PDO::FETCH_ASSOC);

			$listado = [];
			foreach ($resultados as $resultado) {
				$listado[] = self::construct($resultado);
			}
			return $listado;

		} catch (PDOException $e) {
			throw new Exception("Error al obtener lista de Menus: " . $e->getMessage());
		}
	}
	
	/**
	 * Obtiene todos los menús y sus submenús asociados para un perfil específico.
	 *
	 * @param int $id_perfil ID del perfil para el cual se obtendrán los menús.
	 * @param PDO $conexion Conexión PDO.
	 *
	 * @return array Array de objetos Menu con sus submenús asociados.
	 * @throws Exception Si hay error en DB.
	 */
	public static function getByIdPerfil(int $id_perfil, PDO $conexion): array
	{
		if ($id_perfil <= 0) {
			throw new Exception("ID de perfil inválido para obtener menús.");
		}
		
		try {
			// Consulta para obtener menús y submenús asociados al perfil a través de acciones
			$query = <<<SQL
            SELECT DISTINCT
                m.*,
                s.id as submenu_id,
                s.url as submenu_url,
                s.texto as submenu_texto,
                s.prioridad as submenu_prioridad,
                s.estado as submenu_estado
            FROM
                menus m
            LEFT JOIN acciones a ON m.id = a.id_menu
            LEFT JOIN submenus s ON a.id_submenu = s.id
            INNER JOIN perfiles_acciones pa ON a.id = pa.id_accion
            WHERE
                pa.id_perfil = :id_perfil
                AND m.estado = 1
                AND a.estado = 1
                AND (s.estado = 1 OR s.estado IS NULL)
            ORDER BY
                m.prioridad, s.prioridad
            SQL;
			
			$statement = $conexion->prepare($query);
			$statement->bindValue(':id_perfil', $id_perfil, PDO::PARAM_INT);
			$statement->execute();
			$resultados = $statement->fetchAll(PDO::FETCH_ASSOC);
			
			// Estructura para almacenar los menús y sus submenús
			$menus = [];
			$menu_ids = [];
			
			foreach ($resultados as $fila) {
				// ID del menú actual
				$menu_id = (int)$fila['id'];
				
				// Si este menú no ha sido procesado aún, crearlo y agregarlo al array
				if (!in_array($menu_id, $menu_ids)) {
					$menu = self::construct($fila);
					$menu_ids[] = $menu_id;
					
					// Inicializar el array de submenús para este menú
					$menus[$menu_id] = $menu;
					$menus[$menu_id]->submenus = [];
				}
				
				// Si hay un submenu asociado, agregarlo al menú correspondiente
				if (!empty($fila['submenu_id'])) {
					$submenu = new Submenu();
					$submenu->setId((int)$fila['submenu_id']);
					$submenu->setIdMenu($menu_id);
					$submenu->setUrl($fila['submenu_url']);
					$submenu->setTexto($fila['submenu_texto']);
					$submenu->setPrioridad((int)$fila['submenu_prioridad']);
					$submenu->setEstado((int)$fila['submenu_estado']);
					
					// Verificar si este submenu ya existe en el array (para evitar duplicados)
					$submenu_exists = false;
					foreach ($menus[$menu_id]->submenus as $existing_submenu) {
						if ($existing_submenu->getId() === $submenu->getId()) {
							$submenu_exists = true;
							break;
						}
					}
					
					// Agregar el submenu solo si no existe ya
					if (!$submenu_exists) {
						$menus[$menu_id]->submenus[] = $submenu;
					}
				}
			}
			
			// Convertir el array asociativo a un array indexado
			return array_values($menus);
			
		} catch (PDOException $e) {
			throw new Exception("Error de base de datos al obtener menús por perfil: " . $e->getMessage());
		} catch (Exception $e) {
			throw new Exception("Error al obtener menús por perfil: " . $e->getMessage());
		}
	}

	/**
	 * Crea un nuevo menu en la base de datos a partir de un objeto Menu.
	 *
	 * @param PDO $conexion Conexión PDO.
	 *
	 * @return int|false El ID del nuevo menu creado o false en caso de error.
	 * @throws Exception Si los datos requeridos en el objeto están vacíos o hay error en DB.
	 */
	function crear(PDO $conexion): int|false
	{
		// Validaciones básicas sobre el objeto
		if (empty($this->getUrl()) || empty($this->getTexto())) {
			throw new Exception("URL y texto son requeridos en el objeto Menu para crearlo.");
		}

		try {
			// Preparar la consulta INSERT usando Heredoc
			$query = <<<SQL
            INSERT INTO menus (
            	 url
            	,texto
            	,icono
            	,prioridad
            	,tiene_subs
            	,estado
            ) VALUES (
            	 :url
            	,:texto
            	,:icono
            	,:prioridad
            	,:tiene_subs
            	,:estado
            )
            SQL;

			$statement = $conexion->prepare($query);

			// Bind de parámetros desde el objeto
			$statement->bindValue(':url', $this->getUrl(), PDO::PARAM_STR);
			$statement->bindValue(':texto', $this->getTexto(), PDO::PARAM_STR);
			$statement->bindValue(':icono', $this->getIcono(), PDO::PARAM_STR);
			$statement->bindValue(':prioridad', $this->getPrioridad(), PDO::PARAM_INT);
			$statement->bindValue(':tiene_subs', $this->getTieneSubs(), PDO::PARAM_INT);
			$statement->bindValue(':estado', $this->getEstado(), PDO::PARAM_INT);

			// Ejecutar la consulta
			$success = $statement->execute();

			if ($success) {
				// Devolver el ID del menu recién creado
				return (int)$conexion->lastInsertId();
			} else {
				return false; // Error en la ejecución
			}

		} catch (PDOException $e) {
			throw new Exception("Error de base de datos al crear menu: " . $e->getMessage());
		} catch (Exception $e) {
			// Capturar otros errores
			throw new Exception("Error al crear menu: " . $e->getMessage());
		}
	}

	/**
	 * Actualiza un menu existente.
	 *
	 * @param PDO $conexion Conexión PDO.
	 *
	 * @return bool True si la actualización fue exitosa, false en caso contrario.
	 * @throws Exception Si hay error en DB.
	 */
	function modificar(PDO $conexion): bool
	{
		// Validar que el ID exista
		if (empty($this->getId())) {
			throw new Exception("ID es requerido para actualizar un Menu.");
		}

		try {
			// Consulta para actualizar el menu
			$query = <<<SQL
            UPDATE menus SET
                url = :url,
                texto = :texto,
                icono = :icono,
                prioridad = :prioridad,
                tiene_subs = :tiene_subs,
                estado = :estado
            WHERE
                id = :id
            SQL;

			$statement = $conexion->prepare($query);
			$statement->bindValue(':url', $this->getUrl(), PDO::PARAM_STR);
			$statement->bindValue(':texto', $this->getTexto(), PDO::PARAM_STR);
			$statement->bindValue(':icono', $this->getIcono(), PDO::PARAM_STR);
			$statement->bindValue(':prioridad', $this->getPrioridad(), PDO::PARAM_INT);
			$statement->bindValue(':tiene_subs', $this->getTieneSubs(), PDO::PARAM_INT);
			$statement->bindValue(':estado', $this->getEstado(), PDO::PARAM_INT);
			$statement->bindValue(':id', $this->getId(), PDO::PARAM_INT);

			return $statement->execute();

		} catch (PDOException $e) {
			throw new Exception("Error de base de datos al actualizar menu (ID: {$this->getId()}): " . $e->getMessage());
		}
	}

	/**
	 * Desactiva un menu por su ID.
	 *
	 * @param int $id       ID del menu a desactivar.
	 * @param PDO $conexion Conexión PDO.
	 *
	 * @return bool True si la desactivación fue exitosa, false en caso contrario.
	 * @throws Exception Si hay error en DB.
	 */
	public static function desactivar(int $id, PDO $conexion): bool
	{
		try {
			// Consulta para desactivar el menu
			$query = <<<SQL
            UPDATE menus SET
                estado = 0
            WHERE
                id = :id
            SQL;

			$statement = $conexion->prepare($query);
			$statement->bindValue(':id', $id, PDO::PARAM_INT);

			return $statement->execute();

		} catch (PDOException $e) {
			throw new Exception("Error de base de datos al desactivar menu (ID: $id): " . $e->getMessage());
		}
	}

	/**
	 * Activa un menu por su ID.
	 *
	 * @param int $id       ID del menu a activar.
	 * @param PDO $conexion Conexión PDO.
	 *
	 * @return bool True si la activación fue exitosa, false en caso contrario.
	 * @throws Exception Si hay error en DB.
	 */
	public static function activar(int $id, PDO $conexion): bool
	{
		try {
			// Consulta para activar el menu
			$query = <<<SQL
            UPDATE menus SET
                estado = 1
            WHERE
                id = :id
            SQL;

			$statement = $conexion->prepare($query);
			$statement->bindValue(':id', $id, PDO::PARAM_INT);

			return $statement->execute();

		} catch (PDOException $e) {
			throw new Exception("Error de base de datos al activar menu (ID: $id): " . $e->getMessage());
		}
	}

	// --- Getters y Setters ---

	public function getId(): ?int
	{
		return $this->id;
	}

	public function setId(?int $id): self
	{
		$this->id = $id;
		return $this;
	}

	public function getUrl(): ?string
	{
		return $this->url;
	}

	public function setUrl(?string $url): self
	{
		$this->url = $url;
		return $this;
	}

	public function getTexto(): ?string
	{
		return $this->texto;
	}

	public function setTexto(?string $texto): self
	{
		$this->texto = $texto;
		return $this;
	}

	public function getIcono(): ?string
	{
		return $this->icono;
	}

	public function setIcono(?string $icono): self
	{
		$this->icono = $icono;
		return $this;
	}

	public function getPrioridad(): ?int
	{
		return $this->prioridad;
	}

	public function setPrioridad(?int $prioridad): self
	{
		$this->prioridad = $prioridad;
		return $this;
	}

	public function getTieneSubs(): ?int
	{
		return $this->tiene_subs;
	}

	public function setTieneSubs(?int $tiene_subs): self
	{
		$this->tiene_subs = $tiene_subs;
		return $this;
	}

	public function getEstado(): ?int
	{
		return $this->estado;
	}

	public function setEstado(?int $estado): self
	{
		$this->estado = $estado;
		return $this;
	}

	/**
	 * Verifica si el menu está activo.
	 * @return bool
	 */
	public function isActivo(): bool
	{
		return $this->estado === 1;
	}

	/**
	 * Verifica si el menu tiene submenús.
	 * @return bool
	 */
	public function hasSubs(): bool
	{
		return $this->tiene_subs === 1;
	}
}
