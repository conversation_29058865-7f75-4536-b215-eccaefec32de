<?php

declare(strict_types=1);

namespace App\classes;

use Exception;
use PDO;
use PDOException;

class InventarioMovimiento
{
	// --- Atributos ---
	private ?int    $id                   = null;
	private ?int    $id_centro_costo      = null;
	private ?int    $id_producto          = null;
	private ?string $tipo                 = null;
	private ?int    $cantidad             = null;
	private ?string $fecha                = null;
	private ?int    $id_usuario           = null;
	private ?string $nombre_centro_costo  = null;  // Nombre del centro de costo asociado
	private ?string $descripcion_producto = null;  // Descripción del producto asociado
	private ?float  $valor_producto       = null;  // Valor del producto asociado
	private ?string $nombre_usuario       = null;  // Nombre del usuario asociado
	private ?string $nota                 = null;  // Nota adicional para el movimiento

	/**
	 * Constructor: Inicializa las propiedades del objeto InventarioMovimiento.
	 *
	 * @param int|null    $id_centro_costo ID del centro de costo (obligatorio)
	 * @param int|null    $id_producto     ID del producto (obligatorio)
	 * @param string|null $tipo            Tipo de movimiento: 'ingreso' o 'egreso' (obligatorio)
	 * @param int|null    $cantidad        Cantidad del movimiento (obligatorio, debe ser > 0)
	 * @param int|null    $id_usuario      ID del usuario que registra el movimiento (obligatorio)
	 * @param string|null $nota            Nota adicional para el movimiento (opcional)
	 */
	public function __construct(?int $id_centro_costo = null, ?int $id_producto = null, ?string $tipo = null, ?int $cantidad = null, ?int $id_usuario = null, ?string $nota = null)
	{
		// Establecer la zona horaria para Colombia
		date_default_timezone_set('America/Bogota');

		$this->id                   = 0;
		$this->id_centro_costo      = $id_centro_costo;
		$this->id_producto          = $id_producto;
		$this->tipo                 = $tipo;
		$this->cantidad             = $cantidad;
		$this->fecha                = date('Y-m-d H:i:s'); // Timestamp actual automático
		$this->id_usuario           = $id_usuario;
		$this->nombre_centro_costo  = null;
		$this->descripcion_producto = null;
		$this->valor_producto       = null;
		$this->nombre_usuario       = null;
		$this->nota                 = $nota;
	}

	/**
	 * Método estático para construir un objeto InventarioMovimiento desde un array (ej. fila de DB).
	 *
	 * @param array $resultado Array asociativo con los datos del movimiento.
	 *
	 * @return self Instancia de InventarioMovimiento.
	 * @throws Exception Si ocurre un error durante la construcción.
	 */
	public static function construct(array $resultado = []): self
	{
		try {
			$objeto                         = new self();
			$objeto->id                     = isset($resultado['id']) ? (int)$resultado['id'] : 0;
			$objeto->id_centro_costo        = isset($resultado['id_centro_costo']) ? (int)$resultado['id_centro_costo'] : null;
			$objeto->id_producto            = isset($resultado['id_producto']) ? (int)$resultado['id_producto'] : null;
			$objeto->tipo                   = $resultado['tipo'] ?? null;
			$objeto->cantidad               = isset($resultado['cantidad']) ? (int)$resultado['cantidad'] : null;
			$objeto->fecha                  = $resultado['fecha'] ?? null;
			$objeto->id_usuario             = isset($resultado['id_usuario']) ? (int)$resultado['id_usuario'] : null;
			$objeto->nombre_centro_costo    = $resultado['nombre_centro_costo'] ?? null;
			$objeto->descripcion_producto   = $resultado['descripcion_producto'] ?? null;
			$objeto->valor_producto         = isset($resultado['valor_producto']) ? (float)$resultado['valor_producto'] : null;
			$objeto->nombre_usuario         = $resultado['nombre_usuario'] ?? null;
			$objeto->nota                   = $resultado['nota'] ?? null;
			return $objeto;
		} catch (Exception $e) {
			// Considera loggear el error aquí
			throw new Exception("Error al construir InventarioMovimiento: " . $e->getMessage());
		}
	}

	// --- Métodos de Acceso a Datos (Estáticos) ---

	/**
	 * Obtiene un movimiento de inventario por su ID.
	 *
	 * @param int $id       ID del movimiento.
	 * @param PDO $conexion Conexión PDO.
	 *
	 * @return self|null Objeto InventarioMovimiento o null si no se encuentra.
	 * @throws Exception Si hay error en DB.
	 */
	public static function get(int $id, PDO $conexion): ?self
	{
		try {
			// Consulta para obtener movimiento por ID con datos relacionados (Usando Heredoc)
			$query = <<<SQL
            SELECT
            	im.*,
            	cc.nombre AS nombre_centro_costo,
            	p.descripcion AS descripcion_producto,
            	p.valor AS valor_producto,
            	u.nombre AS nombre_usuario
            FROM inventario_movimientos im
            LEFT JOIN centros_costos cc ON im.id_centro_costo = cc.id
            LEFT JOIN productos p ON im.id_producto = p.id
            LEFT JOIN usuarios u ON im.id_usuario = u.id
            WHERE
            	im.id = :id
            LIMIT 1
            SQL;

			$statement = $conexion->prepare($query);
			$statement->bindValue(":id", $id, PDO::PARAM_INT);
			$statement->execute();
			$resultado = $statement->fetch(PDO::FETCH_ASSOC);

			return $resultado ? self::construct($resultado) : null;

		} catch (PDOException $e) {
			throw new Exception("Error al obtener InventarioMovimiento (ID: $id): " . $e->getMessage());
		}
	}

	/**
	 * Obtiene una lista de todos los movimientos de inventario.
	 *
	 * @param PDO $conexion Conexión PDO.
	 *
	 * @return array Array de objetos InventarioMovimiento.
	 * @throws Exception Si hay error en DB.
	 */
	public static function get_list(PDO $conexion): array
	{
		try {
			// Consulta para obtener lista de movimientos con datos relacionados (Usando Heredoc)
			$query = <<<SQL
            SELECT
            	im.*,
            	cc.nombre AS nombre_centro_costo,
            	p.descripcion AS descripcion_producto,
            	p.valor AS valor_producto,
            	u.nombre AS nombre_usuario
            FROM inventario_movimientos im
            LEFT JOIN centros_costos cc ON im.id_centro_costo = cc.id
            LEFT JOIN productos p ON im.id_producto = p.id
            LEFT JOIN usuarios u ON im.id_usuario = u.id
            ORDER BY
            	im.fecha DESC
            SQL;

			$statement = $conexion->prepare($query);
			$statement->execute();
			$resultados = $statement->fetchAll(PDO::FETCH_ASSOC);

			$listado = [];
			foreach ($resultados as $resultado) {
				$listado[] = self::construct($resultado);
			}
			return $listado;

		} catch (PDOException $e) {
			throw new Exception("Error al obtener lista de InventarioMovimiento: " . $e->getMessage());
		}
	}

	/**
	 * Obtiene movimientos por ID de centro de costo.
	 *
	 * @param int $id_centro_costo ID del centro de costo.
	 * @param PDO $conexion        Conexión PDO.
	 *
	 * @return array Array de objetos InventarioMovimiento.
	 * @throws Exception Si hay error en DB.
	 */
	public static function obtenerPorCentroCosto(int $id_centro_costo, PDO $conexion): array
	{
		try {
			// Consulta para obtener movimientos por ID de centro de costo (Usando Heredoc)
			$query = <<<SQL
            SELECT
            	im.*,
            	cc.nombre AS nombre_centro_costo,
            	p.descripcion AS descripcion_producto,
            	p.valor AS valor_producto,
            	u.nombre AS nombre_usuario
            FROM inventario_movimientos im
            LEFT JOIN centros_costos cc ON im.id_centro_costo = cc.id
            LEFT JOIN productos p ON im.id_producto = p.id
            LEFT JOIN usuarios u ON im.id_usuario = u.id
            WHERE
            	im.id_centro_costo = :id_centro_costo
            ORDER BY
            	im.fecha DESC
            SQL;

			$statement = $conexion->prepare($query);
			$statement->bindValue(":id_centro_costo", $id_centro_costo, PDO::PARAM_INT);
			$statement->execute();
			$resultados = $statement->fetchAll(PDO::FETCH_ASSOC);

			$listado = [];
			foreach ($resultados as $resultado) {
				$listado[] = self::construct($resultado);
			}
			return $listado;

		} catch (PDOException $e) {
			throw new Exception("Error al obtener InventarioMovimiento por centro de costo (ID: $id_centro_costo): " . $e->getMessage());
		}
	}

	/**
	 * Obtiene movimientos por ID de producto.
	 *
	 * @param int $id_producto ID del producto.
	 * @param PDO $conexion    Conexión PDO.
	 *
	 * @return array Array de objetos InventarioMovimiento.
	 * @throws Exception Si hay error en DB.
	 */
	public static function obtenerPorProducto(int $id_producto, PDO $conexion): array
	{
		try {
			// Consulta para obtener movimientos por ID de producto (Usando Heredoc)
			$query = <<<SQL
            SELECT
            	im.*,
            	cc.nombre AS nombre_centro_costo,
            	p.descripcion AS descripcion_producto,
            	p.valor AS valor_producto,
            	u.nombre AS nombre_usuario
            FROM inventario_movimientos im
            LEFT JOIN centros_costos cc ON im.id_centro_costo = cc.id
            LEFT JOIN productos p ON im.id_producto = p.id
            LEFT JOIN usuarios u ON im.id_usuario = u.id
            WHERE
            	im.id_producto = :id_producto
            ORDER BY
            	im.fecha DESC
            SQL;

			$statement = $conexion->prepare($query);
			$statement->bindValue(":id_producto", $id_producto, PDO::PARAM_INT);
			$statement->execute();
			$resultados = $statement->fetchAll(PDO::FETCH_ASSOC);

			$listado = [];
			foreach ($resultados as $resultado) {
				$listado[] = self::construct($resultado);
			}
			return $listado;

		} catch (PDOException $e) {
			throw new Exception("Error al obtener InventarioMovimiento por producto (ID: $id_producto): " . $e->getMessage());
		}
	}

	/**
	 * Obtiene movimientos por ID de usuario.
	 *
	 * @param int $id_usuario ID del usuario.
	 * @param PDO $conexion   Conexión PDO.
	 *
	 * @return array Array de objetos InventarioMovimiento.
	 * @throws Exception Si hay error en DB.
	 */
	public static function obtenerPorUsuario(int $id_usuario, PDO $conexion): array
	{
		try {
			// Consulta para obtener movimientos por ID de usuario (Usando Heredoc)
			$query = <<<SQL
            SELECT
            	im.*,
            	cc.nombre AS nombre_centro_costo,
            	p.descripcion AS descripcion_producto,
            	p.valor AS valor_producto,
            	u.nombre AS nombre_usuario
            FROM inventario_movimientos im
            LEFT JOIN centros_costos cc ON im.id_centro_costo = cc.id
            LEFT JOIN productos p ON im.id_producto = p.id
            LEFT JOIN usuarios u ON im.id_usuario = u.id
            WHERE
            	im.id_usuario = :id_usuario
            ORDER BY
            	im.fecha DESC
            SQL;

			$statement = $conexion->prepare($query);
			$statement->bindValue(":id_usuario", $id_usuario, PDO::PARAM_INT);
			$statement->execute();
			$resultados = $statement->fetchAll(PDO::FETCH_ASSOC);

			$listado = [];
			foreach ($resultados as $resultado) {
				$listado[] = self::construct($resultado);
			}
			return $listado;

		} catch (PDOException $e) {
			throw new Exception("Error al obtener InventarioMovimiento por usuario (ID: $id_usuario): " . $e->getMessage());
		}
	}

	/**
	 * Obtiene movimientos por tipo ('ingreso' o 'egreso').
	 *
	 * @param string $tipo     Tipo de movimiento ('ingreso' o 'egreso').
	 * @param PDO    $conexion Conexión PDO.
	 *
	 * @return array Array de objetos InventarioMovimiento.
	 * @throws Exception Si hay error en DB.
	 */
	public static function obtenerPorTipo(string $tipo, PDO $conexion): array
	{
		try {
			// Consulta para obtener movimientos por tipo (Usando Heredoc)
			$query = <<<SQL
            SELECT
            	im.*,
            	cc.nombre AS nombre_centro_costo,
            	p.descripcion AS descripcion_producto,
            	p.valor AS valor_producto,
            	u.nombre AS nombre_usuario
            FROM inventario_movimientos im
            LEFT JOIN centros_costos cc ON im.id_centro_costo = cc.id
            LEFT JOIN productos p ON im.id_producto = p.id
            LEFT JOIN usuarios u ON im.id_usuario = u.id
            WHERE
            	im.tipo = :tipo
            ORDER BY
            	im.fecha DESC
            SQL;

			$statement = $conexion->prepare($query);
			$statement->bindValue(":tipo", $tipo, PDO::PARAM_STR);
			$statement->execute();
			$resultados = $statement->fetchAll(PDO::FETCH_ASSOC);

			$listado = [];
			foreach ($resultados as $resultado) {
				$listado[] = self::construct($resultado);
			}
			return $listado;

		} catch (PDOException $e) {
			throw new Exception("Error al obtener InventarioMovimiento por tipo ($tipo): " . $e->getMessage());
		}
	}

	/**
	 * Obtiene movimientos de inventario por rango de fechas con filtros opcionales.
	 *
	 * @param string      $fecha_inicio Fecha de inicio (formato YYYY-MM-DD).
	 * @param string      $fecha_fin    Fecha de fin (formato YYYY-MM-DD).
	 * @param PDO         $conexion     Conexión PDO.
	 * @param int|null    $id_usuario   ID del usuario (opcional).
	 * @param string|null $tipo         Tipo de movimiento ('ingreso' o 'egreso') (opcional).
	 *
	 * @return array Array de objetos InventarioMovimiento.
	 * @throws Exception Si hay error en DB.
	 */
	public static function obtenerPorRangoFechas(string $fecha_inicio, string $fecha_fin, PDO $conexion, ?int $id_usuario = null, ?string $tipo = null): array
	{
		try {
			// Construir la consulta base
			$query = <<<SQL
            SELECT
            	im.*,
            	cc.nombre AS nombre_centro_costo,
            	p.descripcion AS descripcion_producto,
            	p.valor AS valor_producto,
            	u.nombre AS nombre_usuario
            FROM inventario_movimientos im
            LEFT JOIN centros_costos cc ON im.id_centro_costo = cc.id
            LEFT JOIN productos p ON im.id_producto = p.id
            LEFT JOIN usuarios u ON im.id_usuario = u.id
            WHERE
            	DATE(im.fecha) BETWEEN :fecha_inicio AND :fecha_fin
            SQL;

			// Agregar filtros opcionales
			if ($id_usuario !== null) {
				$query .= " AND im.id_usuario = :id_usuario";
			}

			if ($tipo !== null) {
				$query .= " AND im.tipo = :tipo";
			}

			$query .= " ORDER BY im.fecha DESC, im.id DESC";

			$statement = $conexion->prepare($query);
			$statement->bindValue(':fecha_inicio', $fecha_inicio, PDO::PARAM_STR);
			$statement->bindValue(':fecha_fin', $fecha_fin, PDO::PARAM_STR);

			// Bind filtros opcionales
			if ($id_usuario !== null) {
				$statement->bindValue(':id_usuario', $id_usuario, PDO::PARAM_INT);
			}

			if ($tipo !== null) {
				$statement->bindValue(':tipo', $tipo, PDO::PARAM_STR);
			}

			$statement->execute();
			$resultados = $statement->fetchAll(PDO::FETCH_ASSOC);

			$listado = [];
			foreach ($resultados as $resultado) {
				$listado[] = self::construct($resultado);
			}
			return $listado;

		} catch (PDOException $e) {
			throw new Exception("Error al obtener movimientos por rango de fechas ($fecha_inicio - $fecha_fin): " . $e->getMessage());
		}
	}

	/**
	 * Crea un nuevo movimiento de inventario en la base de datos a partir de un objeto InventarioMovimiento.
	 * El objeto InventarioMovimiento debe estar completamente poblado.
	 *
	 * @param PDO $conexion Conexión PDO.
	 *
	 * @return int|false El ID del nuevo movimiento creado o false en caso de error.
	 * @throws Exception Si los datos requeridos en el objeto están vacíos o hay error en DB.
	 */
	function crear(PDO $conexion): int|false
	{
		// Validaciones básicas sobre el objeto
		if ($this->getId_centro_costo() === null || $this->getId_producto() === null || empty($this->getTipo()) || $this->getCantidad() === null || $this->getId_usuario() === null) {
			throw new Exception("ID centro de costo, ID producto, tipo, cantidad e ID usuario son requeridos en el objeto InventarioMovimiento para crearlo.");
		}

		// Validar tipo enum
		if (!$this->validarTipo($this->getTipo())) {
			throw new Exception("El tipo debe ser 'ingreso' o 'egreso'.");
		}

		// Validar cantidad positiva
		if (!$this->validarCantidadPositiva($this->getCantidad())) {
			throw new Exception("La cantidad debe ser un número entero positivo mayor que cero.");
		}

		// Validar que el centro de costo existe
		if (!$this->validarCentroCostoExiste($this->getId_centro_costo(), $conexion)) {
			throw new Exception("El centro de costo especificado no existe.");
		}

		// Validar que el producto existe
		if (!$this->validarProductoExiste($this->getId_producto(), $conexion)) {
			throw new Exception("El producto especificado no existe.");
		}

		// Validar que el usuario existe
		if (!$this->validarUsuarioExiste($this->getId_usuario(), $conexion)) {
			throw new Exception("El usuario especificado no existe.");
		}

		try {
			// Establecer la zona horaria para Colombia
			date_default_timezone_set('America/Bogota');

			// Si fecha no está establecida, usar timestamp actual
			if (empty($this->getFecha())) {
				$this->setFecha(date('Y-m-d H:i:s'));
			}

			return $this->_insert($conexion);

		} catch (Exception $e) {
			// Capturar otros errores
			throw new Exception("Error al crear movimiento de inventario: " . $e->getMessage());
		}
	}

	/**
	 * Actualiza un movimiento de inventario existente.
	 *
	 * @param PDO $conexion Conexión PDO.
	 *
	 * @return bool True si la actualización fue exitosa, false en caso contrario.
	 * @throws Exception Si hay error en DB.
	 */
	function modificar(PDO $conexion): bool
	{
		// Validar que el ID exista
		if (empty($this->getId())) {
			throw new Exception("ID es requerido para actualizar un InventarioMovimiento.");
		}

		// Validaciones básicas sobre el objeto
		if ($this->getId_centro_costo() === null || $this->getId_producto() === null || empty($this->getTipo()) || $this->getCantidad() === null || $this->getId_usuario() === null) {
			throw new Exception("ID centro de costo, ID producto, tipo, cantidad e ID usuario son requeridos en el objeto InventarioMovimiento para modificarlo.");
		}

		// Validar tipo enum
		if (!$this->validarTipo($this->getTipo())) {
			throw new Exception("El tipo debe ser 'ingreso' o 'egreso'.");
		}

		// Validar cantidad positiva
		if (!$this->validarCantidadPositiva($this->getCantidad())) {
			throw new Exception("La cantidad debe ser un número entero positivo mayor que cero.");
		}

		// Validar que el centro de costo existe
		if (!$this->validarCentroCostoExiste($this->getId_centro_costo(), $conexion)) {
			throw new Exception("El centro de costo especificado no existe.");
		}

		// Validar que el producto existe
		if (!$this->validarProductoExiste($this->getId_producto(), $conexion)) {
			throw new Exception("El producto especificado no existe.");
		}

		// Validar que el usuario existe
		if (!$this->validarUsuarioExiste($this->getId_usuario(), $conexion)) {
			throw new Exception("El usuario especificado no existe.");
		}

		try {
			// Establecer la zona horaria para Colombia
			date_default_timezone_set('America/Bogota');

			return $this->_update($conexion);

		} catch (Exception $e) {
			throw new Exception("Error al modificar movimiento de inventario: " . $e->getMessage());
		}
	}

	/**
	 * Método privado para insertar un nuevo movimiento en la base de datos.
	 *
	 * @param PDO $conexion Conexión PDO.
	 *
	 * @return int|false El ID del nuevo movimiento creado o false en caso de error.
	 * @throws Exception Si hay error en DB.
	 */
	private function _insert(PDO $conexion): int|false
	{
		try {
			// Preparar la consulta INSERT usando Heredoc
			$query = <<<SQL
            INSERT INTO inventario_movimientos (
            	 id_centro_costo
            	,nota
            	,id_producto
            	,tipo
            	,cantidad
            	,fecha
            	,id_usuario
            ) VALUES (
            	 :id_centro_costo
            	,:nota
            	,:id_producto
            	,:tipo
            	,:cantidad
            	,:fecha
            	,:id_usuario
            )
            SQL;

			$statement = $conexion->prepare($query);

			// Bind de parámetros desde el objeto
			$statement->bindValue(':id_centro_costo', $this->getId_centro_costo(), PDO::PARAM_INT);
			$statement->bindValue(':id_producto', $this->getId_producto(), PDO::PARAM_INT);
			$statement->bindValue(':nota', $this->getNota(), PDO::PARAM_STR);
			$statement->bindValue(':tipo', $this->getTipo(), PDO::PARAM_STR);
			$statement->bindValue(':cantidad', $this->getCantidad(), PDO::PARAM_INT);
			$statement->bindValue(':fecha', $this->getFecha(), PDO::PARAM_STR);
			$statement->bindValue(':id_usuario', $this->getId_usuario(), PDO::PARAM_INT);

			// Ejecutar la consulta
			$success = $statement->execute();

			if ($success) {
				// Devolver el ID del movimiento recién creado
				return (int)$conexion->lastInsertId();
			} else {
				return false; // Error en la ejecución
			}

		} catch (PDOException $e) {
			// Manejar errores específicos de DB
			throw new Exception("Error de base de datos al insertar movimiento de inventario: " . $e->getMessage());
		} catch (Exception $e) {
			// Capturar otros errores
			throw new Exception("Error al insertar movimiento de inventario: " . $e->getMessage());
		}
	}

	/**
	 * Método privado para actualizar un movimiento existente en la base de datos.
	 *
	 * @param PDO $conexion Conexión PDO.
	 *
	 * @return bool True si la actualización fue exitosa, False en caso contrario.
	 * @throws Exception Si hay error en DB.
	 */
	private function _update(PDO $conexion): bool
	{
		try {
			// Consulta para actualizar el movimiento
			$query = <<<SQL
            UPDATE inventario_movimientos SET
                id_centro_costo = :id_centro_costo,
                id_producto = :id_producto,
                tipo = :tipo,
                nota = :nota,
                cantidad = :cantidad,
                fecha = :fecha,
                id_usuario = :id_usuario
            WHERE
                id = :id
            SQL;

			$statement = $conexion->prepare($query);
			$statement->bindValue(':id_centro_costo', $this->getId_centro_costo(), PDO::PARAM_INT);
			$statement->bindValue(':id_producto', $this->getId_producto(), PDO::PARAM_INT);
			$statement->bindValue(':tipo', $this->getTipo(), PDO::PARAM_STR);
			$statement->bindValue(':nota', $this->getNota(), PDO::PARAM_STR);
			$statement->bindValue(':cantidad', $this->getCantidad(), PDO::PARAM_INT);
			$statement->bindValue(':fecha', $this->getFecha(), PDO::PARAM_STR);
			$statement->bindValue(':id_usuario', $this->getId_usuario(), PDO::PARAM_INT);
			$statement->bindValue(':id', $this->getId(), PDO::PARAM_INT);

			// execute() devuelve true en éxito, false en error.
			return $statement->execute();

		} catch (PDOException $e) {
			// Manejar errores específicos de DB
			throw new Exception("Error de base de datos al actualizar movimiento de inventario (ID: {$this->getId()}): " . $e->getMessage());
		}
	}

	/**
	 * Elimina un movimiento de inventario por su ID (eliminación física).
	 *
	 * @param int $id       ID del movimiento a eliminar.
	 * @param PDO $conexion Conexión PDO.
	 *
	 * @return bool True si la eliminación fue exitosa, False en caso contrario.
	 * @throws Exception Si hay error en DB.
	 */
	public static function eliminar(int $id, PDO $conexion): bool
	{
		try {
			// Consulta para eliminar físicamente el movimiento
			$query = <<<SQL
            DELETE FROM inventario_movimientos
            WHERE
                id = :id
            SQL;

			$statement = $conexion->prepare($query);
			$statement->bindValue(':id', $id, PDO::PARAM_INT);

			// execute() devuelve true en éxito, false en error.
			return $statement->execute();

		} catch (PDOException $e) {
			// Error de base de datos
			throw new Exception("Error de base de datos al eliminar movimiento de inventario (ID: $id): " . $e->getMessage());
		}
	}

	// --- Métodos de Validación ---

	/**
	 * Valida que el tipo sea 'ingreso' o 'egreso'.
	 *
	 * @param string|null $tipo Tipo a validar.
	 *
	 * @return bool True si el tipo es válido, False en caso contrario.
	 */
	private function validarTipo(?string $tipo): bool
	{
		return $tipo === 'ingreso' || $tipo === 'egreso';
	}

	/**
	 * Valida que una cantidad sea positiva (mayor que cero).
	 *
	 * @param int|null $cantidad Cantidad a validar.
	 *
	 * @return bool True si la cantidad es válida, False en caso contrario.
	 */
	private function validarCantidadPositiva(?int $cantidad): bool
	{
		return $cantidad !== null && is_numeric($cantidad) && $cantidad > 0;
	}

	/**
	 * Valida que un centro de costo exista en la base de datos.
	 *
	 * @param int|null $id_centro_costo ID del centro de costo a validar.
	 * @param PDO      $conexion        Conexión PDO.
	 *
	 * @return bool True si el centro de costo existe, False en caso contrario.
	 */
	private function validarCentroCostoExiste(?int $id_centro_costo, PDO $conexion): bool
	{
		if ($id_centro_costo === null) {
			return false;
		}

		try {
			$query = <<<SQL
            SELECT COUNT(*) as count
            FROM centros_costos
            WHERE id = :id AND estado = 1
            SQL;

			$statement = $conexion->prepare($query);
			$statement->bindValue(':id', $id_centro_costo, PDO::PARAM_INT);
			$statement->execute();
			$resultado = $statement->fetch(PDO::FETCH_ASSOC);

			return $resultado && $resultado['count'] > 0;

		} catch (PDOException $e) {
			// En caso de error, asumir que no existe
			return false;
		}
	}

	/**
	 * Valida que un producto exista en la base de datos.
	 *
	 * @param int|null $id_producto ID del producto a validar.
	 * @param PDO      $conexion    Conexión PDO.
	 *
	 * @return bool True si el producto existe, False en caso contrario.
	 */
	private function validarProductoExiste(?int $id_producto, PDO $conexion): bool
	{
		if ($id_producto === null) {
			return false;
		}

		try {
			$query = <<<SQL
            SELECT COUNT(*) as count
            FROM productos
            WHERE id = :id AND estado = 1
            SQL;

			$statement = $conexion->prepare($query);
			$statement->bindValue(':id', $id_producto, PDO::PARAM_INT);
			$statement->execute();
			$resultado = $statement->fetch(PDO::FETCH_ASSOC);

			return $resultado && $resultado['count'] > 0;

		} catch (PDOException $e) {
			// En caso de error, asumir que no existe
			return false;
		}
	}

	/**
	 * Valida que un usuario exista en la base de datos.
	 *
	 * @param int|null $id_usuario ID del usuario a validar.
	 * @param PDO      $conexion   Conexión PDO.
	 *
	 * @return bool True si el usuario existe, False en caso contrario.
	 */
	private function validarUsuarioExiste(?int $id_usuario, PDO $conexion): bool
	{
		if ($id_usuario === null) {
			return false;
		}

		try {
			$query = <<<SQL
            SELECT COUNT(*) as count
            FROM usuarios
            WHERE id = :id AND estado = 1
            SQL;

			$statement = $conexion->prepare($query);
			$statement->bindValue(':id', $id_usuario, PDO::PARAM_INT);
			$statement->execute();
			$resultado = $statement->fetch(PDO::FETCH_ASSOC);

			return $resultado && $resultado['count'] > 0;

		} catch (PDOException $e) {
			// En caso de error, asumir que no existe
			return false;
		}
	}

	/**
	 * Valida que una fecha tenga el formato correcto (YYYY-MM-DD HH:MM:SS).
	 *
	 * @param string|null $fecha Fecha a validar.
	 *
	 * @return bool True si la fecha es válida, False en caso contrario.
	 */
	private function validarFecha(?string $fecha): bool
	{
		if (empty($fecha)) {
			return false;
		}

		// Validar formato YYYY-MM-DD HH:MM:SS usando DateTime
		$dateTime = \DateTime::createFromFormat('Y-m-d H:i:s', $fecha);
		return $dateTime && $dateTime->format('Y-m-d H:i:s') === $fecha;
	}

	// --- Getters y Setters ---

	/**
	 * Obtiene el ID del movimiento.
	 *
	 * @return int|null ID del movimiento.
	 */
	public function getId(): ?int
	{
		return $this->id;
	}

	/**
	 * Establece el ID del movimiento.
	 *
	 * @param int|null $id ID del movimiento.
	 *
	 * @return self
	 */
	public function setId(?int $id): self
	{
		$this->id = $id;
		return $this;
	}

	/**
	 * Obtiene el ID del centro de costo.
	 *
	 * @return int|null ID del centro de costo.
	 */
	public function getId_centro_costo(): ?int
	{
		return $this->id_centro_costo;
	}

	/**
	 * Establece el ID del centro de costo.
	 *
	 * @param int|null $id_centro_costo ID del centro de costo.
	 *
	 * @return self
	 * @throws Exception Si el ID no es válido.
	 */
	public function setId_centro_costo(?int $id_centro_costo): self
	{
		if ($id_centro_costo !== null && $id_centro_costo <= 0) {
			throw new Exception("El ID del centro de costo debe ser un número entero positivo.");
		}
		$this->id_centro_costo = $id_centro_costo;
		return $this;
	}

	/**
	 * Obtiene el ID del producto.
	 *
	 * @return int|null ID del producto.
	 */
	public function getId_producto(): ?int
	{
		return $this->id_producto;
	}

	/**
	 * Establece el ID del producto.
	 *
	 * @param int|null $id_producto ID del producto.
	 *
	 * @return self
	 * @throws Exception Si el ID no es válido.
	 */
	public function setId_producto(?int $id_producto): self
	{
		if ($id_producto !== null && $id_producto <= 0) {
			throw new Exception("El ID del producto debe ser un número entero positivo.");
		}
		$this->id_producto = $id_producto;
		return $this;
	}

	/**
	 * Obtiene el tipo del movimiento.
	 *
	 * @return string|null Tipo del movimiento ('ingreso' o 'egreso').
	 */
	public function getTipo(): ?string
	{
		return $this->tipo;
	}

	/**
	 * Establece el tipo del movimiento.
	 *
	 * @param string|null $tipo Tipo del movimiento ('ingreso' o 'egreso').
	 *
	 * @return self
	 * @throws Exception Si el tipo no es válido.
	 */
	public function setTipo(?string $tipo): self
	{
		if ($tipo !== null && !$this->validarTipo($tipo)) {
			throw new Exception("El tipo debe ser 'ingreso' o 'egreso'.");
		}
		$this->tipo = $tipo;
		return $this;
	}

	/**
	 * Obtiene la cantidad del movimiento.
	 *
	 * @return int|null Cantidad del movimiento.
	 */
	public function getCantidad(): ?int
	{
		return $this->cantidad;
	}

	/**
	 * Establece la cantidad del movimiento.
	 *
	 * @param int|null $cantidad Cantidad del movimiento.
	 *
	 * @return self
	 * @throws Exception Si la cantidad no es válida.
	 */
	public function setCantidad(?int $cantidad): self
	{
		if ($cantidad !== null && !$this->validarCantidadPositiva($cantidad)) {
			throw new Exception("La cantidad debe ser un número entero positivo mayor que cero.");
		}
		$this->cantidad = $cantidad;
		return $this;
	}

	/**
	 * Obtiene la fecha del movimiento.
	 *
	 * @return string|null Fecha del movimiento.
	 */
	public function getFecha(): ?string
	{
		return $this->fecha;
	}

	/**
	 * Establece la fecha del movimiento.
	 *
	 * @param string|null $fecha Fecha del movimiento (formato YYYY-MM-DD HH:MM:SS).
	 *
	 * @return self
	 * @throws Exception Si la fecha no tiene un formato válido.
	 */
	public function setFecha(?string $fecha): self
	{
		if ($fecha !== null && !$this->validarFecha($fecha)) {
			throw new Exception("La fecha debe tener un formato válido (YYYY-MM-DD HH:MM:SS).");
		}
		$this->fecha = $fecha;
		return $this;
	}

	/**
	 * Obtiene el ID del usuario.
	 *
	 * @return int|null ID del usuario.
	 */
	public function getId_usuario(): ?int
	{
		return $this->id_usuario;
	}

	/**
	 * Establece el ID del usuario.
	 *
	 * @param int|null $id_usuario ID del usuario.
	 *
	 * @return self
	 * @throws Exception Si el ID no es válido.
	 */
	public function setId_usuario(?int $id_usuario): self
	{
		if ($id_usuario !== null && $id_usuario <= 0) {
			throw new Exception("El ID del usuario debe ser un número entero positivo.");
		}
		$this->id_usuario = $id_usuario;
		return $this;
	}

	/**
	 * Obtiene el nombre del centro de costo.
	 *
	 * @return string|null Nombre del centro de costo.
	 */
	public function getNombre_centro_costo(): ?string
	{
		return $this->nombre_centro_costo;
	}

	/**
	 * Establece el nombre del centro de costo.
	 *
	 * @param string|null $nombre_centro_costo Nombre del centro de costo.
	 *
	 * @return self
	 */
	public function setNombre_centro_costo(?string $nombre_centro_costo): self
	{
		$this->nombre_centro_costo = $nombre_centro_costo;
		return $this;
	}

	/**
	 * Obtiene la descripción del producto.
	 *
	 * @return string|null Descripción del producto.
	 */
	public function getDescripcion_producto(): ?string
	{
		return $this->descripcion_producto;
	}

	/**
	 * Establece la descripción del producto.
	 *
	 * @param string|null $descripcion_producto Descripción del producto.
	 *
	 * @return self
	 */
	public function setDescripcion_producto(?string $descripcion_producto): self
	{
		$this->descripcion_producto = $descripcion_producto;
		return $this;
	}

	/**
	 * Obtiene el valor del producto.
	 *
	 * @return float|null Valor del producto.
	 */
	public function getValor_producto(): ?float
	{
		return $this->valor_producto;
	}

	/**
	 * Establece el valor del producto.
	 *
	 * @param float|null $valor_producto Valor del producto.
	 *
	 * @return self
	 */
	public function setValor_producto(?float $valor_producto): self
	{
		$this->valor_producto = $valor_producto;
		return $this;
	}

	/**
	 * Obtiene el nombre del usuario.
	 *
	 * @return string|null Nombre del usuario.
	 */
	public function getNombre_usuario(): ?string
	{
		return $this->nombre_usuario;
	}

	/**
	 * Establece el nombre del usuario.
	 *
	 * @param string|null $nombre_usuario Nombre del usuario.
	 *
	 * @return self
	 */
	public function setNombre_usuario(?string $nombre_usuario): self
	{
		$this->nombre_usuario = $nombre_usuario;
		return $this;
	}

	/**
	 * Obtiene la nota del movimiento.
	 *
	 * @return string|null Nota del movimiento.
	 */
	public function getNota(): ?string
	{
		return $this->nota;
	}

	/**
	 * Establece la nota del movimiento.
	 *
	 * @param string|null $nota Nota del movimiento.
	 *
	 * @return self
	 */
	public function setNota(?string $nota): self
	{
		$this->nota = $nota;
		return $this;
	}
	// --- Métodos adicionales ---

	/**
	 * Verifica si el movimiento es de tipo 'ingreso'.
	 * @return bool
	 */
	public function isIngreso(): bool
	{
		return $this->tipo === 'ingreso';
	}

	/**
	 * Verifica si el movimiento es de tipo 'egreso'.
	 * @return bool
	 */
	public function isEgreso(): bool
	{
		return $this->tipo === 'egreso';
	}

	/**
	 * Obtiene la cantidad formateada con separadores de miles.
	 * @return string
	 */
	public function getCantidadFormateada(): string
	{
		if ($this->cantidad === null) {
			return '0';
		}
		return number_format($this->cantidad, 0, ',', '.');
	}

	/**
	 * Obtiene el valor del producto formateado en pesos colombianos.
	 * @return string
	 */
	public function getValorProductoFormateado(): string
	{
		if ($this->valor_producto === null) {
			return '$0';
		}
		return '$' . number_format($this->valor_producto, 0, ',', '.');
	}

	/**
	 * Obtiene el valor total del movimiento (cantidad * valor del producto).
	 * @return float
	 */
	public function getValorTotal(): float
	{
		if ($this->cantidad === null || $this->valor_producto === null) {
			return 0.0;
		}
		return $this->cantidad * $this->valor_producto;
	}

	/**
	 * Obtiene el valor total formateado en pesos colombianos.
	 * @return string
	 */
	public function getValorTotalFormateado(): string
	{
		$valorTotal = $this->getValorTotal();
		return '$' . number_format($valorTotal, 0, ',', '.');
	}

	/**
	 * Obtiene la fecha formateada para mostrar.
	 * @return string
	 */
	public function getFechaFormateada(): string
	{
		if (empty($this->fecha)) {
			return '';
		}

		try {
			$dateTime = new \DateTime($this->fecha);
			return $dateTime->format('d/m/Y H:i:s');
		} catch (Exception $e) {
			return $this->fecha;
		}
	}

	/**
	 * Obtiene la fecha en formato corto (solo fecha).
	 * @return string
	 */
	public function getFechaCorta(): string
	{
		if (empty($this->fecha)) {
			return '';
		}

		try {
			$dateTime = new \DateTime($this->fecha);
			return $dateTime->format('d/m/Y');
		} catch (Exception $e) {
			return $this->fecha;
		}
	}

	/**
	 * Obtiene una descripción del tipo de movimiento.
	 * @return string
	 */
	public function getTipoDescripcion(): string
	{
		return match($this->tipo) {
			'ingreso' => 'Ingreso',
			'egreso' => 'Egreso',
			default => 'Desconocido'
		};
	}

	/**
	 * Calcula el impacto del movimiento en el inventario.
	 * Los ingresos suman, los egresos restan.
	 * @return int
	 */
	public function calcularImpactoInventario(): int
	{
		if ($this->cantidad === null) {
			return 0;
		}

		return $this->isIngreso() ? $this->cantidad : -$this->cantidad;
	}

	/**
	 * Obtiene un resumen del movimiento para mostrar.
	 * @return string
	 */
	public function getResumen(): string
	{
		$tipo     = $this->getTipoDescripcion();
		$cantidad = $this->getCantidadFormateada();
		$producto = $this->descripcion_producto ?? 'Producto desconocido';
		$fecha    = $this->getFechaCorta();

		$resumen = "$tipo: $cantidad unidades de $producto";
		if (!empty($fecha)) {
			$resumen .= " ($fecha)";
		}
		if (!empty($this->nota)) {
			$resumen .= " - Nota: " . substr($this->nota, 0, 50) . (strlen($this->nota) > 50 ? "..." : "");
		}

		return $resumen;
	}

	/**
	 * Obtiene información completa del movimiento para reportes.
	 * @return array
	 */
	public function getInformacionCompleta(): array
	{
		return [
			'id'                        => $this->id,
			'centro_costo'              => $this->nombre_centro_costo ?? 'No especificado',
			'producto'                  => $this->descripcion_producto ?? 'No especificado',
			'tipo'                      => $this->getTipoDescripcion(),
			'cantidad'                  => $this->cantidad,
			'cantidad_formateada'       => $this->getCantidadFormateada(),
			'valor_unitario'            => $this->valor_producto,
			'valor_unitario_formateado' => $this->getValorProductoFormateado(),
			'valor_total'               => $this->getValorTotal(),
			'valor_total_formateado'    => $this->getValorTotalFormateado(),
			'fecha'                     => $this->fecha,
			'fecha_formateada'          => $this->getFechaFormateada(),
			'fecha_corta'               => $this->getFechaCorta(),
			'usuario'                   => $this->nombre_usuario ?? 'No especificado',
			'impacto_inventario'        => $this->calcularImpactoInventario(),
			'resumen'                   => $this->getResumen(),
			'nota'                      => $this->nota
		];
	}
}