<?php

declare(strict_types=1);

namespace App\classes;

use Exception;
use PDO;
use PDOException;

class Perfil
{
	// --- Atributos ---
	private ?int    $id     = null;
	private ?string $nombre = null;
	private ?int    $estado = null;

	/**
	 * Constructor: Inicializa las propiedades del objeto Perfil.
	 */
	public function __construct()
	{
		$this->id     = 0; // O null si prefieres no usar 0 por defecto
		$this->nombre = null;
		$this->estado = 1; // Estado activo por defecto
	}

	/**
	 * Método estático para construir un objeto Perfil desde un array (ej. fila de DB).
	 *
	 * @param array $resultado Array asociativo con los datos del perfil.
	 *
	 * @return self Instancia de Perfil.
	 * @throws Exception Si ocurre un error durante la construcción.
	 */
	public static function construct(array $resultado = []): self
	{
		try {
			$objeto         = new self();
			$objeto->id     = isset($resultado['id']) ? (int)$resultado['id'] : 0;
			$objeto->nombre = $resultado['nombre'] ?? null;
			$objeto->estado = isset($resultado['estado']) ? (int)$resultado['estado'] : 1;
			return $objeto;
		} catch (Exception $e) {
			// Considera loggear el error aquí
			throw new Exception("Error al construir Perfil: " . $e->getMessage());
		}
	}

	// --- Métodos de Acceso a Datos (Estáticos) ---

	/**
	 * Obtiene un perfil por su ID.
	 *
	 * @param int $id       ID del perfil.
	 * @param PDO $conexion Conexión PDO.
	 *
	 * @return self|null Objeto Perfil o null si no se encuentra.
	 * @throws Exception Si hay error en DB.
	 */
	public static function get(int $id, PDO $conexion): ?self
	{
		try {
			// Consulta para obtener perfil por ID (Usando Heredoc)
			$query = <<<SQL
            SELECT
            	*
            FROM perfiles
            WHERE
            	id = :id
            LIMIT 1
            SQL;

			$statement = $conexion->prepare($query);
			$statement->bindValue(":id", $id, PDO::PARAM_INT);
			$statement->execute();
			$resultado = $statement->fetch(PDO::FETCH_ASSOC);

			return $resultado ? self::construct($resultado) : null;

		} catch (PDOException $e) {
			throw new Exception("Error al obtener Perfil (ID: $id): " . $e->getMessage());
		}
	}

	/**
	 * Obtiene una lista de perfiles activos.
	 *
	 * @param PDO $conexion Conexión PDO.
	 *
	 * @return array Array de objetos Perfil.
	 * @throws Exception Si hay error en DB.
	 */
	public static function get_list(PDO $conexion): array
	{
		try {
			// Consulta para obtener lista de perfiles activos (Usando Heredoc)
			$query = <<<SQL
            SELECT
            	*
            FROM perfiles
            WHERE
            	estado = 1
            ORDER BY
            	nombre
            SQL;

			$statement = $conexion->prepare($query);
			$statement->execute();
			$resultados = $statement->fetchAll(PDO::FETCH_ASSOC);

			$listado = [];
			foreach ($resultados as $resultado) {
				$listado[] = self::construct($resultado);
			}
			return $listado;

		} catch (PDOException $e) {
			throw new Exception("Error al obtener lista de Perfiles: " . $e->getMessage());
		}
	}

	/**
	 * Obtiene un perfil por su nombre.
	 *
	 * @param string $nombre Nombre del perfil.
	 * @param PDO $conexion Conexión PDO.
	 *
	 * @return self|null Objeto Perfil o null si no se encuentra.
	 * @throws Exception Si hay error en DB.
	 */
	public static function getByNombre(string $nombre, PDO $conexion): ?self
	{
		try {
			// Consulta para obtener perfil por nombre (Usando Heredoc)
			$query = <<<SQL
            SELECT
            	*
            FROM perfiles
            WHERE
            	nombre = :nombre
            	AND estado = 1
            LIMIT 1
            SQL;

			$statement = $conexion->prepare($query);
			$statement->bindValue(":nombre", $nombre, PDO::PARAM_STR);
			$statement->execute();
			$resultado = $statement->fetch(PDO::FETCH_ASSOC);

			return $resultado ? self::construct($resultado) : null;

		} catch (PDOException $e) {
			throw new Exception("Error al obtener Perfil por nombre '$nombre': " . $e->getMessage());
		}
	}

	/**
	 * Crea un nuevo perfil en la base de datos a partir de un objeto Perfil.
	 * El objeto Perfil debe estar completamente poblado.
	 *
	 * @param PDO $conexion Conexión PDO.
	 *
	 * @return int|false El ID del nuevo perfil creado o false en caso de error.
	 * @throws Exception Si los datos requeridos en el objeto están vacíos o hay error en DB.
	 */
	function crear(PDO $conexion): int|false
	{
		// Validaciones básicas sobre el objeto
		if (empty($this->getNombre())) {
			throw new Exception("Nombre es requerido en el objeto Perfil para crearlo.");
		}

		try {
			// Preparar la consulta INSERT usando Heredoc
			$query = <<<SQL
            INSERT INTO perfiles (
            	 nombre
            ) VALUES (
            	 :nombre
            )
            SQL;

			$statement = $conexion->prepare($query);

			// Bind de parámetros desde el objeto
			$statement->bindValue(':nombre', $this->getNombre(), PDO::PARAM_STR);

			// Ejecutar la consulta
			$success = $statement->execute();

			if ($success) {
				// Devolver el ID del perfil recién creado
				return (int)$conexion->lastInsertId();
			} else {
				return false; // Error en la ejecución
			}

		} catch (PDOException $e) {
			// Manejar errores específicos de DB (ej. nombre duplicado)
			if ($e->getCode() == 23000 || $e->getCode() == 1062) { // Códigos comunes para violación de UNIQUE constraint
				throw new Exception("Error al crear perfil: El nombre ya existe.");
			} else {
				throw new Exception("Error de base de datos al crear perfil: " . $e->getMessage());
			}
		} catch (Exception $e) {
			// Capturar otros errores
			throw new Exception("Error al crear perfil: " . $e->getMessage());
		}
	}

	/**
	 * Modifica el nombre de un perfil existente.
	 *
	 * @param PDO $conexion Conexión PDO.
	 *
	 * @return bool True si la modificación fue exitosa, False en caso contrario.
	 * @throws Exception Si el nuevo nombre está vacío o si ocurre un error de base de datos.
	 */
	function modificar(PDO $conexion): bool
	{
		if (empty(trim($this->getNombre()))) {
			throw new Exception("El nombre no puede estar vacío.");
		}

		if ($this->getId() <= 0) {
			throw new Exception("ID de perfil inválido para modificar.");
		}

		try {
			// Consulta para actualizar el nombre
			$query = <<<SQL
            UPDATE perfiles SET
                nombre = :nombre
            WHERE
                id = :id
            SQL;

			$statement = $conexion->prepare($query);
			$statement->bindValue(':nombre', trim($this->getNombre()), PDO::PARAM_STR);
			$statement->bindValue(':id', $this->getId(), PDO::PARAM_INT);

			// execute() devuelve true en éxito, false en error.
			return $statement->execute();

		} catch (PDOException $e) {
			// Error de base de datos
			throw new Exception("Error de base de datos al modificar perfil (ID: {$this->getId()}): " . $e->getMessage());
		}
	}

	/**
	 * Desactiva un perfil estableciendo su estado a 0.
	 *
	 * @param int $id       ID del perfil a desactivar.
	 * @param PDO $conexion Conexión PDO.
	 *
	 * @return bool True si la desactivación fue exitosa, False en caso contrario.
	 * @throws Exception Si ocurre un error de base de datos.
	 */
	public static function desactivar(int $id, PDO $conexion): bool
	{
		try {
			// Consulta para actualizar el estado a 0 (inactivo)
			$query = <<<SQL
            UPDATE perfiles SET
            	estado = 0
            WHERE
            	id = :id
            SQL;

			$statement = $conexion->prepare($query);
			$statement->bindValue(':id', $id, PDO::PARAM_INT);

			// execute() devuelve true en éxito, false en error.
			return $statement->execute();

		} catch (PDOException $e) {
			// Error de base de datos
			throw new Exception("Error de base de datos al desactivar perfil (ID: $id): " . $e->getMessage());
		}
	}

	/**
	 * Asocia una acción a este perfil.
	 * Crea un registro en la tabla perfiles_acciones.
	 *
	 * @param int $idAccion ID de la acción a asociar.
	 * @param PDO $conexion Conexión PDO.
	 *
	 * @return bool True si la asociación fue exitosa, False en caso contrario.
	 * @throws Exception Si ocurre un error de base de datos o si el ID del perfil es inválido.
	 */
	public function asociarAccion(int $idAccion, PDO $conexion): bool
	{
		// Validar que el ID del perfil sea válido
		if ($this->getId() <= 0) {
			throw new Exception("ID de perfil inválido para asociar acción.");
		}

		// Validar que el ID de la acción sea válido
		if ($idAccion <= 0) {
			throw new Exception("ID de acción inválido.");
		}

		try {
			// Preparar la consulta INSERT usando Heredoc
			$query = <<<SQL
            INSERT INTO perfiles_acciones (
            	 id_perfil
            	,id_accion
            ) VALUES (
            	 :id_perfil
            	,:id_accion
            )
            SQL;

			$statement = $conexion->prepare($query);

			// Bind de parámetros
			$statement->bindValue(':id_perfil', $this->getId(), PDO::PARAM_INT);
			$statement->bindValue(':id_accion', $idAccion, PDO::PARAM_INT);

			// Ejecutar la consulta
			return $statement->execute();

		} catch (PDOException $e) {
			// Manejar errores específicos de DB (ej. duplicado por restricción única)
			if ($e->getCode() == 23000 || $e->getCode() == 1062) { // Códigos comunes para violación de UNIQUE constraint
				// Si ya existe la asociación, no lo consideramos un error
				return true;
			} else {
				throw new Exception("Error de base de datos al asociar acción (ID: $idAccion) al perfil (ID: {$this->getId()}): " . $e->getMessage());
			}
		} catch (Exception $e) {
			// Capturar otros errores
			throw new Exception("Error al asociar acción al perfil: " . $e->getMessage());
		}
	}

	/**
	 * Desasocia una acción de este perfil.
	 * Elimina el registro correspondiente de la tabla perfiles_acciones.
	 *
	 * @param int $id_perfil_accion
	 * @param PDO $conexion Conexión PDO.
	 *
	 * @return bool True si la desasociación fue exitosa, False si no se encontró el registro.
	 * @throws Exception Si ocurre un error de base de datos o si el ID del perfil es inválido.
	 */
	public static function desasociarAccion(int $id_perfil_accion, PDO $conexion): bool
	{
		// Validar que el ID de la acción sea válido
		if ($id_perfil_accion <= 0) {
			throw new Exception("ID de acción inválido.");
		}

		try {
			// Preparar la consulta DELETE usando Heredoc
			$query = <<<SQL
            DELETE FROM perfiles_acciones
            WHERE
            	 id = :id_perfil_accion
            SQL;

			$statement = $conexion->prepare($query);

			// Bind de parámetros
			$statement->bindValue(':id_perfil_accion', $id_perfil_accion, PDO::PARAM_INT);

			// Ejecutar la consulta
			$statement->execute();

			// Verificar si se eliminó algún registro
			return $statement->rowCount() > 0;

		} catch (PDOException $e) {
			throw new Exception("Error de base de datos al desasociar acción" . $e->getMessage());
		} catch (Exception $e) {
			// Capturar otros errores
			throw new Exception("Error al desasociar acción del perfil: " . $e->getMessage());
		}
	}

	// --- Getters y Setters ---

	public function getId(): ?int
	{
		return $this->id;
	}

	public function setId(?int $id): self
	{
		$this->id = $id;
		return $this;
	}

	public function getNombre(): ?string
	{
		return $this->nombre;
	}

	public function setNombre(?string $nombre): self
	{
		$this->nombre = $nombre;
		return $this;
	}

	public function getEstado(): ?int
	{
		return $this->estado;
	}

	public function setEstado(?int $estado): self
	{
		$this->estado = $estado;
		return $this;
	}

	// --- Métodos adicionales ---

	/**
	 * Verifica si el perfil está activo.
	 * @return bool
	 */
	public function isActivo(): bool
	{
		// Asegúrate de que el estado no sea null antes de comparar
		return $this->estado === 1;
	}

	/**
	 * Obtiene las acciones asociadas a este perfil.
	 *
	 * @param PDO $conexion Conexión PDO.
	 *
	 * @return array Array de objetos Accion asociados a este perfil.
	 * @throws Exception Si hay error en DB.
	 */
	public function getAcciones(PDO $conexion): array
	{
		if ($this->getId() <= 0) {
			throw new Exception("ID de perfil inválido para obtener acciones.");
		}

		try {
			// Consulta para obtener las acciones asociadas al perfil
			$query = <<<SQL
            SELECT a.*, pa.id as id_perfil_accion
            FROM acciones a
            INNER JOIN perfiles_acciones pa ON a.id = pa.id_accion
            WHERE pa.id_perfil = :id_perfil
            ORDER BY a.grupo, a.nombre
            SQL;

			$statement = $conexion->prepare($query);
			$statement->bindValue(':id_perfil', $this->getId(), PDO::PARAM_INT);
			$statement->execute();
			$resultados = $statement->fetchAll(PDO::FETCH_ASSOC);

			$acciones = [];
			foreach ($resultados as $resultado) {
				$accion = Accion::construct($resultado);
				// Añadir el ID de la relación para facilitar la desasociación
				$accion->setIdPerfilAccion($resultado['id_perfil_accion'] ?? 0);
				$acciones[] = $accion;
			}

			return $acciones;

		} catch (PDOException $e) {
			throw new Exception("Error al obtener acciones del perfil (ID: {$this->getId()}): " . $e->getMessage());
		}
	}

	/**
	 * Verifica si este perfil tiene asociada una acción específica.
	 *
	 * @param int $idAccion ID de la acción a verificar.
	 * @param PDO $conexion Conexión PDO.
	 *
	 * @return bool True si la acción está asociada, False en caso contrario.
	 * @throws Exception Si hay error en DB.
	 */
	public function tieneAccion(int $idAccion, PDO $conexion): bool
	{
		if ($this->getId() <= 0) {
			throw new Exception("ID de perfil inválido para verificar acción.");
		}

		if ($idAccion <= 0) {
			throw new Exception("ID de acción inválido.");
		}

		try {
			// Consulta para verificar si existe la asociación
			$query = <<<SQL
            SELECT COUNT(*) as count
            FROM perfiles_acciones
            WHERE
                 id_perfil = :id_perfil
            AND id_accion = :id_accion
            SQL;

			$statement = $conexion->prepare($query);
			$statement->bindValue(':id_perfil', $this->getId(), PDO::PARAM_INT);
			$statement->bindValue(':id_accion', $idAccion, PDO::PARAM_INT);
			$statement->execute();
			$resultado = $statement->fetch(PDO::FETCH_ASSOC);

			return ($resultado['count'] ?? 0) > 0;

		} catch (PDOException $e) {
			throw new Exception("Error al verificar si el perfil (ID: {$this->getId()}) tiene la acción (ID: $idAccion): " . $e->getMessage());
		}
	}
}
