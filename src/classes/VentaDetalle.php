<?php

declare(strict_types=1);

namespace App\classes;


use Exception;
use PDO;
use PDOException;

class VentaDetalle
{
    // --- Atributos ---
    private ?int    $id                   = null;
    private ?int    $id_venta             = null;
    private ?int    $id_producto          = null;
    private ?float  $valor                = null;
    private ?int    $cantidad             = null;
    private ?float  $valor_total          = null;
    private ?string $producto_descripcion = null;
    private ?float  $producto_valor       = null;

    /**
     * Constructor: Inicializa las propiedades del objeto VentaDetalle.
     */
    public function __construct()
    {
        $this->id                   = 0;
        $this->id_venta             = null;
        $this->id_producto          = null;
        $this->valor                = null;
        $this->cantidad             = 1; // Cantidad por defecto
        $this->valor_total          = null;
        $this->producto_descripcion = null;
        $this->producto_valor       = null;
    }

    /**
     * Método estático para construir un objeto VentaDetalle desde un array (ej. fila de DB).
     *
     * @param array $resultado Array asociativo con los datos del detalle de venta.
     *
     * @return self Instancia de VentaDetalle.
     * @throws Exception Si ocurre un error durante la construcción.
     */
    public static function construct(array $resultado = []): self
    {
        try {
            $objeto                       = new self();
            $objeto->id                   = isset($resultado['id']) ? (int)$resultado['id'] : 0;
            $objeto->id_venta             = isset($resultado['id_venta']) ? (int)$resultado['id_venta'] : null;
            $objeto->id_producto          = isset($resultado['id_producto']) ? (int)$resultado['id_producto'] : null;
            $objeto->valor                = isset($resultado['valor']) ? (float)$resultado['valor'] : null;
            $objeto->cantidad             = isset($resultado['cantidad']) ? (int)$resultado['cantidad'] : 1;
            $objeto->valor_total          = isset($resultado['valor_total']) ? (float)$resultado['valor_total'] : null;
            $objeto->producto_descripcion = $resultado['producto_descripcion'] ?? null;
            $objeto->producto_valor       = isset($resultado['producto_valor']) ? (float)$resultado['producto_valor'] : null;
            return $objeto;
        } catch (Exception $e) {
            throw new Exception("Error al construir VentaDetalle: " . $e->getMessage());
        }
    }

    /**
     * Obtiene todos los detalles de una venta específica con información de productos.
     *
     * @param int $id_venta ID de la venta.
     * @param PDO $conexion Conexión PDO.
     *
     * @return array Array de objetos VentaDetalle.
     * @throws Exception Si ocurre un error de base de datos.
     */
    public static function obtenerPorVenta(int $id_venta, PDO $conexion): array
    {
        try {
            $query = <<<SQL
            SELECT
                vd.*,
                p.descripcion AS producto_descripcion,
                p.valor AS producto_valor
            FROM ventas_detalle vd
            INNER JOIN productos p ON vd.id_producto = p.id
            WHERE vd.id_venta = :id_venta
            ORDER BY vd.id
            SQL;

            $statement = $conexion->prepare($query);
            $statement->bindValue(':id_venta', $id_venta, PDO::PARAM_INT);
            $statement->execute();
            $resultados = $statement->fetchAll(PDO::FETCH_ASSOC);

            $detalles = [];
            foreach ($resultados as $resultado) {
                $detalles[] = self::construct($resultado);
            }

            return $detalles;
        } catch (PDOException $e) {
            throw new Exception("Error de base de datos al obtener detalles de venta: " . $e->getMessage());
        }
    }

    /**
     * Obtiene todos los detalles de una venta específica.
     *
     * @param int $id_venta ID de la venta.
     * @param PDO $conexion Conexión PDO.
     *
     * @return array Array de objetos VentaDetalle.
     * @throws Exception Si hay error en DB.
     */
    public static function get_by_venta(int $id_venta, PDO $conexion): array
    {
        try {
            $query = <<<SQL
            SELECT
                vd.*,
                p.descripcion AS producto_descripcion,
                p.valor AS producto_valor
            FROM ventas_detalle vd
            INNER JOIN productos p ON vd.id_producto = p.id
            WHERE vd.id_venta = :id_venta
            ORDER BY p.descripcion
            SQL;

            $statement = $conexion->prepare($query);
            $statement->bindValue(':id_venta', $id_venta, PDO::PARAM_INT);
            $statement->execute();
            $resultados = $statement->fetchAll(PDO::FETCH_ASSOC);

            $detalles = [];
            foreach ($resultados as $resultado) {
                $detalles[] = self::construct($resultado);
            }

            return $detalles;
        } catch (PDOException $e) {
            throw new Exception("Error de base de datos al obtener detalles de venta: " . $e->getMessage());
        }
    }

    /**
     * Obtiene un detalle de venta por su ID con información del producto.
     *
     * @param int $id       ID del detalle de venta.
     * @param PDO $conexion Conexión PDO.
     *
     * @return self|null Objeto VentaDetalle o null si no se encuentra.
     * @throws Exception Si ocurre un error de base de datos.
     */
    public static function obtenerPorId(int $id, PDO $conexion): ?self
    {
        try {
            $query = <<<SQL
            SELECT
                vd.*,
                p.descripcion AS producto_descripcion,
                p.valor AS producto_valor
            FROM ventas_detalle vd
            INNER JOIN productos p ON vd.id_producto = p.id
            WHERE vd.id = :id
            LIMIT 1
            SQL;

            $statement = $conexion->prepare($query);
            $statement->bindValue(':id', $id, PDO::PARAM_INT);
            $statement->execute();
            $resultado = $statement->fetch(PDO::FETCH_ASSOC);

            return $resultado ? self::construct($resultado) : null;
        } catch (PDOException $e) {
            throw new Exception("Error de base de datos al obtener detalle de venta: " . $e->getMessage());
        }
    }

    /**
     * Crea un nuevo detalle de venta en la base de datos a partir de un objeto VentaDetalle.
     *
     * @param PDO $conexion Conexión PDO.
     *
     * @return int|false El ID del nuevo detalle creado o false en caso de error.
     * @throws Exception Si los datos requeridos en el objeto están vacíos o hay error en DB.
     */
    function crear(PDO $conexion): int|false
    {
        // Validaciones básicas sobre el objeto
        if ($this->getId_venta() === null || $this->getId_producto() === null || $this->getValor() === null || $this->getCantidad() === null) {
            throw new Exception("ID de venta, ID de producto, valor y cantidad son requeridos para crear un detalle de venta.");
        }

        // Validar valores positivos
        if ($this->getValor() <= 0) {
            throw new Exception("El valor debe ser un número positivo mayor que cero.");
        }

        if ($this->getCantidad() <= 0) {
            throw new Exception("La cantidad debe ser un número entero positivo mayor que cero.");
        }

        // Validar que la venta existe
        if (!$this->validarVentaExiste($this->getId_venta(), $conexion)) {
            throw new Exception("La venta especificada no existe.");
        }

        // Validar que el producto existe
        if (!$this->validarProductoExiste($this->getId_producto(), $conexion)) {
            throw new Exception("El producto especificado no existe.");
        }

        try {
            // Calcular valor total automáticamente
            $this->setValor_total($this->getValor() * $this->getCantidad());

            return $this->_insert($conexion);
        } catch (Exception $e) {
            throw new Exception("Error al crear detalle de venta: " . $e->getMessage());
        }
    }

    /**
     * Modifica un detalle de venta existente.
     *
     * @param PDO $conexion Conexión PDO.
     *
     * @return bool True si la modificación fue exitosa, False en caso contrario.
     * @throws Exception Si los datos requeridos están vacíos o si ocurre un error de base de datos.
     */
    function modificar(PDO $conexion): bool
    {
        // Validar que el ID sea válido
        if ($this->getId() <= 0) {
            throw new Exception("ID de detalle de venta no válido para modificar.");
        }

        // Validaciones básicas sobre el objeto
        if ($this->getId_venta() === null || $this->getId_producto() === null || $this->getValor() === null || $this->getCantidad() === null) {
            throw new Exception("ID de venta, ID de producto, valor y cantidad son requeridos para modificar un detalle de venta.");
        }

        // Validar valores positivos
        if ($this->getValor() <= 0) {
            throw new Exception("El valor debe ser un número positivo mayor que cero.");
        }

        if ($this->getCantidad() <= 0) {
            throw new Exception("La cantidad debe ser un número entero positivo mayor que cero.");
        }

        // Validar que la venta existe
        if (!$this->validarVentaExiste($this->getId_venta(), $conexion)) {
            throw new Exception("La venta especificada no existe.");
        }

        // Validar que el producto existe
        if (!$this->validarProductoExiste($this->getId_producto(), $conexion)) {
            throw new Exception("El producto especificado no existe.");
        }

        try {
            // Recalcular valor total automáticamente
            $this->setValor_total($this->getValor() * $this->getCantidad());

            return $this->_update($conexion);
        } catch (Exception $e) {
            throw new Exception("Error al modificar detalle de venta: " . $e->getMessage());
        }
    }

    /**
     * Método privado para insertar un nuevo detalle de venta en la base de datos.
     *
     * @param PDO $conexion Conexión PDO.
     *
     * @return int|false El ID del nuevo detalle creado o false en caso de error.
     * @throws Exception Si hay error en DB.
     */
    private function _insert(PDO $conexion): int|false
    {
        try {
            // Preparar la consulta INSERT usando Heredoc
            $query = <<<SQL
            INSERT INTO ventas_detalle (
                 id_venta
                ,id_producto
                ,valor
                ,cantidad
                ,valor_total
            ) VALUES (
                 :id_venta
                ,:id_producto
                ,:valor
                ,:cantidad
                ,:valor_total
            )
            SQL;

            $statement = $conexion->prepare($query);

            // Bind de parámetros desde el objeto
            $statement->bindValue(':id_venta', $this->getId_venta(), PDO::PARAM_INT);
            $statement->bindValue(':id_producto', $this->getId_producto(), PDO::PARAM_INT);
            $statement->bindValue(':valor', $this->getValor(), PDO::PARAM_STR);
            $statement->bindValue(':cantidad', $this->getCantidad(), PDO::PARAM_INT);
            $statement->bindValue(':valor_total', $this->getValor_total(), PDO::PARAM_STR);

            // Ejecutar la consulta
            $success = $statement->execute();

            if ($success) {
                // Devolver el ID del detalle recién creado
                return (int)$conexion->lastInsertId();
            } else {
                return false; // Error en la ejecución
            }

        } catch (PDOException $e) {
            throw new Exception("Error de base de datos al crear detalle de venta: " . $e->getMessage());
        }
    }

    /**
     * Método privado para actualizar un detalle de venta existente en la base de datos.
     *
     * @param PDO $conexion Conexión PDO.
     *
     * @return bool True si la actualización fue exitosa, false en caso contrario.
     * @throws Exception Si hay error en DB.
     */
    private function _update(PDO $conexion): bool
    {
        try {
            // Preparar la consulta UPDATE usando Heredoc
            $query = <<<SQL
            UPDATE ventas_detalle SET
                 id_venta = :id_venta
                ,id_producto = :id_producto
                ,valor = :valor
                ,cantidad = :cantidad
                ,valor_total = :valor_total
            WHERE
                id = :id
            SQL;

            $statement = $conexion->prepare($query);

            // Bind de parámetros desde el objeto
            $statement->bindValue(':id_venta', $this->getId_venta(), PDO::PARAM_INT);
            $statement->bindValue(':id_producto', $this->getId_producto(), PDO::PARAM_INT);
            $statement->bindValue(':valor', $this->getValor(), PDO::PARAM_STR);
            $statement->bindValue(':cantidad', $this->getCantidad(), PDO::PARAM_INT);
            $statement->bindValue(':valor_total', $this->getValor_total(), PDO::PARAM_STR);
            $statement->bindValue(':id', $this->getId(), PDO::PARAM_INT);

            // execute() devuelve true en éxito, false en error.
            return $statement->execute();

        } catch (PDOException $e) {
            throw new Exception("Error de base de datos al modificar detalle de venta (ID: {$this->getId()}): " . $e->getMessage());
        }
    }

    /**
     * Valida que una venta existe en la base de datos.
     *
     * @param int $id_venta ID de la venta a validar.
     * @param PDO $conexion Conexión PDO.
     *
     * @return bool True si la venta existe, false en caso contrario.
     */
    private function validarVentaExiste(int $id_venta, PDO $conexion): bool
    {
        try {
            $query = <<<SQL
            SELECT COUNT(*) as count
            FROM ventas
            WHERE id = :id_venta AND estado = 1
            SQL;

            $statement = $conexion->prepare($query);
            $statement->bindValue(':id_venta', $id_venta, PDO::PARAM_INT);
            $statement->execute();
            $resultado = $statement->fetch(PDO::FETCH_ASSOC);

            return $resultado['count'] > 0;
        } catch (PDOException) {
            return false;
        }
    }

    /**
     * Valida que un producto existe en la base de datos.
     *
     * @param int $id_producto ID del producto a validar.
     * @param PDO $conexion    Conexión PDO.
     *
     * @return bool True si el producto existe, false en caso contrario.
     */
    private function validarProductoExiste(int $id_producto, PDO $conexion): bool
    {
        try {
            $query = <<<SQL
            SELECT COUNT(*) as count
            FROM productos
            WHERE id = :id_producto AND estado = 1
            SQL;

            $statement = $conexion->prepare($query);
            $statement->bindValue(':id_producto', $id_producto, PDO::PARAM_INT);
            $statement->execute();
            $resultado = $statement->fetch(PDO::FETCH_ASSOC);

            return $resultado['count'] > 0;
        } catch (PDOException) {
            return false;
        }
    }

    /**
     * Elimina un detalle de venta por su ID.
     *
     * @param int $id       ID del detalle a eliminar.
     * @param PDO $conexion Conexión PDO.
     *
     * @return bool True si la eliminación fue exitosa, False en caso contrario.
     * @throws Exception Si ocurre un error de base de datos.
     */
    public static function eliminar(int $id, PDO $conexion): bool
    {
        try {
            $query = <<<SQL
            DELETE FROM ventas_detalle
            WHERE id = :id
            SQL;

            $statement = $conexion->prepare($query);
            $statement->bindValue(':id', $id, PDO::PARAM_INT);

            return $statement->execute();

        } catch (PDOException $e) {
            throw new Exception("Error de base de datos al eliminar detalle de venta (ID: $id): " . $e->getMessage());
        }
    }

    /**
     * Calcula el valor total de todos los detalles de una venta.
     *
     * @param int $id_venta ID de la venta.
     * @param PDO $conexion Conexión PDO.
     *
     * @return float Valor total de los detalles de la venta.
     * @throws Exception Si ocurre un error de base de datos.
     */
    public static function calcularTotalVenta(int $id_venta, PDO $conexion): float
    {
        try {
            $query = <<<SQL
            SELECT COALESCE(SUM(valor_total), 0) as total
            FROM ventas_detalle
            WHERE id_venta = :id_venta
            SQL;

            $statement = $conexion->prepare($query);
            $statement->bindValue(':id_venta', $id_venta, PDO::PARAM_INT);
            $statement->execute();
            $resultado = $statement->fetch(PDO::FETCH_ASSOC);

            return (float)$resultado['total'];
        } catch (PDOException $e) {
            throw new Exception("Error de base de datos al calcular total de venta: " . $e->getMessage());
        }
    }

    // --- Getters y Setters ---

    public function getId(): ?int
    {
        return $this->id;
    }

    public function setId(?int $id): self
    {
        $this->id = $id;
        return $this;
    }

    public function getId_venta(): ?int
    {
        return $this->id_venta;
    }

    public function setId_venta(?int $id_venta): self
    {
        $this->id_venta = $id_venta;
        return $this;
    }

    public function getId_producto(): ?int
    {
        return $this->id_producto;
    }

    public function setId_producto(?int $id_producto): self
    {
        $this->id_producto = $id_producto;
        return $this;
    }

    public function getValor(): ?float
    {
        return $this->valor;
    }

    public function setValor(?float $valor): self
    {
        $this->valor = $valor;
        return $this;
    }

    public function getCantidad(): ?int
    {
        return $this->cantidad;
    }

    public function setCantidad(?int $cantidad): self
    {
        $this->cantidad = $cantidad;
        return $this;
    }

    public function getValor_total(): ?float
    {
        return $this->valor_total;
    }

    public function setValor_total(?float $valor_total): self
    {
        $this->valor_total = $valor_total;
        return $this;
    }

    public function getProducto_descripcion(): ?string
    {
        return $this->producto_descripcion;
    }

    public function setProducto_descripcion(?string $producto_descripcion): self
    {
        $this->producto_descripcion = $producto_descripcion;
        return $this;
    }

    public function getProducto_valor(): ?float
    {
        return $this->producto_valor;
    }

    public function setProducto_valor(?float $producto_valor): self
    {
        $this->producto_valor = $producto_valor;
        return $this;
    }

    // --- Métodos adicionales ---

    /**
     * Calcula y establece el valor total basado en valor unitario y cantidad.
     * @return self
     */
    public function calcularValorTotal(): self
    {
        if ($this->valor !== null && $this->cantidad !== null) {
            $this->valor_total = $this->valor * $this->cantidad;
        }
        return $this;
    }

    /**
     * Obtiene el valor total formateado en pesos colombianos.
     * @return string
     */
    public function getValorTotalFormateado(): string
    {
        if ($this->valor_total === null) {
            return '$0';
        }
        return '$' . number_format($this->valor_total, 0, ',', '.');
    }

    /**
     * Obtiene el valor unitario formateado en pesos colombianos.
     * @return string
     */
    public function getValorFormateado(): string
    {
        if ($this->valor === null) {
            return '$0';
        }
        return '$' . number_format($this->valor, 0, ',', '.');
    }

    /**
     * Verifica si el detalle tiene datos válidos para cálculos.
     * @return bool
     */
    public function isValido(): bool
    {
        return $this->valor !== null && $this->cantidad !== null &&
               $this->valor > 0 && $this->cantidad > 0;
    }
}