<?php

declare(strict_types=1);

namespace App\classes;

use App\classes\Proveedor;
use App\classes\CentroCosto;
use App\classes\Usuario;
use App\classes\Cierre;
use Exception;
use PDO;
use PDOException;

class OrdenCompra
{
    // --- Atributos ---
    private ?int    $id                      = null;
    private ?string $fecha                   = null;
    private ?float  $valor_total             = null;
    private ?int    $id_proveedor            = null;
    private ?int    $id_centro_costo         = null;
    private ?int    $id_usuario              = null;
    private ?string $n_referencia_proveedor  = null;
    private ?int    $id_cierre               = null;
    private ?int    $estado                  = null;
    private ?string $proveedor_nombre        = null;
    private ?string $centro_costo_nombre     = null;
    private ?string $usuario_nombre          = null;
    private ?string $cierre_nombre           = null;

    /**
     * Constructor: Inicializa las propiedades del objeto OrdenCompra.
     */
    public function __construct()
    {
        $this->id                     = 0;
        $this->fecha                  = null;
        $this->valor_total            = null;
        $this->id_proveedor           = null;
        $this->id_centro_costo        = null;
        $this->id_usuario             = null;
        $this->n_referencia_proveedor = null;
        $this->id_cierre              = null; // Referencia opcional a la tabla cierres
        $this->estado                 = 1; // Estado activo por defecto
        $this->proveedor_nombre       = null;
        $this->centro_costo_nombre    = null;
        $this->usuario_nombre         = null;
        $this->cierre_nombre          = null;
    }

    /**
     * Método estático para construir un objeto OrdenCompra desde un array (ej. fila de DB).
     *
     * @param array $resultado Array asociativo con los datos de la orden de compra.
     *
     * @return self Instancia de OrdenCompra.
     * @throws Exception Si ocurre un error durante la construcción.
     */
    public static function construct(array $resultado = []): self
    {
        try {
            $objeto                          = new self();
            $objeto->id                      = isset($resultado['id']) ? (int)$resultado['id'] : 0;
            $objeto->fecha                   = $resultado['fecha'] ?? null;
            $objeto->valor_total             = isset($resultado['valor_total']) ? (float)$resultado['valor_total'] : null;
            $objeto->id_proveedor            = isset($resultado['id_proveedor']) ? (int)$resultado['id_proveedor'] : null;
            $objeto->id_centro_costo         = isset($resultado['id_centro_costo']) ? (int)$resultado['id_centro_costo'] : null;
            $objeto->id_usuario              = isset($resultado['id_usuario']) ? (int)$resultado['id_usuario'] : null;
            $objeto->n_referencia_proveedor  = $resultado['n_referencia_proveedor'] ?? null;
            $objeto->id_cierre               = isset($resultado['id_cierre']) ? (int)$resultado['id_cierre'] : null;
            $objeto->estado                  = isset($resultado['estado']) ? (int)$resultado['estado'] : 1;
            $objeto->proveedor_nombre        = $resultado['proveedor_nombre'] ?? null;
            $objeto->centro_costo_nombre     = $resultado['centro_costo_nombre'] ?? null;
            $objeto->usuario_nombre          = $resultado['usuario_nombre'] ?? null;
            $objeto->cierre_nombre           = $resultado['cierre_nombre'] ?? null;
            return $objeto;
        } catch (Exception $e) {
            throw new Exception("Error al construir OrdenCompra: " . $e->getMessage());
        }
    }

    // --- Métodos de Acceso a Datos (Estáticos) ---

    /**
     * Obtiene una orden de compra por su ID.
     *
     * @param int $id       ID de la orden de compra.
     * @param PDO $conexion Conexión PDO.
     *
     * @return self|null Objeto OrdenCompra o null si no se encuentra.
     * @throws Exception Si hay error en DB.
     */
    public static function get(int $id, PDO $conexion): ?self
    {
        try {
            // Consulta para obtener orden de compra por ID (Usando Heredoc)
            $query = <<<SQL
            SELECT
                oc.*,
                p.nombre AS proveedor_nombre,
                cc.nombre AS centro_costo_nombre,
                u.nombre AS usuario_nombre,
                c.fecha AS cierre_nombre
            FROM ordenes_compra oc
            LEFT JOIN proveedores p ON oc.id_proveedor = p.id
            LEFT JOIN centros_costos cc ON oc.id_centro_costo = cc.id
            LEFT JOIN usuarios u ON oc.id_usuario = u.id
            LEFT JOIN cierres c ON oc.id_cierre = c.id
            WHERE
                oc.id = :id
            LIMIT 1
            SQL;

            $statement = $conexion->prepare($query);
            $statement->bindValue(":id", $id, PDO::PARAM_INT);
            $statement->execute();
            $resultado = $statement->fetch(PDO::FETCH_ASSOC);

            return $resultado ? self::construct($resultado) : null;

        } catch (PDOException $e) {
            throw new Exception("Error al obtener OrdenCompra (ID: $id): " . $e->getMessage());
        }
    }

    /**
     * Obtiene una lista de órdenes de compra activas.
     *
     * @param PDO $conexion Conexión PDO.
     *
     * @return array Array de objetos OrdenCompra.
     * @throws Exception Si hay error en DB.
     */
    public static function get_list(PDO $conexion): array
    {
        try {
            // Consulta para obtener lista de órdenes de compra activas (Usando Heredoc)
            $query = <<<SQL
            SELECT
                oc.*,
                p.nombre AS proveedor_nombre,
                cc.nombre AS centro_costo_nombre,
                u.nombre AS usuario_nombre,
                c.fecha AS cierre_nombre
            FROM ordenes_compra oc
            LEFT JOIN proveedores p ON oc.id_proveedor = p.id
            LEFT JOIN centros_costos cc ON oc.id_centro_costo = cc.id
            LEFT JOIN usuarios u ON oc.id_usuario = u.id
            LEFT JOIN cierres c ON oc.id_cierre = c.id
            WHERE
                oc.estado = 1
            ORDER BY
                oc.fecha DESC
            SQL;

            $statement = $conexion->prepare($query);
            $statement->execute();
            $resultados = $statement->fetchAll(PDO::FETCH_ASSOC);

            $listado = [];
            foreach ($resultados as $resultado) {
                $listado[] = self::construct($resultado);
            }
            return $listado;

        } catch (PDOException $e) {
            throw new Exception("Error al obtener lista de OrdenesCompra: " . $e->getMessage());
        }
    }

    /**
     * Obtiene todas las órdenes de compra activas con información de entidades relacionadas.
     *
     * @param PDO $conexion Conexión PDO.
     *
     * @return array Array de objetos OrdenCompra.
     * @throws Exception Si ocurre un error de base de datos.
     */
    public static function obtenerTodas(PDO $conexion): array
    {
        try {
            $query = <<<SQL
            SELECT
                oc.*,
                p.nombre AS proveedor_nombre,
                cc.nombre AS centro_costo_nombre,
                u.nombre AS usuario_nombre,
                c.fecha AS cierre_nombre
            FROM ordenes_compra oc
            LEFT JOIN proveedores p ON oc.id_proveedor = p.id
            INNER JOIN centros_costos cc ON oc.id_centro_costo = cc.id
            INNER JOIN usuarios u ON oc.id_usuario = u.id
            LEFT JOIN cierres c ON oc.id_cierre = c.id
            WHERE oc.estado = 1
            ORDER BY oc.fecha DESC
            SQL;

            $statement = $conexion->prepare($query);
            $statement->execute();
            $resultados = $statement->fetchAll(PDO::FETCH_ASSOC);

            $listado = [];
            foreach ($resultados as $resultado) {
                $listado[] = self::construct($resultado);
            }
            return $listado;

        } catch (PDOException $e) {
            throw new Exception("Error al obtener todas las órdenes de compra: " . $e->getMessage());
        }
    }

    /**
     * Busca órdenes de compra por criterios específicos.
     *
     * @param PDO $conexion Conexión PDO.
     * @param string $termino_proveedor Término de búsqueda para proveedor (nombre).
     * @param string $fecha_desde Fecha desde (formato Y-m-d).
     * @param string $fecha_hasta Fecha hasta (formato Y-m-d).
     * @param int|null $id_centro_costo ID del centro de costo.
     * @param string $numero_referencia Número de referencia del proveedor.
     * @param string $numero_orden Número de orden (ID) para filtrar.
     *
     * @return array Array de objetos OrdenCompra.
     * @throws Exception Si hay error en DB.
     */
    public static function search_ordenes_compra(PDO $conexion, string $termino_proveedor = '', string $fecha_desde = '', string $fecha_hasta = '', ?int $id_centro_costo = null, string $numero_referencia = '', string $numero_orden = ''): array
    {
        try {
            $where_conditions = ['oc.estado = 1'];
            $params = [];

            // Filtro por proveedor (nombre)
            if (!empty($termino_proveedor)) {
                $where_conditions[] = 'p.nombre LIKE :termino_proveedor';
                $params[':termino_proveedor'] = '%' . trim($termino_proveedor) . '%';
            }

            // Filtro por fecha desde
            if (!empty($fecha_desde)) {
                $where_conditions[] = 'DATE(oc.fecha) >= :fecha_desde';
                $params[':fecha_desde'] = $fecha_desde;
            }

            // Filtro por fecha hasta
            if (!empty($fecha_hasta)) {
                $where_conditions[] = 'DATE(oc.fecha) <= :fecha_hasta';
                $params[':fecha_hasta'] = $fecha_hasta;
            }

            // Filtro por centro de costo
            if ($id_centro_costo !== null && $id_centro_costo > 0) {
                $where_conditions[] = 'oc.id_centro_costo = :id_centro_costo';
                $params[':id_centro_costo'] = $id_centro_costo;
            }

            // Filtro por número de referencia del proveedor
            if (!empty($numero_referencia)) {
                $where_conditions[] = 'oc.n_referencia_proveedor LIKE :numero_referencia';
                $params[':numero_referencia'] = '%' . trim($numero_referencia) . '%';
            }

            // Filtro por número de orden (ID)
            if (!empty($numero_orden)) {
                $where_conditions[] = 'oc.id = :numero_orden';
                $params[':numero_orden'] = (int)trim($numero_orden);
            }

            $where_clause = implode(' AND ', $where_conditions);

            $query = <<<SQL
            SELECT
                oc.*,
                p.nombre AS proveedor_nombre,
                cc.nombre AS centro_costo_nombre,
                u.nombre AS usuario_nombre,
                c.fecha AS cierre_nombre
            FROM ordenes_compra oc
            LEFT JOIN proveedores p ON oc.id_proveedor = p.id
            INNER JOIN centros_costos cc ON oc.id_centro_costo = cc.id
            INNER JOIN usuarios u ON oc.id_usuario = u.id
            LEFT JOIN cierres c ON oc.id_cierre = c.id
            WHERE {$where_clause}
            ORDER BY oc.fecha DESC
            SQL;

            $statement = $conexion->prepare($query);

            foreach ($params as $param => $value) {
                $statement->bindValue($param, $value);
            }

            $statement->execute();
            $resultados = $statement->fetchAll(PDO::FETCH_ASSOC);

            $ordenes = [];
            foreach ($resultados as $resultado) {
                $ordenes[] = self::construct($resultado);
            }
            return $ordenes;

        } catch (PDOException $e) {
            throw new Exception("Error de base de datos al buscar órdenes de compra: " . $e->getMessage());
        }
    }

    /**
     * Obtiene las órdenes de compra activas listas para cierre de caja por centro de costo.
     *
     * Filtros aplicados:
     * - estado = 1 (órdenes activas, no canceladas)
     * - id_cierre IS NULL (no incluidas en cierres anteriores)
     * - centro de costo específico
     *
     * @param int $id_centro_costo ID del centro de costo.
     * @param PDO $conexion Conexión PDO.
     *
     * @return array Array de objetos OrdenCompra con información completa.
     * @throws Exception Si hay error en DB.
     */
    public static function getOrdenesCompraParaCierreByCentroCosto(int $id_centro_costo, PDO $conexion): array
    {
        try {
            // Consulta para obtener órdenes de compra listas para cierre con información completa
            $query = <<<SQL
            SELECT
                oc.*,
                p.nombre AS proveedor_nombre,
                cc.nombre AS centro_costo_nombre,
                u.nombre AS usuario_nombre,
                c.fecha AS cierre_nombre
            FROM ordenes_compra oc
            LEFT JOIN proveedores p ON oc.id_proveedor = p.id
            INNER JOIN centros_costos cc ON oc.id_centro_costo = cc.id
            INNER JOIN usuarios u ON oc.id_usuario = u.id
            LEFT JOIN cierres c ON oc.id_cierre = c.id
            WHERE oc.estado = 1
            AND oc.id_cierre IS NULL
            AND oc.id_centro_costo = :id_centro_costo
            ORDER BY oc.fecha DESC
            SQL;

            $statement = $conexion->prepare($query);
            $statement->bindValue(':id_centro_costo', $id_centro_costo, PDO::PARAM_INT);
            $statement->execute();
            $resultados = $statement->fetchAll(PDO::FETCH_ASSOC);

            $listado = [];
            foreach ($resultados as $resultado) {
                $orden = self::construct($resultado);
                $listado[] = $orden;
            }

            return $listado;

        } catch (PDOException $e) {
            throw new Exception("Error al obtener órdenes de compra para cierre por centro de costo: " . $e->getMessage());
        }
    }

    /**
     * Obtiene una orden de compra por su ID con información de entidades relacionadas.
     *
     * @param int $id       ID de la orden de compra.
     * @param PDO $conexion Conexión PDO.
     *
     * @return self|null Objeto OrdenCompra o null si no se encuentra.
     * @throws Exception Si ocurre un error de base de datos.
     */
    public static function obtener(int $id, PDO $conexion): ?self
    {
        try {
            $query = <<<SQL
            SELECT
                oc.*,
                p.nombre AS proveedor_nombre,
                cc.nombre AS centro_costo_nombre,
                u.nombre AS usuario_nombre,
                c.fecha AS cierre_nombre
            FROM ordenes_compra oc
            LEFT JOIN proveedores p ON oc.id_proveedor = p.id
            INNER JOIN centros_costos cc ON oc.id_centro_costo = cc.id
            INNER JOIN usuarios u ON oc.id_usuario = u.id
            LEFT JOIN cierres c ON oc.id_cierre = c.id
            WHERE oc.id = :id AND oc.estado = 1
            LIMIT 1
            SQL;

            $statement = $conexion->prepare($query);
            $statement->bindValue(':id', $id, PDO::PARAM_INT);
            $statement->execute();
            $resultado = $statement->fetch(PDO::FETCH_ASSOC);

            return $resultado ? self::construct($resultado) : null;

        } catch (PDOException $e) {
            throw new Exception("Error al obtener orden de compra (ID: $id): " . $e->getMessage());
        }
    }

    /**
     * Crea una nueva orden de compra en la base de datos a partir de un objeto OrdenCompra.
     *
     * @param PDO $conexion Conexión PDO.
     *
     * @return int|false El ID de la nueva orden de compra creada o false en caso de error.
     * @throws Exception Si los datos requeridos en el objeto están vacíos o hay error en DB.
     */
    function crear(PDO $conexion): int|false
    {
        // Validaciones básicas sobre el objeto
        if ($this->getValor_total() === null || $this->getId_proveedor() === null ||
            $this->getId_centro_costo() === null || $this->getId_usuario() === null) {
            throw new Exception("Valor total, proveedor, centro de costo y usuario son requeridos para crear una orden de compra.");
        }

        // Validar valor total como no negativo
        if ($this->getValor_total() < 0) {
            throw new Exception("El valor total no puede ser negativo.");
        }

        // Validar existencia de foreign keys
        $this->_validarForeignKeys($conexion);

        // Auto-set fecha with America/Bogota timezone
        date_default_timezone_set('America/Bogota');
        $this->setFecha(date('Y-m-d H:i:s'));

        try {
            return $this->_insert($conexion);
        } catch (Exception $e) {
            throw new Exception("Error al crear orden de compra: " . $e->getMessage());
        }
    }

    /**
     * Modifica una orden de compra existente.
     *
     * @param PDO $conexion Conexión PDO.
     *
     * @return bool True si la modificación fue exitosa, False en caso contrario.
     * @throws Exception Si los datos requeridos están vacíos o si ocurre un error de base de datos.
     */
    function modificar(PDO $conexion): bool
    {
        // Validaciones básicas sobre el objeto
        if ($this->getId() === null || $this->getId() <= 0) {
            throw new Exception("ID de orden de compra inválido para modificar.");
        }

        if ($this->getValor_total() === null || $this->getId_proveedor() === null ||
            $this->getId_centro_costo() === null || $this->getId_usuario() === null) {
            throw new Exception("Valor total, proveedor, centro de costo y usuario son requeridos para modificar una orden de compra.");
        }

        // Validar valor total como no negativo
        if ($this->getValor_total() < 0) {
            throw new Exception("El valor total no puede ser negativo.");
        }

        // Validar existencia de foreign keys
        $this->_validarForeignKeys($conexion);

        try {
            return $this->_update($conexion);
        } catch (Exception $e) {
            throw new Exception("Error al modificar orden de compra: " . $e->getMessage());
        }
    }

    /**
     * Elimina (desactiva) una orden de compra estableciendo su estado a 0.
     *
     * @param PDO $conexion Conexión PDO.
     *
     * @return bool True si la eliminación fue exitosa, False en caso contrario.
     * @throws Exception Si ocurre un error de base de datos.
     */
    function eliminar(PDO $conexion): bool
    {
        if ($this->getId() === null || $this->getId() <= 0) {
            throw new Exception("ID de orden de compra inválido para eliminar.");
        }

        try {
            // Consulta para actualizar el estado a 0 (inactivo)
            $query = <<<SQL
            UPDATE ordenes_compra SET
                estado = 0
            WHERE
                id = :id
            SQL;

            $statement = $conexion->prepare($query);
            $statement->bindValue(':id', $this->getId(), PDO::PARAM_INT);

            return $statement->execute();

        } catch (PDOException $e) {
            throw new Exception("Error de base de datos al eliminar orden de compra (ID: {$this->getId()}): " . $e->getMessage());
        }
    }

    /**
     * Método privado para insertar una nueva orden de compra en la base de datos.
     *
     * @param PDO $conexion Conexión PDO.
     *
     * @return int|false El ID de la nueva orden de compra creada o false en caso de error.
     * @throws Exception Si hay error en DB.
     */
    private function _insert(PDO $conexion): int|false
    {
        try {
            // Preparar la consulta INSERT usando Heredoc
            $query = <<<SQL
            INSERT INTO ordenes_compra (
                 fecha
                ,valor_total
                ,id_proveedor
                ,id_centro_costo
                ,id_usuario
                ,n_referencia_proveedor
                ,id_cierre
                ,estado
            ) VALUES (
                 :fecha
                ,:valor_total
                ,:id_proveedor
                ,:id_centro_costo
                ,:id_usuario
                ,:n_referencia_proveedor
                ,:id_cierre
                ,:estado
            )
            SQL;

            $statement = $conexion->prepare($query);

            // Bind de parámetros desde el objeto
            $statement->bindValue(':fecha', $this->getFecha(), PDO::PARAM_STR);
            $statement->bindValue(':valor_total', $this->getValor_total(), PDO::PARAM_STR);
            $statement->bindValue(':id_proveedor', $this->getId_proveedor(), PDO::PARAM_INT);
            $statement->bindValue(':id_centro_costo', $this->getId_centro_costo(), PDO::PARAM_INT);
            $statement->bindValue(':id_usuario', $this->getId_usuario(), PDO::PARAM_INT);
            $statement->bindValue(':n_referencia_proveedor', $this->getN_referencia_proveedor(), PDO::PARAM_STR);

            // Handle null values for id_cierre
            $id_cierre = $this->getId_cierre();
            $statement->bindValue(':id_cierre', $id_cierre, $id_cierre === null ? PDO::PARAM_NULL : PDO::PARAM_INT);

            $statement->bindValue(':estado', $this->getEstado(), PDO::PARAM_INT);

            // Ejecutar la consulta
            $success = $statement->execute();

            if ($success) {
                // Devolver el ID de la orden de compra recién creada
                return (int)$conexion->lastInsertId();
            } else {
                return false; // Error en la ejecución
            }

        } catch (PDOException $e) {
            throw new Exception("Error de base de datos al insertar orden de compra: " . $e->getMessage());
        }
    }

    /**
     * Método privado para actualizar una orden de compra existente en la base de datos.
     *
     * @param PDO $conexion Conexión PDO.
     *
     * @return bool True si la actualización fue exitosa, False en caso contrario.
     * @throws Exception Si hay error en DB.
     */
    private function _update(PDO $conexion): bool
    {
        try {
            // Preparar la consulta UPDATE usando Heredoc
            $query = <<<SQL
            UPDATE ordenes_compra SET
                 valor_total = :valor_total
                ,id_proveedor = :id_proveedor
                ,id_centro_costo = :id_centro_costo
                ,id_usuario = :id_usuario
                ,n_referencia_proveedor = :n_referencia_proveedor
                ,id_cierre = :id_cierre
            WHERE
                id = :id
            SQL;

            $statement = $conexion->prepare($query);

            // Bind de parámetros desde el objeto
            $statement->bindValue(':valor_total', $this->getValor_total(), PDO::PARAM_STR);
            $statement->bindValue(':id_proveedor', $this->getId_proveedor(), PDO::PARAM_INT);
            $statement->bindValue(':id_centro_costo', $this->getId_centro_costo(), PDO::PARAM_INT);
            $statement->bindValue(':id_usuario', $this->getId_usuario(), PDO::PARAM_INT);
            $statement->bindValue(':n_referencia_proveedor', $this->getN_referencia_proveedor(), PDO::PARAM_STR);

            // Handle null values for id_cierre
            $id_cierre = $this->getId_cierre();
            $statement->bindValue(':id_cierre', $id_cierre, $id_cierre === null ? PDO::PARAM_NULL : PDO::PARAM_INT);

            $statement->bindValue(':id', $this->getId(), PDO::PARAM_INT);

            return $statement->execute();

        } catch (PDOException $e) {
            throw new Exception("Error de base de datos al actualizar orden de compra: " . $e->getMessage());
        }
    }

    /**
     * Método privado para validar la existencia de foreign keys.
     *
     * @param PDO $conexion Conexión PDO.
     *
     * @throws Exception Si alguna foreign key no existe.
     */
    private function _validarForeignKeys(PDO $conexion): void
    {
        // Validar existencia del proveedor
        if ($this->getId_proveedor() !== null) {
            $proveedor = Proveedor::get($this->getId_proveedor(), $conexion);
            if ($proveedor === null) {
                throw new Exception("El proveedor especificado no existe.");
            }
        }

        // Validar existencia del centro de costo
        if ($this->getId_centro_costo() !== null) {
            $centroCosto = CentroCosto::get($this->getId_centro_costo(), $conexion);
            if ($centroCosto === null) {
                throw new Exception("El centro de costo especificado no existe.");
            }
        }

        // Validar existencia del usuario
        if ($this->getId_usuario() !== null) {
            $usuario = Usuario::get($this->getId_usuario(), $conexion);
            if ($usuario === null) {
                throw new Exception("El usuario especificado no existe.");
            }
        }

        // Validar existencia del cierre (opcional)
        if ($this->getId_cierre() !== null) {
            $cierre = Cierre::get($this->getId_cierre(), $conexion);
            if ($cierre === null) {
                throw new Exception("El cierre especificado no existe.");
            }
        }
    }

    // --- Getters y Setters ---

    public function getId(): ?int
    {
        return $this->id;
    }

    public function setId(?int $id): self
    {
        $this->id = $id;
        return $this;
    }

    public function getFecha(): ?string
    {
        return $this->fecha;
    }

    public function setFecha(?string $fecha): self
    {
        $this->fecha = $fecha;
        return $this;
    }

    public function getValor_total(): ?float
    {
        return $this->valor_total;
    }

    /**
     * Establece el valor total de la orden de compra.
     *
     * @param float|null $valor_total Valor total de la orden de compra.
     *
     * @return self
     * @throws Exception Si el valor no es positivo.
     */
    public function setValor_total(?float $valor_total): self
    {
        if ($valor_total !== null && $valor_total < 0) {
            throw new Exception("El valor total no puede ser negativo.");
        }
        $this->valor_total = $valor_total;
        return $this;
    }

    public function getId_proveedor(): ?int
    {
        return $this->id_proveedor;
    }

    /**
     * Establece el ID del proveedor.
     *
     * @param int|null $id_proveedor ID del proveedor.
     *
     * @return self
     * @throws Exception Si el ID no es válido.
     */
    public function setId_proveedor(?int $id_proveedor): self
    {
        if ($id_proveedor !== null && $id_proveedor <= 0) {
            throw new Exception("El ID del proveedor debe ser un número entero positivo.");
        }
        $this->id_proveedor = $id_proveedor;
        return $this;
    }

    public function getId_centro_costo(): ?int
    {
        return $this->id_centro_costo;
    }

    /**
     * Establece el ID del centro de costo.
     *
     * @param int|null $id_centro_costo ID del centro de costo.
     *
     * @return self
     * @throws Exception Si el ID no es válido.
     */
    public function setId_centro_costo(?int $id_centro_costo): self
    {
        if ($id_centro_costo !== null && $id_centro_costo <= 0) {
            throw new Exception("El ID del centro de costo debe ser un número entero positivo.");
        }
        $this->id_centro_costo = $id_centro_costo;
        return $this;
    }

    public function getId_usuario(): ?int
    {
        return $this->id_usuario;
    }

    /**
     * Establece el ID del usuario.
     *
     * @param int|null $id_usuario ID del usuario.
     *
     * @return self
     * @throws Exception Si el ID no es válido.
     */
    public function setId_usuario(?int $id_usuario): self
    {
        if ($id_usuario !== null && $id_usuario <= 0) {
            throw new Exception("El ID del usuario debe ser un número entero positivo.");
        }
        $this->id_usuario = $id_usuario;
        return $this;
    }

    public function getN_referencia_proveedor(): ?string
    {
        return $this->n_referencia_proveedor;
    }

    public function setN_referencia_proveedor(?string $n_referencia_proveedor): self
    {
        $this->n_referencia_proveedor = $n_referencia_proveedor;
        return $this;
    }

    public function getId_cierre(): ?int
    {
        return $this->id_cierre;
    }

    /**
     * Establece el ID del cierre.
     *
     * @param int|null $id_cierre ID del cierre.
     *
     * @return self
     * @throws Exception Si el ID no es válido.
     */
    public function setId_cierre(?int $id_cierre): self
    {
        if ($id_cierre !== null && $id_cierre <= 0) {
            throw new Exception("El ID del cierre debe ser un número entero positivo.");
        }
        $this->id_cierre = $id_cierre;
        return $this;
    }

    public function getEstado(): ?int
    {
        return $this->estado;
    }

    public function setEstado(?int $estado): self
    {
        $this->estado = $estado;
        return $this;
    }

    public function getProveedor_nombre(): ?string
    {
        return $this->proveedor_nombre;
    }

    public function setProveedor_nombre(?string $proveedor_nombre): self
    {
        $this->proveedor_nombre = $proveedor_nombre;
        return $this;
    }

    public function getCentro_costo_nombre(): ?string
    {
        return $this->centro_costo_nombre;
    }

    public function setCentro_costo_nombre(?string $centro_costo_nombre): self
    {
        $this->centro_costo_nombre = $centro_costo_nombre;
        return $this;
    }

    public function getUsuario_nombre(): ?string
    {
        return $this->usuario_nombre;
    }

    public function setUsuario_nombre(?string $usuario_nombre): self
    {
        $this->usuario_nombre = $usuario_nombre;
        return $this;
    }

    public function getCierre_nombre(): ?string
    {
        return $this->cierre_nombre;
    }

    public function setCierre_nombre(?string $cierre_nombre): self
    {
        $this->cierre_nombre = $cierre_nombre;
        return $this;
    }

    // --- Métodos adicionales ---

    /**
     * Verifica si la orden de compra está activa.
     * @return bool
     */
    public function isActiva(): bool
    {
        return $this->estado === 1;
    }

    /**
     * Actualiza el id_cierre para múltiples órdenes de compra en una sola operación.
     *
     * @param array $ids_ordenes Array de IDs de órdenes de compra a actualizar.
     * @param int   $id_cierre   ID del cierre a asignar.
     * @param PDO   $conexion    Conexión PDO.
     *
     * @return bool True si la actualización fue exitosa, False en caso contrario.
     * @throws Exception Si hay error en DB.
     */
    public static function actualizarIdCierreBulk(array $ids_ordenes, int $id_cierre, PDO $conexion): bool
    {
        if (empty($ids_ordenes)) {
            return true; // No hay nada que actualizar
        }

        try {
            // Validar y sanitizar todos los IDs para prevenir inyección SQL
            $ids_validados = [];
            foreach ($ids_ordenes as $id) {
                $id_int = filter_var($id, FILTER_VALIDATE_INT, [
                    'options' => ['min_range' => 1]
                ]);
                if ($id_int === false) {
                    throw new Exception("ID de orden de compra inválido detectado: " . var_export($id, true));
                }
                $ids_validados[] = $id_int;
            }

            // Crear placeholders seguros para los IDs validados
            $placeholders = str_repeat('?,', count($ids_validados) - 1) . '?';

            // Consulta para actualizar el id_cierre en múltiples órdenes de compra
            $query = <<<SQL
            UPDATE ordenes_compra SET
                id_cierre = ?
            WHERE
                id IN ($placeholders)
                AND estado = 1
            SQL;

            $statement = $conexion->prepare($query);

            // Bind del id_cierre primero, luego los IDs validados
            $params = [$id_cierre];
            $params = array_merge($params, $ids_validados);

            return $statement->execute($params);

        } catch (PDOException $e) {
            throw new Exception("Error de base de datos al actualizar id_cierre en órdenes de compra: " . $e->getMessage());
        }
    }

    /**
     * Obtiene las órdenes de compra asociadas a un cierre específico.
     *
     * @param int $id_cierre ID del cierre.
     * @param PDO $conexion  Conexión PDO.
     *
     * @return array Array de objetos OrdenCompra con información completa.
     * @throws Exception Si hay error en DB.
     */
    public static function getOrdenesCompraByCierre(int $id_cierre, PDO $conexion): array
    {
        try {
            // Consulta para obtener órdenes de compra por cierre con información completa
            $query = <<<SQL
            SELECT
                oc.*,
                p.nombre AS proveedor_nombre,
                cc.nombre AS centro_costo_nombre,
                u.nombre AS usuario_nombre
            FROM ordenes_compra oc
            LEFT JOIN proveedores p ON oc.id_proveedor = p.id
            INNER JOIN centros_costos cc ON oc.id_centro_costo = cc.id
            LEFT JOIN usuarios u ON oc.id_usuario = u.id
            WHERE oc.id_cierre = :id_cierre
            ORDER BY oc.fecha DESC
            SQL;

            $statement = $conexion->prepare($query);
            $statement->bindValue(':id_cierre', $id_cierre, PDO::PARAM_INT);
            $statement->execute();
            $resultados = $statement->fetchAll(PDO::FETCH_ASSOC);

            $listado = [];
            foreach ($resultados as $resultado) {
                $orden = self::construct($resultado);
                $listado[] = $orden;
            }

            return $listado;

        } catch (PDOException $e) {
            throw new Exception("Error al obtener órdenes de compra por cierre: " . $e->getMessage());
        }
    }

    /**
     * Calcula los egresos totales de órdenes de compra para una fecha específica y centro de costo.
     * Solo incluye órdenes finalizadas (con cierre asignado).
     *
     * @param string $fecha           Fecha a consultar (formato YYYY-MM-DD).
     * @param int    $id_centro_costo ID del centro de costo.
     * @param PDO    $conexion        Conexión PDO.
     *
     * @return float Total de egresos de órdenes de compra para la fecha y centro de costo.
     * @throws Exception Si hay error en DB.
     */
    public static function calcularEgresosPorFecha(string $fecha, int $id_centro_costo, PDO $conexion): float
    {
        try {
            $query = <<<SQL
            SELECT COALESCE(SUM(valor_total), 0) as total
            FROM ordenes_compra
            WHERE DATE(fecha) = :fecha
            AND estado = 1
            AND id_cierre IS NOT NULL
            AND id_centro_costo = :id_centro_costo
            SQL;

            $statement = $conexion->prepare($query);
            $statement->bindValue(':fecha', $fecha, PDO::PARAM_STR);
            $statement->bindValue(':id_centro_costo', $id_centro_costo, PDO::PARAM_INT);
            $statement->execute();
            $resultado = $statement->fetch(PDO::FETCH_ASSOC);

            return (float)($resultado['total'] ?? 0);

        } catch (PDOException $e) {
            throw new Exception("Error al calcular egresos de órdenes por fecha ($fecha): " . $e->getMessage());
        }
    }

    /**
     * Obtiene los registros detallados de órdenes de compra para una fecha específica y centro de costo.
     * Solo incluye órdenes finalizadas (con cierre asignado).
     *
     * @param string $fecha           Fecha a consultar (formato YYYY-MM-DD).
     * @param int    $id_centro_costo ID del centro de costo.
     * @param PDO    $conexion        Conexión PDO.
     *
     * @return array Array de registros de órdenes de compra con detalles.
     * @throws Exception Si hay error en DB.
     */
    public static function obtenerRegistrosPorFecha(string $fecha, int $id_centro_costo, PDO $conexion): array
    {
        try {
            $query = <<<SQL
            SELECT
                oc.id,
                oc.fecha,
                oc.n_referencia_proveedor,
                p.nombre AS nombre_proveedor,
                u.nombre AS nombre_usuario,
                oc.valor_total
            FROM ordenes_compra oc
            LEFT JOIN proveedores p ON oc.id_proveedor = p.id
            INNER JOIN usuarios u ON oc.id_usuario = u.id
            WHERE DATE(oc.fecha) = :fecha
            AND oc.estado = 1
            AND oc.id_cierre IS NOT NULL
            AND oc.id_centro_costo = :id_centro_costo
            ORDER BY oc.fecha DESC
            SQL;

            $statement = $conexion->prepare($query);
            $statement->bindValue(':fecha', $fecha, PDO::PARAM_STR);
            $statement->bindValue(':id_centro_costo', $id_centro_costo, PDO::PARAM_INT);
            $statement->execute();
            $resultados = $statement->fetchAll(PDO::FETCH_ASSOC);

            // Format results
            $registros_formateados = [];
            foreach ($resultados as $resultado) {
                $registros_formateados[] = [
                    'id' => $resultado['id'],
                    'fecha' => $resultado['fecha'],
                    'numero_referencia' => $resultado['n_referencia_proveedor'] ?? '',
                    'nombre_proveedor' => $resultado['nombre_proveedor'] ?? 'Proveedor no especificado',
                    'nombre_usuario' => $resultado['nombre_usuario'],
                    'valor_total' => (float)$resultado['valor_total'],
                    'valor_total_formateado' => '$' . number_format((float)$resultado['valor_total'], 0, ',', '.')
                ];
            }

            return $registros_formateados;

        } catch (PDOException $e) {
            throw new Exception("Error al obtener registros de órdenes por fecha ($fecha): " . $e->getMessage());
        }
    }
}
