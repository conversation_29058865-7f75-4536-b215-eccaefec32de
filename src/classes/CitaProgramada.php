<?php

declare(strict_types=1);

namespace App\classes;

use Exception;
use PDO;
use PDOException;
use App\classes\Servicio;
use App\classes\Cliente;

class CitaProgramada
{
    // --- Atributos ---
    private ?int    $id                = null;
    private ?string $fecha_inicio      = null;
    private ?string $fecha_fin         = null;
    private ?string $razon_cancelacion = null;
    private ?string $fecha_cancelacion = null;
    private ?int    $id_empleado       = null;
    private ?int    $id_cliente        = null;
    private ?int    $duracion          = null;
    private ?int    $estado            = null;
    private ?int    $es_realizado      = null;
    private ?int    $id_centro_costo   = null;

    // Atributos adicionales para información relacionada
    private ?string $nombre_empleado   = null; // Nombre del empleado asociado
    private ?string $nombre_cliente    = null; // Nombre del cliente asociado
    private ?string $celular_cliente   = null; // Celular del cliente asociado
    private ?string $nombre_centro_costo = null; // Nombre del centro de costo asociado

    /**
     * Constructor: Inicializa las propiedades del objeto CitaProgramada.
     */
    public function __construct()
    {
        $this->id                = 0;
        $this->fecha_inicio      = null;
        $this->fecha_fin         = null;
        $this->razon_cancelacion = null;
        $this->fecha_cancelacion = null;
        $this->id_empleado       = null;
        $this->id_cliente        = null;
        $this->duracion          = 0;
        $this->estado            = 1; // Estado activo por defecto
        $this->es_realizado      = 0;
        $this->id_centro_costo   = null;
        $this->nombre_empleado   = null;
        $this->nombre_cliente    = null;
        $this->celular_cliente   = null;
        $this->nombre_centro_costo = null;
    }

    /**
     * Método estático para construir un objeto CitaProgramada desde un array (ej. fila de DB).
     *
     * @param array $resultado Array asociativo con los datos de la cita programada.
     *
     * @return self Instancia de CitaProgramada.
     * @throws Exception Si ocurre un error durante la construcción.
     */
    public static function construct(array $resultado = []): self
    {
        try {
            $objeto                      = new self();
            $objeto->id                  = isset($resultado['id']) ? (int)$resultado['id'] : 0;
            $objeto->fecha_inicio        = $resultado['fecha_inicio'] ?? null;
            $objeto->fecha_fin           = $resultado['fecha_fin'] ?? null;
            $objeto->razon_cancelacion   = $resultado['razon_cancelacion'] ?? null;
            $objeto->fecha_cancelacion   = $resultado['fecha_cancelacion'] ?? null;
            $objeto->id_empleado         = isset($resultado['id_empleado']) ? (int)$resultado['id_empleado'] : null;
            $objeto->id_cliente          = isset($resultado['id_cliente']) ? (int)$resultado['id_cliente'] : null;
            $objeto->duracion            = isset($resultado['duracion']) ? (int)$resultado['duracion'] : 0;
            $objeto->estado              = isset($resultado['estado']) ? (int)$resultado['estado'] : 1;
            $objeto->es_realizado        = isset($resultado['es_realizado']) ? (int)$resultado['es_realizado'] : 0;
            $objeto->id_centro_costo     = isset($resultado['id_centro_costo']) ? (int)$resultado['id_centro_costo'] : null;
            $objeto->nombre_empleado     = $resultado['nombre_empleado'] ?? null;
            $objeto->nombre_cliente      = $resultado['nombre_cliente'] ?? null;
            $objeto->celular_cliente     = $resultado['celular_cliente'] ?? null;
            $objeto->nombre_centro_costo = $resultado['nombre_centro_costo'] ?? null;
            return $objeto;
        } catch (Exception $e) {
            throw new Exception("Error al construir CitaProgramada: " . $e->getMessage());
        }
    }

    // --- Métodos de Acceso a Datos ---

    /**
     * Obtiene una cita programada por su ID.
     *
     * @param int $id       ID de la cita programada.
     * @param PDO $conexion Conexión PDO.
     *
     * @return self|null Objeto CitaProgramada o null si no se encuentra.
     * @throws Exception Si hay error en DB.
     */
    public static function get(int $id, PDO $conexion): ?self
    {
        try {
            // Consulta para obtener cita programada por ID (Usando Heredoc)
            $query = <<<SQL
            SELECT
                cp.*,
                e.nombre AS nombre_empleado,
                c.nombre AS nombre_cliente,
                c.celular AS celular_cliente,
                cc.nombre AS nombre_centro_costo
            FROM citas_programadas cp
            LEFT JOIN empleados e ON cp.id_empleado = e.id
            LEFT JOIN clientes c ON cp.id_cliente = c.id
            LEFT JOIN centros_costos cc ON cp.id_centro_costo = cc.id
            WHERE
                cp.id = :id
            LIMIT 1
            SQL;

            $statement = $conexion->prepare($query);
            $statement->bindValue(":id", $id, PDO::PARAM_INT);
            $statement->execute();
            $resultado = $statement->fetch(PDO::FETCH_ASSOC);

            return $resultado ? self::construct($resultado) : null;

        } catch (PDOException $e) {
            throw new Exception("Error al obtener CitaProgramada (ID: $id): " . $e->getMessage());
        }
    }

    /**
     * Obtiene una lista de citas programadas activas.
     *
     * @param PDO $conexion Conexión PDO.
     * @param bool $incluir_canceladas Si es true, incluye citas canceladas (estado=0).
     *
     * @return array Array de objetos CitaProgramada.
     * @throws Exception Si hay error en DB.
     */
    public static function get_list(PDO $conexion, bool $incluir_canceladas = false): array
    {
        try {
            // Consulta para obtener lista de citas programadas (Usando Heredoc)
            $query = <<<SQL
            SELECT
                cp.*,
                e.nombre AS nombre_empleado,
                c.nombre AS nombre_cliente,
                c.celular AS celular_cliente,
                cc.nombre AS nombre_centro_costo
            FROM citas_programadas cp
            LEFT JOIN empleados e ON cp.id_empleado = e.id
            LEFT JOIN clientes c ON cp.id_cliente = c.id
            LEFT JOIN centros_costos cc ON cp.id_centro_costo = cc.id
            SQL;

            // Agregar filtro de estado si no se incluyen canceladas
            if (!$incluir_canceladas) {
                $query .= " WHERE cp.estado = 1";
            }

            $query .= " ORDER BY cp.fecha_inicio";

            $statement = $conexion->prepare($query);
            $statement->execute();
            $resultados = $statement->fetchAll(PDO::FETCH_ASSOC);

            $listado = [];
            foreach ($resultados as $resultado) {
                $listado[] = self::construct($resultado);
            }
            return $listado;

        } catch (PDOException $e) {
            throw new Exception("Error al obtener lista de CitasProgramadas: " . $e->getMessage());
        }
    }

    /**
     * Obtiene una lista filtrada de citas programadas con filtros aplicados a nivel de base de datos.
     *
     * @param PDO $conexion Conexión PDO.
     * @param array $filtros Array asociativo con filtros: fecha_inicio, fecha_fin, id_empleado, id_servicio, filtro_cliente
     * @param bool $incluir_canceladas Si es true, incluye citas canceladas (estado=0).
     *
     * @return array Array de objetos CitaProgramada.
     * @throws Exception Si hay error en DB.
     */
    public static function get_list_filtered(PDO $conexion, array $filtros = [], bool $incluir_canceladas = false): array
    {
        try {
            // Consulta base con JOINs para obtener información relacionada
            $query = <<<SQL
            SELECT DISTINCT
                cp.*,
                e.nombre AS nombre_empleado,
                c.nombre AS nombre_cliente,
                c.celular AS celular_cliente,
                cc.nombre AS nombre_centro_costo
            FROM citas_programadas cp
            LEFT JOIN empleados e ON cp.id_empleado = e.id
            LEFT JOIN clientes c ON cp.id_cliente = c.id
            LEFT JOIN centros_costos cc ON cp.id_centro_costo = cc.id
            SQL;

            // Agregar JOIN para servicios si se filtra por servicio
            if (!empty($filtros['id_servicio'])) {
                $query .= " LEFT JOIN citas_programadas_servicios cps ON cp.id = cps.id_cita_programada";
            }

            // Construir condiciones WHERE
            $conditions = [];
            $params = [];

            // Filtro de estado (siempre aplicar a menos que se incluyan canceladas)
            if (!$incluir_canceladas) {
                $conditions[] = "cp.estado = :estado";
                $params[':estado'] = 1;
            }

            // Filtro por rango de fechas
            if (!empty($filtros['fecha_inicio']) && !empty($filtros['fecha_fin'])) {
                $conditions[] = "cp.fecha_inicio >= :fecha_inicio AND cp.fecha_inicio <= :fecha_fin";
                $params[':fecha_inicio'] = $filtros['fecha_inicio'];
                $params[':fecha_fin'] = $filtros['fecha_fin'];
            }

            // Filtro por empleado
            if (!empty($filtros['id_empleado'])) {
                if ($filtros['id_empleado'] === 'null') {
                    $conditions[] = "cp.id_empleado IS NULL";
                } else {
                    $conditions[] = "cp.id_empleado = :id_empleado";
                    $params[':id_empleado'] = (int)$filtros['id_empleado'];
                }
            }

            // Filtro por servicio
            if (!empty($filtros['id_servicio'])) {
                $conditions[] = "cps.id_servicio = :id_servicio";
                $params[':id_servicio'] = (int)$filtros['id_servicio'];
            }

            // Filtro por cliente (nombre o celular)
            if (!empty($filtros['filtro_cliente'])) {
                $conditions[] = "(c.nombre LIKE :filtro_cliente_nombre OR c.celular LIKE :filtro_cliente_celular)";
                $searchTerm = '%' . trim($filtros['filtro_cliente']) . '%';
                $params[':filtro_cliente_nombre'] = $searchTerm;
                $params[':filtro_cliente_celular'] = $searchTerm;
            }

            // Agregar condiciones WHERE si existen
            if (!empty($conditions)) {
                $query .= " WHERE " . implode(" AND ", $conditions);
            }

            $query .= " ORDER BY cp.fecha_inicio";

            $statement = $conexion->prepare($query);

            // Bind de parámetros
            foreach ($params as $param => $value) {
                if (is_int($value)) {
                    $statement->bindValue($param, $value, PDO::PARAM_INT);
                } else {
                    $statement->bindValue($param, $value, PDO::PARAM_STR);
                }
            }

            $statement->execute();
            $resultados = $statement->fetchAll(PDO::FETCH_ASSOC);

            $listado = [];
            foreach ($resultados as $resultado) {
                $listado[] = self::construct($resultado);
            }
            return $listado;

        } catch (PDOException $e) {
            throw new Exception("Error al obtener lista filtrada de CitasProgramadas: " . $e->getMessage());
        }
    }

    /**
     * Crea una nueva cita programada en la base de datos.
     * Establece automáticamente la fecha_inicio al momento actual y calcula fecha_fin
     * basado en la duración de los servicios asociados.
     *
     * @param PDO $conexion Conexión PDO.
     *
     * @return int|false El ID de la nueva cita programada creada o false en caso de error.
     * @throws Exception Si hay error en DB o validación.
     */
    function crear(PDO $conexion): int|false
    {
        try {
            // Validar que id_cliente sea obligatorio
            if ($this->getId_cliente() === null || $this->getId_cliente() <= 0) {
                throw new Exception("El ID del cliente es obligatorio para crear una cita programada.");
            }

            // Validar que el cliente exista
            $cliente = Cliente::get($this->getId_cliente(), $conexion);
            if (!$cliente) {
                throw new Exception("El cliente con ID {$this->getId_cliente()} no existe.");
            }

            // Establecer la zona horaria para Colombia
            date_default_timezone_set('America/Bogota');

            // Establecer fecha_inicio al momento actual si no está definida
            if (empty($this->getFecha_inicio())) {
                $this->setFecha_inicio(date('Y-m-d H:i:s'));
            }

            // Validar que la duración sea mayor que cero
            if ($this->getDuracion() <= 0) {
                throw new Exception("La duración debe ser un valor positivo en minutos.");
            }

            // Calcular fecha_fin basado en la duración
            $fechaInicio = new \DateTime($this->getFecha_inicio());
            $fechaInicio->add(new \DateInterval('PT' . $this->getDuracion() . 'M')); // Añadir minutos
            $this->setFecha_fin($fechaInicio->format('Y-m-d H:i:s'));

            return $this->_insert($conexion);

        } catch (Exception $e) {
            throw new Exception("Error al crear cita programada: " . $e->getMessage());
        }
    }

    /**
     * Método privado para insertar una nueva cita programada en la base de datos.
     *
     * @param PDO $conexion Conexión PDO.
     *
     * @return int|false El ID de la nueva cita programada creada o false en caso de error.
     * @throws Exception Si hay error en DB.
     */
    private function _insert(PDO $conexion): int|false
    {
        try {
            // Preparar la consulta INSERT usando Heredoc
            $query = <<<SQL
            INSERT INTO citas_programadas (
                 fecha_inicio
                ,fecha_fin
                ,razon_cancelacion
                ,fecha_cancelacion
                ,id_empleado
                ,id_cliente
                ,duracion
                ,estado
                ,id_centro_costo
            ) VALUES (
                 :fecha_inicio
                ,:fecha_fin
                ,:razon_cancelacion
                ,:fecha_cancelacion
                ,:id_empleado
                ,:id_cliente
                ,:duracion
                ,:estado
                ,:id_centro_costo
            )
            SQL;

            $statement = $conexion->prepare($query);

            // Bind de parámetros desde el objeto
            $statement->bindValue(':fecha_inicio', $this->getFecha_inicio(), PDO::PARAM_STR);
            $statement->bindValue(':fecha_fin', $this->getFecha_fin(), PDO::PARAM_STR);
            $statement->bindValue(':razon_cancelacion', $this->getRazon_cancelacion(), $this->getRazon_cancelacion() === null ? PDO::PARAM_NULL : PDO::PARAM_STR);
            $statement->bindValue(':fecha_cancelacion', $this->getFecha_cancelacion(), $this->getFecha_cancelacion() === null ? PDO::PARAM_NULL : PDO::PARAM_STR);

            // Handle empty values for id_empleado
            $id_empleado = $this->getId_empleado();
            $id_empleado = (empty($id_empleado)) ? null : $id_empleado;
            $statement->bindValue(':id_empleado', $id_empleado, $id_empleado === null ? PDO::PARAM_NULL : PDO::PARAM_INT);

            // Bind id_cliente (mandatory)
            $statement->bindValue(':id_cliente', $this->getId_cliente(), PDO::PARAM_INT);

            $statement->bindValue(':duracion', $this->getDuracion(), PDO::PARAM_INT);
            $statement->bindValue(':estado', $this->getEstado(), PDO::PARAM_INT);

            // Handle empty values for id_centro_costo
            $id_centro_costo = $this->getId_centro_costo();
            $id_centro_costo = (empty($id_centro_costo)) ? null : $id_centro_costo;
            $statement->bindValue(':id_centro_costo', $id_centro_costo, $id_centro_costo === null ? PDO::PARAM_NULL : PDO::PARAM_INT);

            // Ejecutar la consulta
            $success = $statement->execute();

            if ($success) {
                // Devolver el ID de la cita programada recién creada
                $this->setId((int)$conexion->lastInsertId());
                return $this->getId();
            } else {
                return false; // Error en la ejecución
            }

        } catch (PDOException $e) {
            throw new Exception("Error de base de datos al crear cita programada: " . $e->getMessage());
        }
    }

    /**
     * Modifica una cita programada existente.
     *
     * @param PDO $conexion Conexión PDO.
     *
     * @return bool True si la modificación fue exitosa, False en caso contrario.
     * @throws Exception Si los datos requeridos en el objeto están vacíos o hay error en DB.
     */
    function modificar(PDO $conexion): bool
    {
        // Validaciones básicas sobre el objeto
        if ($this->getId() <= 0) {
            throw new Exception("ID es requerido para modificar una cita programada.");
        }

        if (empty($this->getFecha_inicio())) {
            throw new Exception("Fecha de inicio es requerida para modificar una cita programada.");
        }

        if ($this->getDuracion() <= 0) {
            throw new Exception("La duración debe ser un valor positivo en minutos.");
        }

        // Nota: id_cliente no se puede modificar después de la creación (inmutable)

        try {
            // Establecer la zona horaria para Colombia
            date_default_timezone_set('America/Bogota');

            // Recalcular fecha_fin basado en la duración
            $fechaInicio = new \DateTime($this->getFecha_inicio());
            $fechaInicio->add(new \DateInterval('PT' . $this->getDuracion() . 'M')); // Añadir minutos
            $this->setFecha_fin($fechaInicio->format('Y-m-d H:i:s'));

            return $this->_update($conexion);

        } catch (Exception $e) {
            throw new Exception("Error al modificar cita programada: " . $e->getMessage());
        }
    }

    /**
     * Método privado para actualizar una cita programada existente en la base de datos.
     *
     * @param PDO $conexion Conexión PDO.
     *
     * @return bool True si la actualización fue exitosa, False en caso contrario.
     * @throws Exception Si hay error en DB.
     */
    private function _update(PDO $conexion): bool
    {
        try {
            // Consulta para actualizar la cita programada
            // Nota: id_cliente se excluye porque es inmutable después de la creación
            $query = <<<SQL
            UPDATE citas_programadas SET
                fecha_inicio = :fecha_inicio,
                fecha_fin = :fecha_fin,
                razon_cancelacion = :razon_cancelacion,
                fecha_cancelacion = :fecha_cancelacion,
                id_empleado = :id_empleado,
                duracion = :duracion,
                estado = :estado,
                id_centro_costo = :id_centro_costo
            WHERE
                id = :id
            SQL;

            $statement = $conexion->prepare($query);
            $statement->bindValue(':fecha_inicio', $this->getFecha_inicio(), PDO::PARAM_STR);
            $statement->bindValue(':fecha_fin', $this->getFecha_fin(), PDO::PARAM_STR);
            $statement->bindValue(':razon_cancelacion', $this->getRazon_cancelacion(), $this->getRazon_cancelacion() === null ? PDO::PARAM_NULL : PDO::PARAM_STR);
            $statement->bindValue(':fecha_cancelacion', $this->getFecha_cancelacion(), $this->getFecha_cancelacion() === null ? PDO::PARAM_NULL : PDO::PARAM_STR);

            // Handle empty values for id_empleado
            $id_empleado = $this->getId_empleado();
            $id_empleado = (empty($id_empleado)) ? null : $id_empleado;
            $statement->bindValue(':id_empleado', $id_empleado, $id_empleado === null ? PDO::PARAM_NULL : PDO::PARAM_INT);

            $statement->bindValue(':duracion', $this->getDuracion(), PDO::PARAM_INT);
            $statement->bindValue(':estado', $this->getEstado(), PDO::PARAM_INT);

            // Handle empty values for id_centro_costo
            $id_centro_costo = $this->getId_centro_costo();
            $id_centro_costo = (empty($id_centro_costo)) ? null : $id_centro_costo;
            $statement->bindValue(':id_centro_costo', $id_centro_costo, $id_centro_costo === null ? PDO::PARAM_NULL : PDO::PARAM_INT);

            $statement->bindValue(':id', $this->getId(), PDO::PARAM_INT);

            return $statement->execute();

        } catch (PDOException $e) {
            throw new Exception("Error de base de datos al modificar cita programada (ID: {$this->getId()}): " . $e->getMessage());
        }
    }

    /**
     * Cancela una cita programada estableciendo su estado a 0 y registrando la razón y fecha de cancelación.
     *
     * @param string $razon_cancelacion Razón por la que se cancela la cita.
     * @param PDO    $conexion          Conexión PDO.
     *
     * @return bool True si la cancelación fue exitosa, False en caso contrario.
     * @throws Exception Si la razón de cancelación está vacía o si ocurre un error de base de datos.
     */
    public function cancelar(string $razon_cancelacion, PDO $conexion): bool
    {
        if (empty(trim($razon_cancelacion))) {
            throw new Exception("La razón de cancelación es obligatoria.");
        }

        if ($this->getId() <= 0) {
            throw new Exception("ID de cita programada inválido para cancelar.");
        }

        try {
            // Establecer la zona horaria para Colombia
            date_default_timezone_set('America/Bogota');

            // Actualizar el estado y la información de cancelación
            $this->setEstado(0);
            $this->setRazon_cancelacion(trim($razon_cancelacion));
            $this->setFecha_cancelacion(date('Y-m-d H:i:s'));

            return $this->_update($conexion);

        } catch (PDOException $e) {
            throw new Exception("Error de base de datos al cancelar cita programada (ID: {$this->getId()}): " . $e->getMessage());
        }
    }

    /**
     * Calcula y actualiza la duración de la cita programada basada en los servicios asociados.
     * La duración se establece como la mayor duración entre todos los servicios.
     *
     * @param PDO $conexion Conexión PDO.
     *
     * @return int La duración calculada en minutos.
     * @throws Exception Si el ID de la cita programada no es válido o si ocurre un error de base de datos.
     */
    public function calcularDuracion(PDO $conexion): int
    {
        if ($this->getId() <= 0) {
            throw new Exception("La cita programada debe estar guardada en la base de datos para calcular la duración.");
        }

        try {
            // Consulta para obtener la duración máxima de los servicios asociados
            $query = <<<SQL
            SELECT
                MAX(s.duracion) as duracion_maxima
            FROM citas_programadas_servicios cps
            JOIN servicios s ON cps.id_servicio = s.id
            WHERE
                cps.id_cita_programada = :id_cita_programada
            SQL;

            $statement = $conexion->prepare($query);
            $statement->bindValue(':id_cita_programada', $this->getId(), PDO::PARAM_INT);
            $statement->execute();
            $resultado = $statement->fetch(PDO::FETCH_ASSOC);

            // Si no hay servicios asociados o la duración máxima es NULL, usar un valor por defecto
            $duracion = $resultado['duracion_maxima'] ? (int)$resultado['duracion_maxima'] : 30;

            // Actualizar la duración en el objeto
            $this->setDuracion($duracion);

            // Recalcular fecha_fin basado en la nueva duración
            $fechaInicio = new \DateTime($this->getFecha_inicio());
            $fechaInicio->add(new \DateInterval('PT' . $this->getDuracion() . 'M')); // Añadir minutos
            $this->setFecha_fin($fechaInicio->format('Y-m-d H:i:s'));

            // Actualizar en la base de datos
            $this->_update($conexion);

            return $duracion;

        } catch (PDOException $e) {
            throw new Exception("Error de base de datos al calcular duración: " . $e->getMessage());
        }
    }

    /**
     * Agrega un servicio a la cita programada.
     *
     * @param int $id_servicio ID del servicio a agregar.
     * @param PDO $conexion    Conexión PDO.
     *
     * @return bool True si la operación fue exitosa, False en caso contrario.
     * @throws Exception Si el ID de la cita programada no es válido, el ID del servicio no es válido o si ocurre un error de base de datos.
     */
    public function agregarServicio(int $id_servicio, PDO $conexion): bool
    {
        // Validaciones básicas
        if ($this->getId() <= 0) {
            throw new Exception("La cita programada debe estar guardada en la base de datos antes de agregar servicios.");
        }

        if ($id_servicio <= 0) {
            throw new Exception("El ID del servicio debe ser un valor válido mayor que cero.");
        }

        try {
            // Verificar si el servicio existe
            $servicio = Servicio::get($id_servicio, $conexion);
            if (!$servicio) {
                throw new Exception("El servicio con ID $id_servicio no existe.");
            }

            // Verificar si ya existe la relación
            $query = <<<SQL
            SELECT COUNT(*) as total
            FROM citas_programadas_servicios
            WHERE
                id_cita_programada = :id_cita_programada AND id_servicio = :id_servicio
            SQL;

            $statement = $conexion->prepare($query);
            $statement->bindValue(':id_cita_programada', $this->getId(), PDO::PARAM_INT);
            $statement->bindValue(':id_servicio', $id_servicio, PDO::PARAM_INT);
            $statement->execute();
            $resultado = $statement->fetch(PDO::FETCH_ASSOC);

            if ($resultado['total'] > 0) {
                // Ya existe la relación, no hacer nada
                return true;
            } else {
                // No existe la relación, crear una nueva
                $query = <<<SQL
                INSERT INTO citas_programadas_servicios (
                     id_cita_programada
                    ,id_servicio
                ) VALUES (
                     :id_cita_programada
                    ,:id_servicio
                )
                SQL;

                $statement = $conexion->prepare($query);
                $statement->bindValue(':id_cita_programada', $this->getId(), PDO::PARAM_INT);
                $statement->bindValue(':id_servicio', $id_servicio, PDO::PARAM_INT);

                $result = $statement->execute();

                // Si se agregó correctamente, recalcular la duración
                if ($result) {
                    $this->calcularDuracion($conexion);
                }

                return $result;
            }

        } catch (PDOException $e) {
            throw new Exception("Error de base de datos al agregar servicio a la cita programada: " . $e->getMessage());
        }
    }

    /**
     * Elimina la asociación entre un servicio y la cita programada actual.
     *
     * @param int $id_servicio ID del servicio a eliminar de la cita programada.
     * @param PDO $conexion    Conexión PDO.
     *
     * @return bool True si la operación fue exitosa, False en caso contrario.
     * @throws Exception Si el ID de la cita programada no es válido, el ID del servicio no es válido o si ocurre un error de base de datos.
     */
    public function eliminarServicio(int $id_servicio, PDO $conexion): bool
    {
        // Validaciones básicas
        if ($this->getId() <= 0) {
            throw new Exception("La cita programada debe estar guardada en la base de datos antes de eliminar servicios.");
        }

        if ($id_servicio <= 0) {
            throw new Exception("El ID del servicio debe ser un valor válido mayor que cero.");
        }

        try {
            // Eliminar la relación
            $query = <<<SQL
            DELETE FROM citas_programadas_servicios
            WHERE
                id_cita_programada = :id_cita_programada AND id_servicio = :id_servicio
            SQL;

            $statement = $conexion->prepare($query);
            $statement->bindValue(':id_cita_programada', $this->getId(), PDO::PARAM_INT);
            $statement->bindValue(':id_servicio', $id_servicio, PDO::PARAM_INT);

            $result = $statement->execute();

            // Si se eliminó correctamente, recalcular la duración
            if ($result) {
                $this->calcularDuracion($conexion);
            }

            return $result;

        } catch (PDOException $e) {
            throw new Exception("Error de base de datos al eliminar servicio de la cita programada: " . $e->getMessage());
        }
    }

    /**
     * Obtiene todos los servicios asociados a la cita programada actual.
     *
     * @param PDO $conexion Conexión PDO.
     *
     * @return array Array de objetos Servicio.
     * @throws Exception Si el ID de la cita programada no es válido o si ocurre un error de base de datos.
     */
    public function getServicios(PDO $conexion): array
    {
        // Validaciones básicas
        if ($this->getId() <= 0) {
            throw new Exception("La cita programada debe estar guardada en la base de datos para obtener sus servicios.");
        }

        try {
            // Consulta para obtener los servicios asociados a la cita programada
            $query = <<<SQL
            SELECT
                s.*,
                cps.id as id_cita_programada_servicio
            FROM citas_programadas_servicios cps
            JOIN servicios s ON cps.id_servicio = s.id
            WHERE
                cps.id_cita_programada = :id_cita_programada
            ORDER BY
                s.descripcion
            SQL;

            $statement = $conexion->prepare($query);
            $statement->bindValue(':id_cita_programada', $this->getId(), PDO::PARAM_INT);
            $statement->execute();
            $resultados = $statement->fetchAll(PDO::FETCH_ASSOC);

            $servicios = [];
            foreach ($resultados as $resultado) {
                $servicio = Servicio::construct($resultado);
                $servicios[] = $servicio;
            }

            return $servicios;

        } catch (PDOException $e) {
            throw new Exception("Error de base de datos al obtener servicios de la cita programada: " . $e->getMessage());
        }
    }

    /**
     * Marca la cita programada como realizada (es_realizado = 1).
     *
     * @param PDO $conexion Conexión PDO.
     *
     * @return bool True si la operación fue exitosa, False en caso contrario.
     * @throws Exception Si el ID de la cita programada no es válido o si ocurre un error de base de datos.
     */
    public function marcarComoRealizada(PDO $conexion): bool
    {
        // Validaciones básicas
        if ($this->getId() <= 0) {
            throw new Exception("La cita programada debe estar guardada en la base de datos para marcarla como realizada.");
        }

        try {
            // Actualizar el campo es_realizado a 1
            $query = <<<SQL
            UPDATE citas_programadas
            SET es_realizado = 1
            WHERE id = :id
            SQL;

            $statement = $conexion->prepare($query);
            $statement->bindValue(':id', $this->getId(), PDO::PARAM_INT);
            $result = $statement->execute();

            // Si se actualizó correctamente, actualizar el objeto
            if ($result) {
                $this->setEs_realizado(1);
            }

            return $result;

        } catch (PDOException $e) {
            throw new Exception("Error de base de datos al marcar la cita programada como realizada: " . $e->getMessage());
        }
    }

    // --- Getters y Setters ---

    public function getId(): ?int
    {
        return $this->id;
    }

    public function setId(?int $id): self
    {
        $this->id = $id;
        return $this;
    }

    public function getFecha_inicio(): ?string
    {
        return $this->fecha_inicio;
    }

    public function setFecha_inicio(?string $fecha_inicio): self
    {
        $this->fecha_inicio = $fecha_inicio;
        return $this;
    }

    public function getFecha_fin(): ?string
    {
        return $this->fecha_fin;
    }

    public function setFecha_fin(?string $fecha_fin): self
    {
        $this->fecha_fin = $fecha_fin;
        return $this;
    }

    public function getRazon_cancelacion(): ?string
    {
        return $this->razon_cancelacion;
    }

    public function setRazon_cancelacion(?string $razon_cancelacion): self
    {
        $this->razon_cancelacion = $razon_cancelacion;
        return $this;
    }

    public function getFecha_cancelacion(): ?string
    {
        return $this->fecha_cancelacion;
    }

    public function setFecha_cancelacion(?string $fecha_cancelacion): self
    {
        $this->fecha_cancelacion = $fecha_cancelacion;
        return $this;
    }

    public function getId_empleado(): ?int
    {
        return $this->id_empleado;
    }

    public function setId_empleado(?int $id_empleado): self
    {
        $this->id_empleado = $id_empleado;
        return $this;
    }

    public function getDuracion(): ?int
    {
        return $this->duracion;
    }

    public function setDuracion(?int $duracion): self
    {
        $this->duracion = $duracion;
        return $this;
    }

    public function getEstado(): ?int
    {
        return $this->estado;
    }

    public function setEstado(?int $estado): self
    {
        $this->estado = $estado;
        return $this;
    }

    public function getEs_realizado(): ?int
    {
        return $this->es_realizado;
    }

    public function setEs_realizado(?int $es_realizado): self
    {
        $this->es_realizado = $es_realizado;
        return $this;
    }

    public function getNombre_empleado(): ?string
    {
        return $this->nombre_empleado;
    }

    public function setNombre_empleado(?string $nombre_empleado): self
    {
        $this->nombre_empleado = $nombre_empleado;
        return $this;
    }

    public function getId_cliente(): ?int
    {
        return $this->id_cliente;
    }

    public function setId_cliente(?int $id_cliente): self
    {
        $this->id_cliente = $id_cliente;
        return $this;
    }

    public function getNombre_cliente(): ?string
    {
        return $this->nombre_cliente;
    }

    public function setNombre_cliente(?string $nombre_cliente): self
    {
        $this->nombre_cliente = $nombre_cliente;
        return $this;
    }

    public function getCelular_cliente(): ?string
    {
        return $this->celular_cliente;
    }

    public function setCelular_cliente(?string $celular_cliente): self
    {
        $this->celular_cliente = $celular_cliente;
        return $this;
    }

    public function getId_centro_costo(): ?int
    {
        return $this->id_centro_costo;
    }

    public function setId_centro_costo(?int $id_centro_costo): self
    {
        $this->id_centro_costo = $id_centro_costo;
        return $this;
    }

    public function getNombre_centro_costo(): ?string
    {
        return $this->nombre_centro_costo;
    }

    public function setNombre_centro_costo(?string $nombre_centro_costo): self
    {
        $this->nombre_centro_costo = $nombre_centro_costo;
        return $this;
    }

    /**
     * Verifica si la cita programada está activa.
     * @return bool
     */
    public function isActiva(): bool
    {
        return $this->estado === 1;
    }
}