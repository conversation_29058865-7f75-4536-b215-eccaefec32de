<?php

declare(strict_types=1);

namespace App\classes;

use Exception;
use PDO;
use PDOException;

class Producto
{
	// --- Atributos ---
	private ?int    $id          = null;
	private ?string $descripcion = null;
	private ?float  $valor       = null;
	private ?int    $estado      = null;

	/**
	 * Constructor: Inicializa las propiedades del objeto Producto.
	 */
	public function __construct()
	{
		$this->id          = 0;
		$this->descripcion = null;
		$this->valor       = null;
		$this->estado      = 1; // Estado activo por defecto
	}

	/**
	 * Método estático para construir un objeto Producto desde un array (ej. fila de DB).
	 *
	 * @param array $resultado Array asociativo con los datos del producto.
	 *
	 * @return self Instancia de Producto.
	 * @throws Exception Si ocurre un error durante la construcción.
	 */
	public static function construct(array $resultado = []): self
	{
		try {
			$objeto              = new self();
			$objeto->id          = isset($resultado['id']) ? (int)$resultado['id'] : 0;
			$objeto->descripcion = $resultado['descripcion'] ?? null;
			$objeto->valor       = isset($resultado['valor']) ? (float)$resultado['valor'] : null;
			$objeto->estado      = isset($resultado['estado']) ? (int)$resultado['estado'] : 1;
			return $objeto;
		} catch (Exception $e) {
			// Considera loggear el error aquí
			throw new Exception("Error al construir Producto: " . $e->getMessage());
		}
	}

	// --- Métodos de Acceso a Datos (Estáticos) ---

	/**
	 * Obtiene un producto por su ID.
	 *
	 * @param int $id       ID del producto.
	 * @param PDO $conexion Conexión PDO.
	 *
	 * @return self|null Objeto Producto o null si no se encuentra.
	 * @throws Exception Si hay error en DB.
	 */
	public static function get(int $id, PDO $conexion): ?self
	{
		try {
			// Consulta para obtener producto por ID (Usando Heredoc)
			$query = <<<SQL
            SELECT
            	p.*
            FROM productos p
            WHERE
            	p.id = :id
            LIMIT 1
            SQL;

			$statement = $conexion->prepare($query);
			$statement->bindValue(':id', $id, PDO::PARAM_INT);
			$statement->execute();
			$resultado = $statement->fetch(PDO::FETCH_ASSOC);

			return $resultado ? self::construct($resultado) : null;

		} catch (PDOException $e) {
			throw new Exception("Error de base de datos al obtener producto: " . $e->getMessage());
		}
	}

	/**
	 * Obtiene una lista de productos activos.
	 *
	 * @param PDO $conexion Conexión PDO.
	 *
	 * @return array Array de objetos Producto.
	 * @throws Exception Si hay error en DB.
	 */
	public static function get_list(PDO $conexion): array
	{
		try {
			// Consulta para obtener lista de productos activos (Usando Heredoc)
			$query = <<<SQL
            SELECT
            	p.*
            FROM productos p
            WHERE
            	p.estado = 1
            ORDER BY
            	p.descripcion
            SQL;

			$statement = $conexion->prepare($query);
			$statement->execute();
			$resultados = $statement->fetchAll(PDO::FETCH_ASSOC);

			$listado = [];
			foreach ($resultados as $resultado) {
				$listado[] = self::construct($resultado);
			}
			return $listado;

		} catch (PDOException $e) {
			throw new Exception("Error de base de datos al obtener lista de productos: " . $e->getMessage());
		}
	}

	/**
	 * Busca productos por descripción.
	 *
	 * @param PDO $conexion Conexión PDO.
	 * @param string $termino Término de búsqueda.
	 *
	 * @return array Array de objetos Producto que coinciden con la búsqueda.
	 * @throws Exception Si hay error en DB.
	 */
	public static function search_by_descripcion(PDO $conexion, string $termino = ''): array
	{
		try {
			// Si no hay término de búsqueda, devolver todos los productos activos
			if (empty(trim($termino))) {
				return self::get_list($conexion);
			}

			// Consulta para buscar productos por descripción (Usando Heredoc)
			$query = <<<SQL
            SELECT
            	p.*
            FROM productos p
            WHERE
            	p.estado = 1
            	AND p.descripcion LIKE :termino
            ORDER BY
            	p.descripcion
            SQL;

			$statement = $conexion->prepare($query);
			$statement->bindValue(':termino', '%' . trim($termino) . '%', PDO::PARAM_STR);
			$statement->execute();
			$resultados = $statement->fetchAll(PDO::FETCH_ASSOC);

			$listado = [];
			foreach ($resultados as $resultado) {
				$listado[] = self::construct($resultado);
			}
			return $listado;

		} catch (PDOException $e) {
			throw new Exception("Error de base de datos al buscar productos: " . $e->getMessage());
		}
	}

	/**
	 * Crea un nuevo producto en la base de datos a partir de un objeto Producto.
	 * El objeto Producto debe estar completamente poblado con los datos requeridos.
	 *
	 * @param PDO $conexion Conexión PDO.
	 *
	 * @return int|false El ID del nuevo producto creado o false en caso de error.
	 * @throws Exception Si los datos requeridos en el objeto están vacíos o hay error en DB.
	 */
	function crear(PDO $conexion): int|false
	{
		// Validaciones básicas sobre el objeto
		if (empty($this->getDescripcion()) || $this->getValor() === null) {
			throw new Exception("Descripción y valor son requeridos para crear un producto.");
		}

		if ($this->getValor() < 0) {
			throw new Exception("El valor debe ser un número mayor o igual a cero.");
		}

		try {
			// Preparar la consulta INSERT usando Heredoc
			$query = <<<SQL
            INSERT INTO productos (
            	 descripcion
            	,valor
            ) VALUES (
            	 :descripcion
            	,:valor
            )
            SQL;

			$statement = $conexion->prepare($query);

			// Bind de parámetros desde el objeto
			$statement->bindValue(':descripcion', $this->getDescripcion(), PDO::PARAM_STR);
			$statement->bindValue(':valor', $this->getValor(), PDO::PARAM_STR);

			// Ejecutar la consulta
			$success = $statement->execute();

			if ($success) {
				// Devolver el ID del producto recién creado
				return (int)$conexion->lastInsertId();
			} else {
				return false; // Error en la ejecución
			}

		} catch (PDOException $e) {
			throw new Exception("Error de base de datos al crear producto: " . $e->getMessage());
		} catch (Exception $e) {
			// Capturar otros errores
			throw new Exception("Error al crear producto: " . $e->getMessage());
		}
	}

	/**
	 * Modifica un producto existente.
	 *
	 * @param PDO $conexion Conexión PDO.
	 *
	 * @return bool True si la modificación fue exitosa, False en caso contrario.
	 * @throws Exception Si la descripción está vacía, el valor es nulo o si ocurre un error de base de datos.
	 */
	function modificar(PDO $conexion): bool
	{
		if (empty(trim($this->getDescripcion()))) {
			throw new Exception("La descripción no puede estar vacía.");
		}

		if ($this->getValor() === null || $this->getValor() < 0) {
			throw new Exception("El valor debe ser un número válido mayor o igual a cero.");
		}

		if ($this->getId() <= 0) {
			throw new Exception("ID de producto inválido para modificar.");
		}

		try {
			// Consulta para actualizar el producto
			$query = <<<SQL
            UPDATE productos SET
                descripcion = :descripcion,
                valor = :valor,
                estado = :estado
            WHERE
                id = :id
            SQL;

			$statement = $conexion->prepare($query);
			$statement->bindValue(':descripcion', trim($this->getDescripcion()), PDO::PARAM_STR);
			$statement->bindValue(':valor', $this->getValor(), PDO::PARAM_STR);
			$statement->bindValue(':estado', $this->getEstado(), PDO::PARAM_INT);
			$statement->bindValue(':id', $this->getId(), PDO::PARAM_INT);

			return $statement->execute();

		} catch (PDOException $e) {
			// Error de base de datos
			throw new Exception("Error de base de datos al modificar producto (ID: " . $this->getId() . "): " . $e->getMessage());
		}
	}

	/**
	 * Desactiva un producto (soft delete) estableciendo estado = 0.
	 * Valida que no existan registros de inventario con cantidad > 0 antes de desactivar.
	 *
	 * @param int $id       ID del producto a desactivar.
	 * @param PDO $conexion Conexión PDO.
	 *
	 * @return bool True si la desactivación fue exitosa, False en caso contrario.
	 * @throws Exception Si el producto tiene inventario o si ocurre un error de base de datos.
	 */
	public static function desactivar(int $id, PDO $conexion): bool
	{
		try {
			// First check if product has inventory records with quantity > 0
			$inventoryQuery = <<<SQL
            SELECT COUNT(*) as count
            FROM inventario
            WHERE id_producto = :id AND cantidad > 0
            SQL;

			$inventoryStatement = $conexion->prepare($inventoryQuery);
			$inventoryStatement->bindValue(':id', $id, PDO::PARAM_INT);
			$inventoryStatement->execute();
			$inventoryResult = $inventoryStatement->fetch(PDO::FETCH_ASSOC);

			if ($inventoryResult && $inventoryResult['count'] > 0) {
				throw new Exception("No se puede eliminar este producto porque tiene registros de inventario en uno o más centros de costo. Para eliminar este producto, utilice la opción 'Descartar Producto' en la página de Gestión de Inventario para limpiar primero todo el inventario.");
			}

			// If no inventory, proceed with deactivation
			$query = <<<SQL
            UPDATE productos SET
                estado = 0
            WHERE
                id = :id
            SQL;

			$statement = $conexion->prepare($query);
			$statement->bindValue(':id', $id, PDO::PARAM_INT);

			return $statement->execute();

		} catch (PDOException $e) {
			// Error de base de datos
			throw new Exception("Error de base de datos al desactivar producto (ID: $id): " . $e->getMessage());
		}
	}

	// --- Getters y Setters ---

	public function getId(): ?int
	{
		return $this->id;
	}

	public function setId(?int $id): self
	{
		$this->id = $id;
		return $this;
	}

	public function getDescripcion(): ?string
	{
		return $this->descripcion;
	}

	public function setDescripcion(?string $descripcion): self
	{
		$this->descripcion = $descripcion;
		return $this;
	}

	public function getValor(): ?float
	{
		return $this->valor;
	}

	public function setValor(?float $valor): self
	{
		$this->valor = $valor;
		return $this;
	}

	public function getEstado(): ?int
	{
		return $this->estado;
	}

	public function setEstado(?int $estado): self
	{
		$this->estado = $estado;
		return $this;
	}

	// --- Métodos adicionales ---

	/**
	 * Verifica si el producto está activo.
	 * @return bool
	 */
	public function isActivo(): bool
	{
		return $this->estado === 1;
	}
}
