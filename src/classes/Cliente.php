<?php

declare(strict_types=1);

namespace App\classes;

use Exception;
use PDO;
use PDOException;

class Cliente
{
    // --- Atributos ---
    private ?int    $id      = null;
    private ?string $nombre  = null;
    private ?string $celular = null;
    private ?int    $estado  = null;

    /**
     * Constructor: Inicializa las propiedades del objeto Cliente.
     */
    public function __construct()
    {
        $this->id      = 0;
        $this->nombre  = null;
        $this->celular = null;
        $this->estado  = 1; // Estado activo por defecto
    }

    /**
     * Método estático para construir un objeto Cliente desde un array (ej. fila de DB).
     *
     * @param array $resultado Array asociativo con los datos del cliente.
     *
     * @return self Instancia de Cliente.
     * @throws Exception Si ocurre un error durante la construcción.
     */
    public static function construct(array $resultado = []): self
    {
        try {
            $objeto          = new self();
            $objeto->id      = isset($resultado['id']) ? (int)$resultado['id'] : 0;
            $objeto->nombre  = $resultado['nombre'] ?? null;
            $objeto->celular = $resultado['celular'] ?? null;
            $objeto->estado  = isset($resultado['estado']) ? (int)$resultado['estado'] : 1;
            return $objeto;
        } catch (Exception $e) {
            throw new Exception("Error al construir Cliente: " . $e->getMessage());
        }
    }

    // --- Métodos de Acceso a Datos (Estáticos) ---

    /**
     * Obtiene un cliente por su ID.
     *
     * @param int $id       ID del cliente.
     * @param PDO $conexion Conexión PDO.
     *
     * @return self|null Objeto Cliente o null si no se encuentra.
     * @throws Exception Si hay error en DB.
     */
    public static function get(int $id, PDO $conexion): ?self
    {
        try {
            $query = <<<SQL
            SELECT *
            FROM clientes
            WHERE id = :id
            LIMIT 1
            SQL;

            $statement = $conexion->prepare($query);
            $statement->bindValue(":id", $id, PDO::PARAM_INT);
            $statement->execute();
            $resultado = $statement->fetch(PDO::FETCH_ASSOC);

            return $resultado ? self::construct($resultado) : null;

        } catch (PDOException $e) {
            throw new Exception("Error al obtener Cliente (ID: $id): " . $e->getMessage());
        }
    }

    /**
     * Obtiene una lista de clientes activos.
     *
     * @param PDO $conexion Conexión PDO.
     *
     * @return array Array de objetos Cliente.
     * @throws Exception Si hay error en DB.
     */
    public static function get_list(PDO $conexion): array
    {
        try {
            $query = <<<SQL
            SELECT *
            FROM clientes
            WHERE estado = 1
            ORDER BY nombre
            SQL;

            $statement = $conexion->prepare($query);
            $statement->execute();
            $resultados = $statement->fetchAll(PDO::FETCH_ASSOC);

            $listado = [];
            foreach ($resultados as $resultado) {
                $listado[] = self::construct($resultado);
            }
            return $listado;

        } catch (PDOException $e) {
            throw new Exception("Error al obtener lista de Clientes: " . $e->getMessage());
        }
    }

    /**
     * Busca clientes por nombre o celular (alias para compatibilidad).
     *
     * @param PDO    $conexion Conexión PDO.
     * @param string $termino Término de búsqueda.
     *
     * @return array Array de objetos Cliente.
     * @throws Exception Si hay error en DB.
     */
    public static function search_by_nombre_celular(PDO $conexion, string $termino): array
    {
        return self::buscar($termino, $conexion);
    }

    /**
     * Busca clientes por nombre o celular.
     *
     * @param string $termino Término de búsqueda.
     * @param PDO    $conexion Conexión PDO.
     *
     * @return array Array de objetos Cliente.
     * @throws Exception Si hay error en DB.
     */
    public static function buscar(string $termino, PDO $conexion): array
    {
        try {
            // Validate input
            if (empty(trim($termino))) {
                return [];
            }

            $query = <<<SQL
            SELECT *
            FROM clientes
            WHERE estado = 1
            AND (nombre LIKE :termino_nombre OR celular LIKE :termino_celular)
            ORDER BY nombre
            SQL;

            $statement = $conexion->prepare($query);
            $searchTerm = '%' . trim($termino) . '%';
            $statement->bindValue(':termino_nombre', $searchTerm, PDO::PARAM_STR);
            $statement->bindValue(':termino_celular', $searchTerm, PDO::PARAM_STR);
            $statement->execute();
            $resultados = $statement->fetchAll(PDO::FETCH_ASSOC);

            $listado = [];
            foreach ($resultados as $resultado) {
                $listado[] = self::construct($resultado);
            }
            return $listado;

        } catch (PDOException $e) {
            throw new Exception("Error al buscar Clientes: " . $e->getMessage());
        }
    }

    /**
     * Crea un nuevo cliente en la base de datos a partir de un objeto Cliente.
     *
     * @param PDO $conexion Conexión PDO.
     *
     * @return int|false El ID del nuevo cliente creado o false en caso de error.
     * @throws Exception Si los datos requeridos en el objeto están vacíos o hay error en DB.
     */
    function crear(PDO $conexion): int|false
    {
        // Validaciones básicas sobre el objeto
        if (empty($this->getNombre())) {
            throw new Exception("Nombre es requerido en el objeto Cliente para crearlo.");
        }

        try {
            $query = <<<SQL
            INSERT INTO clientes (
                nombre,
                celular,
                estado
            ) VALUES (
                :nombre,
                :celular,
                :estado
            )
            SQL;

            $statement = $conexion->prepare($query);

            // Bind de parámetros desde el objeto
            $statement->bindValue(':nombre', $this->getNombre(), PDO::PARAM_STR);
            $statement->bindValue(':celular', $this->getCelular(), PDO::PARAM_STR);
            $statement->bindValue(':estado', $this->getEstado(), PDO::PARAM_INT);

            // Ejecutar la consulta
            $success = $statement->execute();

            if ($success) {
                // Devolver el ID del cliente recién creado
                return (int)$conexion->lastInsertId();
            } else {
                return false;
            }

        } catch (PDOException $e) {
            throw new Exception("Error de base de datos al crear cliente: " . $e->getMessage());
        } catch (Exception $e) {
            throw new Exception("Error al crear cliente: " . $e->getMessage());
        }
    }

    /**
     * Modifica un cliente existente.
     *
     * @param PDO $conexion Conexión PDO.
     *
     * @return bool True si la modificación fue exitosa, False en caso contrario.
     * @throws Exception Si el nombre o celular están vacíos o si ocurre un error de base de datos.
     */
    function modificar(PDO $conexion): bool
    {
        if (empty(trim($this->getNombre()))) {
            throw new Exception("El nombre no puede estar vacío.");
        }

        if (empty(trim($this->getCelular()))) {
            throw new Exception("El celular no puede estar vacío.");
        }

        if ($this->getId() <= 0) {
            throw new Exception("ID de cliente inválido para modificar.");
        }

        try {
            $query = <<<SQL
            UPDATE clientes SET
                nombre = :nombre,
                celular = :celular
            WHERE
                id = :id
            SQL;

            $statement = $conexion->prepare($query);
            $statement->bindValue(':nombre', trim($this->getNombre()), PDO::PARAM_STR);
            $statement->bindValue(':celular', trim($this->getCelular()), PDO::PARAM_STR);
            $statement->bindValue(':id', $this->getId(), PDO::PARAM_INT);

            return $statement->execute();

        } catch (PDOException $e) {
            throw new Exception("Error de base de datos al modificar cliente (ID: {$this->getId()}): " . $e->getMessage());
        }
    }

    /**
     * Desactiva un cliente estableciendo su estado a 0.
     *
     * @param int $id       ID del cliente a desactivar.
     * @param PDO $conexion Conexión PDO.
     *
     * @return bool True si la desactivación fue exitosa, False en caso contrario.
     * @throws Exception Si ocurre un error de base de datos.
     */
    public static function desactivar(int $id, PDO $conexion): bool
    {
        try {
            $query = <<<SQL
            UPDATE clientes SET
                estado = 0
            WHERE
                id = :id
            SQL;

            $statement = $conexion->prepare($query);
            $statement->bindValue(':id', $id, PDO::PARAM_INT);

            return $statement->execute();

        } catch (PDOException $e) {
            throw new Exception("Error de base de datos al desactivar cliente (ID: $id): " . $e->getMessage());
        }
    }

    // --- Getters y Setters ---

    public function getId(): ?int
    {
        return $this->id;
    }

    public function setId(?int $id): self
    {
        $this->id = $id;
        return $this;
    }

    public function getNombre(): ?string
    {
        return $this->nombre;
    }

    public function setNombre(?string $nombre): self
    {
        $this->nombre = $nombre;
        return $this;
    }

    public function getCelular(): ?string
    {
        return $this->celular;
    }

    public function setCelular(?string $celular): self
    {
        $this->celular = $celular;
        return $this;
    }

    public function getEstado(): ?int
    {
        return $this->estado;
    }

    public function setEstado(?int $estado): self
    {
        $this->estado = $estado;
        return $this;
    }

    // --- Métodos adicionales ---

    /**
     * Verifica si el cliente está activo.
     * @return bool
     */
    public function isActivo(): bool
    {
        return $this->estado === 1;
    }
}
