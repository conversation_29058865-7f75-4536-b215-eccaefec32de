<?php
/**
 * Controlador para la gestión de balances de gastos fijos
 * 
 * Este controlador maneja la visualización y gestión de balances de gastos fijos,
 * incluyendo funcionalidades de filtrado, creación y eliminación con validaciones.
 */

use App\classes\GastoFijoBalance;
use App\classes\GastoFijo;

if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

/** @var PDO $conexion */
global $conexion;

require_once dirname(__FILE__, 2) . '/config/config.php';
require_once __ROOT__ . '/src/sessions/sessions.php';
require_once __ROOT__ . '/src/general/general.php';
require_once __ROOT__ . '/src/general/validaciones.php';
require_once __ROOT__ . '/src/general/preparar.php';

// Check for valid database connection before proceeding
if (!$conexion instanceof PDO) {
	error_log("Error crítico: No hay conexión a la base de datos en lgasto_fijo.php.");
	$response['message'] = 'Error crítico: No se pudo conectar a la base de datos.';
	http_response_code(503); // Service Unavailable
	echo json_encode($response);
	exit;
}

// Set timezone
date_default_timezone_set('America/Bogota');

#region Variables
$gastos_fijos    = [];
$balances        = [];
$error_display   = 'hide';
$error_text      = '';
$success_display = 'hide';
$success_text    = '';
#endregion Variables

#region Handle POST Actions
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $action   = $_POST['action'] ?? '';
    $response = ['success' => false, 'message' => ''];

    #region AJAX Search Balances
    if ($action === 'search_balances') {
        $id_gasto_fijo = !empty($_POST['id_gasto_fijo']) ? (int)$_POST['id_gasto_fijo'] : null;
        $mes           = !empty($_POST['mes']) ? (int)$_POST['mes'] : null;
        $anio          = !empty($_POST['anio']) ? (int)$_POST['anio'] : null;

        try {
            // Validate required filters (only mes and anio are mandatory)
            if ($mes === null || $anio === null) {
                throw new Exception("Los filtros de mes y año son obligatorios para realizar la búsqueda.");
            }

            // Get selected centro de costo from session
            $id_centro_costo = null;
            if (isset($_SESSION[CENTRO_COSTO_SESSION]) && !empty($_SESSION[CENTRO_COSTO_SESSION])) {
                $id_centro_costo = (int)$_SESSION[CENTRO_COSTO_SESSION];
            }

            // Get filtered balances with centro de costo filtering
            $balances_filtrados = GastoFijoBalance::obtenerTodos($conexion, $mes, $id_gasto_fijo, $anio, $id_centro_costo);

            $response['success']  = true;
            $response['balances'] = [];
            $total_valor          = 0;

            foreach ($balances_filtrados as $balance) {
                $balance_data = [
                    'id'                     => $balance->getId(),
                    'id_gasto_fijo'          => $balance->getId_gasto_fijo(),
                    'id_centro_costo'        => $balance->getId_centro_costo(),
                    'centro_costo_nombre'    => $balance->getCentro_costo_nombre(),
                    'gasto_fijo_descripcion' => $balance->getGasto_fijo_descripcion(),
                    'valor'                  => $balance->getValor(),
                    'valor_formateado'       => $balance->getValorFormateado(),
                    'mes'                    => $balance->getMes(),
                    'anio'                   => $balance->getAnio(),
                    'nombre_mes'             => $balance->getNombreMes()
                ];
                $response['balances'][] = $balance_data;
                $total_valor += $balance->getValor();
            }

            $response['total_valor'] = $total_valor;
            $response['total_valor_formateado'] = '$' . number_format($total_valor, 0, ',', '.');

        } catch (Exception $e) {
            $response['message'] = "Error al buscar balances: " . $e->getMessage();
            http_response_code(500); // Internal Server Error
        }

        header('Content-Type: application/json');
        echo json_encode($response);
        exit;
    }
    #endregion AJAX Search Balances

    #region Create Balance
    if ($action === 'create_balance') {
        $id_gasto_fijo = !empty($_POST['id_gasto_fijo']) ? (int)$_POST['id_gasto_fijo'] : 0;
        $mes           = !empty($_POST['mes']) ? (int)$_POST['mes'] : 0;
        $anio          = !empty($_POST['anio']) ? (int)$_POST['anio'] : 0;
        $valor_raw     = trim($_POST['valor'] ?? '');

        try {
            // Validate required fields
            if ($id_gasto_fijo <= 0) {
                throw new Exception("Debe seleccionar un gasto fijo.");
            }

            if ($mes < 1 || $mes > 12) {
                throw new Exception("El mes debe estar entre 1 y 12.");
            }

            if ($anio < 2020 || $anio > 2050) {
                throw new Exception("El año debe ser válido.");
            }

            // Parse currency value (remove $ and dots, convert to float)
            $valor_clean = str_replace(['$', '.', ','], ['', '', '.'], $valor_raw);
            $valor = (float)$valor_clean;
            if ($valor <= 0) {
                throw new Exception("El valor debe ser un número positivo mayor que cero.");
            }

            // Get centro de costo from session
            if (!isset($_SESSION[CENTRO_COSTO_SESSION]) || empty($_SESSION[CENTRO_COSTO_SESSION])) {
                throw new Exception('Error: No hay un centro de costo seleccionado. Por favor, seleccione un centro de costo antes de proceder.');
            }

            $id_centro_costo = (int)$_SESSION[CENTRO_COSTO_SESSION];

            // Create new GastoFijoBalance with centro de costo
            $balance = new GastoFijoBalance($id_gasto_fijo, $id_centro_costo, $valor, $mes, $anio);
            $nuevo_id = $balance->crear($conexion);

            if ($nuevo_id) {
                $response['success']    = true;
                $response['message']    = "Balance de gasto fijo creado exitosamente.";
                $response['balance_id'] = $nuevo_id;
            } else {
                $response['message'] = "Error al crear el balance de gasto fijo.";
            }

        } catch (Exception $e) {
            $response['message'] = "Error: " . $e->getMessage();
            http_response_code(400); // Bad Request
        }

        header('Content-Type: application/json');
        echo json_encode($response);
        exit;
    }
    #endregion Create Balance

    #region Delete Balance
    if ($action === 'delete_balance') {
        $id = !empty($_POST['id']) ? (int)$_POST['id'] : 0;

        try {
            if ($id <= 0) {
                throw new Exception("ID de balance inválido.");
            }

            // Get existing balance
            $balance = GastoFijoBalance::obtenerPorId($id, $conexion);
            if (!$balance) {
                throw new Exception("Balance no encontrado.");
            }

            // Validate that it's from current or future month/year (prevent deletion of historical data)
            $current_date  = new DateTime('now', new DateTimeZone('America/Bogota'));
            $current_month = (int)$current_date->format('n');
            $current_year  = (int)$current_date->format('Y');

            // Create date objects for comparison
            $record_date = new DateTime();
            $record_date->setDate($balance->getAnio(), $balance->getMes(), 1);

            $current_period_date = new DateTime();
            $current_period_date->setDate($current_year, $current_month, 1);

            // Check if record is from a past period (before current month/year)
            if ($record_date < $current_period_date) {
                throw new Exception("Solo se pueden eliminar balances del período actual y futuros. No se pueden modificar datos históricos.");
            }

            // Delete the balance
            $success = GastoFijoBalance::eliminar($id, $conexion);

            if ($success) {
                $response['success'] = true;
                $response['message'] = "Balance eliminado exitosamente.";
            } else {
                $response['message'] = "Error al eliminar el balance.";
            }

        } catch (Exception $e) {
            $response['message'] = "Error: " . $e->getMessage();
            http_response_code(400); // Bad Request
        }

        header('Content-Type: application/json');
        echo json_encode($response);
        exit;
    }
    #endregion Delete Balance
}
#endregion Handle POST Actions

#region Get Initial Data
try {
    // Get all active gastos fijos for dropdown
    $gastos_fijos = GastoFijo::obtenerTodos($conexion);

} catch (PDOException $e) {
    // Specific handling for database errors
    $error_display = 'show';
    $error_text = "Error de base de datos al obtener los datos para gestión de balances de gastos fijos.";
} catch (Exception $e) {
    // General error handling
    $error_display = 'show';
    $error_text = "Ocurrió un error inesperado: " . $e->getMessage();
}
#endregion Get Initial Data

require_once __ROOT__ . '/views/lgasto_fijo_balance.view.php';

?>
