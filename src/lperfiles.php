<?php

// Iniciar sesión si es necesario
use App\classes\Perfil;

if (session_status() === PHP_SESSION_NONE) {
	session_start();
}

/** @var PDO $conexion */
global $conexion;

require_once dirname(__FILE__, 2) . '/config/config.php';
require_once __ROOT__ . '/src/sessions/sessions.php';
require_once __ROOT__ . '/src/general/general.php';
require_once __ROOT__ . '/src/general/validaciones.php';
require_once __ROOT__ . '/src/general/preparar.php';

// Check for valid database connection before proceeding
if (!$conexion instanceof PDO) {
	error_log("Error crítico: No hay conexión a la base de datos en lperfiles.php (POST).");
	$response['message'] = 'Error crítico: No se pudo conectar a la base de datos.';
	http_response_code(503); // Service Unavailable
	echo json_encode($response);
	exit;
}

#region region init variables
$perfiles = []; // Initialize as an empty array
#endregion init variables

#region region Handle Flash Message Success
// Check for a success flash message from session
if (!empty($_SESSION['flash_message_success'])) {
	$success_text    = $_SESSION['flash_message_success'];
	$success_display = 'show';
	// Clear the flash message so it doesn't show again
	unset($_SESSION['flash_message_success']);
}
#endregion Handle Flash Message Success

#region region Handle Flash Message Error
// Check for an error flash message from session
if (!empty($_SESSION['flash_message_error'])) {
	$error_text    = $_SESSION['flash_message_error'];
	$error_display = 'show';
	// Clear the flash message so it doesn't show again
	unset($_SESSION['flash_message_error']);
}
#endregion Handle Flash Message Error

#region region Handle POST Actions
// --- Handle AJAX Request (Create Perfil) ---
if ($_SERVER['REQUEST_METHOD'] == 'POST' && isset($_POST['action']) && $_POST['action'] == 'crear') {
	header('Content-Type: application/json');                            // Set response header for JSON
	$response = ['success' => false, 'message' => 'Error desconocido.']; // Default error response
	
	$nombre = filter_input(INPUT_POST, 'nombre', FILTER_SANITIZE_SPECIAL_CHARS); // Sanitize name
	$nombre = trim($nombre ?? '');                                               // Trim whitespace
	
	try {
		// Validate input
		if (empty($nombre)) {
			throw new Exception("El nombre del perfil es requerido.");
		}
		
		// Create new Perfil object
		$perfil = new Perfil();
		$perfil->setNombre($nombre);
		
		// Save to database
		$newId = $perfil->crear($conexion);
		
		if ($newId) {
			$response = [
				'success' => true,
				'message' => "Perfil '$nombre' creado exitosamente.",
				'id'      => $newId,
				'nombre'  => $nombre
			];
		} else {
			throw new Exception("No se pudo crear el perfil.");
		}
		
	} catch (Exception $e) {
		$response['message'] = "Error: " . $e->getMessage();
	}
	
	echo json_encode($response);
	exit;
}

// --- Handle AJAX Request (Modify Perfil) ---
if ($_SERVER['REQUEST_METHOD'] == 'POST' && isset($_POST['action']) && $_POST['action'] == 'modificar') {
	header('Content-Type: application/json');                            // Set response header for JSON
	$response = ['success' => false, 'message' => 'Error desconocido.']; // Default error response
	
	$perfilId     = filter_input(INPUT_POST, 'perfilId', FILTER_VALIDATE_INT);
	$nuevoNombre = filter_input(INPUT_POST, 'nombre', FILTER_SANITIZE_SPECIAL_CHARS); // Sanitize name
	$nuevoNombre = trim($nuevoNombre ?? '');                                          // Trim whitespace
	
	try {
		// Validate input
		if (empty($nuevoNombre)) {
			throw new Exception("El nombre del perfil es requerido.");
		}
		
		if (!$perfilId || $perfilId <= 0) {
			throw new Exception("ID de perfil inválido.");
		}
		
		// Get the perfil
		$perfil = Perfil::get($perfilId, $conexion);
		
		if (!$perfil) {
			throw new Exception("Perfil no encontrado.");
		}
		
		// Update the name
		$perfil->setNombre($nuevoNombre);
		
		// Save to database
		$success = $perfil->modificar($conexion);
		
		if ($success) {
			$response = [
				'success' => true,
				'message' => "Perfil actualizado exitosamente.",
				'id'      => $perfilId,
				'nombre'  => $nuevoNombre
			];
		} else {
			throw new Exception("No se pudo actualizar el perfil.");
		}
		
	} catch (Exception $e) {
		$response['message'] = "Error: " . $e->getMessage();
	}
	
	echo json_encode($response);
	exit;
}

// --- Handle POST Request (Deactivate Perfil) ---
if ($_SERVER['REQUEST_METHOD'] == 'POST' && isset($_POST['action']) && $_POST['action'] == 'desactivar') {
	$perfilId = filter_input(INPUT_POST, 'perfilId', FILTER_VALIDATE_INT);
	
	try {
		if ($perfilId && $perfilId > 0) {
			$success = Perfil::desactivar($perfilId, $conexion);
			
			if ($success) {
				$_SESSION['flash_message_success'] = "Perfil desactivado exitosamente.";
			} else {
				$_SESSION['flash_message_error'] = "No se pudo desactivar el perfil.";
			}
		} else {
			$_SESSION['flash_message_error'] = "Error: ID de perfil inválido para desactivar.";
		}
	} catch (Exception $e) {
		$_SESSION['flash_message_error'] = "Error: " . $e->getMessage();
	}
	
	// Redirect back to the perfil list page after processing
	header('Location: lperfiles');
	exit;
}
#endregion Handle POST Actions

#region try
try {
	$perfiles = Perfil::get_list($conexion);
	
} catch (PDOException $e) {
	// Specific handling for database errors
	$error_display = 'show';
	$error_text    = "Error de base de datos al obtener la lista de perfiles.";
} catch (Exception $e) {
	// General error handling
	$error_display = 'show';
	$error_text    = "Ocurrió un error inesperado al obtener la lista de perfiles: " . $e->getMessage();
}
#endregion try

require_once __ROOT__ . '/views/lperfiles.view.php';

?>
