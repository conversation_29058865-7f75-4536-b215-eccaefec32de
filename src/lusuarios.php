<?php

// Iniciar sesión si es necesario
use App\classes\Usuario;
use App\classes\Perfil;
use App\classes\Empleado;

if (session_status() === PHP_SESSION_NONE) {
	session_start();
}

/** @var PDO $conexion */
global $conexion;

require_once dirname(__FILE__, 2) . '/config/config.php';
require_once __ROOT__ . '/src/sessions/sessions.php';
require_once __ROOT__ . '/src/general/general.php';
require_once __ROOT__ . '/src/general/validaciones.php';
require_once __ROOT__ . '/src/general/preparar.php';
require_once __ROOT__ . '/src/classes/Empleado.php';

// Check for valid database connection before proceeding
if (!$conexion instanceof PDO) {
	error_log("Error crítico: No hay conexión a la base de datos en form_aliados_comerciales.php (POST).");
	$response['message'] = 'Error crítico: No se pudo conectar a la base de datos.';
	http_response_code(503); // Service Unavailable
	echo json_encode($response);
	exit;
}

#region region init variables
$usuarios = []; // Initialize as an empty array
$perfiles = []; // Initialize as an empty array for profiles
$empleados = []; // Initialize as an empty array for employees
#endregion init variables
#region region Handle Flash Message Success
// Check for a success flash message from session
if (!empty($_SESSION['flash_message_success'])) {
	$success_text    = $_SESSION['flash_message_success'];
	$success_display = 'show';
	// Clear the flash message so it doesn't show again
	unset($_SESSION['flash_message_success']);
}
#endregion Handle Flash Message Success
#region region Handle Flash Message Error
// Check for an error flash message from session
if (!empty($_SESSION['flash_message_error'])) {
	$error_text    = $_SESSION['flash_message_error'];
	$error_display = 'show';
	// Clear the flash message so it doesn't show again
	unset($_SESSION['flash_message_error']);
}
#region region Modify user
// --- Handle AJAX Request (Modify User) ---
if ($_SERVER['REQUEST_METHOD'] == 'POST' && isset($_POST['action']) && $_POST['action'] == 'modificar') {
	header('Content-Type: application/json');                            // Set response header for JSON
	$response = ['success' => false, 'message' => 'Error desconocido.']; // Default error response

	$userId      = filter_input(INPUT_POST, 'userId', FILTER_VALIDATE_INT);
	$nuevoNombre = filter_input(INPUT_POST, 'nombre', FILTER_SANITIZE_SPECIAL_CHARS); // Sanitize name
	$nuevoNombre = trim($nuevoNombre ?? '');                                          // Trim whitespace
	$idPerfil    = filter_input(INPUT_POST, 'id_perfil', FILTER_VALIDATE_INT);
	$idEmpleado  = filter_input(INPUT_POST, 'id_empleado', FILTER_VALIDATE_INT); // New field for empleado
	// Handle empty employee selection
	$idEmpleado  = (empty($idEmpleado) || $idEmpleado === 0) ? null : $idEmpleado;

	if ($userId && !empty($nuevoNombre) && $idPerfil > 0) {
		try {
			// Get the profile name for the response
			$perfil = Perfil::get($idPerfil, $conexion);
			$nombrePerfil = $perfil ? $perfil->getNombre() : 'Desconocido';

			$success = Usuario::modificar($userId, $nuevoNombre, $idPerfil, $conexion, $idEmpleado);

			if ($success) {
				$response['success'] = true;
				$response['message'] = 'Usuario actualizado correctamente.';
				// Add the profile name to the response for updating the table
				$response['nombre_perfil'] = $nombrePerfil;
			} else {
				// This might happen if the ID doesn't exist, though modificar doesn't explicitly check
				$response['message'] = 'Error: No se pudo encontrar o modificar el usuario.';
				http_response_code(404); // Not Found or Bad Request
			}
		} catch (Exception $e) {
			// Log the detailed error: error_log("Error modificando usuario ID $userId: " . $e->getMessage());
			$response['message'] = "Error al modificar usuario: " . $e->getMessage();
			http_response_code(500); // Internal Server Error
		}
	} else if (!$userId) {
		$response['message'] = 'Error: ID de usuario inválido.';
		http_response_code(400); // Bad Request
	} else if (empty($nuevoNombre)) {
		$response['message'] = 'Error: El nombre no puede estar vacío.';
		http_response_code(400); // Bad Request
	} else {
		$response['message'] = 'Error: Debe seleccionar un perfil válido.';
		http_response_code(400); // Bad Request
	}

	echo json_encode($response);
	exit; // IMPORTANT: Stop script execution after sending JSON response for AJAX
}
#endregion Modify user

#region region Check Employee Availability
// --- Handle AJAX Request (Check Employee Availability) ---
if ($_SERVER['REQUEST_METHOD'] == 'POST' && isset($_POST['action']) && $_POST['action'] == 'check_employee') {
	header('Content-Type: application/json');
	$response = ['has_user' => false];

	$idEmpleado = filter_input(INPUT_POST, 'id_empleado', FILTER_VALIDATE_INT);

	if ($idEmpleado) {
		try {
			// Use the static method from Usuario class to check if employee has a user
			$hasUser = Usuario::empleadoTieneUsuario($idEmpleado, $conexion);
			$response['has_user'] = $hasUser;
		} catch (Exception $e) {
			// Log error but don't expose details to client
			error_log("Error checking employee availability: " . $e->getMessage());
			$response['has_user'] = false; // Default to false on error
		}
	}

	echo json_encode($response);
	exit;
}
#endregion Check Employee Availability

#region region Change Password
// --- Handle AJAX Request (Change Password) ---
if ($_SERVER['REQUEST_METHOD'] == 'POST' && isset($_POST['action']) && $_POST['action'] == 'cambiar_clave') {
	header('Content-Type: application/json');
	$response = ['success' => false, 'message' => 'Error desconocido.'];

	$userId = filter_input(INPUT_POST, 'userId', FILTER_VALIDATE_INT);
	$nuevaClave = trim($_POST['nueva_clave'] ?? '');
	$confirmarClave = trim($_POST['confirmar_clave'] ?? '');

	// Basic validation
	if (!$userId) {
		$response['message'] = 'Error: ID de usuario inválido.';
		http_response_code(400);
	} else if (empty($nuevaClave)) {
		$response['message'] = 'Error: La nueva contraseña no puede estar vacía.';
		http_response_code(400);
	} else if (empty($confirmarClave)) {
		$response['message'] = 'Error: Debe confirmar la contraseña.';
		http_response_code(400);
	} else if ($nuevaClave !== $confirmarClave) {
		$response['message'] = 'Error: Las contraseñas no coinciden.';
		http_response_code(400);
	} else {
		try {
			// Use the static method from Usuario class to update password
			$success = Usuario::actualizarClave($userId, $nuevaClave, $conexion);

			if ($success) {
				$response['success'] = true;
				$response['message'] = 'Contraseña actualizada correctamente.';
			} else {
				$response['message'] = 'Error: No se pudo actualizar la contraseña.';
				http_response_code(500);
			}
		} catch (Exception $e) {
			// Log the detailed error: error_log("Error cambiando contraseña usuario ID $userId: " . $e->getMessage());
			$response['message'] = "Error al cambiar contraseña: " . $e->getMessage();
			http_response_code(500);
		}
	}

	echo json_encode($response);
	exit;
}
#endregion Change Password

#region region Handle POST Actions (Deactivation)
// --- Handle Non-AJAX POST Action (Deactivate User) ---
if ($_SERVER['REQUEST_METHOD'] == 'POST' && isset($_POST['action']) && $_POST['action'] == 'desactivar') {
	$userIdToDeactivate = filter_input(INPUT_POST, 'userId', FILTER_VALIDATE_INT);

	if ($userIdToDeactivate) {
		try {
			$success = Usuario::desactivar($userIdToDeactivate, $conexion);

			if ($success) {
				$_SESSION['flash_message_success'] = "Usuario desactivado correctamente.";
			} else {
				// This case might happen if the ID doesn't exist, though desactivar doesn't explicitly check
				$_SESSION['flash_message_error'] = "Error: No se pudo encontrar o desactivar el usuario.";
				// Consider setting an error type for the flash message if your system supports it
			}
		} catch (Exception $e) {
			// Log the detailed error: error_log("Error desactivando usuario ID $userIdToDeactivate: " . $e->getMessage());
			$_SESSION['flash_message_error'] = "Error al desactivar usuario: " . $e->getMessage();
			// Consider setting an error type for the flash message
		}
	} else {
		$_SESSION['flash_message_error'] = "Error: ID de usuario inválido para desactivar.";
		// Consider setting an error type
	}

	// Redirect back to the user list page after processing
	header('Location: lusuarios');
	exit;
}
#endregion Handle POST Actions

#region try
try {
	$usuarios = Usuario::get_list($conexion);
	$perfiles = Perfil::get_list($conexion);
	$empleados = Empleado::get_list($conexion);

} catch (PDOException $e) {
	// Specific handling for database errors
	// Log the error: error_log("Database error fetching users: " . $e->getMessage());
	$error_display = 'show';
	$error_text    = "Error de base de datos al obtener la lista de usuarios.";
} catch (Exception $e) {
	// General error handling
	// Log the error: error_log("Error fetching users: " . $e->getMessage());
	$error_display = 'show';
	$error_text    = "Ocurrió un error inesperado al obtener la lista de usuarios: " . $e->getMessage();
}

#endregion try

require_once __ROOT__ . '/views/lusuarios.view.php';

?>