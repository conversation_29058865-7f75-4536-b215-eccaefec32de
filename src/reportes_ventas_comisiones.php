<?php

declare(strict_types=1);

use App\classes\Cita;
use App\classes\CentroCosto;
use App\classes\MetodoPago;
use App\classes\Empleado;
use PhpOffice\PhpSpreadsheet\Spreadsheet;
use PhpOffice\PhpSpreadsheet\Writer\Xlsx;
use PhpOffice\PhpSpreadsheet\Style\Alignment;
use PhpOffice\PhpSpreadsheet\Style\Border;
use PhpOffice\PhpSpreadsheet\Style\Fill;
use PhpOffice\PhpSpreadsheet\Style\Font;

if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

/** @var PDO $conexion */
global $conexion;

require_once dirname(__FILE__, 2) . '/config/config.php';
require_once __ROOT__ . '/src/sessions/sessions.php';
require_once __ROOT__ . '/src/general/general.php';
require_once __ROOT__ . '/src/general/validaciones.php';
require_once __ROOT__ . '/src/general/preparar.php';

// Check for valid database connection before proceeding
if (!$conexion instanceof PDO) {
	error_log("Error crítico: No hay conexión a la base de datos en lmetodos_pagos.php.");
	$response['message'] = 'Error crítico: No se pudo conectar a la base de datos.';
	http_response_code(503); // Service Unavailable
	echo json_encode($response);
	exit;
}

// Variables para manejo de errores y mensajes
$error_display   = null;
$error_text      = null;
$success_display = null;
$success_text    = null;

// Variables para los datos del reporte
$reporte_data = null;
$fecha_inicio = '';
$fecha_fin = '';
$id_centro_costo = null;
$centro_costo_nombre = '';

#region Handle POST Actions
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $action = $_POST['action'] ?? '';
    
    #region Generate Report
    if ($action === 'generar_reporte') {
        $fecha_inicio    = trim($_POST['fecha_inicio'] ?? '');
        $fecha_fin       = trim($_POST['fecha_fin'] ?? '');
        $id_centro_costo = !empty($_POST['id_centro_costo']) ? (int)$_POST['id_centro_costo'] : null;
        
        // Validaciones
        if (empty($fecha_inicio) || empty($fecha_fin)) {
            $error_display = 'show';
            $error_text    = 'Las fechas de inicio y fin son obligatorias.';
        } elseif ($id_centro_costo === null) {
            $error_display = 'show';
            $error_text    = 'Debe seleccionar un centro de costo.';
        } elseif (strtotime($fecha_inicio) > strtotime($fecha_fin)) {
            $error_display = 'show';
            $error_text    = 'La fecha de inicio no puede ser mayor que la fecha de fin.';
        } else {
            try {
                // Establecer zona horaria
                date_default_timezone_set('America/Bogota');
                
                // Obtener nombre del centro de costo
                $centro_costo = CentroCosto::get($id_centro_costo, $conexion);
                if ($centro_costo) {
                    $centro_costo_nombre = $centro_costo->getNombre();
                }
                
                // Generar datos del reporte
                $reporte_data = generarReporteVentasComisiones($conexion, $fecha_inicio, $fecha_fin, $id_centro_costo);

                if (empty($reporte_data['citas'])) {
                    $error_display = 'show';
                    $error_text    = 'No se encontraron citas para el rango de fechas y centro de costo seleccionados.';
                } elseif (isset($reporte_data['schema_warning']) && $reporte_data['schema_warning']) {
                    $success_display = 'show';
                    $success_text    = 'Reporte generado exitosamente. NOTA: El filtro por centro de costo no está disponible debido a la estructura actual de la base de datos. Se muestran todas las citas del rango de fechas seleccionado.';
                }
                
            } catch (Exception $e) {
                $error_display = 'show';
                $error_text    = 'Error al generar el reporte: ' . $e->getMessage();
            }
        }
    }
    #endregion Generate Report

    #region Export Excel
    if ($action === 'exportar_excel') {
        $fecha_inicio    = trim($_POST['fecha_inicio'] ?? '');
        $fecha_fin       = trim($_POST['fecha_fin'] ?? '');
        $id_centro_costo = !empty($_POST['id_centro_costo']) ? (int)$_POST['id_centro_costo'] : null;

        // Validaciones
        if (empty($fecha_inicio) || empty($fecha_fin)) {
            $error_display = 'show';
            $error_text    = 'Las fechas de inicio y fin son obligatorias para exportar.';
        } elseif ($id_centro_costo === null) {
            $error_display = 'show';
            $error_text    = 'Debe seleccionar un centro de costo para exportar.';
        } elseif (strtotime($fecha_inicio) > strtotime($fecha_fin)) {
            $error_display = 'show';
            $error_text    = 'La fecha de inicio no puede ser mayor que la fecha de fin.';
        } else {
            try {
                // Establecer zona horaria
                date_default_timezone_set('America/Bogota');

                // Obtener nombre del centro de costo
                $centro_costo = CentroCosto::get($id_centro_costo, $conexion);
                if ($centro_costo) {
                    $centro_costo_nombre = $centro_costo->getNombre();
                }

                // Generar datos del reporte
                $reporte_data = generarReporteVentasComisiones($conexion, $fecha_inicio, $fecha_fin, $id_centro_costo);

                if (empty($reporte_data['citas'])) {
                    $error_display = 'show';
                    $error_text    = 'No se encontraron datos para exportar en el rango de fechas y centro de costo seleccionados.';
                } else {
                    // Generar y descargar el archivo Excel
                    generarExcelVentasComisiones($reporte_data, $fecha_inicio, $fecha_fin, $centro_costo_nombre);
                    exit; // Terminar la ejecución después de la descarga
                }

            } catch (Exception $e) {
                $error_display = 'show';
                $error_text    = 'Error al exportar el reporte: ' . $e->getMessage();
            }
        }
    }
    #endregion Export Excel
}
#endregion Handle POST Actions

#region try
try {
    // Establecer zona horaria
    date_default_timezone_set('America/Bogota');
    
    // Obtener lista de centros de costo activos
    $centros_costos = CentroCosto::get_list($conexion);
    
} catch (PDOException $e) {
    $error_display = 'show';
    $error_text    = "Error de base de datos al cargar los datos iniciales.";
} catch (Exception $e) {
    $error_display = 'show';
    $error_text    = "Ocurrió un error inesperado: " . $e->getMessage();
}
#endregion try

/**
 * Función para generar archivo Excel del reporte de ventas y comisiones
 */
function generarExcelVentasComisiones(array $reporte_data, string $fecha_inicio, string $fecha_fin, string $centro_costo_nombre): void
{
    try {
        // Crear nuevo spreadsheet
        $spreadsheet = new Spreadsheet();
        $sheet = $spreadsheet->getActiveSheet();

        // Configurar propiedades del documento
        $spreadsheet->getProperties()
            ->setCreator('Sistema de Gestión de Barbería')
            ->setTitle('Reporte de Ventas y Comisiones')
            ->setSubject('Reporte de Ventas y Comisiones')
            ->setDescription('Reporte detallado de ventas y comisiones por empleado y método de pago');

        // Configurar zona horaria
        date_default_timezone_set('America/Bogota');

        // Variables para el diseño
        $row = 1;

        // METADATA DEL REPORTE
        $sheet->setCellValue('A' . $row, 'REPORTE DE VENTAS Y COMISIONES');
        $sheet->mergeCells('A' . $row . ':' . chr(65 + count($reporte_data['empleados']) + 1) . $row);
        $sheet->getStyle('A' . $row)->getFont()->setBold(true)->setSize(16);
        $sheet->getStyle('A' . $row)->getAlignment()->setHorizontal(Alignment::HORIZONTAL_CENTER);
        $row++;

        // Header section with borders - split into field name and value columns
        $headerStartRow = $row;

        // Fecha de generación
        $sheet->setCellValue('A' . $row, 'Fecha de generación:');
        $sheet->setCellValue('B' . $row, date('Y-m-d H:i:s'));
        $sheet->mergeCells('B' . $row . ':E' . $row); // Merge B, C, D, E columns for value
        $sheet->getStyle('A' . $row)->getFont()->setBold(true);
        $sheet->getStyle('B' . $row)->getFont()->setBold(true);
        $row++;

        // Centro de costo
        $sheet->setCellValue('A' . $row, 'Centro de costo:');
        $sheet->setCellValue('B' . $row, $centro_costo_nombre);
        $sheet->mergeCells('B' . $row . ':E' . $row); // Merge B, C, D, E columns for value
        $sheet->getStyle('A' . $row)->getFont()->setBold(true);
        $sheet->getStyle('B' . $row)->getFont()->setBold(true);
        $row++;

        // Período
        $sheet->setCellValue('A' . $row, 'Período:');
        $sheet->setCellValue('B' . $row, date('Y-m-d', strtotime($fecha_inicio)) . ' - ' . date('Y-m-d', strtotime($fecha_fin)));
        $sheet->mergeCells('B' . $row . ':E' . $row); // Merge B, C, D, E columns for value
        $sheet->getStyle('A' . $row)->getFont()->setBold(true);
        $sheet->getStyle('B' . $row)->getFont()->setBold(true);

        // Apply borders to header section (columns A through E to cover merged cells, rows 2-4)
        $headerEndRow = $row;
        $sheet->getStyle('A' . $headerStartRow . ':E' . $headerEndRow)->getBorders()->getAllBorders()->setBorderStyle(Border::BORDER_THIN);

        $row += 2; // One blank row for spacing (no borders, no background)

        // ENCABEZADOS DE LA TABLA
        $dataTableStartRow = $row; // Store the start row for data table borders
        $sheet->setCellValue('A' . $row, 'Barberos:');
        $sheet->getStyle('A' . $row)->getFont()->setBold(true);
        $sheet->getStyle('A' . $row)->getFill()->setFillType(Fill::FILL_SOLID)->getStartColor()->setRGB('4472C4');
        $sheet->getStyle('A' . $row)->getFont()->getColor()->setRGB('FFFFFF');

        $col = 1; // Columna B
        foreach ($reporte_data['empleados'] as $empleado_id => $empleado_nombre) {
            $sheet->setCellValue(chr(65 + $col) . $row, $empleado_nombre);
            $sheet->getStyle(chr(65 + $col) . $row)->getFont()->setBold(true);
            $sheet->getStyle(chr(65 + $col) . $row)->getFill()->setFillType(Fill::FILL_SOLID)->getStartColor()->setRGB('4472C4');
            $sheet->getStyle(chr(65 + $col) . $row)->getFont()->getColor()->setRGB('FFFFFF');
            $sheet->getStyle(chr(65 + $col) . $row)->getAlignment()->setHorizontal(Alignment::HORIZONTAL_CENTER);
            $col++;
        }

        // Columna Total
        $sheet->setCellValue(chr(65 + $col) . $row, 'Total');
        $sheet->getStyle(chr(65 + $col) . $row)->getFont()->setBold(true);
        $sheet->getStyle(chr(65 + $col) . $row)->getFill()->setFillType(Fill::FILL_SOLID)->getStartColor()->setRGB('4472C4');
        $sheet->getStyle(chr(65 + $col) . $row)->getFont()->getColor()->setRGB('FFFFFF');
        $sheet->getStyle(chr(65 + $col) . $row)->getAlignment()->setHorizontal(Alignment::HORIZONTAL_CENTER);
        $row++;

        // Calcular totales
        $total_electronicos = 0;
        $total_no_electronicos = 0;
        $totales_por_empleado = [];

        // Inicializar totales por empleado
        foreach ($reporte_data['empleados'] as $empleado_id => $empleado_nombre) {
            $totales_por_empleado[$empleado_id] = 0;
        }

        // MÉTODOS DE PAGO ELECTRÓNICOS
        if (!empty($reporte_data['metodos_electronicos'])) {
            foreach ($reporte_data['metodos_electronicos'] as $metodo_id => $metodo) {
                $sheet->setCellValue('A' . $row, $metodo['descripcion']);
                $sheet->getStyle('A' . $row)->getFont()->setBold(true);

                $total_fila = 0;
                $col = 1;
                foreach ($reporte_data['empleados'] as $empleado_id => $empleado_nombre) {
                    $valor = $reporte_data['matriz_ventas'][$metodo_id][$empleado_id] ?? 0;
                    $sheet->setCellValue(chr(65 + $col) . $row, $valor);
                    $sheet->getStyle(chr(65 + $col) . $row)->getNumberFormat()->setFormatCode('#,##0');
                    $sheet->getStyle(chr(65 + $col) . $row)->getAlignment()->setHorizontal(Alignment::HORIZONTAL_RIGHT);

                    $total_fila += $valor;
                    $totales_por_empleado[$empleado_id] += $valor;
                    $col++;
                }

                // Total de la fila
                $sheet->setCellValue(chr(65 + $col) . $row, $total_fila);
                $sheet->getStyle(chr(65 + $col) . $row)->getNumberFormat()->setFormatCode('#,##0');
                $sheet->getStyle(chr(65 + $col) . $row)->getAlignment()->setHorizontal(Alignment::HORIZONTAL_RIGHT);
                $sheet->getStyle(chr(65 + $col) . $row)->getFont()->setBold(true);

                $total_electronicos += $total_fila;
                $row++;
            }

            // Subtotal Transferencia
            $sheet->setCellValue('A' . $row, 'Total Transferencia');
            $sheet->getStyle('A' . $row)->getFont()->setBold(true);
            $sheet->getStyle('A' . $row)->getFill()->setFillType(Fill::FILL_SOLID)->getStartColor()->setRGB('D9E2F3');

            $col = 1;
            foreach ($reporte_data['empleados'] as $empleado_id => $empleado_nombre) {
                $total_empleado_electronico = 0;
                foreach ($reporte_data['metodos_electronicos'] as $metodo_id => $metodo) {
                    $total_empleado_electronico += $reporte_data['matriz_ventas'][$metodo_id][$empleado_id] ?? 0;
                }
                $sheet->setCellValue(chr(65 + $col) . $row, $total_empleado_electronico);
                $sheet->getStyle(chr(65 + $col) . $row)->getNumberFormat()->setFormatCode('#,##0');
                $sheet->getStyle(chr(65 + $col) . $row)->getAlignment()->setHorizontal(Alignment::HORIZONTAL_RIGHT);
                $sheet->getStyle(chr(65 + $col) . $row)->getFont()->setBold(true);
                $sheet->getStyle(chr(65 + $col) . $row)->getFill()->setFillType(Fill::FILL_SOLID)->getStartColor()->setRGB('D9E2F3');
                $col++;
            }

            $sheet->setCellValue(chr(65 + $col) . $row, $total_electronicos);
            $sheet->getStyle(chr(65 + $col) . $row)->getNumberFormat()->setFormatCode('#,##0');
            $sheet->getStyle(chr(65 + $col) . $row)->getAlignment()->setHorizontal(Alignment::HORIZONTAL_RIGHT);
            $sheet->getStyle(chr(65 + $col) . $row)->getFont()->setBold(true);
            $sheet->getStyle(chr(65 + $col) . $row)->getFill()->setFillType(Fill::FILL_SOLID)->getStartColor()->setRGB('D9E2F3');
            $row++;
        }

        // MÉTODOS DE PAGO NO ELECTRÓNICOS
        if (!empty($reporte_data['metodos_no_electronicos'])) {
            foreach ($reporte_data['metodos_no_electronicos'] as $metodo_id => $metodo) {
                $sheet->setCellValue('A' . $row, $metodo['descripcion']);
                $sheet->getStyle('A' . $row)->getFont()->setBold(true);

                $total_fila = 0;
                $col = 1;
                foreach ($reporte_data['empleados'] as $empleado_id => $empleado_nombre) {
                    $valor = $reporte_data['matriz_ventas'][$metodo_id][$empleado_id] ?? 0;
                    $sheet->setCellValue(chr(65 + $col) . $row, $valor);
                    $sheet->getStyle(chr(65 + $col) . $row)->getNumberFormat()->setFormatCode('#,##0');
                    $sheet->getStyle(chr(65 + $col) . $row)->getAlignment()->setHorizontal(Alignment::HORIZONTAL_RIGHT);

                    $total_fila += $valor;
                    $totales_por_empleado[$empleado_id] += $valor;
                    $col++;
                }

                // Total de la fila
                $sheet->setCellValue(chr(65 + $col) . $row, $total_fila);
                $sheet->getStyle(chr(65 + $col) . $row)->getNumberFormat()->setFormatCode('#,##0');
                $sheet->getStyle(chr(65 + $col) . $row)->getAlignment()->setHorizontal(Alignment::HORIZONTAL_RIGHT);
                $sheet->getStyle(chr(65 + $col) . $row)->getFont()->setBold(true);

                $total_no_electronicos += $total_fila;
                $row++;
            }
        }

        $total_ventas = $total_electronicos + $total_no_electronicos;
        $ganancias = $total_ventas - $reporte_data['total_comisiones'];

        // TOTALES FINALES
        $row++; // Espacio adicional

        // Total ventas
        $sheet->setCellValue('A' . $row, 'Total ventas');
        $sheet->getStyle('A' . $row)->getFont()->setBold(true);
        $sheet->getStyle('A' . $row)->getFill()->setFillType(Fill::FILL_SOLID)->getStartColor()->setRGB('D9E2F3');

        $col = 1;
        foreach ($reporte_data['empleados'] as $empleado_id => $empleado_nombre) {
            $sheet->setCellValue(chr(65 + $col) . $row, $totales_por_empleado[$empleado_id]);
            $sheet->getStyle(chr(65 + $col) . $row)->getNumberFormat()->setFormatCode('#,##0');
            $sheet->getStyle(chr(65 + $col) . $row)->getAlignment()->setHorizontal(Alignment::HORIZONTAL_RIGHT);
            $sheet->getStyle(chr(65 + $col) . $row)->getFont()->setBold(true);
            $sheet->getStyle(chr(65 + $col) . $row)->getFill()->setFillType(Fill::FILL_SOLID)->getStartColor()->setRGB('D9E2F3');
            $col++;
        }

        $sheet->setCellValue(chr(65 + $col) . $row, $total_ventas);
        $sheet->getStyle(chr(65 + $col) . $row)->getNumberFormat()->setFormatCode('#,##0');
        $sheet->getStyle(chr(65 + $col) . $row)->getAlignment()->setHorizontal(Alignment::HORIZONTAL_RIGHT);
        $sheet->getStyle(chr(65 + $col) . $row)->getFont()->setBold(true);
        $sheet->getStyle(chr(65 + $col) . $row)->getFill()->setFillType(Fill::FILL_SOLID)->getStartColor()->setRGB('D9E2F3');
        $row++;

        // Nomina
        $sheet->setCellValue('A' . $row, 'Nomina');
        $sheet->getStyle('A' . $row)->getFont()->setBold(true);
        $sheet->getStyle('A' . $row)->getFill()->setFillType(Fill::FILL_SOLID)->getStartColor()->setRGB('D9E2F3');
        $sheet->mergeCells('A' . $row . ':' . chr(65 + count($reporte_data['empleados'])) . $row);

        $sheet->setCellValue(chr(65 + count($reporte_data['empleados']) + 1) . $row, $reporte_data['total_comisiones']);
        $sheet->getStyle(chr(65 + count($reporte_data['empleados']) + 1) . $row)->getNumberFormat()->setFormatCode('#,##0');
        $sheet->getStyle(chr(65 + count($reporte_data['empleados']) + 1) . $row)->getAlignment()->setHorizontal(Alignment::HORIZONTAL_RIGHT);
        $sheet->getStyle(chr(65 + count($reporte_data['empleados']) + 1) . $row)->getFont()->setBold(true);
        $sheet->getStyle(chr(65 + count($reporte_data['empleados']) + 1) . $row)->getFill()->setFillType(Fill::FILL_SOLID)->getStartColor()->setRGB('D9E2F3');
        $row++;

        // Ganancias
        $sheet->setCellValue('A' . $row, 'Ganancias');
        $sheet->getStyle('A' . $row)->getFont()->setBold(true);
        $sheet->getStyle('A' . $row)->getFill()->setFillType(Fill::FILL_SOLID)->getStartColor()->setRGB('C6EFCE');
        $sheet->mergeCells('A' . $row . ':' . chr(65 + count($reporte_data['empleados'])) . $row);

        $sheet->setCellValue(chr(65 + count($reporte_data['empleados']) + 1) . $row, $ganancias);
        $sheet->getStyle(chr(65 + count($reporte_data['empleados']) + 1) . $row)->getNumberFormat()->setFormatCode('#,##0');
        $sheet->getStyle(chr(65 + count($reporte_data['empleados']) + 1) . $row)->getAlignment()->setHorizontal(Alignment::HORIZONTAL_RIGHT);
        $sheet->getStyle(chr(65 + count($reporte_data['empleados']) + 1) . $row)->getFont()->setBold(true);
        $sheet->getStyle(chr(65 + count($reporte_data['empleados']) + 1) . $row)->getFill()->setFillType(Fill::FILL_SOLID)->getStartColor()->setRGB('C6EFCE');

        // Ajustar ancho de columnas
        $sheet->getColumnDimension('A')->setWidth(20);
        for ($i = 1; $i <= count($reporte_data['empleados']) + 1; $i++) {
            $sheet->getColumnDimension(chr(65 + $i))->setWidth(15);
        }

        // Agregar bordes a toda la tabla (excluding the blank row between header and data)
        $lastColumn = chr(65 + count($reporte_data['empleados']) + 1);
        $sheet->getStyle('A' . $dataTableStartRow . ':' . $lastColumn . $row)->getBorders()->getAllBorders()->setBorderStyle(Border::BORDER_THIN);

        // Configurar nombre del archivo
        $filename = 'Reporte_Ventas_Comisiones_' . date('Y-m-d_H-i-s') . '.xlsx';

        // Configurar headers para descarga
        header('Content-Type: application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
        header('Content-Disposition: attachment;filename="' . $filename . '"');
        header('Cache-Control: max-age=0');
        header('Cache-Control: max-age=1');
        header('Expires: Mon, 26 Jul 1997 05:00:00 GMT');
        header('Last-Modified: ' . gmdate('D, d M Y H:i:s') . ' GMT');
        header('Cache-Control: cache, must-revalidate');
        header('Pragma: public');

        // Crear writer y enviar archivo
        $writer = new Xlsx($spreadsheet);
        $writer->save('php://output');

    } catch (Exception $e) {
        throw new Exception("Error al generar el archivo Excel: " . $e->getMessage());
    }
}

/**
 * Función para generar los datos del reporte de ventas y comisiones
 */
function generarReporteVentasComisiones(PDO $conexion, string $fecha_inicio, string $fecha_fin, int $id_centro_costo): array
{
    try {
        // Primero verificar si existe la relación entre puestos y centros_costos
        $check_query = <<<SQL
        SELECT COLUMN_NAME
        FROM INFORMATION_SCHEMA.COLUMNS
        WHERE TABLE_NAME = 'puestos' AND COLUMN_NAME = 'id_centro_costo'
        SQL;

        $check_statement = $conexion->prepare($check_query);
        $check_statement->execute();
        $column_exists = $check_statement->fetch(PDO::FETCH_ASSOC);

        if ($column_exists) {
            // Si existe la columna id_centro_costo en puestos, usar la consulta completa
            $query = <<<SQL
            SELECT
                c.id as cita_id,
                c.fecha_fin,
                c.valor_comision_empleado,
                e.id as empleado_id,
                e.nombre as empleado_nombre,
                mp.id as metodo_pago_id,
                mp.descripcion as metodo_pago_descripcion,
                mp.es_metodo_electronico,
                cc.nombre as centro_costo_nombre,
                (
                    SELECT SUM(cs.valor)
                    FROM citas_servicios cs
                    WHERE cs.id_cita = c.id
                ) as total_valor_servicios
            FROM citas c
            JOIN empleados_turnos et ON c.id_empleado_turno = et.id
            JOIN empleados e ON et.id_empleado = e.id
            JOIN puestos p ON et.id_puesto = p.id
            JOIN centros_costos cc ON p.id_centro_costo = cc.id
            JOIN metodos_pagos mp ON c.id_metodo_pago = mp.id
            WHERE
                c.estado = 1
                AND c.fecha_fin IS NOT NULL
                AND DATE(c.fecha_fin) BETWEEN :fecha_inicio AND :fecha_fin
                AND cc.id = :id_centro_costo
            ORDER BY e.nombre, mp.descripcion
            SQL;
        } else {
            // Si no existe la relación, usar una consulta simplificada que ignore el filtro de centro de costo
            // y mostrar todas las citas del rango de fechas
            $query = <<<SQL
            SELECT
                c.id as cita_id,
                c.fecha_fin,
                c.valor_comision_empleado,
                e.id as empleado_id,
                e.nombre as empleado_nombre,
                mp.id as metodo_pago_id,
                mp.descripcion as metodo_pago_descripcion,
                mp.es_metodo_electronico,
                'N/A' as centro_costo_nombre,
                (
                    SELECT SUM(cs.valor)
                    FROM citas_servicios cs
                    WHERE cs.id_cita = c.id
                ) as total_valor_servicios
            FROM citas c
            JOIN empleados_turnos et ON c.id_empleado_turno = et.id
            JOIN empleados e ON et.id_empleado = e.id
            JOIN puestos p ON et.id_puesto = p.id
            JOIN metodos_pagos mp ON c.id_metodo_pago = mp.id
            WHERE
                c.estado = 1
                AND c.fecha_fin IS NOT NULL
                AND DATE(c.fecha_fin) BETWEEN :fecha_inicio AND :fecha_fin
            ORDER BY e.nombre, mp.descripcion
            SQL;
        }

        $statement = $conexion->prepare($query);
        $statement->bindValue(':fecha_inicio', $fecha_inicio, PDO::PARAM_STR);
        $statement->bindValue(':fecha_fin', $fecha_fin, PDO::PARAM_STR);

        // Solo vincular el parámetro id_centro_costo si existe la columna
        if ($column_exists) {
            $statement->bindValue(':id_centro_costo', $id_centro_costo, PDO::PARAM_INT);
        }

        $statement->execute();
        
        $citas = $statement->fetchAll(PDO::FETCH_ASSOC);
        
        // Procesar datos para el reporte
        $empleados = [];
        $metodos_pago = [];
        $matriz_ventas = [];
        $total_comisiones = 0;
        
        foreach ($citas as $cita) {
            $empleado_id      = $cita['empleado_id'];
            $empleado_nombre  = $cita['empleado_nombre'];
            $metodo_pago_id   = $cita['metodo_pago_id'];
            $metodo_pago_desc = $cita['metodo_pago_descripcion'];
            $es_electronico   = (int)$cita['es_metodo_electronico'];
            $valor_servicios  = (float)$cita['total_valor_servicios'];
            $comision         = (float)$cita['valor_comision_empleado'];
            
            // Recopilar empleados únicos
            if (!isset($empleados[$empleado_id])) {
                $empleados[$empleado_id] = $empleado_nombre;
            }
            
            // Recopilar métodos de pago únicos
            if (!isset($metodos_pago[$metodo_pago_id])) {
                $metodos_pago[$metodo_pago_id] = [
                    'descripcion'    => $metodo_pago_desc,
                    'es_electronico' => $es_electronico
                ];
            }
            
            // Construir matriz de ventas
            if (!isset($matriz_ventas[$metodo_pago_id])) {
                $matriz_ventas[$metodo_pago_id] = [];
            }
            if (!isset($matriz_ventas[$metodo_pago_id][$empleado_id])) {
                $matriz_ventas[$metodo_pago_id][$empleado_id] = 0;
            }
            
            $matriz_ventas[$metodo_pago_id][$empleado_id] += $valor_servicios;
            $total_comisiones += $comision;
        }
        
        // Ordenar empleados alfabéticamente
        asort($empleados);
        
        // Separar métodos de pago electrónicos y no electrónicos
        $metodos_electronicos = [];
        $metodos_no_electronicos = [];
        
        foreach ($metodos_pago as $id => $metodo) {
            if ($metodo['es_electronico'] === 1) {
                $metodos_electronicos[$id] = $metodo;
            } else {
                $metodos_no_electronicos[$id] = $metodo;
            }
        }
        
        // Ordenar métodos de pago alfabéticamente
        uasort($metodos_electronicos, function($a, $b) {
            return strcmp($a['descripcion'], $b['descripcion']);
        });
        
        uasort($metodos_no_electronicos, function($a, $b) {
            return strcmp($a['descripcion'], $b['descripcion']);
        });
        
        return [
            'citas'                   => $citas,
            'empleados'               => $empleados,
            'metodos_electronicos'    => $metodos_electronicos,
            'metodos_no_electronicos' => $metodos_no_electronicos,
            'matriz_ventas'           => $matriz_ventas,
            'total_comisiones'        => $total_comisiones,
            'schema_warning'          => !$column_exists
        ];
        
    } catch (PDOException $e) {
        throw new Exception("Error de base de datos al generar el reporte: " . $e->getMessage());
    }
}

require_once __ROOT__ . '/views/reportes_ventas_comisiones.view.php';
