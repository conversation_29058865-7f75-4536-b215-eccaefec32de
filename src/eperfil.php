<?php
// Use the Perfil class with its namespace
use App\classes\Accion;
use App\classes\Perfil;

// Start session if not already started
if (session_status() === PHP_SESSION_NONE) {
	session_start();
}

/** @var PDO $conexion */
global $conexion; // Assuming $conexion is globally available or included

// Include necessary files
require_once dirname(__FILE__, 2) . '/config/config.php';
require_once __ROOT__ . '/src/sessions/sessions.php';
require_once __ROOT__ . '/src/general/general.php';
require_once __ROOT__ . '/src/general/validaciones.php';
require_once __ROOT__ . '/src/general/preparar.php';

#region region INIT VARIABLES
// Variables to hold form input (useful if re-displaying form after error)
$id = 0;
$nombre = '';
$is_edit_mode = false;
$error_display = '';
$error_text = '';
$acciones_asociadas = [];
$todas_acciones = [];
#endregion INIT VARIABLES

#region region GET Request Handling
if ($_SERVER['REQUEST_METHOD'] == 'GET') {
	// Get the ID from the query string
	$id = isset($_GET['id']) ? filter_input(INPUT_GET, 'id', FILTER_VALIDATE_INT) : 0;
	
	if ($id) {
		try {
			$perfil = Perfil::get($id, $conexion);
			
			if ($perfil) {
				// Set edit mode and populate form variables
				$is_edit_mode = true;
				$nombre = $perfil->getNombre();
				
				// Get associated actions
				$acciones_asociadas = $perfil->getAcciones($conexion);
				
				// Get all actions for the checkboxes
				$todas_acciones = Accion::get_list($conexion);
			} else {
				// Perfil not found
				$_SESSION['flash_message_error'] = "El perfil con ID $id no existe.";
				header('Location: lperfiles');
				exit;
			}
		} catch (Exception $e) {
			$_SESSION['flash_message_error'] = "Error al cargar el perfil: " . $e->getMessage();
			header('Location: lperfiles');
			exit;
		}
	} else {
		// No ID provided, redirect to list
		$_SESSION['flash_message_error'] = "ID de perfil no proporcionado.";
		header('Location: lperfiles');
		exit;
	}
}
#endregion GET Request Handling

#region region POST Request Handling
if ($_SERVER['REQUEST_METHOD'] == 'POST') {
	try {
		// 1. Get data from $_POST
		$id = isset($_POST['id']) ? filter_input(INPUT_POST, 'id', FILTER_VALIDATE_INT) : 0;
		$nombre = trim($_POST['nombre'] ?? '');
		$accion_ids = $_POST['accion_ids'] ?? [];
		
		// 2. Validate data
		if (empty($nombre)) {
			throw new Exception("El nombre del perfil es requerido.");
		}
		
		if ($id <= 0) {
			throw new Exception("ID de perfil inválido.");
		}
		
		// 3. Get the perfil object
		$perfil = Perfil::get($id, $conexion);
		
		if (!$perfil) {
			throw new Exception("Perfil no encontrado.");
		}
		
		// 4. Update the perfil name
		$perfil->setNombre($nombre);
		$success = $perfil->modificar($conexion);
		
		if (!$success) {
			throw new Exception("No se pudo actualizar el nombre del perfil.");
		}
		
		// 5. Get current associated actions
		$acciones_actuales = $perfil->getAcciones($conexion);
		$acciones_actuales_ids = [];
		
		foreach ($acciones_actuales as $accion) {
			$acciones_actuales_ids[] = $accion->getId();
		}
		
		// 6. Process actions to associate/disassociate
		
		// Actions to associate (new ones)
		$acciones_a_asociar = array_diff($accion_ids, $acciones_actuales_ids);
		foreach ($acciones_a_asociar as $accion_id) {
			$perfil->asociarAccion($accion_id, $conexion);
		}
		
		// Actions to disassociate (removed ones)
		$acciones_a_desasociar = array_diff($acciones_actuales_ids, $accion_ids);
		foreach ($acciones_actuales as $accion) {
			if (in_array($accion->getId(), $acciones_a_desasociar)) {
				Perfil::desasociarAccion($accion->getIdPerfilAccion(), $conexion);
			}
		}
		
		// 7. Success message and redirect
		$_SESSION['flash_message_success'] = "Perfil '$nombre' actualizado exitosamente.";
		header('Location: lperfiles');
		exit;
		
	} catch (Exception $e) {
		// Handle errors
		$error_display = 'show';
		$error_text = $e->getMessage();
		
		// Re-load the perfil and actions for the form
		try {
			$perfil = Perfil::get($id, $conexion);
			if ($perfil) {
				$acciones_asociadas = $perfil->getAcciones($conexion);
				$todas_acciones = Accion::get_list($conexion);
			}
		} catch (Exception $e2) {
			// If we can't even load the perfil, redirect to list
			$_SESSION['flash_message_error'] = "Error al cargar el perfil: " . $e2->getMessage();
			header('Location: lperfiles');
			exit;
		}
	}
}
#endregion POST Request Handling

require_once __ROOT__ . '/views/eperfil.view.php';

?>
