<?php

// Iniciar sesión si es necesario
use App\classes\CentroCosto;
use App\classes\Usuario;
use App\classes\Empleado;

if (session_status() === PHP_SESSION_NONE) {
	session_start();
}

/** @var PDO $conexion */
global $conexion;

require_once dirname(__FILE__, 2) . '/config/config.php';
require_once __ROOT__ . '/src/sessions/sessions.php';
require_once __ROOT__ . '/src/general/general.php';
require_once __ROOT__ . '/src/general/validaciones.php';
require_once __ROOT__ . '/src/general/preparar.php';

// Check for valid database connection before proceeding
if (!$conexion instanceof PDO) {
	error_log("Error crítico: No hay conexión a la base de datos en lcentros_costos.php.");
	$response['message'] = 'Error crítico: No se pudo conectar a la base de datos.';
	http_response_code(503); // Service Unavailable
	echo json_encode($response);
	exit;
}

#region init variables
$centros_costos = []; // Initialize as an empty array
#endregion init variables

#region Handle Flash Message Success
// Check for a success flash message from session
if (!empty($_SESSION['flash_message_success'])) {
	$success_text    = $_SESSION['flash_message_success'];
	$success_display = 'show';
	// Clear the flash message so it doesn't show again
	unset($_SESSION['flash_message_success']);
}
#endregion Handle Flash Message Success

#region Handle Flash Message Error
// Check for an error flash message from session
if (!empty($_SESSION['flash_message_error'])) {
	$error_text    = $_SESSION['flash_message_error'];
	$error_display = 'show';
	// Clear the flash message so it doesn't show again
	unset($_SESSION['flash_message_error']);
}
#endregion Handle Flash Message Error

#region Create Centro Costo
// --- Handle AJAX Request (Create Centro Costo) ---
if ($_SERVER['REQUEST_METHOD'] == 'POST' && isset($_POST['action']) && $_POST['action'] == 'crear') {
	header('Content-Type: application/json');                            // Set response header for JSON
	$response = ['success' => false, 'message' => 'Error desconocido.']; // Default error response
	
	$nombre = filter_input(INPUT_POST, 'nombre', FILTER_SANITIZE_SPECIAL_CHARS); // Sanitize name
	$nombre = trim($nombre ?? '');                                               // Trim whitespace
	
	if (!empty($nombre)) {
		try {
			$centroCosto = new CentroCosto();
			$centroCosto->setNombre($nombre);
			
			$newId = $centroCosto->crear($conexion);
			
			if ($newId) {
				$response['success'] = true;
				$response['message'] = 'Centro de Costo creado correctamente.';
				$response['id']      = $newId;
				$response['nombre']  = $nombre;
				$response['estado']  = 1; // Active by default
			} else {
				$response['message'] = 'Error: No se pudo crear el Centro de Costo.';
				http_response_code(500); // Internal Server Error
			}
		} catch (Exception $e) {
			$response['message'] = "Error al crear Centro de Costo: " . $e->getMessage();
			http_response_code(500); // Internal Server Error
		}
	} else {
		$response['message'] = 'Error: El nombre no puede estar vacío.';
		http_response_code(400); // Bad Request
	}
	
	echo json_encode($response);
	exit; // IMPORTANT: Stop script execution after sending JSON response for AJAX
}
#endregion Create Centro Costo

#region Modify Centro Costo
// --- Handle AJAX Request (Modify Centro Costo) ---
if ($_SERVER['REQUEST_METHOD'] == 'POST' && isset($_POST['action']) && $_POST['action'] == 'modificar') {
	header('Content-Type: application/json');                            // Set response header for JSON
	$response = ['success' => false, 'message' => 'Error desconocido.']; // Default error response
	
	$centroId    = filter_input(INPUT_POST, 'centroId', FILTER_VALIDATE_INT);
	$nuevoNombre = filter_input(INPUT_POST, 'nombre', FILTER_SANITIZE_SPECIAL_CHARS); // Sanitize name
	$nuevoNombre = trim($nuevoNombre ?? '');                                          // Trim whitespace
	
	if ($centroId && !empty($nuevoNombre)) {
		try {
			// Get the centro costo first
			$centroCosto = CentroCosto::get($centroId, $conexion);
			
			if ($centroCosto) {
				$centroCosto->setNombre($nuevoNombre);
				$success = $centroCosto->modificar($conexion);
				
				if ($success) {
					$response['success'] = true;
					$response['message'] = 'Centro de Costo actualizado correctamente.';
				} else {
					$response['message'] = 'Error: No se pudo actualizar el Centro de Costo.';
					http_response_code(500); // Internal Server Error
				}
			} else {
				$response['message'] = 'Error: No se encontró el Centro de Costo.';
				http_response_code(404); // Not Found
			}
		} catch (Exception $e) {
			$response['message'] = "Error al modificar Centro de Costo: " . $e->getMessage();
			http_response_code(500); // Internal Server Error
		}
	} else if (!$centroId) {
		$response['message'] = 'Error: ID de Centro de Costo inválido.';
		http_response_code(400); // Bad Request
	} else {
		$response['message'] = 'Error: El nombre no puede estar vacío.';
		http_response_code(400); // Bad Request
	}
	
	echo json_encode($response);
	exit; // IMPORTANT: Stop script execution after sending JSON response for AJAX
}
#endregion Modify Centro Costo

#region Handle POST Actions (Deactivation)
// --- Handle Non-AJAX POST Action (Deactivate Centro Costo) ---
if ($_SERVER['REQUEST_METHOD'] == 'POST' && isset($_POST['action']) && $_POST['action'] == 'desactivar') {
	$centroIdToDeactivate = filter_input(INPUT_POST, 'centroId', FILTER_VALIDATE_INT);
	
	if ($centroIdToDeactivate) {
		try {
			$success = CentroCosto::desactivar($centroIdToDeactivate, $conexion);
			
			if ($success) {
				$_SESSION['flash_message_success'] = "Centro de Costo desactivado correctamente.";
			} else {
				$_SESSION['flash_message_error'] = "Error: No se pudo encontrar o desactivar el Centro de Costo.";
			}
		} catch (Exception $e) {
			$_SESSION['flash_message_error'] = "Error al desactivar Centro de Costo: " . $e->getMessage();
		}
	} else {
		$_SESSION['flash_message_error'] = "Error: ID de Centro de Costo inválido para desactivar.";
	}
	
	// Redirect back to the centro costo list page after processing
	header('Location: centros-costos');
	exit;
}
#endregion Handle POST Actions

#region Handle GET Action - Get Users for Cost Center
// --- Handle AJAX GET Request (Get Users for Cost Center) ---
if ($_SERVER['REQUEST_METHOD'] == 'GET' && isset($_GET['action']) && $_GET['action'] == 'get_usuarios_centro') {
	header('Content-Type: application/json');
	$response = ['success' => false, 'message' => 'Error desconocido.'];

	$centroId = filter_input(INPUT_GET, 'centroId', FILTER_VALIDATE_INT);

	if ($centroId) {
		try {
			// Get all active users
			$usuarios = Usuario::get_list($conexion);

			// Get users associated with this cost center
			$centroUsuarios = CentroCosto::get_usuarios_by_centro($centroId, $conexion);

			// Extract IDs of cost center's users
			$centroUsuarioIds = array_map(function($usuario) {
				return $usuario->getId();
			}, $centroUsuarios);

			// Prepare response data
			$usuariosData = array_map(function($usuario) {
				return [
					'id' => $usuario->getId(),
					'nombre' => $usuario->getNombre(),
					'username' => $usuario->getUsername()
				];
			}, $usuarios);

			$response['success']         = true;
			$response['usuarios']        = $usuariosData;
			$response['centro_usuarios'] = $centroUsuarioIds;

		} catch (Exception $e) {
			$response['message'] = "Error al obtener usuarios: " . $e->getMessage();
			http_response_code(500);
		}
	} else {
		$response['message'] = 'Error: ID de centro de costo inválido.';
		http_response_code(400);
	}

	echo json_encode($response);
	exit;
}
#endregion Handle GET Action

#region Handle POST Action - Sync Users for Cost Center
// --- Handle AJAX POST Request (Sync Users for Cost Center) ---
if ($_SERVER['REQUEST_METHOD'] == 'POST' && isset($_POST['action']) && $_POST['action'] == 'sync_usuarios_centro') {
	header('Content-Type: application/json');
	$response = ['success' => false, 'message' => 'Error desconocido.'];

	$centroId     = filter_input(INPUT_POST, 'centroId', FILTER_VALIDATE_INT);
	$usuariosJson = filter_input(INPUT_POST, 'usuarios', FILTER_UNSAFE_RAW);

	if ($centroId && $usuariosJson !== null) {
		try {
			// Decode the JSON array of user IDs
			$usuarioIds = json_decode($usuariosJson, true);

			if (!is_array($usuarioIds)) {
				throw new Exception("Formato de datos inválido para usuarios.");
			}

			// Convert to integers and filter out invalid values
			$usuarioIds = array_filter(array_map('intval', $usuarioIds), function($id) {
				return $id > 0;
			});

			// Sync the associations
			$success = CentroCosto::sync_centro_usuario_associations($centroId, $usuarioIds, $conexion);

			if ($success) {
				$response['success'] = true;
				$response['message'] = 'Usuarios actualizados correctamente.';
			} else {
				$response['message'] = 'Error al sincronizar usuarios.';
				http_response_code(500);
			}

		} catch (Exception $e) {
			$response['message'] = "Error al sincronizar usuarios: " . $e->getMessage();
			http_response_code(500);
		}
	} else {
		$response['message'] = 'Error: Datos de entrada inválidos.';
		http_response_code(400);
	}

	echo json_encode($response);
	exit;
}
#endregion Handle POST Action

#region Handle POST Action - Sync Employees for Cost Center
// --- Handle AJAX POST Request (Sync Employees for Cost Center) ---
if ($_SERVER['REQUEST_METHOD'] == 'POST' && isset($_POST['action']) && $_POST['action'] == 'sync_empleados_centro') {
	header('Content-Type: application/json');
	$response = ['success' => false, 'message' => 'Error desconocido.'];

	$centroId      = filter_input(INPUT_POST, 'centroId', FILTER_VALIDATE_INT);
	$empleadosJson = filter_input(INPUT_POST, 'empleados', FILTER_UNSAFE_RAW);

	if ($centroId && $empleadosJson !== null) {
		try {
			// Decode the JSON array of employee IDs
			$empleadoIds = json_decode($empleadosJson, true);

			if (!is_array($empleadoIds)) {
				throw new Exception("Formato de datos inválido para empleados.");
			}

			// Convert to integers and filter out invalid values
			$empleadoIds = array_filter(array_map('intval', $empleadoIds), function($id) {
				return $id > 0;
			});

			// Sync the associations
			$success = CentroCosto::sync_centro_empleado_associations($centroId, $empleadoIds, $conexion);

			if ($success) {
				$response['success'] = true;
				$response['message'] = 'Empleados actualizados correctamente.';
			} else {
				$response['message'] = 'Error al sincronizar empleados.';
				http_response_code(500);
			}

		} catch (Exception $e) {
			$response['message'] = "Error al sincronizar empleados: " . $e->getMessage();
			http_response_code(500);
		}
	} else {
		$response['message'] = 'Error: Datos de entrada inválidos.';
		http_response_code(400);
	}

	echo json_encode($response);
	exit;
}
#endregion Handle POST Action - Employees

#region Handle GET Action - Get Employees for Cost Center
// --- Handle AJAX GET Request (Get Employees for Cost Center) ---
if ($_SERVER['REQUEST_METHOD'] == 'GET' && isset($_GET['action']) && $_GET['action'] == 'get_empleados_centro') {
	header('Content-Type: application/json');
	$response = ['success' => false, 'message' => 'Error desconocido.'];

	$centroId = filter_input(INPUT_GET, 'centro_id', FILTER_VALIDATE_INT);

	if ($centroId) {
		try {
			// Get all active employees
			$empleados = Empleado::get_list($conexion);

			// Get employees associated with this cost center
			$centroEmpleados = CentroCosto::get_empleados_by_centro($centroId, $conexion);

			// Extract IDs of employees associated with this cost center
			$centroEmpleadoIds = array_map(function($empleado) {
				return $empleado->getId();
			}, $centroEmpleados);

			$response['success'] = true;
			$response['empleados'] = array_map(function($empleado) {
				return [
					'id' => $empleado->getId(),
					'nombre' => $empleado->getNombre(),
					'email' => $empleado->getEmail()
				];
			}, $empleados);
			$response['centro_empleados'] = $centroEmpleadoIds;

		} catch (Exception $e) {
			$response['message'] = "Error al obtener empleados: " . $e->getMessage();
			http_response_code(500);
		}
	} else {
		$response['message'] = 'Error: ID de centro de costo inválido.';
		http_response_code(400);
	}

	echo json_encode($response);
	exit;
}
#endregion Handle GET Action - Employees

#region try
try {
	// Set timezone for any date operations
	date_default_timezone_set('America/Bogota');

	// Get list of active centro costos with user and employee counts
	$centros_costos = CentroCosto::get_list_with_counts($conexion);

	// Get list of active users for cost center management
	$usuarios = Usuario::get_list($conexion);

	// Get list of active employees for cost center management
	$empleados = Empleado::get_list($conexion);
	
} catch (PDOException $e) {
	// Specific handling for database errors
	$error_display = 'show';
	$error_text    = "Error de base de datos al obtener la lista de Centros de Costos.";
} catch (Exception $e) {
	// General error handling
	$error_display = 'show';
	$error_text    = "Ocurrió un error inesperado al obtener la lista de Centros de Costos: " . $e->getMessage();
}
#endregion try

require_once __ROOT__ . '/views/lcentros_costos.view.php';
