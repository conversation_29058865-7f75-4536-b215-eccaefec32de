<?php
/**
 * Controlador para la consulta de órdenes de compra
 *
 * Este controlador maneja la visualización y filtrado de órdenes de compra,
 * incluyendo funcionalidades de búsqueda por proveedor, fecha, centro de costo y número de referencia.
 * También maneja la eliminación (soft delete) de órdenes de compra con validación de inventario.
 */

use App\classes\OrdenCompra;
use App\classes\OrdenCompraDetalle;
use App\classes\Proveedor;
use App\classes\CentroCosto;
use App\classes\Inventario;
use App\classes\InventarioMovimiento;

if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

/** @var PDO $conexion */
global $conexion;

require_once dirname(__FILE__, 2) . '/config/config.php';
require_once __ROOT__ . '/src/sessions/sessions.php';
require_once __ROOT__ . '/src/general/general.php';
require_once __ROOT__ . '/src/general/validaciones.php';
require_once __ROOT__ . '/src/general/preparar.php';

// Check for valid database connection before proceeding
if (!$conexion instanceof PDO) {
    error_log("Error crítico: No hay conexión a la base de datos en lorden_compra.php.");
    $response['message'] = 'Error crítico: No se pudo conectar a la base de datos.';
    http_response_code(503); // Service Unavailable
    echo json_encode($response);
    exit;
}

// Initialize response for AJAX requests
$response = ['success' => false, 'message' => ''];

#region Handle POST Actions
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $action = $_POST['action'] ?? '';

    #region AJAX Search Ordenes Compra
    if ($action === 'search_ordenes_compra') {
        $termino_proveedor = trim($_POST['termino_proveedor'] ?? '');
        $fecha_desde       = trim($_POST['fecha_desde'] ?? '');
        $fecha_hasta       = trim($_POST['fecha_hasta'] ?? '');
        $id_centro_costo   = !empty($_POST['id_centro_costo']) ? (int)$_POST['id_centro_costo'] : null;
        $numero_referencia = trim($_POST['numero_referencia'] ?? '');
        $numero_orden      = trim($_POST['numero_orden'] ?? '');

        try {
            // Get filtered list of ordenes de compra
            $ordenes_filtradas = OrdenCompra::search_ordenes_compra($conexion, $termino_proveedor, $fecha_desde, $fecha_hasta, $id_centro_costo, $numero_referencia, $numero_orden);

            $response['success'] = true;
            $response['ordenes'] = [];

            foreach ($ordenes_filtradas as $orden) {
                $response['ordenes'][] = [
                    'id'                        => $orden->getId(),
                    'fecha'                     => $orden->getFecha(),
                    'fecha_formateada'          => date('Y-m-d', strtotime($orden->getFecha())),
                    'proveedor_nombre'          => htmlspecialchars($orden->getProveedor_nombre() ?? 'Sin proveedor'),
                    'centro_costo_nombre'       => htmlspecialchars($orden->getCentro_costo_nombre()),
                    'n_referencia_proveedor'    => htmlspecialchars($orden->getN_referencia_proveedor() ?? ''),
                    'valor_total'               => $orden->getValor_total(),
                    'valor_total_formateado'    => format_currency_consigno($orden->getValor_total())
                ];
            }

        } catch (Exception $e) {
            $response['message'] = "Error al buscar órdenes de compra: " . $e->getMessage();
            http_response_code(500); // Internal Server Error
        }

        header('Content-Type: application/json');
        echo json_encode($response);
        exit;
    }
    #endregion AJAX Search Ordenes Compra

    #region Delete Orden Compra
    if ($action === 'delete_orden_compra') {
        $ordenId = !empty($_POST['orden_id']) ? (int)$_POST['orden_id'] : 0;

        if ($ordenId > 0) {
            try {
                // Start transaction
                $conexion->beginTransaction();

                // Get the orden de compra first
                $orden = OrdenCompra::get($ordenId, $conexion);

                if ($orden && $orden->isActiva()) {
                    // Get all orden details for inventory validation
                    $orden_detalles = OrdenCompraDetalle::get_by_orden_compra($ordenId, $conexion);

                    // Validate inventory before deletion
                    foreach ($orden_detalles as $detalle) {
                        $inventario = Inventario::get_by_centro_producto($orden->getId_centro_costo(), $detalle->getId_producto(), $conexion);

                        if (!$inventario || $inventario->getCantidad() < $detalle->getCantidad()) {
                            $conexion->rollBack();
                            $response['message'] = 'No se puede eliminar la orden: inventario insuficiente para el producto ' . $detalle->getProducto_descripcion();
                            http_response_code(400);
                            header('Content-Type: application/json');
                            echo json_encode($response);
                            exit;
                        }
                    }

                    // Process inventory corrections and create movements
                    foreach ($orden_detalles as $detalle) {
                        // Update inventory (subtract quantities)
                        $inventario     = Inventario::get_by_centro_producto($orden->getId_centro_costo(), $detalle->getId_producto(), $conexion);
                        $nueva_cantidad = $inventario->getCantidad() - $detalle->getCantidad();
                        $inventario->setCantidad($nueva_cantidad);
                        $inventario->modificar($conexion);

                        // Create inventory movement record
                        $movimiento = new InventarioMovimiento(
                            $orden->getId_centro_costo(),
                            $detalle->getId_producto(),
                            'egreso',
                            $detalle->getCantidad(),
                            $logged_usuario_info->getId(),
                            "Correcion: Orden de compra #" . $orden->getId()
                        );
                        $movimiento->crear($conexion);

                        // Note: OrdenCompraDetalle records are NOT deleted - they remain in the database
                        // Only the parent OrdenCompra is soft deleted (estado = 0)
                    }

                    // Soft delete the orden de compra using the proper eliminar() method
                    $success = $orden->eliminar($conexion);

                    if ($success) {
                        $conexion->commit();
                        $response['success'] = true;
                        $response['message'] = 'Orden de compra eliminada correctamente. Inventario corregido.';
                    } else {
                        $conexion->rollBack();
                        $response['message'] = 'Error: No se pudo eliminar la orden de compra.';
                        http_response_code(500);
                    }
                } else {
                    $conexion->rollBack();
                    $response['message'] = 'Error: No se encontró la orden de compra o ya está inactiva.';
                    http_response_code(404);
                }

            } catch (Exception $e) {
                $conexion->rollBack();
                $response['message'] = "Error al eliminar orden de compra: " . $e->getMessage();
                http_response_code(500);
            }
        } else {
            $response['message'] = 'ID de orden de compra inválido.';
            http_response_code(400);
        }

        header('Content-Type: application/json');
        echo json_encode($response);
        exit;
    }
    #endregion Delete Orden Compra

    #region Get Orden Details
    if ($action === 'get_orden_details') {
        $response = ['success' => false, 'message' => ''];
        $orden_id = !empty($_POST['orden_id']) ? (int)$_POST['orden_id'] : null;

        if (!$orden_id) {
            $response['message'] = 'ID de orden requerido.';
            header('Content-Type: application/json');
            echo json_encode($response);
            exit;
        }

        try {
            // Get order details
            $orden = OrdenCompra::obtener($orden_id, $conexion);
            if (!$orden) {
                $response['message'] = 'Orden de compra no encontrada.';
                header('Content-Type: application/json');
                echo json_encode($response);
                exit;
            }

            // Get order detail items
            $detalles = OrdenCompraDetalle::obtenerPorOrdenCompra($orden_id, $conexion);

            // Format order data
            $orden_data = [
                'id' => $orden->getId(),
                'fecha_formateada' => date('Y-m-d', strtotime($orden->getFecha())),
                'proveedor_nombre' => $orden->getProveedor_nombre(),
                'centro_costo_nombre' => $orden->getCentro_costo_nombre(),
                'usuario_nombre' => $orden->getUsuario_nombre(),
                'n_referencia_proveedor' => $orden->getN_referencia_proveedor(),
                'valor_total_formateado' => '$' . number_format($orden->getValor_total(), 0, ',', '.')
            ];

            // Format details data
            $detalles_data = [];
            foreach ($detalles as $detalle) {
                $detalles_data[] = [
                    'producto_descripcion' => $detalle->getProducto_descripcion(),
                    'cantidad' => $detalle->getCantidad(),
                    'valor_formateado' => '$' . number_format($detalle->getValor(), 0, ',', '.'),
                    'valor_total_formateado' => '$' . number_format($detalle->getValor_total(), 0, ',', '.')
                ];
            }

            $response['success'] = true;
            $response['orden'] = $orden_data;
            $response['detalles'] = $detalles_data;

        } catch (Exception $e) {
            $response['message'] = 'Error al obtener los detalles de la orden: ' . $e->getMessage();
        }

        header('Content-Type: application/json');
        echo json_encode($response);
        exit;
    }
    #endregion Get Orden Details
}
#endregion Handle POST Actions

#region try
try {
    // Set timezone for any date operations
    date_default_timezone_set('America/Bogota');

    // Get list of active proveedores for filter dropdown
    $proveedores = Proveedor::get_list($conexion);

    // Get list of active centros de costo for filter dropdown
    $centros_costos = CentroCosto::get_list($conexion);

    $error_text      = '';
    $error_display   = 'hide';
    $success_text    = '';
    $success_display = 'hide';

} catch (PDOException $e) {
    // Specific handling for database errors
    $error_display = 'show';
    $error_text    = "Error de base de datos al obtener los datos para consulta de órdenes de compra.";
} catch (Exception $e) {
    // General error handling
    $error_display = 'show';
    $error_text    = "Ocurrió un error inesperado: " . $e->getMessage();
}
#endregion try

require_once __ROOT__ . '/views/lorden_compra.view.php';

?>
