<?php

declare(strict_types=1);

// Incluir clases necesarias
use App\classes\Cita;
use App\classes\Empleado;
use App\classes\Puesto;
use App\classes\MetodoPago;
use PhpOffice\PhpSpreadsheet\Spreadsheet;
use PhpOffice\PhpSpreadsheet\Writer\Xlsx;
use PhpOffice\PhpSpreadsheet\Style\Alignment;
use PhpOffice\PhpSpreadsheet\Style\Border;
use PhpOffice\PhpSpreadsheet\Style\Fill;

if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

/** @var PDO $conexion */
global $conexion;

require_once dirname(__FILE__, 2) . '/config/config.php';
require_once __ROOT__ . '/src/sessions/sessions.php';
require_once __ROOT__ . '/src/general/general.php';
require_once __ROOT__ . '/src/general/validaciones.php';
require_once __ROOT__ . '/src/general/preparar.php';

// Check for valid database connection before proceeding
if (!$conexion instanceof PDO) {
    error_log("Error crítico: No hay conexión a la base de datos en lcitas_empleado.php.");
    $response['message'] = 'Error crítico: No se pudo conectar a la base de datos.';
    http_response_code(503); // Service Unavailable
    echo json_encode($response);
    exit;
}

// Configuración de zona horaria
date_default_timezone_set('America/Bogota');

// Initialize response array
$response = [
    'success'        => false,
    'message'        => '',
    'citas'          => [],
    'schema_warning' => false
];

// Initialize display variables
$success_display = null;
$success_text    = null;
$error_display   = null;
$error_text      = null;

// Initialize filter variables
$fecha_inicio = '';
$fecha_fin    = '';
$id_empleado  = '';

try {
    // Get active employees for dropdown
    $empleados = Empleado::get_list($conexion);
} catch (Exception $e) {
    error_log("Error al obtener empleados: " . $e->getMessage());
    $empleados = [];
}

#region POST Request Handling
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    
    // Handle AJAX request for getting cita services
    if (isset($_POST['action']) && $_POST['action'] === 'get_cita_services') {
        $cita_id = filter_input(INPUT_POST, 'cita_id', FILTER_VALIDATE_INT);
        
        if (!$cita_id) {
            $response['message'] = 'ID de cita inválido.';
            http_response_code(400);
            echo json_encode($response);
            exit;
        }
        
        try {
            // Get cita details
            $cita = Cita::get($cita_id, $conexion, true); // Include cancelled citas for historical view
            if (!$cita) {
                $response['message'] = 'Cita no encontrada.';
                http_response_code(404);
                echo json_encode($response);
                exit;
            }
            
            // Get cita services
            $servicios = $cita->getServicios($conexion);
            
            // Calculate total value
            $total_valor = 0;
            foreach ($servicios as $servicio) {
                $total_valor += $servicio->getValor();
            }
            
            // Format total value
            $total_valor_formateado = '$' . number_format($total_valor, 0, ',', '.');
            
            // Prepare cita data for modal
            $cita_data = [
                'id'                      => $cita->getId(),
                'descripcion_puesto'      => $cita->getDescripcion_puesto() ?? 'N/A',
                'nombre_empleado'         => $cita->getNombre_empleado() ?? 'N/A',
                'nombre_metodo_pago'      => $cita->getNombre_metodo_pago() ?? 'N/A',
                'fecha_inicio_formateada' => $cita->getFecha_inicio() ? date('Y-m-d H:i', strtotime($cita->getFecha_inicio())) : 'N/A',
                'fecha_fin_formateada'    => $cita->getFecha_fin() ? date('Y-m-d H:i', strtotime($cita->getFecha_fin())) : 'N/A',
                'estado'                  => $cita->getEstado()
            ];
            
            // Prepare services data
            $servicios_data = [];
            foreach ($servicios as $servicio) {
                $servicios_data[] = [
                    'descripcion'      => $servicio->getDescripcion(),
                    'valor_formateado' => '$' . number_format($servicio->getValor(), 0, ',', '.')
                ];
            }
            
            $response['success']                = true;
            $response['cita']                   = $cita_data;
            $response['servicios']              = $servicios_data;
            $response['total_valor_formateado'] = $total_valor_formateado;
            
        } catch (Exception $e) {
            error_log("Error al obtener servicios de la cita: " . $e->getMessage());
            $response['message'] = 'Error al obtener los servicios de la cita.';
            http_response_code(500);
        }
        
        echo json_encode($response);
        exit;
    }
    
    // Handle search request
    if (isset($_POST['buscar'])) {
        // Get and validate form data
        $fecha_inicio = trim($_POST['fecha_inicio'] ?? '');
        $fecha_fin    = trim($_POST['fecha_fin'] ?? '');
        $id_empleado  = filter_input(INPUT_POST, 'id_empleado', FILTER_VALIDATE_INT);
        
        // Validate required fields
        if (empty($fecha_inicio) || empty($fecha_fin)) {
            $error_display = 'show';
            $error_text    = 'Las fechas de inicio y fin son obligatorias.';
        } elseif (!$id_empleado) {
            $error_display = 'show';
            $error_text    = 'Debe seleccionar un empleado.';
        } elseif (!preg_match('/^\d{4}-\d{2}-\d{2}$/', $fecha_inicio) || !preg_match('/^\d{4}-\d{2}-\d{2}$/', $fecha_fin)) {
            $error_display = 'show';
            $error_text    = 'Las fechas deben tener formato YYYY-MM-DD.';
        } elseif ($fecha_inicio > $fecha_fin) {
            $error_display = 'show';
            $error_text    = 'La fecha de inicio no puede ser mayor que la fecha de fin.';
        } else {
            try {
                // Prepare additional filters for employee
                $filtros_adicionales = [
                    'id_empleado' => $id_empleado
                ];
                
                // Generate query data using the Cita class method with additional filters
                // Note: We pass null for id_centro_costo since this is employee-specific
                $consulta_data = Cita::get_historico_by_fecha_fin_rango($fecha_inicio, $fecha_fin, null, $conexion, true, $filtros_adicionales);
                
                if (empty($consulta_data['citas'])) {
                    $error_display = 'show';
                    $error_text    = 'No se encontraron citas completadas para el empleado y rango de fechas seleccionados.';
                }
                
            } catch (Exception $e) {
                error_log("Error en consulta de citas por empleado: " . $e->getMessage());
                $error_display = 'show';
                $error_text    = 'Error al consultar las citas. Por favor, inténtelo de nuevo.';
            }
        }
    }
    
    // Handle AJAX search request
    if (isset($_POST['action']) && $_POST['action'] === 'buscar_citas') {
        // Get and validate form data
        $fecha_inicio = trim($_POST['fecha_inicio'] ?? '');
        $fecha_fin    = trim($_POST['fecha_fin'] ?? '');
        $id_empleado  = filter_input(INPUT_POST, 'id_empleado', FILTER_VALIDATE_INT);
        
        // Validate required fields
        if (empty($fecha_inicio) || empty($fecha_fin)) {
            $response['message'] = 'Las fechas de inicio y fin son obligatorias.';
            http_response_code(400);
            echo json_encode($response);
            exit;
        }
        
        if (!$id_empleado) {
            $response['message'] = 'Debe seleccionar un empleado.';
            http_response_code(400);
            echo json_encode($response);
            exit;
        }
        
        // Validate date format
        if (!preg_match('/^\d{4}-\d{2}-\d{2}$/', $fecha_inicio) || !preg_match('/^\d{4}-\d{2}-\d{2}$/', $fecha_fin)) {
            $response['message'] = 'Las fechas deben tener formato YYYY-MM-DD.';
            http_response_code(400);
            echo json_encode($response);
            exit;
        }
        
        // Validate date range
        if ($fecha_inicio > $fecha_fin) {
            $response['message'] = 'La fecha de inicio no puede ser mayor que la fecha de fin.';
            http_response_code(400);
            echo json_encode($response);
            exit;
        }
        
        try {
            // Prepare additional filters for employee
            $filtros_adicionales = [
                'id_empleado' => $id_empleado
            ];
            
            // Generate query data using the Cita class method with additional filters
            $consulta_data = Cita::get_historico_by_fecha_fin_rango($fecha_inicio, $fecha_fin, null, $conexion, true, $filtros_adicionales);
            
            if (!empty($consulta_data['citas'])) {
                // The get_historico_by_fecha_fin_rango method returns formatted data, not Cita objects
                $response['success'] = true;
                $response['citas']   = $consulta_data['citas'];
            } else {
                $response['message'] = 'No se encontraron citas completadas para el empleado y rango de fechas seleccionados.';
            }
            
        } catch (Exception $e) {
            error_log("Error en consulta AJAX de citas por empleado: " . $e->getMessage());
            $response['message'] = 'Error al consultar las citas. Por favor, inténtelo de nuevo.';
            http_response_code(500);
        }
        
        echo json_encode($response);
        exit;
    }

    #region Export Excel
    if (isset($_POST['action']) && $_POST['action'] === 'exportar_excel') {
        $fecha_inicio = trim($_POST['fecha_inicio'] ?? '');
        $fecha_fin    = trim($_POST['fecha_fin'] ?? '');
        $id_empleado  = filter_input(INPUT_POST, 'id_empleado', FILTER_VALIDATE_INT);

        // Validaciones
        if (empty($fecha_inicio) || empty($fecha_fin) || !$id_empleado) {
            $error_display = 'show';
            $error_text    = 'Los campos fecha de inicio, fecha de fin y empleado son obligatorios para exportar.';
        } else {
            try {
                // Validar formato de fechas
                if (!DateTime::createFromFormat('Y-m-d', $fecha_inicio) || !DateTime::createFromFormat('Y-m-d', $fecha_fin)) {
                    throw new Exception("Las fechas deben tener el formato YYYY-MM-DD.");
                }

                // Validar que fecha inicio <= fecha fin
                if ($fecha_inicio > $fecha_fin) {
                    throw new Exception("La fecha de inicio no puede ser mayor que la fecha de fin.");
                }

                // Validar que el empleado existe
                $empleado = Empleado::get($id_empleado, $conexion);
                if (!$empleado) {
                    throw new Exception("El empleado seleccionado no existe.");
                }
                $empleado_nombre = $empleado->getNombre();

                // Preparar filtros adicionales para empleado
                $filtros_adicionales = [
                    'id_empleado' => $id_empleado
                ];

                // Generar datos de la consulta usando el mismo método que la búsqueda
                $consulta_data = Cita::get_historico_by_fecha_fin_rango($fecha_inicio, $fecha_fin, null, $conexion, true, $filtros_adicionales);

                // Log para debugging
                error_log("Excel Export - Datos obtenidos: " . count($consulta_data['citas'] ?? []) . " citas");

                if (empty($consulta_data['citas'])) {
                    $error_display = 'show';
                    $error_text    = 'No se encontraron datos para exportar en el rango de fechas y empleado seleccionados.';
                } else {
                    // Limpiar cualquier salida previa antes de generar Excel
                    while (ob_get_level()) {
                        ob_end_clean();
                    }

                    // Generar y descargar el archivo Excel
                    generarExcelCitasEmpleado($consulta_data, $fecha_inicio, $fecha_fin, $empleado_nombre);
                    exit; // Terminar la ejecución después de la descarga
                }

            } catch (Exception $e) {
                $error_display = 'show';
                $error_text    = 'Error al exportar el reporte: ' . $e->getMessage();
            }
        }
    }
    #endregion Export Excel
}
#endregion POST Request Handling

/**
 * Función para generar archivo Excel de citas por empleado
 */
function generarExcelCitasEmpleado(array $consulta_data, string $fecha_inicio, string $fecha_fin, string $empleado_nombre): void
{
    try {
        // Log para debugging
        error_log("Iniciando generación de Excel con " . count($consulta_data['citas']) . " citas");

        // Crear nuevo spreadsheet
        $spreadsheet = new Spreadsheet();
        $sheet = $spreadsheet->getActiveSheet();

        // Configurar propiedades del documento
        $spreadsheet->getProperties()
            ->setCreator('Sistema de Gestión de Barbería')
            ->setTitle('Consulta de Citas por Empleado')
            ->setSubject('Consulta de Citas por Empleado')
            ->setDescription('Reporte detallado de citas completadas por empleado específico');

        // Configurar zona horaria
        date_default_timezone_set('America/Bogota');

        // Variables para el diseño
        $row = 1;

        // METADATA DEL REPORTE
        $sheet->setCellValue('A' . $row, 'CONSULTA DE CITAS POR EMPLEADO');
        $sheet->mergeCells('A' . $row . ':H' . $row);
        $sheet->getStyle('A' . $row)->getFont()->setBold(true)->setSize(16);
        $sheet->getStyle('A' . $row)->getAlignment()->setHorizontal(Alignment::HORIZONTAL_CENTER);
        $row++;

        // Header section with borders - split into field name and value columns
        $headerStartRow = $row;

        // Fecha de generación
        $sheet->setCellValue('A' . $row, 'Fecha de generación:');
        $sheet->setCellValue('B' . $row, date('Y-m-d H:i:s'));
        $sheet->mergeCells('B' . $row . ':F' . $row); // Merge B, C, D, E, F columns for value
        $sheet->getStyle('A' . $row)->getFont()->setBold(true);
        $sheet->getStyle('B' . $row)->getFont()->setBold(true);
        $row++;

        // Empleado
        $sheet->setCellValue('A' . $row, 'Empleado:');
        $sheet->setCellValue('B' . $row, $empleado_nombre);
        $sheet->mergeCells('B' . $row . ':F' . $row); // Merge B, C, D, E, F columns for value
        $sheet->getStyle('A' . $row)->getFont()->setBold(true);
        $sheet->getStyle('B' . $row)->getFont()->setBold(true);
        $row++;

        // Período
        $sheet->setCellValue('A' . $row, 'Período:');
        $sheet->setCellValue('B' . $row, date('Y-m-d', strtotime($fecha_inicio)) . ' - ' . date('Y-m-d', strtotime($fecha_fin)));
        $sheet->mergeCells('B' . $row . ':F' . $row); // Merge B, C, D, E, F columns for value
        $sheet->getStyle('A' . $row)->getFont()->setBold(true);
        $sheet->getStyle('B' . $row)->getFont()->setBold(true);

        // Apply borders to header section (columns A through F to cover merged cells)
        $headerEndRow = $row;
        $sheet->getStyle('A' . $headerStartRow . ':F' . $headerEndRow)->getBorders()->getAllBorders()->setBorderStyle(Border::BORDER_THIN);

        $row += 2; // One blank row for spacing (no borders, no background)

        // ENCABEZADOS DE LA TABLA
        $dataTableStartRow = $row; // Store the start row for data table borders
        $headers = ['Centro Costo', 'Puesto', 'Método Pago', 'Fecha Inicio', 'Fecha Fin', 'Estado', 'Total', 'Comisión'];
        $columns = ['A', 'B', 'C', 'D', 'E', 'F', 'G', 'H'];

        foreach ($headers as $index => $header) {
            $sheet->setCellValue($columns[$index] . $row, $header);
            $sheet->getStyle($columns[$index] . $row)->getFont()->setBold(true);
            $sheet->getStyle($columns[$index] . $row)->getFill()->setFillType(Fill::FILL_SOLID)->getStartColor()->setRGB('4472C4');
            $sheet->getStyle($columns[$index] . $row)->getFont()->getColor()->setRGB('FFFFFF');
            $sheet->getStyle($columns[$index] . $row)->getAlignment()->setHorizontal(Alignment::HORIZONTAL_CENTER);
        }
        $row++;

        // DATOS DE LAS CITAS
        $total_general = 0;
        $total_comision = 0;
        foreach ($consulta_data['citas'] as $cita) {
            $sheet->setCellValue('A' . $row, $cita['nombre_centro_costo'] ?? 'N/A');
            $sheet->setCellValue('B' . $row, $cita['descripcion_puesto'] ?? 'N/A');
            $sheet->setCellValue('C' . $row, $cita['nombre_metodo_pago'] ?? 'N/A');

            // Formatear fechas
            $fecha_inicio_formateada = $cita['fecha_inicio'] ? date('Y-m-d H:i', strtotime($cita['fecha_inicio'])) : 'N/A';
            $fecha_fin_formateada = $cita['fecha_fin'] ? date('Y-m-d H:i', strtotime($cita['fecha_fin'])) : 'N/A';

            $sheet->setCellValue('D' . $row, $fecha_inicio_formateada);
            $sheet->setCellValue('E' . $row, $fecha_fin_formateada);

            // Estado
            $estado_texto = $cita['estado'] == 1 ? 'Finalizada' : 'Cancelada';
            if ($cita['estado'] != 1 && !empty($cita['razon_cancelacion'])) {
                $estado_texto .= ' (' . $cita['razon_cancelacion'] . ')';
            }
            $sheet->setCellValue('F' . $row, $estado_texto);

            // Total
            $total_valor = (float)$cita['total_valor_servicios'];
            $sheet->setCellValue('G' . $row, $total_valor);
            $sheet->getStyle('G' . $row)->getNumberFormat()->setFormatCode('#,##0');
            $sheet->getStyle('G' . $row)->getAlignment()->setHorizontal(Alignment::HORIZONTAL_RIGHT);

            // Comisión
            $comision_valor = (float)($cita['valor_comision_empleado'] ?? 0);
            $sheet->setCellValue('H' . $row, $comision_valor);
            $sheet->getStyle('H' . $row)->getNumberFormat()->setFormatCode('#,##0');
            $sheet->getStyle('H' . $row)->getAlignment()->setHorizontal(Alignment::HORIZONTAL_RIGHT);

            $total_general += $total_valor;
            $total_comision += $comision_valor;
            $row++;
        }

        // TOTALES
        $row++; // Espacio adicional
        $sheet->setCellValue('A' . $row, 'Totales:');
        $sheet->mergeCells('A' . $row . ':F' . $row);
        $sheet->getStyle('A' . $row . ':F' . $row)->getFont()->setBold(true);
        $sheet->getStyle('A' . $row . ':F' . $row)->getFill()->setFillType(Fill::FILL_SOLID)->getStartColor()->setRGB('D9E2F3');
        $sheet->getStyle('A' . $row)->getAlignment()->setHorizontal(Alignment::HORIZONTAL_LEFT);

        // Total general
        $sheet->setCellValue('G' . $row, $total_general);
        $sheet->getStyle('G' . $row)->getNumberFormat()->setFormatCode('#,##0');
        $sheet->getStyle('G' . $row)->getAlignment()->setHorizontal(Alignment::HORIZONTAL_RIGHT);
        $sheet->getStyle('G' . $row)->getFont()->setBold(true);
        $sheet->getStyle('G' . $row)->getFill()->setFillType(Fill::FILL_SOLID)->getStartColor()->setRGB('D9E2F3');

        // Total comisión
        $sheet->setCellValue('H' . $row, $total_comision);
        $sheet->getStyle('H' . $row)->getNumberFormat()->setFormatCode('#,##0');
        $sheet->getStyle('H' . $row)->getAlignment()->setHorizontal(Alignment::HORIZONTAL_RIGHT);
        $sheet->getStyle('H' . $row)->getFont()->setBold(true);
        $sheet->getStyle('H' . $row)->getFill()->setFillType(Fill::FILL_SOLID)->getStartColor()->setRGB('D9E2F3');

        // Ajustar ancho de columnas
        $sheet->getColumnDimension('A')->setWidth(18); // Centro Costo
        $sheet->getColumnDimension('B')->setWidth(20); // Puesto
        $sheet->getColumnDimension('C')->setWidth(18); // Método Pago
        $sheet->getColumnDimension('D')->setWidth(18); // Fecha Inicio
        $sheet->getColumnDimension('E')->setWidth(18); // Fecha Fin
        $sheet->getColumnDimension('F')->setWidth(15); // Estado
        $sheet->getColumnDimension('G')->setWidth(15); // Total
        $sheet->getColumnDimension('H')->setWidth(15); // Comisión

        // Agregar bordes a toda la tabla (excluding the blank row between header and data)
        $sheet->getStyle('A' . $dataTableStartRow . ':H' . $row)->getBorders()->getAllBorders()->setBorderStyle(Border::BORDER_THIN);

        // Configurar nombre del archivo
        $filename = 'Consulta_Citas_Empleado_' . date('Y-m-d_H-i-s') . '.xlsx';

        // Configurar headers para descarga
        header('Content-Type: application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
        header('Content-Disposition: attachment;filename="' . $filename . '"');
        header('Cache-Control: max-age=0');
        header('Cache-Control: max-age=1');
        header('Expires: Mon, 26 Jul 1997 05:00:00 GMT');
        header('Last-Modified: ' . gmdate('D, d M Y H:i:s') . ' GMT');
        header('Cache-Control: cache, must-revalidate');
        header('Pragma: public');

        // Crear writer y enviar archivo
        $writer = new Xlsx($spreadsheet);
        $writer->save('php://output');

    } catch (Exception $e) {
        // Log the error for debugging
        error_log("Error en generarExcelCitasEmpleado: " . $e->getMessage());
        error_log("Stack trace: " . $e->getTraceAsString());
        throw new Exception("Error al generar el archivo Excel: " . $e->getMessage());
    }
}

// Include the view
require_once __ROOT__ . '/views/lcitas_empleado.view.php';
