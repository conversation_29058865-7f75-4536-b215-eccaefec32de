<?php

// Iniciar sesión si es necesario
use App\classes\Cliente;

if (session_status() === PHP_SESSION_NONE) {
	session_start();
}

/** @var PDO $conexion */
global $conexion;

require_once dirname(__FILE__, 2) . '/config/config.php';
require_once __ROOT__ . '/src/sessions/sessions.php';
require_once __ROOT__ . '/src/general/general.php';
require_once __ROOT__ . '/src/general/validaciones.php';
require_once __ROOT__ . '/src/general/preparar.php';

// Check for valid database connection before proceeding
if (!$conexion instanceof PDO) {
	error_log("Error crítico: No hay conexión a la base de datos en lclientes.php.");
	$response['message'] = 'Error crítico: No se pudo conectar a la base de datos.';
	http_response_code(503); // Service Unavailable
	echo json_encode($response);
	exit;
}

#region init variables
$clientes = []; // Initialize as an empty array
#endregion init variables

#region Handle Flash Message Success
// Check for a success flash message from session
if (!empty($_SESSION['flash_message_success'])) {
	$success_text    = $_SESSION['flash_message_success'];
	$success_display = 'show';
	// Clear the flash message so it doesn't show again
	unset($_SESSION['flash_message_success']);
}
#endregion Handle Flash Message Success

#region Handle Flash Message Error
// Check for an error flash message from session
if (!empty($_SESSION['flash_message_error'])) {
	$error_text    = $_SESSION['flash_message_error'];
	$error_display = 'show';
	// Clear the flash message so it doesn't show again
	unset($_SESSION['flash_message_error']);
}
#endregion Handle Flash Message Error

#region Create Cliente
// --- Handle AJAX Request (Create Cliente) ---
if ($_SERVER['REQUEST_METHOD'] == 'POST' && isset($_POST['action']) && $_POST['action'] == 'crear') {
	header('Content-Type: application/json');                            // Set response header for JSON
	$response = ['success' => false, 'message' => 'Error desconocido.']; // Default error response

	$nombre  = filter_input(INPUT_POST, 'nombre', FILTER_SANITIZE_SPECIAL_CHARS); // Sanitize name
	$nombre  = trim($nombre ?? '');                                               // Trim whitespace
	$celular = filter_input(INPUT_POST, 'celular', FILTER_SANITIZE_SPECIAL_CHARS); // Sanitize celular
	$celular = trim($celular ?? '');                                              // Trim whitespace

	if (!empty($nombre) && !empty($celular)) {
		try {
			$cliente = new Cliente();
			$cliente->setNombre($nombre);
			$cliente->setCelular($celular);

			$newId = $cliente->crear($conexion);

			if ($newId) {
				$response['success'] = true;
				$response['message'] = 'Cliente creado correctamente.';
				$response['id']      = $newId;
				$response['nombre']  = $nombre;
				$response['celular'] = $celular;
				$response['estado']  = 1; // Active by default
			} else {
				$response['message'] = 'Error: No se pudo crear el Cliente.';
				http_response_code(500); // Internal Server Error
			}
		} catch (Exception $e) {
			$response['message'] = "Error al crear Cliente: " . $e->getMessage();
			http_response_code(500); // Internal Server Error
		}
	} else if (empty($nombre)) {
		$response['message'] = 'Error: El nombre no puede estar vacío.';
		http_response_code(400); // Bad Request
	} else {
		$response['message'] = 'Error: El celular no puede estar vacío.';
		http_response_code(400); // Bad Request
	}

	echo json_encode($response);
	exit; // IMPORTANT: Stop script execution after sending JSON response for AJAX
}
#endregion Create Cliente

#region Modify Cliente
// --- Handle AJAX Request (Modify Cliente) ---
if ($_SERVER['REQUEST_METHOD'] == 'POST' && isset($_POST['action']) && $_POST['action'] == 'modificar') {
	header('Content-Type: application/json');                            // Set response header for JSON
	$response = ['success' => false, 'message' => 'Error desconocido.']; // Default error response

	$clienteId    = filter_input(INPUT_POST, 'clienteId', FILTER_VALIDATE_INT);
	$nuevoNombre  = filter_input(INPUT_POST, 'nombre', FILTER_SANITIZE_SPECIAL_CHARS); // Sanitize name
	$nuevoNombre  = trim($nuevoNombre ?? '');                                          // Trim whitespace
	$nuevoCelular = filter_input(INPUT_POST, 'celular', FILTER_SANITIZE_SPECIAL_CHARS); // Sanitize celular
	$nuevoCelular = trim($nuevoCelular ?? '');                                         // Trim whitespace

	if ($clienteId && !empty($nuevoNombre) && !empty($nuevoCelular)) {
		try {
			// Get the cliente first
			$cliente = Cliente::get($clienteId, $conexion);

			if ($cliente) {
				$cliente->setNombre($nuevoNombre);
				$cliente->setCelular($nuevoCelular);
				$success = $cliente->modificar($conexion);

				if ($success) {
					$response['success'] = true;
					$response['message'] = 'Cliente actualizado correctamente.';
				} else {
					$response['message'] = 'Error: No se pudo actualizar el Cliente.';
					http_response_code(500); // Internal Server Error
				}
			} else {
				$response['message'] = 'Error: No se encontró el Cliente.';
				http_response_code(404); // Not Found
			}
		} catch (Exception $e) {
			$response['message'] = "Error al modificar Cliente: " . $e->getMessage();
			http_response_code(500); // Internal Server Error
		}
	} else if (!$clienteId) {
		$response['message'] = 'Error: ID de Cliente inválido.';
		http_response_code(400); // Bad Request
	} else if (empty($nuevoNombre)) {
		$response['message'] = 'Error: El nombre no puede estar vacío.';
		http_response_code(400); // Bad Request
	} else {
		$response['message'] = 'Error: El celular no puede estar vacío.';
		http_response_code(400); // Bad Request
	}

	echo json_encode($response);
	exit; // IMPORTANT: Stop script execution after sending JSON response for AJAX
}
#endregion Modify Cliente

#region Search Clientes
// --- Handle AJAX Request (Search Clientes) ---
if ($_SERVER['REQUEST_METHOD'] == 'POST' && isset($_POST['action']) && $_POST['action'] == 'buscar') {
	header('Content-Type: application/json');                            // Set response header for JSON
	$response = ['success' => false, 'message' => 'Error desconocido.']; // Default error response

	$termino = filter_input(INPUT_POST, 'termino', FILTER_SANITIZE_SPECIAL_CHARS); // Sanitize search term
	$termino = trim($termino ?? '');                                               // Trim whitespace

	try {
		if (!empty($termino)) {
			$clientes = Cliente::buscar($termino, $conexion);
		} else {
			$clientes = Cliente::get_list($conexion);
		}

		$response['success'] = true;
		$response['message'] = 'Búsqueda realizada correctamente.';
		$response['clientes'] = [];

		foreach ($clientes as $cliente) {
			$response['clientes'][] = [
				'id'      => $cliente->getId(),
				'nombre'  => $cliente->getNombre(),
				'celular' => $cliente->getCelular(),
				'estado'  => $cliente->getEstado()
			];
		}

	} catch (Exception $e) {
		$response['message'] = "Error al buscar clientes: " . $e->getMessage();
		http_response_code(500); // Internal Server Error
	}

	echo json_encode($response);
	exit; // IMPORTANT: Stop script execution after sending JSON response for AJAX
}
#endregion Search Clientes

#region Handle POST Actions (Deactivation)
// --- Handle Non-AJAX POST Action (Deactivate Cliente) ---
if ($_SERVER['REQUEST_METHOD'] == 'POST' && isset($_POST['action']) && $_POST['action'] == 'desactivar') {
	$clienteIdToDeactivate = filter_input(INPUT_POST, 'clienteId', FILTER_VALIDATE_INT);

	if ($clienteIdToDeactivate) {
		try {
			$success = Cliente::desactivar($clienteIdToDeactivate, $conexion);

			if ($success) {
				$_SESSION['flash_message_success'] = "Cliente desactivado correctamente.";
			} else {
				$_SESSION['flash_message_error'] = "Error: No se pudo encontrar o desactivar el Cliente.";
			}
		} catch (Exception $e) {
			$_SESSION['flash_message_error'] = "Error al desactivar Cliente: " . $e->getMessage();
		}
	} else {
		$_SESSION['flash_message_error'] = "Error: ID de Cliente inválido para desactivar.";
	}

	// Redirect back to the cliente list page after processing
	header('Location: clientes');
	exit;
}
#endregion Handle POST Actions

#region try
try {
	// Set timezone for any date operations
	date_default_timezone_set('America/Bogota');

	// Get list of active clients
	$clientes = Cliente::get_list($conexion);

} catch (PDOException $e) {
	// Specific handling for database errors
	$error_display = 'show';
	$error_text    = "Error de base de datos al obtener la lista de Clientes.";
} catch (Exception $e) {
	// General error handling
	$error_display = 'show';
	$error_text    = "Ocurrió un error inesperado al obtener la lista de Clientes: " . $e->getMessage();
}
#endregion try

require_once __ROOT__ . '/views/lclientes.view.php';

?>
