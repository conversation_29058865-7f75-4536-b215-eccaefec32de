<?php

// Iniciar sesión si es necesario
use App\classes\Empleado;

if (session_status() === PHP_SESSION_NONE) {
	session_start();
}

/** @var PDO $conexion */
global $conexion;

require_once dirname(__FILE__, 2) . '/config/config.php';
require_once __ROOT__ . '/src/sessions/sessions.php';
require_once __ROOT__ . '/src/general/general.php';
require_once __ROOT__ . '/src/general/validaciones.php';
require_once __ROOT__ . '/src/general/preparar.php';

// Check for valid database connection before proceeding
if (!$conexion instanceof PDO) {
	error_log("Error crítico: No hay conexión a la base de datos en lempleados.php (POST).");
	$response['message'] = 'Error crítico: No se pudo conectar a la base de datos.';
	http_response_code(503); // Service Unavailable
	echo json_encode($response);
	exit;
}

#region region init variables
$empleados = []; // Initialize as an empty array
#endregion init variables

#region region Handle Flash Message Success
// Check for a success flash message from session
if (!empty($_SESSION['flash_message_success'])) {
	$success_text    = $_SESSION['flash_message_success'];
	$success_display = 'show';
	// Clear the flash message so it doesn't show again
	unset($_SESSION['flash_message_success']);
}
#endregion Handle Flash Message Success

#region region Handle Flash Message Error
// Check for an error flash message from session
if (!empty($_SESSION['flash_message_error'])) {
	$error_text    = $_SESSION['flash_message_error'];
	$error_display = 'show';
	// Clear the flash message so it doesn't show again
	unset($_SESSION['flash_message_error']);
}
#endregion Handle Flash Message Error



#region region Handle POST Actions (Deactivation)
// --- Handle Non-AJAX POST Action (Deactivate Employee) ---
if ($_SERVER['REQUEST_METHOD'] == 'POST' && isset($_POST['action']) && $_POST['action'] == 'desactivar') {
	$empleadoIdToDeactivate = filter_input(INPUT_POST, 'empleadoId', FILTER_VALIDATE_INT);

	if ($empleadoIdToDeactivate) {
		try {
			$success = Empleado::desactivar($empleadoIdToDeactivate, $conexion);

			if ($success) {
				$_SESSION['flash_message_success'] = "Empleado desactivado correctamente.";
			} else {
				// This case might happen if the ID doesn't exist, though desactivar doesn't explicitly check
				$_SESSION['flash_message_error'] = "Error: No se pudo encontrar o desactivar el empleado.";
				// Consider setting an error type for the flash message if your system supports it
			}
		} catch (Exception $e) {
			// Log the detailed error: error_log("Error desactivando empleado ID $empleadoIdToDeactivate: " . $e->getMessage());
			$_SESSION['flash_message_error'] = "Error al desactivar empleado: " . $e->getMessage();
			// Consider setting an error type for the flash message
		}
	} else {
		$_SESSION['flash_message_error'] = "Error: ID de empleado inválido para desactivar.";
		// Consider setting an error type
	}

	// Redirect back to the employee list page after processing
	header('Location: lempleados');
	exit;
}
#endregion Handle POST Actions

#region try
try {
	$empleados = Empleado::get_list($conexion);

} catch (PDOException $e) {
	// Specific handling for database errors
	// Log the error: error_log("Database error fetching employees: " . $e->getMessage());
	$error_display = 'show';
	$error_text    = "Error de base de datos al obtener la lista de empleados.";
} catch (Exception $e) {
	// General error handling
	// Log the error: error_log("Error fetching employees: " . $e->getMessage());
	$error_display = 'show';
	$error_text    = "Ocurrió un error inesperado al obtener la lista de empleados: " . $e->getMessage();
}
#endregion try

require_once __ROOT__ . '/views/lempleados.view.php';

?>
