<?php
/**
 * Controlador para la consulta de ventas
 * 
 * Este controlador maneja la visualización y filtrado de ventas,
 * incluyendo funcionalidades de búsqueda por cliente, fecha y centro de costo.
 * También maneja la eliminación (soft delete) de ventas con restauración de inventario.
 */



use App\classes\Venta;
use App\classes\VentaDetalle;
use App\classes\Cliente;
use App\classes\CentroCosto;
use App\classes\MetodoPago;
use App\classes\Inventario;
use App\classes\InventarioMovimiento;

if (session_status() === PHP_SESSION_NONE) {
	session_start();
}

/** @var PDO $conexion */
global $conexion;

require_once dirname(__FILE__, 2) . '/config/config.php';
require_once __ROOT__ . '/src/sessions/sessions.php';
require_once __ROOT__ . '/src/general/general.php';
require_once __ROOT__ . '/src/general/validaciones.php';
require_once __ROOT__ . '/src/general/preparar.php';

// Check for valid database connection before proceeding
if (!$conexion instanceof PDO) {
    error_log("Error crítico: No hay conexión a la base de datos en lventas.php.");
    $response['message'] = 'Error crítico: No se pudo conectar a la base de datos.';
    http_response_code(503); // Service Unavailable
    echo json_encode($response);
    exit;
}

// Initialize response for AJAX requests
$response = ['success' => false, 'message' => ''];

#region Handle POST Actions
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $action = $_POST['action'] ?? '';

    #region AJAX Search Ventas
    if ($action === 'search_ventas') {
        $termino_cliente = trim($_POST['termino_cliente'] ?? '');
        $fecha_desde     = trim($_POST['fecha_desde'] ?? '');
        $fecha_hasta     = trim($_POST['fecha_hasta'] ?? '');
        $id_centro_costo = !empty($_POST['id_centro_costo']) ? (int)$_POST['id_centro_costo'] : null;
        $id_venta        = !empty($_POST['id_venta']) ? (int)$_POST['id_venta'] : null;

        try {
            // Get filtered list of ventas
            $ventas_filtradas = Venta::search_ventas($conexion, $termino_cliente, $fecha_desde, $fecha_hasta, $id_centro_costo, $id_venta);

            $response['success'] = true;
            $response['ventas'] = [];

            foreach ($ventas_filtradas as $venta) {
                $response['ventas'][] = [
                    'id'                     => $venta->getId(),
                    'fecha'                  => $venta->getFecha(),
                    'fecha_formateada'       => date('Y-m-d', strtotime($venta->getFecha())),
                    'cliente_nombre'         => $venta->getCliente_nombre() ?? 'Sin cliente',
                    'centro_costo_nombre'    => $venta->getCentro_costo_nombre(),
                    'metodo_pago_nombre'     => $venta->getMetodo_pago_nombre(),
                    'valor_total'            => $venta->getValor_total(),
                    'valor_total_formateado' => format_currency_consigno($venta->getValor_total()),
                    'estado'                 => $venta->getEstado(),
                    'is_activo'              => $venta->isActivo()
                ];
            }

        } catch (Exception $e) {
            $response['message'] = "Error al buscar ventas: " . $e->getMessage();
            http_response_code(500); // Internal Server Error
        }

        header('Content-Type: application/json');
        echo json_encode($response);
        exit;
    }
    #endregion AJAX Search Ventas

    #region Delete Venta
    if ($action === 'delete_venta') {
        $ventaId = !empty($_POST['venta_id']) ? (int)$_POST['venta_id'] : 0;

        if ($ventaId > 0) {
            try {
                // Start transaction
                $conexion->beginTransaction();

                // Get the venta first
                $venta = Venta::get($ventaId, $conexion);

                if ($venta && $venta->isActivo()) {
                    // Get all venta details for inventory restoration
                    $venta_detalles = VentaDetalle::get_by_venta($ventaId, $conexion);

                    // Restore inventory for each detail
                    foreach ($venta_detalles as $detalle) {
                        // Update inventory: add back the sold quantity
                        $inventario = Inventario::get_by_centro_producto($venta->getId_centro_costo(), $detalle->getId_producto(), $conexion);
                        
                        if ($inventario) {
                            $nueva_cantidad = $inventario->getCantidad() + $detalle->getCantidad();
                            $inventario->setCantidad($nueva_cantidad);
                            $inventario->modificar($conexion);
                        }

                        // Create corrective inventory movement
                        $movimiento = new InventarioMovimiento(
                            $venta->getId_centro_costo(),
                            $detalle->getId_producto(),
                            'ingreso',
                            $detalle->getCantidad(),
                            $logged_usuario_info->getId(),
                            "Corrección: Venta #" . $venta->getId()
                        );
                        $movimiento->crear($conexion);
                    }

                    // Soft delete the venta
                    $venta->setEstado(0);
                    $success = $venta->modificar($conexion);

                    if ($success) {
                        $conexion->commit();
                        $response['success'] = true;
                        $response['message'] = 'Venta eliminada correctamente. Inventario restaurado.';
                    } else {
                        $conexion->rollBack();
                        $response['message'] = 'Error: No se pudo eliminar la venta.';
                        http_response_code(500);
                    }
                } else {
                    $conexion->rollBack();
                    $response['message'] = 'Error: No se encontró la venta o ya está inactiva.';
                    http_response_code(404);
                }

            } catch (Exception $e) {
                $conexion->rollBack();
                $response['message'] = "Error al eliminar venta: " . $e->getMessage();
                http_response_code(500);
            }
        } else {
            $response['message'] = 'ID de venta inválido.';
            http_response_code(400);
        }

        header('Content-Type: application/json');
        echo json_encode($response);
        exit;
    }
    #endregion Delete Venta

    #region Get Venta Details
    if ($action === 'get_venta_details') {
        $ventaId = !empty($_POST['venta_id']) ? (int)$_POST['venta_id'] : 0;

        if ($ventaId > 0) {
            try {
                // Get the venta header information
                $venta = Venta::get($ventaId, $conexion);

                if (!$venta) {
                    $response['message'] = 'Venta no encontrada.';
                    http_response_code(404);
                } else {
                    // Get the venta details
                    $venta_detalles = VentaDetalle::get_by_venta($ventaId, $conexion);

                    // Format venta header data
                    $venta_data = [
                        'id'                     => $venta->getId(),
                        'fecha'                  => $venta->getFecha(),
                        'fecha_formateada'       => date('Y-m-d H:i:s', strtotime($venta->getFecha())),
                        'cliente_nombre'         => $venta->getCliente_nombre(),
                        'centro_costo_nombre'    => $venta->getCentro_costo_nombre(),
                        'metodo_pago_nombre'     => $venta->getMetodo_pago_nombre(),
                        'valor_total'            => $venta->getValor_total(),
                        'valor_total_formateado' => format_currency_consigno($venta->getValor_total()),
                        'usuario_nombre'         => 'Sistema' // TODO: Add user tracking to ventas table
                    ];

                    // Format details data
                    $detalles_data = [];
                    foreach ($venta_detalles as $detalle) {
                        $detalles_data[] = [
                            'id'                     => $detalle->getId(),
                            'id_producto'            => $detalle->getId_producto(),
                            'producto_descripcion'   => $detalle->getProducto_descripcion(),
                            'cantidad'               => $detalle->getCantidad(),
                            'valor'                  => $detalle->getValor(),
                            'valor_formateado'       => $detalle->getValorFormateado(),
                            'valor_total'            => $detalle->getValor_total(),
                            'valor_total_formateado' => $detalle->getValorTotalFormateado()
                        ];
                    }

                    $response['success'] = true;
                    $response['venta'] = $venta_data;
                    $response['detalles'] = $detalles_data;
                }

            } catch (Exception $e) {
                $response['message'] = "Error al obtener detalles de venta: " . $e->getMessage();
                http_response_code(500);
            }
        } else {
            $response['message'] = 'ID de venta inválido.';
            http_response_code(400);
        }

        header('Content-Type: application/json');
        echo json_encode($response);
        exit;
    }
    #endregion Get Venta Details

    #region Get Ventas Agrupadas por Método de Pago
    if ($action === 'get_ventas_agrupadas_metodo_pago') {
        $termino_cliente = trim($_POST['termino_cliente'] ?? '');
        $fecha_desde     = trim($_POST['fecha_desde'] ?? '');
        $fecha_hasta     = trim($_POST['fecha_hasta'] ?? '');
        $id_centro_costo = !empty($_POST['id_centro_costo']) ? (int)$_POST['id_centro_costo'] : null;
        $id_venta        = !empty($_POST['id_venta']) ? (int)$_POST['id_venta'] : null;

        try {
            // Get grouped sales data using the same filters as the main search
            $datos_agrupados = Venta::obtenerVentasAgrupadasPorMetodoPago($conexion, $termino_cliente, $fecha_desde, $fecha_hasta, $id_centro_costo, $id_venta);

            $response['success'] = true;
            $response['datos'] = $datos_agrupados['datos'];
            $response['total_general'] = $datos_agrupados['total_general'];
            $response['total_general_formateado'] = $datos_agrupados['total_general_formateado'];

        } catch (Exception $e) {
            $response['message'] = "Error al obtener ventas agrupadas por método de pago: " . $e->getMessage();
            http_response_code(500); // Internal Server Error
        }

        header('Content-Type: application/json');
        echo json_encode($response);
        exit;
    }
    #endregion Get Ventas Agrupadas por Método de Pago
}
#endregion Handle POST Actions

#region try
try {
    // Set timezone for any date operations
    date_default_timezone_set('America/Bogota');

    // Get list of active ventas (initially empty, will be populated via AJAX)
    $ventas = [];

    // Get list of active clientes for filter dropdown
    $clientes = Cliente::get_list($conexion);

    // Get list of active centros de costo for filter dropdown
    $centros_costos = CentroCosto::get_list($conexion);

} catch (PDOException $e) {
    // Specific handling for database errors
    $error_display = 'show';
    $error_text    = "Error de base de datos al obtener los datos para consulta de ventas.";
} catch (Exception $e) {
    // General error handling
    $error_display = 'show';
    $error_text    = "Ocurrió un error inesperado: " . $e->getMessage();
}
#endregion try

require_once __ROOT__ . '/views/lventas.view.php';
