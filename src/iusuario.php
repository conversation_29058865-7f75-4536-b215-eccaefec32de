<?php
// Use the required classes with their namespaces
use App\classes\Usuario;
use App\classes\Perfil;
use App\classes\Empleado;

// Start session if not already started
if (session_status() === PHP_SESSION_NONE) {
	session_start();
}

/** @var PDO $conexion */
global $conexion; // Assuming $conexion is globally available or included

// Include necessary files
require_once dirname(__FILE__, 2) . '/config/config.php';
require_once __ROOT__ . '/src/sessions/sessions.php';
require_once __ROOT__ . '/src/general/general.php';
require_once __ROOT__ . '/src/classes/Usuario.php';
require_once __ROOT__ . '/src/classes/Perfil.php';
require_once __ROOT__ . '/src/classes/Empleado.php';
require_once __ROOT__ . '/src/general/validaciones.php';
require_once __ROOT__ . '/src/general/preparar.php';

#region region INIT VARIABLES
// Variables to hold form input (useful if re-displaying form after error)
$nombre   = '';
$username = '';
$id_perfil = '';
$id_empleado = null;

// Get list of active profiles and employees for the dropdowns
try {
	$perfiles = Perfil::get_list($conexion);
	$empleados = Empleado::get_list($conexion);
} catch (Exception $e) {
	$perfiles = [];
	$empleados = [];
	$error_text = 'Error al cargar las listas de perfiles y empleados: ' . $e->getMessage();
	$error_display = 'show';
}
#endregion INIT VARIABLES

#region region POST Request Handling
if ($_SERVER['REQUEST_METHOD'] == 'POST') {
	try {
		// 1. Get data from $_POST
		$nombre        = trim($_POST['nombre'] ?? '');
		$username      = trim($_POST['username'] ?? '');
		$id_perfil     = filter_input(INPUT_POST, 'id_perfil', FILTER_VALIDATE_INT);
		$id_empleado   = filter_input(INPUT_POST, 'id_empleado', FILTER_VALIDATE_INT);
		// Handle empty employee selection
		$id_empleado   = (empty($id_empleado) || $id_empleado === 0) ? null : $id_empleado;
		$clave         = $_POST['clave'] ?? ''; // Don't trim password
		$confirm_clave = $_POST['confirm_clave'] ?? '';

		// 2. Validate data (Add more specific validation as needed)
		if (empty($nombre)) throw new Exception("El nombre completo es requerido.");
		if (empty($username)) throw new Exception("El nombre de usuario es requerido.");
		if (empty($id_perfil) || $id_perfil <= 0) throw new Exception("Debe seleccionar un perfil válido.");
		if (empty($clave)) throw new Exception("La contraseña (clave) es requerida.");
		if ($clave !== $confirm_clave) throw new Exception("Las contraseñas no coinciden.");
		// Password strength validation could be added here

		// 3. Create and save new Usuario using the class methods
		$usuario = new Usuario();

		// Set properties using setters (setClave handles hashing)
		$usuario->setNombre($nombre);
		$usuario->setUsername($username);
		$usuario->setId_perfil($id_perfil);
		$usuario->setId_empleado($id_empleado); // New field - optional
		$usuario->setClave($clave); // This hashes the password

		// Call the instance method to save to DB
		$newUserId = $usuario->crear($conexion);

		if ($newUserId !== false && $newUserId > 0) {
			$_SESSION['flash_message_success'] = "Usuario '$nombre' ($username) creado exitosamente con ID: $newUserId.";

			header('Location: lusuarios'); // Use RUTA constant for the base URL
			exit;

		} else {
			// If crearUsuario returned false, it means execute failed, but didn't throw an exception
			// This might indicate a non-exception DB error or logical issue in crearUsuario
			throw new Exception("Hubo un error al guardar el usuario. Intente nuevamente.");
		}

	} catch (PDOException $e) {
		// Handle potential database errors (e.g., unique constraint violation on username)
		// Log the detailed error: error_log("Database error: " . $e->getMessage());
		if (str_contains($e->getMessage(), 'Duplicate entry')) { // Basic check for unique constraint
			$error_text = 'Error: El nombre de usuario \'' . htmlspecialchars($username) . '\' ya existe.';
		} else {
			$error_text = 'Error de base de datos al crear el usuario. Por favor, contacte al administrador.';
		}
		$error_display = 'show';
	} catch (Exception $e) {
		// Handle other potential errors during user creation
		// Log the detailed error: error_log("General error: " . $e->getMessage());
		$error_text    = 'Ocurrió un error inesperado al crear el usuario: ' . $e->getMessage();
		$error_display = 'show';
	}

}
#endregion POST Request Handling

// Load the view file. Variables defined above will be available in the view.
require_once __ROOT__ . '/views/iusuario.view.php';
?>