<?php

// Iniciar sesión si es necesario
use App\classes\ControlBase;
use App\classes\ControlBaseMovimiento;
use App\classes\GastoOperativo;

if (session_status() === PHP_SESSION_NONE) {
	session_start();
}

/** @var PDO $conexion */
global $conexion;

require_once dirname(__FILE__, 2) . '/config/config.php';
require_once __ROOT__ . '/src/sessions/sessions.php';
require_once __ROOT__ . '/src/general/general.php';
require_once __ROOT__ . '/src/general/validaciones.php';
require_once __ROOT__ . '/src/general/preparar.php';

// Check for valid database connection before proceeding
if (!$conexion instanceof PDO) {
	error_log("Error crítico: No hay conexión a la base de datos en gasto_operativo.php.");
	$response['message'] = 'Error crítico: No se pudo conectar a la base de datos.';
	http_response_code(503); // Service Unavailable
	echo json_encode($response);
	exit;
}

#region init variables
// Establecer la zona horaria para Colombia
date_default_timezone_set('America/Bogota');

$fecha_actual = date('Y-m-d');
$control_base = null;
$gastos_operativos = [];
$total_gastos = 0.0;
#endregion init variables

#region Handle Flash Message Success
// Check for a success flash message from session
if (!empty($_SESSION['flash_message_success'])) {
	$success_text    = $_SESSION['flash_message_success'];
	$success_display = 'show';
	// Clear the flash message so it doesn't show again
	unset($_SESSION['flash_message_success']);
}
#endregion Handle Flash Message Success

#region Handle Flash Message Error
// Check for an error flash message from session
if (!empty($_SESSION['flash_message_error'])) {
	$error_text    = $_SESSION['flash_message_error'];
	$error_display = 'show';
	// Clear the flash message so it doesn't show again
	unset($_SESSION['flash_message_error']);
}
#endregion Handle Flash Message Error

#region Create Control Base
// --- Handle AJAX Request (Create Control Base) ---
if ($_SERVER['REQUEST_METHOD'] == 'POST' && isset($_POST['action']) && $_POST['action'] == 'crear_control_base') {
	header('Content-Type: application/json');
	$response = ['success' => false, 'message' => 'Error desconocido.'];

	$valor_inicial = filter_input(INPUT_POST, 'valor_inicial', FILTER_VALIDATE_FLOAT);

	if ($valor_inicial !== false && $valor_inicial >= 0) {
		try {
			// Verificar que no exista ya un control base para hoy
			$control_existente = ControlBase::obtenerPorFecha($fecha_actual, $conexion);
			if ($control_existente) {
				$response['message'] = 'Ya existe un control base para la fecha de hoy.';
				http_response_code(400);
			} else {
				// Crear el control base
				$control_base = new ControlBase($fecha_actual, $valor_inicial);
				$newId = $control_base->crear($conexion);

				if ($newId) {
					// Crear el movimiento inicial
					$movimiento = new ControlBaseMovimiento($newId, 'ingreso', $valor_inicial, 'Base inicial del dia');
					$movimiento_id = $movimiento->crear($conexion);

					if ($movimiento_id) {
						$response['success'] = true;
						$response['message'] = 'Control base creado correctamente.';
						$response['control_base'] = [
							'id' => $newId,
							'valor_inicial' => $valor_inicial,
							'valor_actual' => $valor_inicial,
							'valor_inicial_formateado' => '$' . number_format($valor_inicial, 0, ',', '.'),
							'valor_actual_formateado' => '$' . number_format($valor_inicial, 0, ',', '.')
						];
					} else {
						$response['message'] = 'Error: No se pudo crear el movimiento inicial.';
						http_response_code(500);
					}
				} else {
					$response['message'] = 'Error: No se pudo crear el Control Base.';
					http_response_code(500);
				}
			}
		} catch (Exception $e) {
			$response['message'] = "Error al crear Control Base: " . $e->getMessage();
			http_response_code(500);
		}
	} else {
		$response['message'] = 'Error: El valor inicial debe ser un número válido mayor o igual a cero.';
		http_response_code(400);
	}

	echo json_encode($response);
	exit;
}
#endregion Create Control Base

#region Edit Control Base
// --- Handle AJAX Request (Edit Control Base) ---
if ($_SERVER['REQUEST_METHOD'] == 'POST' && isset($_POST['action']) && $_POST['action'] == 'editar_control_base') {
	header('Content-Type: application/json');
	$response = ['success' => false, 'message' => 'Error desconocido.'];

	$valor_adicional = filter_input(INPUT_POST, 'valor_adicional', FILTER_VALIDATE_FLOAT);

	if ($valor_adicional !== false && $valor_adicional > 0) {
		try {
			// Obtener el control base existente
			$control_existente = ControlBase::obtenerPorFecha($fecha_actual, $conexion);
			if (!$control_existente) {
				$response['message'] = 'No existe un control base para la fecha de hoy.';
				http_response_code(404);
			} else {
				// Actualizar el valor actual
				$nuevo_valor_actual = $control_existente->getValor_actual() + $valor_adicional;
				$control_existente->setValor_actual($nuevo_valor_actual);
				$success = $control_existente->modificar($conexion);

				if ($success) {
					// Crear el movimiento de abono
					$movimiento = new ControlBaseMovimiento($control_existente->getId(), 'ingreso', $valor_adicional, 'Abono a base del dia');
					$movimiento_id = $movimiento->crear($conexion);

					if ($movimiento_id) {
						$response['success'] = true;
						$response['message'] = 'Control base actualizado correctamente.';
						$response['control_base'] = [
							'id' => $control_existente->getId(),
							'valor_inicial' => $control_existente->getValor_inicial(),
							'valor_actual' => $nuevo_valor_actual,
							'valor_inicial_formateado' => '$' . number_format($control_existente->getValor_inicial(), 0, ',', '.'),
							'valor_actual_formateado' => '$' . number_format($nuevo_valor_actual, 0, ',', '.')
						];
					} else {
						$response['message'] = 'Error: No se pudo crear el movimiento de abono.';
						http_response_code(500);
					}
				} else {
					$response['message'] = 'Error: No se pudo actualizar el Control Base.';
					http_response_code(500);
				}
			}
		} catch (Exception $e) {
			$response['message'] = "Error al actualizar Control Base: " . $e->getMessage();
			http_response_code(500);
		}
	} else {
		$response['message'] = 'Error: El valor adicional debe ser un número válido mayor que cero.';
		http_response_code(400);
	}

	echo json_encode($response);
	exit;
}
#endregion Edit Control Base

#region Verificar Control Base
// --- Handle AJAX Request (Verificar Control Base) ---
if ($_SERVER['REQUEST_METHOD'] == 'POST' && isset($_POST['action']) && $_POST['action'] == 'verificar_control_base') {
	header('Content-Type: application/json');
	$response = ['success' => false, 'exists' => false, 'message' => 'Error desconocido.'];

	try {
		// Verificar si existe un control base para la fecha actual
		$control_existente = ControlBase::obtenerPorFecha($fecha_actual, $conexion);

		$response['success'] = true;
		$response['exists'] = ($control_existente !== null);
		$response['message'] = $response['exists']
			? 'Control Base encontrado para el día de hoy.'
			: 'No existe Control Base para el día de hoy.';

	} catch (Exception $e) {
		$response['message'] = 'Error al verificar Control Base: ' . $e->getMessage();
		http_response_code(500);
	}

	echo json_encode($response);
	exit;
}
#endregion Verificar Control Base

#region Create Gasto Operativo
// --- Handle AJAX Request (Create Gasto Operativo) ---
if ($_SERVER['REQUEST_METHOD'] == 'POST' && isset($_POST['action']) && $_POST['action'] == 'crear_gasto') {
	header('Content-Type: application/json');
	$response = ['success' => false, 'message' => 'Error desconocido.'];

	$descripcion = filter_input(INPUT_POST, 'descripcion', FILTER_SANITIZE_SPECIAL_CHARS);
	$descripcion = trim($descripcion ?? '');
	$valor       = filter_input(INPUT_POST, 'valor', FILTER_VALIDATE_FLOAT);

	if (!empty($descripcion) && $valor !== false && $valor > 0) {
		try {
			// Verificar que hay un centro de costo seleccionado en sesión
			if (!isset($_SESSION[CENTRO_COSTO_SESSION]) || empty($_SESSION[CENTRO_COSTO_SESSION])) {
				throw new Exception('Error: No hay un centro de costo seleccionado. Por favor, seleccione un centro de costo antes de proceder.');
			}

			$id_centro_costo = (int)$_SESSION[CENTRO_COSTO_SESSION];

			// Crear el gasto operativo con centro de costo
			$gasto = new GastoOperativo($descripcion, $fecha_actual, $valor, $id_centro_costo);
			$newId = $gasto->crear($conexion);

			if ($newId) {
				// Obtener el control base del día para crear el movimiento y actualizar valor_actual
				$control_base = ControlBase::obtenerPorFecha($fecha_actual, $conexion);
				if ($control_base) {
					try {
						// Iniciar transacción para asegurar consistencia de datos
						$conexion->beginTransaction();

						// 1. Crear el movimiento de egreso
						$movimiento = new ControlBaseMovimiento($control_base->getId(), 'egreso', $valor, $descripcion);
						$movimiento_id = $movimiento->crear($conexion);

						if (!$movimiento_id) {
							throw new Exception('No se pudo crear el movimiento de egreso.');
						}

						// 2. Actualizar el valor_actual del control base (restar el gasto)
						$nuevo_valor_actual = $control_base->getValor_actual() - $valor;
						$control_base->setValor_actual($nuevo_valor_actual);
						$control_base_updated = $control_base->modificar($conexion);

						if (!$control_base_updated) {
							throw new Exception('No se pudo actualizar el valor actual del control base.');
						}

						// Confirmar transacción
						$conexion->commit();

						$response['success'] = true;
						$response['message'] = 'Gasto operativo creado correctamente.';
						$response['gasto'] = [
							'id' => $newId,
							'descripcion' => $descripcion,
							'valor' => $valor,
							'fecha' => $fecha_actual,
							'valor_formateado' => '$' . number_format($valor, 0, ',', '.')
						];
						$response['control_base'] = [
							'valor_actual' => $nuevo_valor_actual,
							'valor_actual_formateado' => '$' . number_format($nuevo_valor_actual, 0, ',', '.')
						];

					} catch (Exception $e) {
						// Revertir transacción en caso de error
						$conexion->rollBack();
						$response['message'] = 'Error: ' . $e->getMessage();
						http_response_code(500);
					}
				} else {
					$response['message'] = 'Error: No existe un control base para registrar el movimiento.';
					http_response_code(400);
				}
			} else {
				$response['message'] = 'Error: No se pudo crear el Gasto Operativo.';
				http_response_code(500);
			}
		} catch (Exception $e) {
			$response['message'] = "Error al crear Gasto Operativo: " . $e->getMessage();
			http_response_code(500);
		}
	} else if (empty($descripcion)) {
		$response['message'] = 'Error: La descripción no puede estar vacía.';
		http_response_code(400);
	} else {
		$response['message'] = 'Error: El valor debe ser un número válido mayor que cero.';
		http_response_code(400);
	}

	echo json_encode($response);
	exit;
}
#endregion Create Gasto Operativo

#region Delete Gasto Operativo
// --- Handle AJAX Request (Delete Gasto Operativo) ---
if ($_SERVER['REQUEST_METHOD'] == 'POST' && isset($_POST['action']) && $_POST['action'] == 'eliminar_gasto') {
	header('Content-Type: application/json');
	$response = ['success' => false, 'message' => 'Error desconocido.'];

	$gasto_id = filter_input(INPUT_POST, 'gasto_id', FILTER_VALIDATE_INT);

	if ($gasto_id) {
		try {
			// Obtener el gasto operativo
			$gasto = GastoOperativo::get($gasto_id, $conexion);
			if (!$gasto) {
				$response['message'] = 'No se encontró el gasto operativo.';
				http_response_code(404);
			} else if (!$gasto->isActivo()) {
				$response['message'] = 'El gasto operativo ya está eliminado.';
				http_response_code(400);
			} else {
				// Obtener el control base del día antes de la eliminación
				$control_base = ControlBase::obtenerPorFecha($fecha_actual, $conexion);
				if (!$control_base) {
					$response['message'] = 'Error: No existe un control base para registrar la corrección.';
					http_response_code(400);
				} else {
					try {
						// Iniciar transacción para asegurar consistencia de datos
						$conexion->beginTransaction();

						// 1. Realizar eliminación lógica (cambiar estado a 0)
						$gasto->setEstado(0);
						$success = $gasto->modificar($conexion);

						if (!$success) {
							throw new Exception('No se pudo eliminar el Gasto Operativo.');
						}

						// 2. Crear el movimiento de corrección (ingreso)
						$nota_correccion = "Correccion: " . $gasto->getDescripcion();
						$movimiento = new ControlBaseMovimiento($control_base->getId(), 'ingreso', $gasto->getValor(), $nota_correccion);
						$movimiento_id = $movimiento->crear($conexion);

						if (!$movimiento_id) {
							throw new Exception('No se pudo crear el movimiento de corrección.');
						}

						// 3. Actualizar el valor_actual del control base (sumar el valor del gasto eliminado)
						$nuevo_valor_actual = $control_base->getValor_actual() + $gasto->getValor();
						$control_base->setValor_actual($nuevo_valor_actual);
						$control_base_updated = $control_base->modificar($conexion);

						if (!$control_base_updated) {
							throw new Exception('No se pudo actualizar el valor actual del control base.');
						}

						// Confirmar transacción
						$conexion->commit();

						$response['success'] = true;
						$response['message'] = 'Gasto operativo eliminado correctamente.';
						$response['control_base'] = [
							'valor_actual' => $nuevo_valor_actual,
							'valor_actual_formateado' => '$' . number_format($nuevo_valor_actual, 0, ',', '.')
						];

					} catch (Exception $e) {
						// Revertir transacción en caso de error
						$conexion->rollBack();
						$response['message'] = 'Error: ' . $e->getMessage();
						http_response_code(500);
					}
				}
			}
		} catch (Exception $e) {
			$response['message'] = "Error al eliminar Gasto Operativo: " . $e->getMessage();
			http_response_code(500);
		}
	} else {
		$response['message'] = 'Error: ID de gasto operativo inválido.';
		http_response_code(400);
	}

	echo json_encode($response);
	exit;
}
#endregion Delete Gasto Operativo

#region try
try {
	// Verificar que hay un centro de costo seleccionado en sesión
	if (!isset($_SESSION[CENTRO_COSTO_SESSION]) || empty($_SESSION[CENTRO_COSTO_SESSION])) {
		throw new Exception('Error: No hay un centro de costo seleccionado. Por favor, seleccione un centro de costo antes de proceder.');
	}

	$id_centro_costo = (int)$_SESSION[CENTRO_COSTO_SESSION];

	// Obtener el control base del día actual
	$control_base = ControlBase::obtenerPorFecha($fecha_actual, $conexion);

	// Obtener los gastos operativos del día actual filtrados por centro de costo
	$gastos_operativos = GastoOperativo::obtenerPorFechaYCentroCosto($fecha_actual, $id_centro_costo, $conexion);

	// Calcular el total de gastos filtrado por centro de costo
	$total_gastos = GastoOperativo::calcularTotalPorFechaYCentroCosto($fecha_actual, $id_centro_costo, $conexion);

} catch (PDOException $e) {
	// Specific handling for database errors
	$error_display = 'show';
	$error_text    = "Error de base de datos al obtener la información del control operativo.";
} catch (Exception $e) {
	// General error handling
	$error_display = 'show';
	$error_text    = "Ocurrió un error inesperado al obtener la información del control operativo: " . $e->getMessage();
}
#endregion try

require_once __ROOT__ . '/views/admin/lgastos_operativos.view.php';
