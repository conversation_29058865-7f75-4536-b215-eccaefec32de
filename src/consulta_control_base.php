<?php

declare(strict_types=1);

use App\classes\ControlBase;
use App\classes\ControlBaseMovimiento;

if (session_status() === PHP_SESSION_NONE) {
	session_start();
}

/** @var PDO $conexion */
global $conexion;

require_once dirname(__FILE__, 2) . '/config/config.php';
require_once __ROOT__ . '/src/sessions/sessions.php';
require_once __ROOT__ . '/src/general/general.php';
require_once __ROOT__ . '/src/general/validaciones.php';
require_once __ROOT__ . '/src/general/preparar.php';

// Check for valid database connection before proceeding
if (!$conexion instanceof PDO) {
	error_log("Error crítico: No hay conexión a la base de datos en consulta_control_base.php.");
	$response['message'] = 'Error crítico: No se pudo conectar a la base de datos.';
	http_response_code(503); // Service Unavailable
	echo json_encode($response);
	exit;
}

#region init variables
// Establecer la zona horaria para Colombia
date_default_timezone_set('America/Bogota');

$controles_base = [];
$fecha_inicio   = null;
$fecha_fin      = null;
$show_results   = false;
#endregion init variables

#region Handle Flash Message Success
// Check for a success flash message from session
if (!empty($_SESSION['flash_message_success'])) {
    $success_text    = $_SESSION['flash_message_success'];
    $success_display = 'show';
    // Clear the flash message so it doesn't show again
    unset($_SESSION['flash_message_success']);
}
#endregion Handle Flash Message Success

#region Handle Flash Message Error
// Check for an error flash message from session
if (!empty($_SESSION['flash_message_error'])) {
    $error_text    = $_SESSION['flash_message_error'];
    $error_display = 'show';
    // Clear the flash message so it doesn't show again
    unset($_SESSION['flash_message_error']);
}
#endregion Handle Flash Message Error

#region Handle AJAX Request (Consultar por Rango de Fechas)
if ($_SERVER['REQUEST_METHOD'] == 'POST' && isset($_POST['action']) && $_POST['action'] == 'consultar_control_base') {
    header('Content-Type: application/json');
    $response = ['success' => false, 'message' => 'Error desconocido.'];

    $fecha_inicio = filter_input(INPUT_POST, 'fecha_inicio', FILTER_SANITIZE_STRING);
    $fecha_fin    = filter_input(INPUT_POST, 'fecha_fin', FILTER_SANITIZE_STRING);

    // Validar que ambas fechas estén presentes
    if (empty($fecha_inicio) || empty($fecha_fin)) {
        $response['message'] = 'Ambas fechas son obligatorias.';
        http_response_code(400);
        echo json_encode($response);
        exit;
    }

    // Validar formato de fechas
    if (!preg_match('/^\d{4}-\d{2}-\d{2}$/', $fecha_inicio) || !preg_match('/^\d{4}-\d{2}-\d{2}$/', $fecha_fin)) {
        $response['message'] = 'Las fechas deben tener formato YYYY-MM-DD.';
        http_response_code(400);
        echo json_encode($response);
        exit;
    }

    // Validar que fecha_inicio no sea mayor que fecha_fin
    if ($fecha_inicio > $fecha_fin) {
        $response['message'] = 'La fecha de inicio no puede ser mayor que la fecha de fin.';
        http_response_code(400);
        echo json_encode($response);
        exit;
    }

    try {
        // Obtener controles base por rango de fechas
        $controles_base = ControlBase::obtenerPorRangoFechas($fecha_inicio, $fecha_fin, $conexion);

        // Preparar datos para respuesta
        $controles_data = [];
        foreach ($controles_base as $control) {
            $controles_data[] = [
                'id'                       => $control->getId(),
                'fecha'                    => $control->getFecha(),
                'valor_inicial'            => $control->getValor_inicial(),
                'valor_actual'             => $control->getValor_actual(),
                'valor_final'              => $control->getValor_final(),
                'valor_inicial_formateado' => $control->getValor_inicialFormateado(),
                'valor_actual_formateado'  => $control->getValor_actualFormateado(),
                'valor_final_formateado'   => $control->getValor_finalFormateado(),
                'id_cierre'                => $control->getId_cierre(),
                'estado'                   => $control->getEstado(),
                'tiene_cierre'             => $control->tieneCierre()
            ];
        }

        $response['success']         = true;
        $response['message']         = 'Consulta realizada correctamente.';
        $response['controles_base']  = $controles_data;
        $response['total_registros'] = count($controles_base);
        $response['fecha_inicio']    = $fecha_inicio;
        $response['fecha_fin']       = $fecha_fin;

    } catch (Exception $e) {
        $response['message'] = 'Error al consultar controles base: ' . $e->getMessage();
        http_response_code(500);
    }

    echo json_encode($response);
    exit;
}
#endregion Handle AJAX Request (Consultar por Rango de Fechas)

#region Handle AJAX Request (Obtener Movimientos por ControlBase)
if ($_SERVER['REQUEST_METHOD'] == 'POST' && isset($_POST['action']) && $_POST['action'] == 'obtener_movimientos') {
    header('Content-Type: application/json');
    $response = ['success' => false, 'message' => 'Error desconocido.'];

    $id_control_base = filter_input(INPUT_POST, 'id_control_base', FILTER_VALIDATE_INT);

    // Validar que el ID esté presente
    if (!$id_control_base) {
        $response['message'] = 'El ID del control base es obligatorio.';
        http_response_code(400);
        echo json_encode($response);
        exit;
    }

    try {
        // Obtener control base
        $control_base = ControlBase::get($id_control_base, $conexion);
        
        if (!$control_base) {
            $response['message'] = 'No se encontró el control base especificado.';
            http_response_code(404);
            echo json_encode($response);
            exit;
        }

        // Obtener movimientos del control base
        $movimientos = ControlBaseMovimiento::obtenerPorControlBase($id_control_base, $conexion);
        
        // Preparar datos de movimientos
        $movimientos_data = [];
        foreach ($movimientos as $movimiento) {
            $movimientos_data[] = [
                'id'               => $movimiento->getId(),
                'tipo'             => $movimiento->getTipo(),
                'tipo_descripcion' => $movimiento->getTipoDescripcion(),
                'nota'             => $movimiento->getNota(),
                'valor'            => $movimiento->getValor(),
                'valor_formateado' => $movimiento->getValorFormateado(),
                'fecha'            => $movimiento->getFecha(),
                'fecha_formateada' => $movimiento->getFechaFormateada(),
                'impacto'          => $movimiento->calcularImpacto(),
                'is_ingreso'       => $movimiento->isIngreso(),
                'is_egreso'        => $movimiento->isEgreso()
            ];
        }

        $response['success'] = true;
        $response['message'] = 'Movimientos obtenidos correctamente.';
        $response['control_base'] = [
            'id'                       => $control_base->getId(),
            'fecha'                    => $control_base->getFecha(),
            'valor_inicial'            => $control_base->getValor_inicial(),
            'valor_actual'             => $control_base->getValor_actual(),
            'valor_final'              => $control_base->getValor_final(),
            'valor_inicial_formateado' => $control_base->getValor_inicialFormateado(),
            'valor_actual_formateado'  => $control_base->getValor_actualFormateado(),
            'valor_final_formateado'   => $control_base->getValor_finalFormateado()
        ];
        $response['movimientos'] = $movimientos_data;
        $response['total_movimientos'] = count($movimientos);

    } catch (Exception $e) {
        $response['message'] = 'Error al obtener movimientos: ' . $e->getMessage();
        http_response_code(500);
    }

    echo json_encode($response);
    exit;
}
#endregion Handle AJAX Request (Obtener Movimientos por ControlBase)

#region Include View
require_once __ROOT__ . '/views/admin/consulta_control_base.view.php';
#endregion Include View
