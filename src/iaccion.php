<?php
// Use the Accion class with its namespace
use App\classes\Accion;
use App\classes\Menu;
use App\classes\Submenu;

// Start session if not already started
if (session_status() === PHP_SESSION_NONE) {
	session_start();
}

/** @var PDO $conexion */
global $conexion; // Assuming $conexion is globally available or included

// Include necessary files
require_once dirname(__FILE__, 2) . '/config/config.php';
require_once __ROOT__ . '/src/sessions/sessions.php';
require_once __ROOT__ . '/src/general/general.php';
require_once __ROOT__ . '/src/general/validaciones.php';
require_once __ROOT__ . '/src/general/preparar.php';

#region region INIT VARIABLES
// Variables to hold form input (useful if re-displaying form after error)
$id = 0;
$nombre = '';
$grupo = '';
$id_menu = null;
$id_submenu = null;
$estado = 1;
$is_edit_mode = false;
$error_display = '';
$error_text = '';

// Get all menus for the dropdown
try {
	$menus = Menu::get_list($conexion);
	$submenus = []; // Will be populated via AJAX when a menu is selected
} catch (Exception $e) {
	$error_display = 'show';
	$error_text = "Error al cargar los menús: " . $e->getMessage();
}
#endregion INIT VARIABLES

#region region GET Request Handling - Edit Mode
if ($_SERVER['REQUEST_METHOD'] == 'GET' && isset($_GET['id'])) {
	$id = filter_input(INPUT_GET, 'id', FILTER_VALIDATE_INT);
	
	if ($id) {
		try {
			$accion = Accion::get($id, $conexion);
			
			if ($accion) {
				// Set edit mode and populate form variables
				$is_edit_mode = true;
				$nombre = $accion->getNombre();
				$grupo = $accion->getGrupo();
				$id_menu = $accion->getIdMenu();
				$id_submenu = $accion->getIdSubmenu();
				$estado = $accion->getEstado();
				
				// If a menu is selected, load its submenus
				if ($id_menu) {
					$submenus = Submenu::get_list($conexion, $id_menu);
				}
			} else {
				// Accion not found
				$_SESSION['flash_message_error'] = "La acción con ID $id no existe.";
				header('Location: lacciones');
				exit;
			}
		} catch (Exception $e) {
			$_SESSION['flash_message_error'] = "Error al cargar la acción: " . $e->getMessage();
			header('Location: lacciones');
			exit;
		}
	} else {
		$_SESSION['flash_message_error'] = "ID de acción inválido.";
		header('Location: lacciones');
		exit;
	}
}
#endregion GET Request Handling

#region region POST Request Handling
if ($_SERVER['REQUEST_METHOD'] == 'POST') {
	try {
		// 1. Get data from $_POST
		$id = isset($_POST['id']) ? filter_input(INPUT_POST, 'id', FILTER_VALIDATE_INT) : 0;
		$nombre = trim($_POST['nombre'] ?? '');
		$grupo = trim($_POST['grupo'] ?? '');
		$id_menu = !empty($_POST['id_menu']) ? filter_input(INPUT_POST, 'id_menu', FILTER_VALIDATE_INT) : null;
		$id_submenu = !empty($_POST['id_submenu']) ? filter_input(INPUT_POST, 'id_submenu', FILTER_VALIDATE_INT) : null;
		$estado = isset($_POST['estado']) ? filter_input(INPUT_POST, 'estado', FILTER_VALIDATE_INT) : 1;
		
		$is_edit_mode = ($id > 0);
		
		// 2. Validate data
		if (empty($nombre)) {
			throw new Exception("El nombre de la acción es requerido.");
		}
		
		// If submenu is selected, menu must also be selected
		if ($id_submenu && !$id_menu) {
			throw new Exception("Debe seleccionar un menú si selecciona un submenú.");
		}
		
		// 3. Create or update Accion object
		$accion = new Accion();
		
		if ($is_edit_mode) {
			$accion = Accion::get($id, $conexion);
			if (!$accion) {
				throw new Exception("La acción con ID $id no existe.");
			}
		}
		
		// Set properties using setters
		$accion->setNombre($nombre);
		$accion->setGrupo($grupo);
		$accion->setIdMenu($id_menu);
		$accion->setIdSubmenu($id_submenu);
		$accion->setEstado($estado);
		
		// 4. Save to database
		if ($is_edit_mode) {
			$success = $accion->modificar($conexion);
			$message = "Acción '$nombre' actualizada exitosamente.";
		} else {
			$newId = $accion->crear($conexion);
			$success = ($newId !== false && $newId > 0);
			$message = "Acción '$nombre' creada exitosamente" . ($success ? " con ID: $newId." : ".");
		}
		
		if ($success) {
			$_SESSION['flash_message_success'] = $message;
			header('Location: lacciones');
			exit;
		} else {
			throw new Exception("No se pudo " . ($is_edit_mode ? "actualizar" : "crear") . " la acción.");
		}
		
	} catch (PDOException $e) {
		// Handle database errors
		$error_text = 'Error de base de datos: ' . $e->getMessage();
		$error_display = 'show';
	} catch (Exception $e) {
		// Handle other potential errors
		$error_text = 'Ocurrió un error: ' . $e->getMessage();
		$error_display = 'show';
	}
	
	// If a menu is selected, reload its submenus for the form
	if ($id_menu) {
		try {
			$submenus = Submenu::get_list($conexion, $id_menu);
		} catch (Exception $e) {
			// Just log the error, don't override the main error message
			error_log("Error loading submenus: " . $e->getMessage());
		}
	}
}
#endregion POST Request Handling

// Load the view file. Variables defined above will be available in the view.
require_once __ROOT__ . '/views/iaccion.view.php';
?>
