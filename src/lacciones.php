<?php

// Iniciar sesión si es necesario
use App\classes\Accion;
use App\classes\Menu;
use App\classes\Submenu;

if (session_status() === PHP_SESSION_NONE) {
	session_start();
}

/** @var PDO $conexion */
global $conexion;

require_once dirname(__FILE__, 2) . '/config/config.php';
require_once __ROOT__ . '/src/sessions/sessions.php';
require_once __ROOT__ . '/src/general/general.php';
require_once __ROOT__ . '/src/general/validaciones.php';
require_once __ROOT__ . '/src/general/preparar.php';

// Check for valid database connection before proceeding
if (!$conexion instanceof PDO) {
	error_log("Error crítico: No hay conexión a la base de datos en lacciones.php");
	$_SESSION['flash_message_error'] = 'Error crítico: No se pudo conectar a la base de datos.';
	header('Location: dashboard');
	exit;
}

// Inicializar variables
$error_display = '';
$error_text    = '';

// Obtener parámetros de filtro
$filtro_nombre = isset($_GET['filtro_nombre']) ? trim($_GET['filtro_nombre']) : '';
$filtro_grupo = isset($_GET['filtro_grupo']) ? trim($_GET['filtro_grupo']) : '';

#region region Handle POST Actions
if ($_SERVER['REQUEST_METHOD'] == 'POST' && isset($_POST['action'])) {
	// --- Handle Deactivate Action ---
	if ($_POST['action'] == 'desactivar') {
		$accionIdToDeactivate = filter_input(INPUT_POST, 'accionId', FILTER_VALIDATE_INT);

		if ($accionIdToDeactivate) {
			try {
				$success = Accion::desactivar($accionIdToDeactivate, $conexion);

				if ($success) {
					$_SESSION['flash_message_success'] = "Acción desactivada correctamente.";
				} else {
					$_SESSION['flash_message_error'] = "Error: No se pudo encontrar o desactivar la acción.";
				}
			} catch (Exception $e) {
				$_SESSION['flash_message_error'] = "Error al desactivar acción: " . $e->getMessage();
			}
		} else {
			$_SESSION['flash_message_error'] = "Error: ID de acción inválido para desactivar.";
		}

		// Redirect back to the action list page after processing
		header('Location: lacciones');
		exit;
	}

	// --- Handle Activate Action ---
	if ($_POST['action'] == 'activar') {
		$accionIdToActivate = filter_input(INPUT_POST, 'accionId', FILTER_VALIDATE_INT);

		if ($accionIdToActivate) {
			try {
				$success = Accion::activar($accionIdToActivate, $conexion);

				if ($success) {
					$_SESSION['flash_message_success'] = "Acción activada correctamente.";
				} else {
					$_SESSION['flash_message_error'] = "Error: No se pudo encontrar o activar la acción.";
				}
			} catch (Exception $e) {
				$_SESSION['flash_message_error'] = "Error al activar acción: " . $e->getMessage();
			}
		} else {
			$_SESSION['flash_message_error'] = "Error: ID de acción inválido para activar.";
		}

		// Redirect back to the action list page after processing
		header('Location: lacciones');
		exit;
	}
}
#endregion Handle POST Actions

#region try
try {
	// Preparar parámetros para la consulta
	$parametros = ['incluir_inactivas' => true];

	// Aplicar filtro de grupo si se proporciona
	if (!empty($filtro_grupo)) {
		$parametros['grupo'] = $filtro_grupo;
	}

	// Obtener todas las acciones con los filtros aplicados
	$acciones = Accion::get_list($conexion, $parametros);

	// Filtrar por nombre si se proporciona (filtrado en memoria ya que no está en la consulta SQL)
	if (!empty($filtro_nombre)) {
		$acciones_filtradas = [];
		foreach ($acciones as $accion) {
			if (stripos($accion->getNombre(), $filtro_nombre) !== false) {
				$acciones_filtradas[] = $accion;
			}
		}
		$acciones = $acciones_filtradas;
	}

	// Obtener grupos únicos para el filtro de grupo
	$grupos_unicos = [];
	$todas_acciones = Accion::get_list($conexion, ['incluir_inactivas' => true]);
	foreach ($todas_acciones as $accion) {
		$grupo = $accion->getGrupo();
		if (!empty($grupo) && !in_array($grupo, $grupos_unicos)) {
			$grupos_unicos[] = $grupo;
		}
	}
	// Ordenar los grupos alfabéticamente
	sort($grupos_unicos);

} catch (PDOException $e) {
	// Specific handling for database errors
	$error_display = 'show';
	$error_text    = "Error de base de datos al obtener la lista de acciones: " . $e->getMessage();
} catch (Exception $e) {
	// General error handling
	$error_display = 'show';
	$error_text    = "Ocurrió un error inesperado al obtener la lista de acciones: " . $e->getMessage();
}
#endregion try

require_once __ROOT__ . '/views/lacciones.view.php';

?>
