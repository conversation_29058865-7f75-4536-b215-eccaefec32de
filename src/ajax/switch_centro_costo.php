<?php
// Start session if not already started
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

/** @var PDO $conexion */
global $conexion;

use App\classes\Usuario;
use App\classes\CentroCosto;

// Include necessary files
require_once dirname(__FILE__, 3) . '/config/config.php';
require_once __ROOT__ . '/src/query/connection.php';
require_once __ROOT__ . '/src/sessions/sessions.php';
require_once __ROOT__ . '/src/general/general.php';
require_once __ROOT__ . '/src/general/preparar.php';

// Set content type to JSON
header('Content-Type: application/json');

// Initialize response array
$response = [
    'success' => false,
    'message' => '',
    'centro_costo_nombre' => ''
];

try {    
    // Check for valid database connection
    if (!$conexion instanceof PDO) {
        throw new Exception('Error crítico: No se pudo conectar a la base de datos.');
    }
    
    // Validate request method
    if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
        throw new Exception('Método de solicitud no válido.');
    }
    
    // Get and validate input
    $input = json_decode(file_get_contents('php://input'), true);
    
    if (!isset($input['centro_costo_id']) || !is_numeric($input['centro_costo_id'])) {
        throw new Exception('ID de centro de costo no válido.');
    }
    
    $centro_costo_id = (int)$input['centro_costo_id'];
    
    if ($centro_costo_id <= 0) {
        throw new Exception('ID de centro de costo debe ser mayor a cero.');
    }
    
    // Get current user info
    $logged_usuario_info = Usuario::get($_SESSION[USR_SESSION], $conexion);
    
    if (!$logged_usuario_info || $logged_usuario_info->getEstado() == 0) {
        throw new Exception('Usuario no válido o inactivo.');
    }
    
    // Get user's associated centro de costos
    $user_centros_costos = CentroCosto::get_by_usuario($logged_usuario_info->getId(), $conexion);
    
    if (empty($user_centros_costos)) {
        throw new Exception('El usuario no tiene centros de costo asociados.');
    }
    
    // Validate that the requested centro de costo belongs to the user
    $valid_centro_costo = null;
    foreach ($user_centros_costos as $centro) {
        if ($centro->getId() === $centro_costo_id) {
            $valid_centro_costo = $centro;
            break;
        }
    }
    
    if (!$valid_centro_costo) {
        throw new Exception('El centro de costo seleccionado no está asociado al usuario.');
    }
    
    // Update session with new centro de costo selection
    $_SESSION[CENTRO_COSTO_SESSION] = $centro_costo_id;
    
    // Prepare success response
    $response['success'] = true;
    $response['message'] = 'Centro de costo cambiado exitosamente.';
    $response['centro_costo_nombre'] = $valid_centro_costo->getNombre();
    
} catch (Exception $e) {
    $response['success'] = false;
    $response['message'] = $e->getMessage();
    
    // Log error for debugging (optional)
    error_log("Error en switch_centro_costo.php: " . $e->getMessage());
}

// Return JSON response
echo json_encode($response);
exit;
?>
