<?php
// This file handles AJAX requests to get submenus for a specific menu

use App\classes\Submenu;

// Start session if not already started
if (session_status() === PHP_SESSION_NONE) {
	session_start();
}

// Set content type to JSON
header('Content-Type: application/json');

/** @var PDO $conexion */
global $conexion;

// Include necessary files
require_once dirname(__FILE__, 3) . '/config/config.php';
require_once __ROOT__ . '/src/sessions/sessions.php';
require_once __ROOT__ . '/src/general/general.php';
require_once __ROOT__ . '/src/general/validaciones.php';
require_once __ROOT__ . '/src/general/preparar.php';

// Default response
$response = [
	'success' => false,
	'message' => 'Error desconocido',
	'submenus' => []
];

// Check if menu ID is provided
if (isset($_GET['id_menu'])) {
	$id_menu = filter_input(INPUT_GET, 'id_menu', FILTER_VALIDATE_INT);
	
	if ($id_menu) {
		try {
			// Get submenus for the selected menu
			$submenus = Submenu::get_list($conexion, $id_menu);
			
			// Format submenus for JSON response
			$formatted_submenus = [];
			foreach ($submenus as $submenu) {
				$formatted_submenus[] = [
					'id' => $submenu->getId(),
					'texto' => $submenu->getTexto()
				];
			}
			
			// Update response
			$response['success'] = true;
			$response['message'] = 'Submenús cargados correctamente';
			$response['submenus'] = $formatted_submenus;
			
		} catch (Exception $e) {
			$response['message'] = 'Error al cargar submenús: ' . $e->getMessage();
		}
	} else {
		$response['message'] = 'ID de menú inválido';
	}
} else {
	$response['message'] = 'ID de menú no proporcionado';
}

// Return JSON response
echo json_encode($response);
exit;
?>
