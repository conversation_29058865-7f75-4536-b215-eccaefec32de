<?php

use App\classes\Proveedor;

if (session_status() === PHP_SESSION_NONE) {
	session_start();
}

/** @var PDO $conexion */
global $conexion;

require_once dirname(__FILE__, 2) . '/config/config.php';
require_once __ROOT__ . '/src/sessions/sessions.php';
require_once __ROOT__ . '/src/general/general.php';
require_once __ROOT__ . '/src/general/validaciones.php';
require_once __ROOT__ . '/src/general/preparar.php';

// Check for valid database connection before proceeding
if (!$conexion instanceof PDO) {
	error_log("Error crítico: No hay conexión a la base de datos en eproveedor.php.");
	$response['message'] = 'Error crítico: No se pudo conectar a la base de datos.';
	http_response_code(503); // Service Unavailable
	echo json_encode($response);
	exit;
}

// Get provider ID from URL parameter
$id = (int)($_GET['id'] ?? 0);

if ($id <= 0) {
	$_SESSION['flash_message_error'] = "ID de proveedor inválido.";
	header('Location: lproveedores');
	exit;
}

#region Handle POST - Update Provider
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
	try {
		// Get and clean form data
		$nombre    = limpiar_datos($_POST['nombre'] ?? '');
		$nit       = limpiar_datos($_POST['nit'] ?? '');
		$telefono  = limpiar_datos($_POST['telefono'] ?? '');
		$correo    = limpiar_datos($_POST['correo'] ?? '');
		$direccion = limpiar_datos($_POST['direccion'] ?? '');

		// Server-side validation
		validar_campovacio($nombre, "El nombre es requerido.");

		// Validate email format if provided
		if (!empty($correo) && !filter_var($correo, FILTER_VALIDATE_EMAIL)) {
			throw new Exception("El formato del correo no es válido.");
		}

		// Get existing provider
		$proveedor = Proveedor::get($id, $conexion);
		if (!$proveedor) {
			throw new Exception("Proveedor no encontrado.");
		}

		// Update provider data
		$proveedor->setNombre($nombre);
		$proveedor->setNit(!empty($nit) ? $nit : null);
		$proveedor->setTelefono(!empty($telefono) ? $telefono : null);
		$proveedor->setCorreo(!empty($correo) ? $correo : null);
		$proveedor->setDireccion(!empty($direccion) ? $direccion : null);

		// Update provider in database
		$success = $proveedor->modificar($conexion);

		if ($success) {
			$_SESSION['flash_message_success'] = "Proveedor actualizado exitosamente.";
			header('Location: lproveedores');
			exit;
		} else {
			throw new Exception("Error al actualizar el proveedor en la base de datos.");
		}

	} catch (Exception $e) {
		error_log("Error updating provider: " . $e->getMessage());
		$error_display = 'show';
		$error_text = $e->getMessage();
	}
}
#endregion Handle POST

#region Get Provider Data
try {
	$proveedor = Proveedor::get($id, $conexion);

	if (!$proveedor) {
		$_SESSION['flash_message_error'] = "Proveedor no encontrado.";
		header('Location: lproveedores');
		exit;
	}

	// Extract data for the view
	$nombre    = $proveedor->getNombre();
	$nit       = $proveedor->getNit();
	$telefono  = $proveedor->getTelefono();
	$correo    = $proveedor->getCorreo();
	$direccion = $proveedor->getDireccion();

} catch (Exception $e) {
	error_log("Error fetching provider: " . $e->getMessage());
	$_SESSION['flash_message_error'] = "Error al obtener los datos del proveedor.";
	header('Location: lproveedores');
	exit;
}
#endregion Get Provider Data

require_once __ROOT__ . '/views/eproveedor.view.php';

?>
