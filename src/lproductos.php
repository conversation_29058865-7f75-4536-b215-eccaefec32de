<?php

// Iniciar sesión si es necesario
use App\classes\Producto;
use App\classes\Inventario;

if (session_status() === PHP_SESSION_NONE) {
	session_start();
}

/** @var PDO $conexion */
global $conexion;

require_once dirname(__FILE__, 2) . '/config/config.php';
require_once __ROOT__ . '/src/sessions/sessions.php';
require_once __ROOT__ . '/src/general/general.php';
require_once __ROOT__ . '/src/general/validaciones.php';
require_once __ROOT__ . '/src/general/preparar.php';

// Check for valid database connection before proceeding
if (!$conexion instanceof PDO) {
	error_log("Error crítico: No hay conexión a la base de datos en lproductos.php.");
	$response['message'] = 'Error crítico: No se pudo conectar a la base de datos.';
	http_response_code(503); // Service Unavailable
	echo json_encode($response);
	exit;
}

#region init variables
$productos = []; // Initialize as an empty array
#endregion init variables

#region Handle Flash Message Success
// Check for a success flash message from session
if (!empty($_SESSION['flash_message_success'])) {
	$success_text    = $_SESSION['flash_message_success'];
	$success_display = 'show';
	// Clear the flash message so it doesn't show again
	unset($_SESSION['flash_message_success']);
}
#endregion Handle Flash Message Success

#region Handle Flash Message Error
// Check for an error flash message from session
if (!empty($_SESSION['flash_message_error'])) {
	$error_text    = $_SESSION['flash_message_error'];
	$error_display = 'show';
	// Clear the flash message so it doesn't show again
	unset($_SESSION['flash_message_error']);
}
#endregion Handle Flash Message Error

#region Search Productos
// --- Handle AJAX Request (Search Productos) ---
if ($_SERVER['REQUEST_METHOD'] == 'POST' && isset($_POST['action']) && $_POST['action'] == 'buscar') {
	header('Content-Type: application/json');                            // Set response header for JSON
	$response = ['success' => false, 'message' => 'Error desconocido.']; // Default error response

	$termino = filter_input(INPUT_POST, 'termino', FILTER_SANITIZE_SPECIAL_CHARS); // Sanitize search term
	$termino = trim($termino ?? '');                                               // Trim whitespace

	try {
		// Get filtered list of productos
		$productos_filtrados = Producto::search_by_descripcion($conexion, $termino);

		$response['success'] = true;
		$response['productos'] = [];

		foreach ($productos_filtrados as $producto) {
			$response['productos'][] = [
				'id'               => $producto->getId(),
				'descripcion'      => htmlspecialchars($producto->getDescripcion()),
				'valor'            => $producto->getValor(),
				'valor_formateado' => format_currency_consigno($producto->getValor()),
				'estado'           => $producto->getEstado(),
				'is_activo'        => $producto->isActivo()
			];
		}

	} catch (Exception $e) {
		$response['message'] = "Error al buscar productos: " . $e->getMessage();
		http_response_code(500); // Internal Server Error
	}

	echo json_encode($response);
	exit; // IMPORTANT: Stop script execution after sending JSON response for AJAX
}
#endregion Search Productos

#region Create Producto
// --- Handle AJAX Request (Create Producto) ---
if ($_SERVER['REQUEST_METHOD'] == 'POST' && isset($_POST['action']) && $_POST['action'] == 'crear') {
	header('Content-Type: application/json');                            // Set response header for JSON
	$response = ['success' => false, 'message' => 'Error desconocido.']; // Default error response

	$descripcion = filter_input(INPUT_POST, 'descripcion', FILTER_SANITIZE_SPECIAL_CHARS); // Sanitize description
	$descripcion = trim($descripcion ?? '');                                              // Trim whitespace

	$valor = filter_input(INPUT_POST, 'valor', FILTER_SANITIZE_SPECIAL_CHARS);
	// Clean currency format (remove $ and thousand separators)
	$valor = str_replace('$', '', $valor ?? '0');
	$valor = str_replace('.', '', $valor);
	$valor = str_replace(',', '.', $valor); // Replace comma with dot for decimal
	$valor = floatval($valor);

	if (!empty($descripcion) && $valor > 0) {
		try {
			$producto = new Producto();
			$producto->setDescripcion($descripcion);
			$producto->setValor($valor);

			$newId = $producto->crear($conexion);

			if ($newId) {
				$response['success']     = true;
				$response['message']     = 'Producto creado correctamente.';
				$response['id']          = $newId;
				$response['descripcion'] = $descripcion;
				$response['valor']       = format_currency_consigno($valor);
				$response['estado']      = 1;                                 // Active by default
			} else {
				$response['message'] = 'Error: No se pudo crear el Producto.';
				http_response_code(500); // Internal Server Error
			}
		} catch (Exception $e) {
			$response['message'] = "Error al crear Producto: " . $e->getMessage();
			http_response_code(500); // Internal Server Error
		}
	} else if (empty($descripcion)) {
		$response['message'] = 'Error: La descripción no puede estar vacía.';
		http_response_code(400); // Bad Request
	} else {
		$response['message'] = 'Error: El valor debe ser mayor que cero.';
		http_response_code(400); // Bad Request
	}

	echo json_encode($response);
	exit; // IMPORTANT: Stop script execution after sending JSON response for AJAX
}
#endregion Create Producto

#region Modify Producto
// --- Handle AJAX Request (Modify Producto) ---
if ($_SERVER['REQUEST_METHOD'] == 'POST' && isset($_POST['action']) && $_POST['action'] == 'modificar') {
	header('Content-Type: application/json');                            // Set response header for JSON
	$response = ['success' => false, 'message' => 'Error desconocido.']; // Default error response

	$productoId       = filter_input(INPUT_POST, 'productoId', FILTER_VALIDATE_INT);
	$nuevaDescripcion = filter_input(INPUT_POST, 'descripcion', FILTER_SANITIZE_SPECIAL_CHARS);  // Sanitize description
	$nuevaDescripcion = trim($nuevaDescripcion ?? '');                                           // Trim whitespace

	$nuevoValor = filter_input(INPUT_POST, 'valor', FILTER_SANITIZE_SPECIAL_CHARS);
	// Clean currency format (remove $ and thousand separators)
	$nuevoValor = str_replace('$', '', $nuevoValor ?? '0');
	$nuevoValor = str_replace('.', '', $nuevoValor);
	$nuevoValor = str_replace(',', '.', $nuevoValor);        // Replace comma with dot for decimal
	$nuevoValor = floatval($nuevoValor);

	if ($productoId && !empty($nuevaDescripcion) && $nuevoValor > 0) {
		try {
			// Get the producto first
			$producto = Producto::get($productoId, $conexion);

			if ($producto) {
				$producto->setDescripcion($nuevaDescripcion);
				$producto->setValor($nuevoValor);
				$success = $producto->modificar($conexion);

				if ($success) {
					$response['success'] = true;
					$response['message'] = 'Producto actualizado correctamente.';
					$response['valor_formateado'] = format_currency_consigno($nuevoValor);
				} else {
					$response['message'] = 'Error: No se pudo actualizar el Producto.';
					http_response_code(500); // Internal Server Error
				}
			} else {
				$response['message'] = 'Error: No se encontró el Producto.';
				http_response_code(404); // Not Found
			}
		} catch (Exception $e) {
			$response['message'] = "Error al modificar Producto: " . $e->getMessage();
			http_response_code(500); // Internal Server Error
		}
	} else if (!$productoId) {
		$response['message'] = 'Error: ID de Producto inválido.';
		http_response_code(400); // Bad Request
	} else if (empty($nuevaDescripcion)) {
		$response['message'] = 'Error: La descripción no puede estar vacía.';
		http_response_code(400); // Bad Request
	} else {
		$response['message'] = 'Error: El valor debe ser mayor que cero.';
		http_response_code(400); // Bad Request
	}

	echo json_encode($response);
	exit; // IMPORTANT: Stop script execution after sending JSON response for AJAX
}
#endregion Modify Producto

#region Handle POST Actions (Deactivation)
// --- Handle Non-AJAX POST Action (Deactivate Producto) ---
if ($_SERVER['REQUEST_METHOD'] == 'POST' && isset($_POST['action']) && $_POST['action'] == 'desactivar') {
	$productoIdToDeactivate = filter_input(INPUT_POST, 'productoId', FILTER_VALIDATE_INT);

	if ($productoIdToDeactivate) {
		try {
			$success = Producto::desactivar($productoIdToDeactivate, $conexion);

			if ($success) {
				$_SESSION['flash_message_success'] = "Producto desactivado correctamente.";
			} else {
				$_SESSION['flash_message_error'] = "Error: No se pudo encontrar o desactivar el Producto.";
			}
		} catch (Exception $e) {
			$errorMessage = $e->getMessage();
			// Check if it's an inventory-related error and add link
			if (strpos($errorMessage, 'registros de inventario') !== false) {
				$errorMessage .= ' <a href="inventario" class="btn btn-sm btn-primary ms-2">Ir a Gestión de Inventario</a>';
			}
			$_SESSION['flash_message_error'] = $errorMessage;
		}
	} else {
		$_SESSION['flash_message_error'] = "Error: ID de Producto inválido para desactivar.";
	}

	// Redirect back to the producto list page after processing
	header('Location: productos');
	exit;
}
#endregion Handle POST Actions

#region try
try {
	// Set timezone for any date operations
	date_default_timezone_set('America/Bogota');

	// Get list of active productos
	$productos = Producto::get_list($conexion);

	// Get inventory information grouped by product
	$inventario_por_producto = Inventario::obtenerInventarioAgrupadoPorProducto($conexion);

} catch (PDOException $e) {
	// Specific handling for database errors
	$error_display = 'show';
	$error_text    = "Error de base de datos al obtener la lista de Productos.";
} catch (Exception $e) {
	// General error handling
	$error_display = 'show';
	$error_text    = "Ocurrió un error inesperado al obtener la lista de Productos: " . $e->getMessage();
}
#endregion try

require_once __ROOT__ . '/views/lproductos.view.php';
