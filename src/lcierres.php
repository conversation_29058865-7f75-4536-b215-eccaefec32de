<?php
/**
 * Controlador para la consulta de cierres de caja
 * 
 * Este controlador maneja la visualización y filtrado de cierres de caja,
 * incluyendo funcionalidades de búsqueda por fecha y centro de costo.
 */

use App\classes\Cierre;
use App\classes\CentroCosto;

if (session_status() === PHP_SESSION_NONE) {
	session_start();
}

/** @var PDO $conexion */
global $conexion;

require_once dirname(__FILE__, 2) . '/config/config.php';
require_once __ROOT__ . '/src/sessions/sessions.php';
require_once __ROOT__ . '/src/general/general.php';
require_once __ROOT__ . '/src/general/validaciones.php';
require_once __ROOT__ . '/src/general/preparar.php';

// Check for valid database connection before proceeding
if (!$conexion instanceof PDO) {
    error_log("Error crítico: No hay conexión a la base de datos en lcierres.php.");
    $response['message'] = 'Error crítico: No se pudo conectar a la base de datos.';
    http_response_code(503); // Service Unavailable
    echo json_encode($response);
    exit;
}

// Initialize response for AJAX requests
$response = ['success' => false, 'message' => ''];

#region Handle POST Actions
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $action = $_POST['action'] ?? '';

    #region AJAX Search Cierres
    if ($action === 'search_cierres') {
        $fecha_desde     = trim($_POST['fecha_desde'] ?? '');
        $fecha_hasta     = trim($_POST['fecha_hasta'] ?? '');
        $id_centro_costo = !empty($_POST['id_centro_costo']) ? (int)$_POST['id_centro_costo'] : null;

        try {
            // Validate that both dates are provided (mandatory)
            if (empty($fecha_desde) || empty($fecha_hasta)) {
                $response['message'] = 'Las fechas desde y hasta son obligatorias para realizar la consulta.';
                http_response_code(400); // Bad Request
            } else {
                // Get filtered list of cierres
                $cierres_filtrados = Cierre::search_cierres($conexion, $fecha_desde, $fecha_hasta, $id_centro_costo);

                $response['success'] = true;
                $response['cierres'] = [];

                foreach ($cierres_filtrados as $cierre) {
                    $response['cierres'][] = [
                        'id'                                => $cierre->getId(),
                        'fecha'                             => $cierre->getFecha(),
                        'fecha_formateada'                  => date('Y-m-d', strtotime($cierre->getFecha())),
                        'centro_costo_nombre'               => $cierre->centro_costo_nombre ?? 'Sin centro de costo',
                        'valor_total_citas'                 => $cierre->getValorTotalCitas(),
                        'valor_total_citas_formateado'      => format_currency_consigno($cierre->getValorTotalCitas()),
                        'valor_comision_total_citas'        => $cierre->getValorComisionTotalCitas(),
                        'valor_comision_total_citas_formateado' => format_currency_consigno($cierre->getValorComisionTotalCitas()),
                        'valor_total_ventas'                => $cierre->getValorTotalVentas(),
                        'valor_total_ventas_formateado'     => format_currency_consigno($cierre->getValorTotalVentas()),
                        'valor_total_ordenes_compra'        => $cierre->getValorTotalOrdenesCompra(),
                        'valor_total_ordenes_compra_formateado' => format_currency_consigno($cierre->getValorTotalOrdenesCompra()),
                        'valor_total_gastos_operativos'     => $cierre->getValorTotalGastosOperativos(),
                        'valor_total_gastos_operativos_formateado' => format_currency_consigno($cierre->getValorTotalGastosOperativos())
                    ];
                }
            }

        } catch (Exception $e) {
            $response['message'] = "Error al buscar cierres: " . $e->getMessage();
            http_response_code(500); // Internal Server Error
        }

        header('Content-Type: application/json');
        echo json_encode($response);
        exit;
    }
    #endregion AJAX Search Cierres
}
#endregion Handle POST Actions

#region try
try {
    // Set timezone for any date operations
    date_default_timezone_set('America/Bogota');

    // Get list of active cierres (initially empty, will be populated via AJAX)
    $cierres = [];

    // Get list of active centros de costo for filter dropdown
    $centros_costos = CentroCosto::get_list($conexion);

} catch (PDOException $e) {
    // Specific handling for database errors
    $error_display = 'show';
    $error_text    = "Error de base de datos al obtener los datos para consulta de cierres.";
} catch (Exception $e) {
    // General error handling
    $error_display = 'show';
    $error_text    = "Ocurrió un error inesperado: " . $e->getMessage();
}
#endregion try

require_once __ROOT__ . '/views/lcierres.view.php';
