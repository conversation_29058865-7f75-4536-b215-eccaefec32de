<?php
/**
 * Controlador para la creación de órdenes de compra
 *
 * Este controlador maneja la creación de nuevas órdenes de compra con sus detalles,
 * incluyendo validación de productos, actualización automática de inventario
 * y creación de movimientos de inventario.
 */

use App\classes\OrdenCompra;
use App\classes\OrdenCompraDetalle;
use App\classes\Proveedor;
use App\classes\CentroCosto;
use App\classes\Producto;
use App\classes\Inventario;
use App\classes\InventarioMovimiento;

if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

/** @var PDO $conexion */
global $conexion;

require_once dirname(__FILE__, 2) . '/config/config.php';
require_once __ROOT__ . '/src/sessions/sessions.php';
require_once __ROOT__ . '/src/general/general.php';
require_once __ROOT__ . '/src/general/validaciones.php';
require_once __ROOT__ . '/src/general/preparar.php';

// Check for valid database connection before proceeding
if (!$conexion instanceof PDO) {
    error_log("Error crítico: No hay conexión a la base de datos en iorden_compra.php.");
    $response['message'] = 'Error crítico: No se pudo conectar a la base de datos.';
    http_response_code(503); // Service Unavailable
    echo json_encode($response);
    exit;
}

// Initialize response for AJAX requests
$response = ['success' => false, 'message' => ''];

#region Handle POST Actions
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $action = $_POST['action'] ?? '';

    #region Search Productos
    if ($action === 'search_productos') {
        $termino = trim($_POST['termino'] ?? '');

        try {
            $productos_filtrados = Producto::search_by_descripcion($conexion, $termino);

            $response['success']   = true;
            $response['productos'] = [];

            foreach ($productos_filtrados as $producto) {
                $response['productos'][] = [
                    'id'               => $producto->getId(),
                    'descripcion'      => htmlspecialchars($producto->getDescripcion()),
                    'valor'            => $producto->getValor(),
                    'valor_formateado' => format_currency_consigno($producto->getValor())
                ];
            }

        } catch (Exception $e) {
            $response['message'] = "Error al buscar productos: " . $e->getMessage();
            http_response_code(500);
        }

        header('Content-Type: application/json');
        echo json_encode($response);
        exit;
    }
    #endregion Search Productos

    #region Create Orden Compra
    if ($action === 'create_orden_compra') {
        $id_proveedor           = !empty($_POST['id_proveedor']) ? (int)$_POST['id_proveedor'] : null;
        $id_centro_costo        = !empty($_POST['id_centro_costo']) ? (int)$_POST['id_centro_costo'] : null;
        $n_referencia_proveedor = trim($_POST['n_referencia_proveedor'] ?? '');
        $detalles_json          = $_POST['detalles'] ?? '';

        try {
            // Validate required fields
            if (!$id_proveedor || !$id_centro_costo) {
                throw new Exception("Proveedor y Centro de Costo son campos obligatorios.");
            }

            // Verify that the proveedor exists and is active
            $proveedor = Proveedor::get($id_proveedor, $conexion);
            if (!$proveedor || $proveedor->getEstado() !== 1) {
                throw new Exception("El proveedor seleccionado no existe o está inactivo.");
            }

            // Verify that the centro_costo exists and is active
            $centro_costo = CentroCosto::get($id_centro_costo, $conexion);
            if (!$centro_costo || $centro_costo->getEstado() !== 1) {
                throw new Exception("El centro de costo seleccionado no existe o está inactivo.");
            }

            // Parse and validate details
            $detalles = json_decode($detalles_json, true);
            if (!$detalles || !is_array($detalles) || empty($detalles)) {
                throw new Exception("Debe agregar al menos un producto a la orden de compra.");
            }

            // Validate each detail
            $valor_total_orden = 0;
            foreach ($detalles as $detalle) {
                if (!isset($detalle['id_producto']) || !isset($detalle['cantidad']) || !isset($detalle['valor'])) {
                    throw new Exception("Datos de producto incompletos.");
                }

                // Validate id_producto is a positive integer
                $id_producto = (int)$detalle['id_producto'];
                if ($id_producto <= 0) {
                    throw new Exception("ID de producto inválido: {$detalle['id_producto']}");
                }

                // Verify that the product exists and is active
                $producto = Producto::get($id_producto, $conexion);
                if (!$producto || $producto->getEstado() !== 1) {
                    throw new Exception("El producto con ID {$id_producto} no existe o está inactivo.");
                }

                if ($detalle['cantidad'] <= 0) {
                    throw new Exception("La cantidad debe ser mayor que cero.");
                }

                if ($detalle['valor'] < 0) {
                    throw new Exception("El valor no puede ser negativo.");
                }

                $valor_total_orden += $detalle['cantidad'] * $detalle['valor'];
            }

            // Start transaction
            $conexion->beginTransaction();

            // Create OrdenCompra
            $orden = new OrdenCompra();
            $orden->setValor_total($valor_total_orden);
            $orden->setId_proveedor($id_proveedor);
            $orden->setId_centro_costo($id_centro_costo);
            $orden->setId_usuario($logged_usuario_info->getId());
            $orden->setN_referencia_proveedor($n_referencia_proveedor ?: null);
            $orden->setEstado(1);

            $orden_id = $orden->crear($conexion);

            if (!$orden_id) {
                throw new Exception("Error al crear la orden de compra.");
            }

            // Create OrdenCompraDetalle records and update inventory
            foreach ($detalles as $detalle) {
                // Create detail record
                $orden_detalle = new OrdenCompraDetalle();
                $orden_detalle->setId_orden_compra($orden_id);
                $orden_detalle->setId_producto($detalle['id_producto']);
                $orden_detalle->setCantidad($detalle['cantidad']);
                $orden_detalle->setValor($detalle['valor']);
                $orden_detalle->setValor_total($detalle['cantidad'] * $detalle['valor']);

                $detalle_id = $orden_detalle->crear($conexion);
                if (!$detalle_id) {
                    throw new Exception("Error al crear detalle de orden de compra.");
                }

                // Update or create inventory record
                $inventario = Inventario::get_by_centro_producto($id_centro_costo, $detalle['id_producto'], $conexion);

                if ($inventario) {
                    // Update existing inventory
                    $nueva_cantidad = $inventario->getCantidad() + $detalle['cantidad'];
                    $inventario->setCantidad($nueva_cantidad);
                    $inventario->modificar($conexion);
                } else {
                    // Create new inventory record
                    $inventario = new Inventario();
                    $inventario->setId_centro_costo($id_centro_costo);
                    $inventario->setId_producto($detalle['id_producto']);
                    $inventario->setCantidad($detalle['cantidad']);
                    $inventario->crear($conexion);
                }

                // Create inventory movement record
                $movimiento = new InventarioMovimiento(
                    $id_centro_costo,
                    $detalle['id_producto'],
                    'ingreso',
                    $detalle['cantidad'],
                    $logged_usuario_info->getId(),
                    "Orden de compra #" . $orden_id
                );

                $movimiento_id = $movimiento->crear($conexion);
                if (!$movimiento_id) {
                    throw new Exception("Error al crear movimiento de inventario para producto ID: {$detalle['id_producto']}");
                }
            }

            $conexion->commit();

            $response['success'] = true;
            $response['message'] = 'Orden de compra creada exitosamente.';
            $response['orden_id'] = $orden_id;

        } catch (Exception $e) {
            $conexion->rollBack();
            $response['message'] = "Error al crear orden de compra: " . $e->getMessage();
            http_response_code(500);
        }

        header('Content-Type: application/json');
        echo json_encode($response);
        exit;
    }
    #endregion Create Orden Compra
}
#endregion Handle POST Actions

#region try
try {
    // Set timezone for any date operations
    date_default_timezone_set('America/Bogota');

    // Get list of active proveedores
    $proveedores = Proveedor::get_list($conexion);

    // Get list of active centros de costo
    $centros_costos = CentroCosto::get_list($conexion);

    $error_text      = '';
    $error_display   = 'hide';
    $success_text    = '';
    $success_display = 'hide';

} catch (PDOException $e) {
    // Specific handling for database errors
    $error_display = 'show';
    $error_text    = "Error de base de datos al obtener los datos para crear orden de compra.";
} catch (Exception $e) {
    // General error handling
    $error_display = 'show';
    $error_text    = "Ocurrió un error inesperado: " . $e->getMessage();
}
#endregion try

require_once __ROOT__ . '/views/iorden_compra.view.php';

?>
