<?php

// Iniciar sesión si es necesario
use App\classes\CitaProgramada;
use App\classes\Empleado;
use App\classes\Servicio;
use App\classes\Cliente;

if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

/** @var PDO $conexion */
global $conexion;

require_once dirname(__FILE__, 2) . '/config/config.php';
require_once __ROOT__ . '/src/sessions/sessions.php';
require_once __ROOT__ . '/src/general/general.php';
require_once __ROOT__ . '/src/general/validaciones.php';
require_once __ROOT__ . '/src/general/preparar.php';

// Check for valid database connection before proceeding
if (!$conexion instanceof PDO) {
    error_log("Error crítico: No hay conexión a la base de datos en lcitas_programadas.php.");
    $response['message'] = 'Error crítico: No se pudo conectar a la base de datos.';
    http_response_code(503); // Service Unavailable
    echo json_encode($response);
    exit;
}

// Establecer la zona horaria para Colombia
date_default_timezone_set('America/Bogota');

// Variables para mensajes de éxito/error
$success_text    = null;
$success_display = null;
$error_text      = null;
$error_display   = null;

#region Handle Flash Message Success
if (isset($_SESSION['flash_message_success'])) {
    $success_text    = $_SESSION['flash_message_success'];
    $success_display = 'show';
    unset($_SESSION['flash_message_success']);
}
#endregion Handle Flash Message Success

#region Handle Flash Message Error
if (isset($_SESSION['flash_message_error'])) {
    $error_text    = $_SESSION['flash_message_error'];
    $error_display = 'show';
    unset($_SESSION['flash_message_error']);
}
#endregion Handle Flash Message Error

#region Handle AJAX Requests
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['action'])) {
    header('Content-Type: application/json');
    $response = ['success' => false, 'message' => 'Error desconocido.'];

    try {
        $action = $_POST['action'];

        // Obtener citas programadas para el calendario
        if ($action === 'get_citas_calendario') {
            $fecha_inicio = filter_input(INPUT_POST, 'start', FILTER_SANITIZE_SPECIAL_CHARS);
            $fecha_fin = filter_input(INPUT_POST, 'end', FILTER_SANITIZE_SPECIAL_CHARS);
            $id_empleado = filter_input(INPUT_POST, 'id_empleado', FILTER_SANITIZE_SPECIAL_CHARS);
            $id_servicio = filter_input(INPUT_POST, 'id_servicio', FILTER_VALIDATE_INT);
            $filtro_cliente = filter_input(INPUT_POST, 'filtro_cliente', FILTER_SANITIZE_SPECIAL_CHARS);

            // Construir array de filtros para la consulta optimizada
            $filtros = [];

            if ($fecha_inicio && $fecha_fin) {
                $filtros['fecha_inicio'] = $fecha_inicio;
                $filtros['fecha_fin'] = $fecha_fin;
            }

            if ($id_empleado) {
                $filtros['id_empleado'] = $id_empleado;
            }

            if ($id_servicio) {
                $filtros['id_servicio'] = $id_servicio;
            }

            if (!empty(trim($filtro_cliente))) {
                $filtros['filtro_cliente'] = trim($filtro_cliente);
            }

            // Obtener citas programadas con filtros aplicados a nivel de base de datos
            $citas_programadas = CitaProgramada::get_list_filtered($conexion, $filtros);

            // Formatear las citas para el calendario
            $eventos = [];
            foreach ($citas_programadas as $cita) {
                // Construir título con cliente y empleado
                $titulo_partes = [];

                if ($cita->getNombre_cliente()) {
                    $titulo_partes[] = $cita->getNombre_cliente();
                }

                if ($cita->getNombre_empleado()) {
                    $titulo_partes[] = $cita->getNombre_empleado();
                } else {
                    $titulo_partes[] = 'Sin asignar';
                }

                // Agregar duración con icono de reloj
                $titulo_partes[] = '🕐 ' . $cita->getDuracion() . ' min';

                $titulo = implode(' - ', $titulo_partes);

                $eventos[] = [
                    'id' => $cita->getId(),
                    'title' => $titulo,
                    'start' => $cita->getFecha_inicio(),
                    'end' => $cita->getFecha_fin(),
                    'backgroundColor' => $cita->getId_empleado() ? '#3498db' : '#f39c12', // Azul para asignados, naranja para no asignados
                    'borderColor' => $cita->getId_empleado() ? '#2980b9' : '#e67e22',
                    'textColor' => '#ffffff',
                    'extendedProps' => [
                        'id_empleado' => $cita->getId_empleado(),
                        'id_cliente' => $cita->getId_cliente(),
                        'nombre_cliente' => $cita->getNombre_cliente(),
                        'duracion' => $cita->getDuracion(),
                        'estado' => $cita->getEstado()
                    ]
                ];
            }

            $response = [
                'success' => true,
                'eventos' => $eventos
            ];
        }

        // Obtener detalles de una cita programada
        else if ($action === 'get_cita_detalles') {
            $id_cita = filter_input(INPUT_POST, 'id_cita', FILTER_VALIDATE_INT);

            if (!$id_cita) {
                throw new Exception("ID de cita inválido");
            }

            $cita = CitaProgramada::get($id_cita, $conexion);

            if (!$cita) {
                throw new Exception("Cita no encontrada");
            }

            // Obtener servicios asociados a la cita
            $servicios = $cita->getServicios($conexion);
            $servicios_data = [];
            $total_valor = 0;

            foreach ($servicios as $servicio) {
                $servicios_data[] = [
                    'id' => $servicio->getId(),
                    'descripcion' => $servicio->getDescripcion(),
                    'valor' => $servicio->getValor(),
                    'duracion' => $servicio->getDuracion()
                ];
                $total_valor += $servicio->getValor();
            }

            $response = [
                'success' => true,
                'cita' => [
                    'id' => $cita->getId(),
                    'fecha_inicio' => $cita->getFecha_inicio(),
                    'fecha_fin' => $cita->getFecha_fin(),
                    'id_empleado' => $cita->getId_empleado(),
                    'nombre_empleado' => $cita->getNombre_empleado() ?: 'Sin asignar',
                    'id_cliente' => $cita->getId_cliente(),
                    'nombre_cliente' => $cita->getNombre_cliente() ?: 'Cliente no encontrado',
                    'celular_cliente' => $cita->getCelular_cliente() ?: 'No disponible',
                    'duracion' => $cita->getDuracion(),
                    'estado' => $cita->getEstado(),
                    'servicios' => $servicios_data,
                    'total_valor' => $total_valor
                ]
            ];
        }

        // Obtener servicios disponibles
        else if ($action === 'get_servicios') {
            $servicios = Servicio::get_list($conexion);
            $servicios_data = [];

            foreach ($servicios as $servicio) {
                $servicios_data[] = [
                    'id' => $servicio->getId(),
                    'descripcion' => $servicio->getDescripcion(),
                    'valor' => $servicio->getValor(),
                    'duracion' => $servicio->getDuracion()
                ];
            }

            $response = [
                'success' => true,
                'servicios' => $servicios_data
            ];
        }

        // Buscar clientes
        else if ($action === 'buscar_clientes') {
            $termino = filter_input(INPUT_POST, 'termino', FILTER_SANITIZE_SPECIAL_CHARS);
            $termino = trim($termino ?? ''); // Ensure we have a string, not null

            if (empty($termino) || strlen($termino) < 2) {
                $response = [
                    'success' => true,
                    'clientes' => []
                ];
            } else {
                $clientes = Cliente::buscar($termino, $conexion);
                $clientes_data = [];

                foreach ($clientes as $cliente) {
                    $clientes_data[] = [
                        'id' => $cliente->getId(),
                        'nombre' => $cliente->getNombre(),
                        'celular' => $cliente->getCelular()
                    ];
                }

                $response = [
                    'success' => true,
                    'clientes' => $clientes_data
                ];
            }
        }

        // Crear nuevo cliente
        else if ($action === 'crear_cliente') {
            $nombre = filter_input(INPUT_POST, 'nombre', FILTER_SANITIZE_SPECIAL_CHARS);
            $celular = filter_input(INPUT_POST, 'celular', FILTER_SANITIZE_SPECIAL_CHARS);

            if (empty(trim($nombre))) {
                throw new Exception("El nombre es obligatorio");
            }

            if (empty(trim($celular))) {
                throw new Exception("El celular es obligatorio");
            }

            $cliente = new Cliente();
            $cliente->setNombre(trim($nombre));
            $cliente->setCelular(trim($celular));

            $id_cliente = $cliente->crear($conexion);

            if (!$id_cliente) {
                throw new Exception("Error al crear el cliente");
            }

            $response = [
                'success' => true,
                'message' => 'Cliente creado correctamente',
                'cliente' => [
                    'id' => $id_cliente,
                    'nombre' => $cliente->getNombre(),
                    'celular' => $cliente->getCelular()
                ]
            ];
        }

        // Crear una nueva cita programada
        else if ($action === 'crear_cita') {
            $fecha_inicio = filter_input(INPUT_POST, 'fecha_inicio', FILTER_SANITIZE_SPECIAL_CHARS);
            $id_empleado = filter_input(INPUT_POST, 'id_empleado', FILTER_VALIDATE_INT);
            $id_cliente = filter_input(INPUT_POST, 'id_cliente', FILTER_VALIDATE_INT);
            $servicios_json = filter_input(INPUT_POST, 'servicios', FILTER_SANITIZE_SPECIAL_CHARS);

            if (empty($fecha_inicio)) {
                throw new Exception("La fecha de inicio es obligatoria");
            }

            if (!$id_cliente) {
                throw new Exception("Debe seleccionar un cliente");
            }

            if (empty($servicios_json)) {
                throw new Exception("Debe seleccionar al menos un servicio");
            }

            $servicios_ids = json_decode($servicios_json);

            if (empty($servicios_ids) || !is_array($servicios_ids)) {
                throw new Exception("Formato de servicios inválido");
            }

            // Crear la cita programada
            $cita = new CitaProgramada();
            $cita->setFecha_inicio($fecha_inicio);
            $cita->setId_cliente($id_cliente);

            // Establecer id_empleado (puede ser null)
            if ($id_empleado) {
                $cita->setId_empleado($id_empleado);
            }

            // Establecer centro de costo automáticamente desde la sesión
            if (isset($_SESSION[CENTRO_COSTO_SESSION]) && !empty($_SESSION[CENTRO_COSTO_SESSION])) {
                $cita->setId_centro_costo((int)$_SESSION[CENTRO_COSTO_SESSION]);
            }

            // Establecer duración inicial (se recalculará después)
            $cita->setDuracion(30);

            // Guardar la cita en la base de datos
            $id_cita = $cita->crear($conexion);

            if (!$id_cita) {
                throw new Exception("Error al crear la cita programada");
            }

            // Agregar servicios a la cita
            foreach ($servicios_ids as $id_servicio) {
                $cita->agregarServicio($id_servicio, $conexion);
            }

            // Recalcular duración basada en los servicios
            $cita->calcularDuracion($conexion);

            $response = [
                'success' => true,
                'message' => 'Cita programada creada correctamente',
                'id_cita' => $id_cita
            ];
        }

        // Modificar una cita programada existente
        else if ($action === 'modificar_cita') {
            $id_cita = filter_input(INPUT_POST, 'id_cita', FILTER_VALIDATE_INT);
            $fecha_inicio = filter_input(INPUT_POST, 'fecha_inicio', FILTER_SANITIZE_SPECIAL_CHARS);
            $id_empleado = filter_input(INPUT_POST, 'id_empleado', FILTER_VALIDATE_INT);

            if (!$id_cita) {
                throw new Exception("ID de cita inválido");
            }

            if (empty($fecha_inicio)) {
                throw new Exception("La fecha de inicio es obligatoria");
            }

            // Obtener la cita existente
            $cita = CitaProgramada::get($id_cita, $conexion);

            if (!$cita) {
                throw new Exception("Cita no encontrada");
            }

            // Actualizar datos
            $cita->setFecha_inicio($fecha_inicio);

            // Establecer id_empleado (puede ser null)
            if ($id_empleado) {
                $cita->setId_empleado($id_empleado);
            } else {
                $cita->setId_empleado(null);
            }

            // Guardar cambios
            $resultado = $cita->modificar($conexion);

            if (!$resultado) {
                throw new Exception("Error al modificar la cita programada");
            }

            $response = [
                'success' => true,
                'message' => 'Cita programada modificada correctamente',
                'id_cita' => $id_cita
            ];
        }

        // Cancelar una cita programada
        else if ($action === 'cancelar_cita') {
            $id_cita = filter_input(INPUT_POST, 'id_cita', FILTER_VALIDATE_INT);
            $razon_cancelacion = filter_input(INPUT_POST, 'razon_cancelacion', FILTER_SANITIZE_SPECIAL_CHARS);

            if (!$id_cita) {
                throw new Exception("ID de cita inválido");
            }

            if (empty(trim($razon_cancelacion))) {
                throw new Exception("La razón de cancelación es obligatoria");
            }

            // Obtener la cita
            $cita = CitaProgramada::get($id_cita, $conexion);

            if (!$cita) {
                throw new Exception("Cita no encontrada");
            }

            // Cancelar la cita
            $resultado = $cita->cancelar($razon_cancelacion, $conexion);

            if (!$resultado) {
                throw new Exception("Error al cancelar la cita programada");
            }

            $response = [
                'success' => true,
                'message' => 'Cita programada cancelada correctamente',
                'id_cita' => $id_cita
            ];
        }

        // Agregar servicio a una cita
        else if ($action === 'agregar_servicio') {
            $id_cita = filter_input(INPUT_POST, 'id_cita', FILTER_VALIDATE_INT);
            $id_servicio = filter_input(INPUT_POST, 'id_servicio', FILTER_VALIDATE_INT);

            if (!$id_cita || !$id_servicio) {
                throw new Exception("ID de cita o servicio inválido");
            }

            // Obtener la cita
            $cita = CitaProgramada::get($id_cita, $conexion);

            if (!$cita) {
                throw new Exception("Cita no encontrada");
            }

            // Agregar servicio
            $resultado = $cita->agregarServicio($id_servicio, $conexion);

            if (!$resultado) {
                throw new Exception("Error al agregar servicio a la cita programada");
            }

            // Obtener la cita actualizada
            $cita = CitaProgramada::get($id_cita, $conexion);

            $response = [
                'success' => true,
                'message' => 'Servicio agregado correctamente',
                'id_cita' => $id_cita,
                'cita' => [
                    'duracion' => $cita->getDuracion(),
                    'fecha_fin' => $cita->getFecha_fin()
                ]
            ];
        }

        // Eliminar servicio de una cita
        else if ($action === 'eliminar_servicio') {
            $id_cita = filter_input(INPUT_POST, 'id_cita', FILTER_VALIDATE_INT);
            $id_servicio = filter_input(INPUT_POST, 'id_servicio', FILTER_VALIDATE_INT);

            if (!$id_cita || !$id_servicio) {
                throw new Exception("ID de cita o servicio inválido");
            }

            // Obtener la cita
            $cita = CitaProgramada::get($id_cita, $conexion);

            if (!$cita) {
                throw new Exception("Cita no encontrada");
            }

            // Eliminar servicio
            $resultado = $cita->eliminarServicio($id_servicio, $conexion);

            if (!$resultado) {
                throw new Exception("Error al eliminar servicio de la cita programada");
            }

            // Obtener la cita actualizada
            $cita = CitaProgramada::get($id_cita, $conexion);

            $response = [
                'success' => true,
                'message' => 'Servicio eliminado correctamente',
                'id_cita' => $id_cita,
                'cita' => [
                    'duracion' => $cita->getDuracion(),
                    'fecha_fin' => $cita->getFecha_fin()
                ]
            ];
        }

        else {
            throw new Exception("Acción desconocida");
        }

    } catch (Exception $e) {
        $response = [
            'success' => false,
            'message' => $e->getMessage()
        ];
    }

    echo json_encode($response);
    exit;
}
#endregion Handle AJAX Requests

#region Get Data for View
try {
    // Obtener lista de empleados activos para el filtro
    $empleados = Empleado::get_list($conexion);

    // Obtener lista de servicios activos para el filtro
    $servicios = Servicio::get_list($conexion);

    // Obtener lista de clientes activos para el filtro
    $clientes = Cliente::get_list($conexion);

} catch (PDOException $e) {
    $error_display = 'show';
    $error_text    = "Error de base de datos al obtener datos: " . $e->getMessage();
} catch (Exception $e) {
    $error_display = 'show';
    $error_text    = "Error inesperado: " . $e->getMessage();
}
#endregion Get Data for View

// Cargar la vista
require_once __ROOT__ . '/views/lcitas_programadas.view.php';
