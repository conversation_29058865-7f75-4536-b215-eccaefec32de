# Empleado-CentroCosto Many-to-Many Relationship Implementation

## Overview
This document describes the implementation of the many-to-many relationship between Empleado and CentroCosto classes, following the exact same pattern as the existing Usuario-CentroCosto association.

## Database Changes

### New Table: `centros_costos_empleados`
A junction table was created to manage the many-to-many relationship:

```sql
CREATE TABLE `centros_costos_empleados` (
    `id` int(11) NOT NULL AUTO_INCREMENT,
    `id_centro_costo` int(11) NOT NULL,
    `id_empleado` int(11) NOT NULL,
    `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
    PRIMARY KEY (`id`),
    UNIQUE KEY `unique_centro_empleado` (`id_centro_costo`, `id_empleado`),
    FOREIGN KEY (`id_centro_costo`) REFERENCES `centros_costos` (`id`) ON DELETE CASCADE,
    FOREIGN KEY (`id_empleado`) REFERENCES `empleados` (`id`) ON DELETE CASCADE
);
```

**Installation**: Run the SQL script `database_updates/create_centros_costos_empleados_table.sql`

## Class Changes

### Empleado Class (`src/classes/Empleado.php`)

#### New Methods Added:

1. **`obtenerCentrosCostos(PDO $conexion): array`**
   - Retrieves all cost centers associated with the employee
   - Returns array of CentroCosto objects
   - Only returns active cost centers (estado = 1)

2. **`sincronizarCentrosCostos(array $ids_centros_costos, PDO $conexion): bool`**
   - Synchronizes cost center associations for the employee
   - Deletes existing associations and inserts new ones
   - Uses database transactions for data integrity
   - Validates cost center IDs and active status

### CentroCosto Class (`src/classes/CentroCosto.php`)

#### New Methods Added:

1. **`get_empleados_by_centro(int $id_centro_costo, PDO $conexion): array`**
   - Static method to get all employees associated with a cost center
   - Returns array of Empleado objects
   - Only returns active employees (estado = 1)

2. **`sync_centro_empleado_associations(int $id_centro_costo, array $ids_empleados, PDO $conexion): bool`**
   - Static method to synchronize employee associations for a cost center
   - Deletes existing associations and inserts new ones
   - Uses database transactions for data integrity
   - Validates employee IDs and active status

3. **`get_list_with_counts(PDO $conexion): array`**
   - Enhanced version of existing method that returns cost centers with both user and employee counts
   - Adds `employee_count` property to CentroCosto objects
   - Uses LEFT JOINs and COUNT(DISTINCT) for accurate counts

#### New Property Added:
- `public ?int $employee_count = null;` - Non-persistent property for displaying employee counts

## Controller Changes

### `src/lcentros_costos.php`

#### New Imports:
- Added `use App\classes\Empleado;`

#### Updated Data Loading:
- Changed from `get_list_with_user_count()` to `get_list_with_counts()` to include employee counts
- Added `$empleados = Empleado::get_list($conexion);` for employee management

#### New AJAX Endpoints:

1. **POST `/centros-costos` with `action=sync_empleados_centro`**
   - Handles employee association synchronization
   - Accepts `centroId` and `empleados` (JSON array) parameters
   - Returns success/error response

2. **GET `/centros-costos?action=get_empleados_centro&centro_id={id}`**
   - Returns all employees and current associations for a cost center
   - Response includes `empleados` array and `centro_empleados` array of IDs

## View Changes

### `views/lcentros_costos.view.php`

#### Table Structure Updates:
- Added "# Empleados" column header
- Increased actions column width to accommodate new button
- Added employee count display with warning badge styling

#### New UI Elements:

1. **Employee Association Button**
   - Warning-colored button with user-tie icon
   - Title: "Asociar Empleados"
   - Opens employee management modal

2. **Employee Management Modal (`#gestionarEmpleadosModal`)**
   - Similar structure to user management modal
   - Displays list of all employees with checkboxes
   - Shows employee name and email
   - Includes select-all functionality
   - Panel-based design with dark theme consistency

#### JavaScript Functionality:

1. **Modal Elements**
   - Added employee modal elements and Bootstrap modal initialization
   - Employee loading, container, table body, error div, and save button elements

2. **Event Handlers**
   - Added employee button click handler in table event delegation
   - Employee modal management with loading states

3. **AJAX Functions**
   - `loadEmpleadosCentro(centroId)` - Loads employees for cost center
   - `renderEmpleados(empleados, centroEmpleados)` - Renders employee checkboxes
   - `updateSelectAllEmpleadosState()` - Manages select-all checkbox state
   - Employee save functionality with form data submission

4. **UI Updates**
   - `updateEmployeeCountInMainTable(centroId)` - Updates employee count badges
   - Updated new row creation to include employee button and count
   - Updated edit form to maintain employee button data attributes

## Features

### Employee Association Management
- **Modal Interface**: Clean, user-friendly modal for managing employee associations
- **Checkbox Selection**: Individual and bulk selection of employees
- **Real-time Updates**: Employee counts update immediately after saving
- **Error Handling**: Comprehensive error handling with user-friendly messages
- **Validation**: Server-side validation of employee IDs and active status

### Data Integrity
- **Database Transactions**: All association changes use transactions
- **Foreign Key Constraints**: Proper CASCADE behavior on deletions
- **Unique Constraints**: Prevents duplicate associations
- **Active Status Validation**: Only active employees can be associated

### User Experience
- **Consistent UI**: Follows exact same patterns as user association
- **Visual Feedback**: Loading states, success/error messages
- **Responsive Design**: Works on different screen sizes
- **Accessibility**: Proper ARIA labels and keyboard navigation

## Usage

1. **Navigate to Cost Centers page** (`/centros-costos`)
2. **Click the warning "Asociar Empleados" button** for any cost center
3. **Select/deselect employees** using checkboxes or select-all
4. **Click "Guardar Cambios"** to save associations
5. **View updated employee counts** in the main table

## Integration Points

### Existing System Compatibility
- Follows established patterns from Usuario-CentroCosto implementation
- Uses same validation, error handling, and UI patterns
- Maintains consistency with existing codebase architecture
- No breaking changes to existing functionality

### Future Enhancements
- Employee filtering by cost center in other modules
- Reporting capabilities using employee-cost center relationships
- Permission management based on cost center associations
- Dashboard widgets showing employee distribution

## Testing Recommendations

1. **Database Operations**
   - Test association creation, update, and deletion
   - Verify transaction rollback on errors
   - Test foreign key constraint behavior

2. **UI Functionality**
   - Test modal opening/closing
   - Verify checkbox selection and select-all behavior
   - Test save operation and count updates

3. **Error Handling**
   - Test with invalid employee IDs
   - Test with inactive employees
   - Test network error scenarios

4. **Integration**
   - Verify no impact on existing user associations
   - Test concurrent user and employee association management
   - Verify proper data loading and display
