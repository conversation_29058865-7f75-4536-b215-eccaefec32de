<?php
// Check where PHP error logs are configured
echo "<h2>PHP Error Log Configuration</h2>";
echo "<strong>Error Log Location:</strong> " . ini_get('error_log') . "<br>";
echo "<strong>Log Errors:</strong> " . (ini_get('log_errors') ? 'Enabled' : 'Disabled') . "<br>";
echo "<strong>Display Errors:</strong> " . (ini_get('display_errors') ? 'Enabled' : 'Disabled') . "<br>";

// Test error logging
error_log("TEST: This is a test error log entry from check_error_log.php");
echo "<br><strong>Test log entry written.</strong> Check the error log file above.";

// Show recent error log entries if file exists
$error_log_file = ini_get('error_log');
if ($error_log_file && file_exists($error_log_file)) {
    echo "<h3>Recent Error Log Entries (last 20 lines):</h3>";
    $lines = file($error_log_file);
    $recent_lines = array_slice($lines, -20);
    echo "<pre style='background: #f0f0f0; padding: 10px; max-height: 400px; overflow-y: scroll;'>";
    foreach ($recent_lines as $line) {
        echo htmlspecialchars($line);
    }
    echo "</pre>";
} else {
    echo "<p><strong>Error log file not found or not configured.</strong></p>";
}
?>
