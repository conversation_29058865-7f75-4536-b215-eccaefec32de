<?php
#region DOCS

/** @var CentroCosto[] $centros_costo */
/** @var Inventario[] $inventarios */
/** @var string|null $success_text Success message to display */
/** @var string|null $success_display Whether to show success message ('show' or null) */
/** @var string|null $error_text Error message to display */
/** @var string|null $error_display Whether to show error message ('show' or null) */

use App\classes\CentroCosto;
use App\classes\Inventario;

#endregion DOCS
?>
<!DOCTYPE html>
<html lang="en" class="dark-mode">
<head>
    <meta charset="utf-8"/>
    <title><?php echo APP_NAME; ?> | Gestión de Inventario</title>
    <meta content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no" name="viewport"/>
    <meta content="" name="description"/>
    <meta content="" name="author"/>

    <?php #region HEAD ?>
    <?php require_once __ROOT__ . '/views/general/head.view.php'; ?>
    <?php #endregion HEAD ?>
</head>
<body>
<!-- BEGIN #loader -->
<div id="loader" class="app-loader">
    <span class="spinner"></span>
</div>
<!-- END #loader -->

<!-- BEGIN #app -->
<div id="app" class="app app-header-fixed app-sidebar-fixed">
    <!-- #header -->
    <?php require_once __ROOT__ . '/views/general/header.view.php'; ?>

    <!-- #sidebar -->
    <?php require_once __ROOT__ . '/views/general/sidebar.view.php'; ?>

    <!-- BEGIN #content -->
    <div id="content" class="app-content">
        <?php #region PAGE HEADER ?>
        <div class="d-flex align-items-center mb-3">
            <div>
                <h4 class="mb-0">Gestión de Inventario</h4>
                <p class="mb-0 text-muted">Consulta y gestión del inventario por centro de costo</p>
            </div>
        </div>
        <?php #endregion PAGE HEADER ?>

        <div class="row">
            <div class="col-12">
                <?php #region FILTERS ?>
                <div class="panel panel-inverse no-border-radious">
                    <div class="panel-heading no-border-radious">
                        <h4 class="panel-title">Filtros de Búsqueda</h4>
                    </div>
                    <div class="panel-body">
                        <form id="filter-form">
                            <div class="row">
                                <!-- Centro de Costo Filter (Mandatory) -->
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="centro-costo-select" class="form-label">
                                            <strong>Centro de Costo <span style="color: red;">*</span></strong>
                                        </label>
                                        <select class="form-select" id="centro-costo-select" name="id_centro_costo" required>
                                            <option value="">Seleccione un centro de costo...</option>
                                            <?php foreach ($centros_costo as $centro): ?>
                                                <option value="<?php echo $centro->getId(); ?>">
                                                    <?php echo htmlspecialchars($centro->getNombre()); ?>
                                                </option>
                                            <?php endforeach; ?>
                                        </select>
                                    </div>
                                </div>

                                <!-- Product Description Filter (Optional) -->
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="descripcion-producto-input" class="form-label">
                                            Descripción del Producto
                                        </label>
                                        <input type="text"
                                                class="form-control"
                                                id="descripcion-producto-input"
                                                name="descripcion_producto"
                                                placeholder="Buscar por descripción del producto...">
                                    </div>
                                </div>
                            </div>

                            <div class="row">
                                <div class="col-12">
                                    <button type="submit" class="btn btn-primary" id="search-btn">
                                        <i class="fa fa-search me-1"></i> Buscar Inventario
                                    </button>
                                    <button type="button" class="btn btn-secondary" id="clear-btn">
                                        <i class="fa fa-times me-1"></i> Limpiar
                                    </button>
                                </div>
                            </div>
                        </form>
                    </div>
                </div>
                <?php #endregion FILTERS ?>

                <?php #region RESULTS ?>
                <div class="panel panel-inverse mt-3 no-border-radious">
                    <div class="panel-heading no-border-radious">
                        <h4 class="panel-title">Resultados del Inventario</h4>
                    </div>
                    <div>
                        <!-- No selection message -->
                        <div id="no-selection-message" class="text-center py-4">
                            <i class="fa fa-info-circle fa-2x text-muted mb-2"></i>
                            <p class="text-muted">Seleccione un centro de costo para ver el inventario disponible.</p>
                        </div>

                        <!-- Loading message -->
                        <div id="loading-message" class="text-center py-4" style="display: none;">
                            <div class="spinner-border text-primary" role="status">
                                <span class="visually-hidden">Cargando...</span>
                            </div>
                            <p class="text-muted mt-2">Cargando inventario...</p>
                        </div>

                        <!-- Results table -->
                        <div id="results-table-container" style="overflow-x: auto; display: none;">
                            <table class="table table-hover table-sm mb-0" id="inventario-table">
                                <thead class="table-dark">
                                    <tr>
                                        <th class="text-center" style="width: 120px;">Acciones</th>
                                        <th>Centro de Costo</th>
                                        <th>Descripción del Producto</th>
                                        <th class="text-end" style="width: 120px;">Cantidad</th>
                                        <th class="text-center" style="width: 100px;">Estado</th>
                                    </tr>
                                </thead>
                                <tbody id="inventario-table-body">
                                    <!-- Results will be populated via AJAX -->
                                </tbody>
                            </table>
                        </div>

                        <!-- No results message -->
                        <div id="no-results-message" class="text-center py-4" style="display: none;">
                            <i class="fa fa-search fa-2x text-muted mb-2"></i>
                            <p class="text-muted">No se encontraron registros de inventario con los filtros seleccionados.</p>
                        </div>
                    </div>
                </div>
                <?php #endregion RESULTS ?>

            </div>
            <!-- END col-12 -->
        </div>
        <!-- END row -->
    </div>
    <!-- END #content -->

    <!-- BEGIN scroll-top-btn -->
    <a href="#" class="btn btn-icon btn-circle btn-success btn-scroll-to-top" data-toggle="scroll-to-top"><i class="fa fa-angle-up"></i></a>
    <!-- END scroll-top-btn -->
</div>
<!-- END #app -->

<?php #region JS ?>
<?php require_once __ROOT__ . '/views/general/core_js.view.php'; ?>

<!-- ================== BEGIN PAGE LEVEL JS ================== -->
<script>
$(document).ready(function() {
    const filterForm               = document.getElementById('filter-form');
    const centroCostoSelect        = document.getElementById('centro-costo-select');
    const descripcionProductoInput = document.getElementById('descripcion-producto-input');
    const searchBtn                = document.getElementById('search-btn');
    const clearBtn                 = document.getElementById('clear-btn');

    const noSelectionMessage    = document.getElementById('no-selection-message');
    const loadingMessage        = document.getElementById('loading-message');
    const resultsTableContainer = document.getElementById('results-table-container');
    const noResultsMessage      = document.getElementById('no-results-message');
    const inventarioTableBody   = document.getElementById('inventario-table-body');

    // Form submit handler
    filterForm.addEventListener('submit', function(e) {
        e.preventDefault();

        const centroCostoId = centroCostoSelect.value;
        if (!centroCostoId) {
            showSweetAlertError('Error', 'Debe seleccionar un centro de costo.');
            return;
        }

        searchInventario();
    });

    // Clear button handler
    clearBtn.addEventListener('click', function() {
        filterForm.reset();
        showNoSelectionState();
    });

    // Search function
    function searchInventario() {
        const formData = new FormData();
        formData.append('action', 'buscar');
        formData.append('id_centro_costo', centroCostoSelect.value);
        formData.append('descripcion_producto', descripcionProductoInput.value);

        showLoadingState();

        fetch('inventario', {
            method: 'POST',
            body: formData
        })
        .then(response => {
            if (!response.ok) {
                throw new Error(`Error ${response.status}: ${response.statusText}`);
            }
            return response.json();
        })
        .then(data => {
            if (data.success) {
                displayResults(data.inventarios);
            } else {
                showSweetAlertError('Error', data.message);
                showNoResultsState();
            }
        })
        .catch(error => {
            showSweetAlertError('Error', 'Error al buscar inventario: ' + error.message);
            showNoResultsState();
        });
    }

    // Display results in table
    function displayResults(inventarios) {
        inventarioTableBody.innerHTML = '';

        if (inventarios.length === 0) {
            showNoResultsState();
            return;
        }

        inventarios.forEach(inventario => {
            const row = document.createElement('tr');

            const estadoBadge = inventario.tiene_stock
                ? '<span class="badge bg-success">Con Stock</span>'
                : '<span class="badge bg-warning">Sin Stock</span>';

            row.innerHTML = `
                <td class="text-center align-middle">
                    <button class="btn btn-danger btn-xs"
                            onclick="descartarProducto(${inventario.id_producto}, '${inventario.descripcion_producto}', ${inventario.producto_estado})"
                            title="Descartar producto">
                        <i class="fa fa-trash"></i>
                    </button>
                </td>
                <td class="align-middle">${inventario.nombre_centro_costo}</td>
                <td class="align-middle">${inventario.descripcion_producto}</td>
                <td class="align-middle text-end">${inventario.cantidad_formateada}</td>
                <td class="align-middle text-center">${estadoBadge}</td>
            `;

            inventarioTableBody.appendChild(row);
        });

        showResultsState();
    }

    // State management functions
    function showNoSelectionState() {
        noSelectionMessage.style.display    = 'block';
        loadingMessage.style.display        = 'none';
        resultsTableContainer.style.display = 'none';
        noResultsMessage.style.display      = 'none';
    }

    function showLoadingState() {
        noSelectionMessage.style.display    = 'none';
        loadingMessage.style.display        = 'block';
        resultsTableContainer.style.display = 'none';
        noResultsMessage.style.display      = 'none';
    }

    function showResultsState() {
        noSelectionMessage.style.display    = 'none';
        loadingMessage.style.display        = 'none';
        resultsTableContainer.style.display = 'block';
        noResultsMessage.style.display      = 'none';
    }

    function showNoResultsState() {
        noSelectionMessage.style.display    = 'none';
        loadingMessage.style.display        = 'none';
        resultsTableContainer.style.display = 'none';
        noResultsMessage.style.display      = 'block';
    }

    // Initialize with no selection state
    showNoSelectionState();
});

// Product discard function
function descartarProducto(idProducto, descripcionProducto, estadoProducto) {
    // Check if product is already inactive (estado = 0)
    if (estadoProducto === 0) {
        showSweetAlertError(
            'Producto Inactivo',
            'No se puede descartar este producto porque ya está inactivo en el sistema. Los productos inactivos no pueden ser descartados.'
        );
        return;
    }

    const warningMessage = `Producto: ${descripcionProducto}\n\n¡ADVERTENCIA!\nEsta acción realizará las siguientes operaciones de forma permanente:\n• Establecerá la cantidad de inventario a 0 para este producto en TODOS los centros de costo\n• Desactivará el producto del sistema\n• Creará registros de movimiento de inventario para auditoría\n\nEsta acción NO se puede deshacer.`;

    swal({
        title: '¿Descartar Producto?',
        text: warningMessage,
        icon: 'warning',
        buttons: {
            cancel: {
                text: "Cancelar",
                value: null,
                visible: true,
                className: "btn-secondary",
                closeModal: true
            },
            confirm: {
                text: "Sí, descartar producto",
                value: true,
                visible: true,
                className: "btn-danger",
                closeModal: true
            }
        },
        dangerMode: true
    }).then((willDiscard) => {
        if (willDiscard) {
            performProductDiscard(idProducto);
        }
    });
}

// Perform product discard
function performProductDiscard(idProducto) {
    const formData = new FormData();
    formData.append('action', 'descartar_producto');
    formData.append('id_producto', idProducto);

    fetch('inventario', {
        method: 'POST',
        body: formData
    })
    .then(response => {
        if (!response.ok) {
            throw new Error(`Error ${response.status}: ${response.statusText}`);
        }
        return response.json();
    })
    .then(data => {
        if (data.success) {
            showSweetAlertSuccess('Éxito', data.message);
            // Refresh the search results
            const centroCostoSelect = document.getElementById('centro-costo-select');
            if (centroCostoSelect.value) {
                document.getElementById('filter-form').dispatchEvent(new Event('submit'));
            }
        } else {
            showSweetAlertError('Error', data.message);
        }
    })
    .catch(error => {
        showSweetAlertError('Error', 'Error al descartar producto: ' + error.message);
    });
}
</script>
<!-- ================== END PAGE LEVEL JS ================== -->
<?php #endregion JS ?>

</body>
</html>
