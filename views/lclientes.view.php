<?php
#region DOCS

/** @var Cliente[] $clientes */
/** @var string|null $success_text Success message to display */
/** @var string|null $success_display Whether to show success message ('show' or null) */
/** @var string|null $error_text Error message to display */

/** @var string|null $error_display Whether to show error message ('show' or null) */

use App\classes\Cliente;

#endregion DOCS
?>
<!DOCTYPE html>
<html lang="en" class="dark-mode">
<head>
	<meta charset="utf-8"/>
	<title><?php echo APP_NAME; ?> | Clientes</title>
	<meta content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no" name="viewport"/>
	<meta content="" name="description"/>
	<meta content="" name="author"/>

	<?php #region HEAD ?>
	<?php require_once __ROOT__ . '/views/general/head.view.php'; ?>
	<?php #endregion HEAD ?>
</head>
<body>
<!-- BEGIN #loader -->
<div id="loader" class="app-loader">
	<span class="spinner"></span>
</div>
<!-- END #loader -->

<!-- BEGIN #app -->
<div id="app" class="app app-header-fixed app-sidebar-fixed">
	<!-- #header -->
	<?php require_once __ROOT__ . '/views/general/header.view.php'; ?>

	<!-- #sidebar -->
	<?php require_once __ROOT__ . '/views/general/sidebar.view.php'; ?>

	<!-- BEGIN #content -->
	<div id="content" class="app-content">
		<?php #region PAGE HEADER ?>
		<div class="d-flex align-items-center mb-3">
			<div>
				<h4 class="mb-0">Gestión de Clientes</h4>
				<p class="mb-0 text-muted">Administra los clientes del sistema</p>
			</div>
			<div class="ms-auto">
				<button type="button" class="btn btn-success" data-bs-toggle="modal" data-bs-target="#createClienteModal">
					<i class="fa fa-plus-circle fa-fw me-1"></i> Crear nuevo
				</button>
			</div>
		</div>

		<hr>
		<?php #endregion PAGE HEADER ?>

		<?php #region SEARCH SECTION ?>
		<div class="row mb-3">
			<div class="col-md-6">
				<div class="input-group">
					<span class="input-group-text"><i class="fa fa-search"></i></span>
					<input type="text" class="form-control" id="search-input" placeholder="Buscar por nombre o celular...">
					<button class="btn btn-outline-secondary" type="button" id="clear-search">
						<i class="fa fa-times"></i>
					</button>
				</div>
			</div>
		</div>
		<?php #endregion SEARCH SECTION ?>

		<?php #region PANEL CLIENTES ?>
		<div class="panel panel-inverse mt-3 no-border-radious">
			<div class="panel-heading no-border-radious">
				<h4 class="panel-title">
					Listado de Clientes Activos
				</h4>
			</div>
			<!-- BEGIN PANEL body -->
			<div class="p-1 table-nowrap" style="overflow: auto">
				<?php #region TABLE CLIENTES ?>
				<table class="table table-hover table-sm">
					<thead>
					<tr>
						<th class="w-70px text-center">Acciones</th>
						<th>Nombre</th>
						<th>Celular</th>
						<th class="text-center">Estado</th>
					</tr>
					</thead>
					<tbody class="fs-12px" id="cliente-table-body">
					<?php foreach ($clientes as $cliente): ?>
						<tr data-cliente-id="<?php echo $cliente->getId(); ?>">
							<td class="text-center">
								<?php // Edit Button - Triggers Modal ?>
								<button type="button" class="btn btn-xs btn-primary me-1 btn-edit-cliente"
								        title="Editar Cliente"
								        data-bs-toggle="modal"
								        data-bs-target="#editClienteModal"
								        data-cliente-id="<?php echo $cliente->getId(); ?>"
								        data-nombre="<?php echo htmlspecialchars($cliente->getNombre() ?? ''); ?>"
								        data-celular="<?php echo htmlspecialchars($cliente->getCelular() ?? ''); ?>">
									<i class="fa fa-edit"></i>
								</button>
								<?php // Deactivate Button - Only show if active ?>
								<?php if ($cliente->isActivo()): ?>
									<button type="button" class="btn btn-xs btn-danger btn-desactivar-cliente"
									        title="Desactivar"
									        data-cliente-id="<?php echo $cliente->getId(); ?>"
									        data-nombre="<?php echo htmlspecialchars($cliente->getNombre() ?? ''); ?>">
										<i class="fa fa-trash-alt"></i>
									</button>
								<?php endif; ?>
							</td>
							<td class="cliente-nombre-cell"><?php echo htmlspecialchars($cliente->getNombre()); ?></td>
							<td class="cliente-celular-cell"><?php echo htmlspecialchars($cliente->getCelular()); ?></td>
							<td class="text-center">
								<?php if ($cliente->isActivo()): ?>
									<span class="badge bg-success">Activo</span>
								<?php else: ?>
									<span class="badge bg-danger">Inactivo</span>
								<?php endif; ?>
							</td>
						</tr>
					<?php endforeach; ?>
					<?php if (empty($clientes)): ?>
						<tr>
							<td colspan="4" class="text-center">No hay clientes para mostrar.</td>
						</tr>
					<?php endif; ?>

					</tbody>
				</table>
				<?php #endregion TABLE CLIENTES ?>
			</div>
			<!-- END PANEL body -->
		</div>
		<?php #endregion PANEL CLIENTES ?>

		<?php #region NO RESULTS MESSAGE ?>
		<div id="no-results-message" class="text-center mt-4" style="display: none;">
			<div class="alert alert-info">
				<i class="fa fa-info-circle me-2"></i>
				No se encontraron clientes que coincidan con la búsqueda.
			</div>
		</div>
		<?php #endregion NO RESULTS MESSAGE ?>

		<?php #region Create Cliente Modal ?>
		<div class="modal fade" id="createClienteModal" tabindex="-1" aria-labelledby="createClienteModalLabel" aria-hidden="true">
			<div class="modal-dialog">
				<div class="modal-content">
					<form id="create-cliente-form">
						<div class="modal-header">
							<h5 class="modal-title" id="createClienteModalLabel">Crear Cliente</h5>
							<button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
						</div>
						<div class="modal-body">
							<input type="hidden" name="action" value="crear">

							<div class="mb-3">
								<label for="create-cliente-nombre" class="form-label">Nombre:</label>
								<input type="text" class="form-control" id="create-cliente-nombre" name="nombre" required>
							</div>

							<div class="mb-3">
								<label for="create-cliente-celular" class="form-label">Celular:</label>
								<input type="text" class="form-control" id="create-cliente-celular" name="celular" required>
							</div>

							<div id="create-cliente-error" class="alert alert-danger mt-2" style="display: none;"></div>
						</div>
						<div class="modal-footer">
							<button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancelar</button>
							<button type="submit" class="btn btn-success">Crear</button>
						</div>
					</form>
				</div>
			</div>
		</div>
		<?php #endregion Create Cliente Modal ?>

		<?php #region Edit Cliente Modal ?>
		<div class="modal fade" id="editClienteModal" tabindex="-1" aria-labelledby="editClienteModalLabel" aria-hidden="true">
			<div class="modal-dialog">
				<div class="modal-content">
					<form id="edit-cliente-form">
						<div class="modal-header">
							<h5 class="modal-title" id="editClienteModalLabel">Editar Cliente</h5>
							<button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
						</div>
						<div class="modal-body">
							<input type="hidden" name="clienteId" id="edit-cliente-id">
							<input type="hidden" name="action" value="modificar">

							<div class="mb-3">
								<label for="edit-cliente-nombre" class="form-label">Nombre:</label>
								<input type="text" class="form-control" id="edit-cliente-nombre" name="nombre" required>
							</div>

							<div class="mb-3">
								<label for="edit-cliente-celular" class="form-label">Celular:</label>
								<input type="text" class="form-control" id="edit-cliente-celular" name="celular" required>
							</div>

							<div id="edit-cliente-error" class="alert alert-danger mt-2" style="display: none;"></div>
						</div>
						<div class="modal-footer">
							<button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancelar</button>
							<button type="submit" class="btn btn-primary">Guardar Cambios</button>
						</div>
					</form>
				</div>
			</div>
		</div>
		<?php #endregion Edit Cliente Modal ?>

	</div>
	<!-- END #content -->

	<?php #region Hidden Form for Deactivation ?>
	<form id="deactivate-cliente-form" method="POST" action="clientes" style="display: none;">
		<input type="hidden" name="action" value="desactivar">
		<input type="hidden" name="clienteId" id="deactivate-cliente-id">
	</form>
	<?php #endregion Hidden Form ?>

	<!-- BEGIN scroll-top-btn -->
	<a href="#" class="btn btn-icon btn-circle btn-success btn-scroll-to-top" data-toggle="scroll-to-top"><i class="fa fa-angle-up"></i></a>
	<!-- END scroll-top-btn -->
</div>
<!-- END #app ?>

<?php #region JS ?>
<?php require_once __ROOT__ . '/views/general/core_js.view.php'; ?>

<!-- ================== BEGIN PAGE LEVEL JS ================== -->
<!-- Assuming SweetAlert is loaded in core_js or globally -->
<!-- Assuming SweetAlert helper functions (showSweetAlertSuccess/Error) are available -->
<script>
    document.addEventListener('DOMContentLoaded', function () {
        // Use event delegation on the table body
        const tableBody = document.getElementById('cliente-table-body');

        // Create Cliente Modal Elements
        const createClienteModalElement = document.getElementById('createClienteModal');
        const createClienteModal        = new bootstrap.Modal(createClienteModalElement);
        const createClienteForm         = document.getElementById('create-cliente-form');
        const createClienteErrorDiv     = document.getElementById('create-cliente-error');

        // Edit Cliente Modal Elements
        const editClienteModalElement = document.getElementById('editClienteModal');
        const editClienteModal        = new bootstrap.Modal(editClienteModalElement);
        const editClienteForm         = document.getElementById('edit-cliente-form');
        const editClienteIdInput      = document.getElementById('edit-cliente-id');
        const editClienteNombreInput  = document.getElementById('edit-cliente-nombre');
        const editClienteCelularInput = document.getElementById('edit-cliente-celular');
        const editClienteErrorDiv     = document.getElementById('edit-cliente-error');

        // Search Elements
        const searchInput = document.getElementById('search-input');
        const clearSearchBtn = document.getElementById('clear-search');
        const noResultsMessage = document.getElementById('no-results-message');

        let searchTimeout;

        if (tableBody) {
            tableBody.addEventListener('click', function (event) {
                const deactivateButton = event.target.closest('.btn-desactivar-cliente');
                const editButton       = event.target.closest('.btn-edit-cliente');

                // --- Handle Deactivate Click ---
				<?php #region JS AJAX -- Deactivate Cliente ?>
                if (deactivateButton) {
                    event.preventDefault();
                    const clienteId     = deactivateButton.dataset.clienteId;
                    const clienteNombre = deactivateButton.dataset.nombre || 'este cliente';

                    swal({
                        title     : "Confirmar Desactivación",
                        text      : `¿Seguro que quieres desactivar el cliente '${clienteNombre}'?`,
                        icon      : "warning",
                        buttons   : {
                            cancel : {text: "Cancelar", value: null, visible: true, className: "", closeModal: true},
                            confirm: {text: "Confirmar", value: true, visible: true, className: "btn-danger", closeModal: true}
                        },
                        dangerMode: true,
                    })
                        .then((willDelete) => {
                            if (willDelete) {
                                document.getElementById('deactivate-cliente-id').value = clienteId;
                                document.getElementById('deactivate-cliente-form').submit();
                            }
                        });
                }
				<?php #endregion JS AJAX -- Deactivate Cliente ?>

                // --- Handle Edit Click ---
				<?php #region JS AJAX - Handle Edit click ?>
                if (editButton) {
                    event.preventDefault(); // Prevent default if it was an <a>
                    const clienteId      = editButton.dataset.clienteId;
                    const currentNombre  = editButton.dataset.nombre;
                    const currentCelular = editButton.dataset.celular;

                    // Populate the modal form
                    editClienteIdInput.value          = clienteId;
                    editClienteNombreInput.value      = currentNombre;
                    editClienteCelularInput.value     = currentCelular;
                    editClienteErrorDiv.style.display = 'none'; // Hide previous errors
                    editClienteErrorDiv.textContent   = '';

                    // The data-bs-toggle and data-bs-target attributes on the button handle showing the modal
                }
				<?php #endregion JS AJAX - Edit Cliente ?>
            });
        }

        // --- Handle Create Form Submission (AJAX) ---
		<?php #region JS AJAX - Create Form Submission ?>
        if (createClienteForm) {
            createClienteForm.addEventListener('submit', function (event) {
                event.preventDefault(); // Stop default form submission
                createClienteErrorDiv.style.display = 'none'; // Hide error div initially

                const formData = new FormData(createClienteForm);
                const nombre   = formData.get('nombre').trim(); // Get trimmed name
                const celular  = formData.get('celular').trim(); // Get trimmed celular

                // Basic client-side validation
                if (!nombre) {
                    createClienteErrorDiv.textContent   = 'El nombre no puede estar vacío.';
                    createClienteErrorDiv.style.display = 'block';
                    return; // Stop submission
                }

                if (!celular) {
                    createClienteErrorDiv.textContent   = 'El celular no puede estar vacío.';
                    createClienteErrorDiv.style.display = 'block';
                    return; // Stop submission
                }

                // Disable submit button during request
                const submitButton    = createClienteForm.querySelector('button[type="submit"]');
                submitButton.disabled = true;

                fetch('clientes', {
                    method: 'POST',
                    body  : formData
                })
                    .then(response => {
                        if (!response.ok) {
                            return response.json().then(errData => {
                                throw new Error(errData.message || `Error ${response.status}: ${response.statusText}`);
                            }).catch(() => {
                                throw new Error(`Error ${response.status}: ${response.statusText}`);
                            });
                        }
                        return response.json();
                    })
                    .then(data => {
                        if (data.success) {
                            createClienteModal.hide(); // Close modal on success

                            // Reset the form for next use
                            createClienteForm.reset();

                            // Refresh the search to show updated results
                            performSearch(searchInput.value.trim());

                            showSweetAlertSuccess('Éxito', 'Cliente creado correctamente.');
                        } else {
                            // Show error message inside the modal
                            createClienteErrorDiv.textContent   = data.message || 'Ocurrió un error al crear el Cliente.';
                            createClienteErrorDiv.style.display = 'block';
                        }
                    })
                    .catch(error => {
                        console.error('Error creating Cliente:', error);
                        // Show error message inside the modal
                        createClienteErrorDiv.textContent   = 'Error de red o del servidor: ' + error.message;
                        createClienteErrorDiv.style.display = 'block';
                    })
                    .finally(() => {
                        // Re-enable submit button
                        submitButton.disabled = false;
                    });
            });
        }
		<?php #endregion JS AJAX - Create Form Submission ?>

        // --- Handle Edit Form Submission (AJAX) ---
		<?php #region JS AJAX - Edit Form Submission ?>
        if (editClienteForm) {
            editClienteForm.addEventListener('submit', function (event) {
                event.preventDefault(); // Stop default form submission
                editClienteErrorDiv.style.display = 'none'; // Hide error div initially

                const formData  = new FormData(editClienteForm);
                const clienteId = formData.get('clienteId');
                const newNombre = formData.get('nombre').trim(); // Get trimmed name
                const newCelular = formData.get('celular').trim(); // Get trimmed celular

                // Basic client-side validation
                if (!newNombre) {
                    editClienteErrorDiv.textContent   = 'El nombre no puede estar vacío.';
                    editClienteErrorDiv.style.display = 'block';
                    return; // Stop submission
                }

                if (!newCelular) {
                    editClienteErrorDiv.textContent   = 'El celular no puede estar vacío.';
                    editClienteErrorDiv.style.display = 'block';
                    return; // Stop submission
                }

                // Disable submit button during request
                const submitButton    = editClienteForm.querySelector('button[type="submit"]');
                submitButton.disabled = true;

                fetch('clientes', {
                    method: 'POST',
                    body  : formData
                })
                    .then(response => {
                        if (!response.ok) {
                            return response.json().then(errData => {
                                throw new Error(errData.message || `Error ${response.status}: ${response.statusText}`);
                            }).catch(() => {
                                throw new Error(`Error ${response.status}: ${response.statusText}`);
                            });
                        }
                        return response.json();
                    })
                    .then(data => {
                        if (data.success) {
                            editClienteModal.hide(); // Close modal on success

                            // Update the row in the table directly
                            const tableRow = document.querySelector(`#cliente-table-body tr[data-cliente-id="${clienteId}"]`);
                            if (tableRow) {
                                // Find the cells with the specific classes within that row
                                const nombreCell = tableRow.querySelector('.cliente-nombre-cell');
                                if (nombreCell) {
                                    nombreCell.textContent = newNombre; // Update cell text
                                }

                                const celularCell = tableRow.querySelector('.cliente-celular-cell');
                                if (celularCell) {
                                    celularCell.textContent = newCelular; // Update cell text
                                }

                                // Also update the data attributes on the edit and delete buttons for next time
                                const editButton = tableRow.querySelector('.btn-edit-cliente');
                                if (editButton) {
                                    editButton.dataset.nombre = newNombre;
                                    editButton.dataset.celular = newCelular;
                                }

                                const deleteButton = tableRow.querySelector('.btn-desactivar-cliente');
                                if (deleteButton) {
                                    deleteButton.dataset.nombre = newNombre;
                                }
                            }

                            showSweetAlertSuccess('Éxito', 'Cliente actualizado correctamente.');
                        } else {
                            // Show error message inside the modal
                            editClienteErrorDiv.textContent   = data.message || 'Ocurrió un error al guardar.';
                            editClienteErrorDiv.style.display = 'block';
                        }
                    })
                    .catch(error => {
                        console.error('Error updating Cliente:', error);
                        // Show error message inside the modal
                        editClienteErrorDiv.textContent   = 'Error de red o del servidor: ' + error.message;
                        editClienteErrorDiv.style.display = 'block';
                    })
                    .finally(() => {
                        // Re-enable submit button
                        submitButton.disabled = false;
                    });
            });
        }
		<?php #endregion JS AJAX - Edit Form Submission ?>

        // --- Search Functionality ---
		<?php #region JS AJAX - Search Functionality ?>
        if (searchInput) {
            searchInput.addEventListener('input', function() {
                clearTimeout(searchTimeout);
                searchTimeout = setTimeout(() => {
                    performSearch(this.value.trim());
                }, 300); // Debounce search
            });
        }

        if (clearSearchBtn) {
            clearSearchBtn.addEventListener('click', function() {
                searchInput.value = '';
                performSearch('');
            });
        }

        function performSearch(termino) {
            const formData = new FormData();
            formData.append('action', 'buscar');
            formData.append('termino', termino);

            fetch('clientes', {
                method: 'POST',
                body: formData
            })
            .then(response => {
                if (!response.ok) {
                    throw new Error(`Error ${response.status}: ${response.statusText}`);
                }
                return response.json();
            })
            .then(data => {
                if (data.success) {
                    updateTable(data.clientes);
                } else {
                    showSweetAlertError('Error en la búsqueda: ' + data.message);
                }
            })
            .catch(error => {
                console.error('Error:', error);
                showSweetAlertError('Error de conexión al realizar la búsqueda.');
            });
        }

        function updateTable(clientes) {
            tableBody.innerHTML = '';

            if (clientes.length === 0) {
                noResultsMessage.style.display = 'block';
                // Add empty row to maintain table structure
                const emptyRow = document.createElement('tr');
                emptyRow.innerHTML = '<td colspan="4" class="text-center">No hay clientes para mostrar.</td>';
                tableBody.appendChild(emptyRow);
                return;
            }

            noResultsMessage.style.display = 'none';

            clientes.forEach(cliente => {
                const row = document.createElement('tr');
                row.setAttribute('data-cliente-id', cliente.id);
                row.innerHTML = `
                    <td class="text-center">
                        <button type="button" class="btn btn-xs btn-primary me-1 btn-edit-cliente"
                                title="Editar Cliente"
                                data-bs-toggle="modal"
                                data-bs-target="#editClienteModal"
                                data-cliente-id="${cliente.id}"
                                data-nombre="${escapeHtml(cliente.nombre)}"
                                data-celular="${escapeHtml(cliente.celular)}">
                            <i class="fa fa-edit"></i>
                        </button>
                        ${cliente.estado == 1 ? `
                        <button type="button" class="btn btn-xs btn-danger btn-desactivar-cliente"
                                title="Desactivar"
                                data-cliente-id="${cliente.id}"
                                data-nombre="${escapeHtml(cliente.nombre)}">
                            <i class="fa fa-trash-alt"></i>
                        </button>
                        ` : ''}
                    </td>
                    <td class="cliente-nombre-cell">${escapeHtml(cliente.nombre)}</td>
                    <td class="cliente-celular-cell">${escapeHtml(cliente.celular)}</td>
                    <td class="text-center">
                        ${cliente.estado == 1 ?
                            '<span class="badge bg-success">Activo</span>' :
                            '<span class="badge bg-danger">Inactivo</span>'
                        }
                    </td>
                `;
                tableBody.appendChild(row);
            });
        }

        function escapeHtml(text) {
            const map = {
                '&': '&amp;',
                '<': '&lt;',
                '>': '&gt;',
                '"': '&quot;',
                "'": '&#039;'
            };
            return text.replace(/[&<>"']/g, function(m) { return map[m]; });
        }
		<?php #endregion JS AJAX - Search Functionality ?>
    });
</script>
<!-- ================== END PAGE LEVEL JS ================== -->

<?php #endregion JS ?>

</body>
</html>