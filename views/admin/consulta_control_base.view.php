<?php
#region DOCS

/** @var ControlBase[] $controles_base */
/** @var string|null $fecha_inicio */
/** @var string|null $fecha_fin */
/** @var bool $show_results */
/** @var string|null $success_text Success message to display */
/** @var string|null $success_display Whether to show success message ('show' or null) */
/** @var string|null $error_text Error message to display */
/** @var string|null $error_display Whether to show error message ('show' or null) */

#endregion DOCS
?>
<!DOCTYPE html>
<html lang="en" class="dark-mode">
<head>
    <meta charset="utf-8"/>
    <title><?php echo APP_NAME; ?> | Consulta Histórica de Control Base</title>
    <meta content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no" name="viewport"/>
    <meta content="" name="description"/>
    <meta content="" name="author"/>

    <?php #region HEAD ?>
    <?php require_once __ROOT__ . '/views/general/head.view.php'; ?>
    <?php #endregion HEAD ?>

    <!-- Include Bootstrap Datepicker CSS -->
    <link href="<?php echo RUTA_ADM_ASSETS; ?>plugins/bootstrap-datepicker/dist/css/bootstrap-datepicker.css" rel="stylesheet"/>

    <!-- CSS for currency formatting and dark-mode styling -->
    <style>
        .loading-overlay {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0, 0, 0, 0.5);
            display: flex;
            justify-content: center;
            align-items: center;
            z-index: 1000;
        }
        .loading-spinner {
            border: 4px solid #f3f3f3;
            border-top: 4px solid #3498db;
            border-radius: 50%;
            width: 40px;
            height: 40px;
            animation: spin 2s linear infinite;
        }
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        .no-data-message {
            text-align: center;
            padding: 2rem;
            color: #6c757d;
            font-style: italic;
        }

        /* Date Range Container - Dark Mode Styling */
        .date-range-container {
            background: rgba(255, 255, 255, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.15);
            border-radius: 8px;
            padding: 1.5rem;
            margin-bottom: 1.5rem;
        }

        /* Control Base Modal Styling */
        .control-base-info-card {
            background: rgba(255, 255, 255, 0.05);
            border: 1px solid rgba(255, 255, 255, 0.15);
            border-radius: 8px;
            padding: 1.25rem;
            margin-bottom: 1rem;
        }

        .control-base-field {
            margin-bottom: 1rem;
        }

        .control-base-field:last-child {
            margin-bottom: 0;
        }

        .control-base-label {
            font-weight: 600;
            font-size: 0.875rem;
            color: #7e9bb5;
            margin-bottom: 0.25rem;
            display: block;
        }

        .control-base-value {
            font-size: 1.125rem;
            font-weight: 700;
            color: var(--bs-body-color);
        }

        .control-base-value.highlight {
            color: var(--bs-primary);
        }

        .control-base-value.final {
            color: #28a745;
        }

        .control-base-divider {
            border-top: 1px solid rgba(255, 255, 255, 0.1);
            margin: 1.25rem 0;
        }

        /* Movements table styling */
        .movements-table {
            margin-top: 1rem;
        }

        .movements-table .table {
            background-color: rgba(255, 255, 255, 0.02);
        }

        .movements-table .table tbody {
            background-color: rgba(255, 255, 255, 0.05);
        }

        .movements-table .table thead th {
            background-color: rgba(255, 255, 255, 0.1);
            border-bottom: 1px solid rgba(255, 255, 255, 0.2);
        }

        .movement-ingreso {
            color: #28a745;
        }

        .movement-egreso {
            color: #dc3545;
        }

        .badge-ingreso {
            background-color: #28a745;
        }

        .badge-egreso {
            background-color: #dc3545;
        }
    </style>
</head>
<body>
<!-- BEGIN #loader -->
<div id="loader" class="app-loader">
    <span class="spinner"></span>
</div>
<!-- END #loader -->

<!-- BEGIN #app -->
<div id="app" class="app app-header-fixed app-sidebar-fixed">
    <!-- #header -->
    <?php require_once __ROOT__ . '/views/general/header.view.php'; ?>

    <!-- #sidebar -->
    <?php require_once __ROOT__ . '/views/general/sidebar.view.php'; ?>

    <!-- BEGIN #content -->
    <div id="content" class="app-content">
        <?php #region PAGE HEADER ?>
        <div class="d-flex align-items-center mb-3">
            <div>
                <h4 class="mb-0">Consulta Histórica de Control Base</h4>
                <p class="mb-0 text-muted">Consulte registros de control base por rango de fechas</p>
            </div>
        </div>
        <?php #endregion PAGE HEADER ?>

        <?php #region DATE RANGE FILTER ?>
        <div class="date-range-container">
            <h5 class="mb-3">Filtros de Consulta</h5>
            <form id="consulta-form">
                <div class="row">
                    <div class="col-md-4">
                        <label for="fecha_inicio" class="form-label">Fecha de Inicio <span class="text-danger">*</span></label>
                        <div class="input-group">
                            <input type="text" class="form-control datepicker" id="fecha_inicio" name="fecha_inicio"
                                   placeholder="yyyy-mm-dd" required>
                            <span class="input-group-text" id="fecha_inicio_icon" style="cursor: pointer;">
                                <i class="fa fa-calendar"></i>
                            </span>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <label for="fecha_fin" class="form-label">Fecha de Fin <span class="text-danger">*</span></label>
                        <div class="input-group">
                            <input type="text" class="form-control datepicker" id="fecha_fin" name="fecha_fin"
                                   placeholder="yyyy-mm-dd" required>
                            <span class="input-group-text" id="fecha_fin_icon" style="cursor: pointer;">
                                <i class="fa fa-calendar"></i>
                            </span>
                        </div>
                    </div>
                    <div class="col-md-4 d-flex align-items-end">
                        <button type="submit" class="btn btn-primary me-2">
                            <i class="fa fa-search me-1"></i> Consultar
                        </button>
                        <button type="button" class="btn btn-secondary" id="limpiar-filtros">
                            <i class="fa fa-times me-1"></i> Limpiar
                        </button>
                    </div>
                </div>
                <div class="mt-2">
                    <div id="consulta-error" class="alert alert-danger" style="display: none;"></div>
                </div>
            </form>
        </div>
        <?php #endregion DATE RANGE FILTER ?>

        <?php #region RESULTS SECTION ?>
        <div class="panel panel-inverse no-border-radious" id="results-panel" style="display: none;">
            <div class="panel-heading">
                <h4 class="panel-title fs-18px" id="results-title">Control Base</h4>
            </div>
            <div class="table-nowrap" style="overflow: auto; position: relative;">
                <!-- Loading overlay -->
                <div id="loading-overlay" class="loading-overlay" style="display: none;">
                    <div class="loading-spinner"></div>
                </div>

                <?php #region TABLE CONTROL BASE ?>
                <table class="table table-hover table-sm">
                    <thead>
                    <tr>
                        <th class="w-70px text-center">Acciones</th>
                        <th class="text-center">Fecha</th>
                        <th class="text-end">Valor Inicial</th>
                        <th class="text-end">Valor Actual</th>
                        <th class="text-end">Valor Final</th>
                    </tr>
                    </thead>
                    <tbody class="fs-12px" id="control-base-table-body">
                        <!-- Results will be populated here via AJAX -->
                    </tbody>
                    <tfoot id="control-base-table-footer" style="display: none;">
                    <tr class="table-info">
                        <td colspan="5" class="text-start fw-bold" id="total-registros">Total: 0 registros</td>
                    </tr>
                    </tfoot>
                </table>
                <?php #endregion TABLE CONTROL BASE ?>
            </div>
        </div>
        <?php #endregion RESULTS SECTION ?>

    </div>
    <!-- END #content -->
</div>
<!-- END #app -->

<?php #region ControlBase Movements Modal ?>
<div class="modal fade" id="movimientosModal" tabindex="-1" aria-labelledby="movimientosModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="movimientosModalLabel">Movimientos del Control Base</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <div id="control-base-info">
                    <!-- Control Base info will be populated via AJAX -->
                </div>
                <div id="movimientos-content" class="movements-table">
                    <!-- Movements content will be populated via AJAX -->
                </div>
                <div id="movimientos-error" class="alert alert-danger" style="display: none;"></div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cerrar</button>
            </div>
        </div>
    </div>
</div>
<?php #endregion ControlBase Movements Modal ?>

<?php require_once __ROOT__ . '/views/general/core_js.view.php'; ?>

<!-- Include Bootstrap Datepicker JS -->
<script src="<?php echo RUTA_ADM_ASSETS; ?>plugins/bootstrap-datepicker/dist/js/bootstrap-datepicker.js"></script>

<script>
    document.addEventListener('DOMContentLoaded', function () {
        // Initialize datepickers
        $('.datepicker').datepicker({
            autoclose: true,
            todayHighlight: true,
            format: 'yyyy-mm-dd'
        });

        // Make calendar icons clickable
        document.getElementById('fecha_inicio_icon').addEventListener('click', function() {
            $('#fecha_inicio').datepicker('show');
        });

        document.getElementById('fecha_fin_icon').addEventListener('click', function() {
            $('#fecha_fin').datepicker('show');
        });

        // Form elements
        const consultaForm           = document.getElementById('consulta-form');
        const consultaError          = document.getElementById('consulta-error');
        const resultsPanel           = document.getElementById('results-panel');
        const resultsTitle           = document.getElementById('results-title');
        const controlBaseTableBody   = document.getElementById('control-base-table-body');
        const controlBaseTableFooter = document.getElementById('control-base-table-footer');
        const totalRegistros         = document.getElementById('total-registros');
        const loadingOverlay         = document.getElementById('loading-overlay');
        const limpiarFiltros         = document.getElementById('limpiar-filtros');

        // Modal elements
        const movimientosModal   = new bootstrap.Modal(document.getElementById('movimientosModal'));
        const controlBaseInfo    = document.getElementById('control-base-info');
        const movimientosContent = document.getElementById('movimientos-content');
        const movimientosError   = document.getElementById('movimientos-error');

        // Handle form submission
        consultaForm.addEventListener('submit', function (event) {
            event.preventDefault();
            consultaError.style.display = 'none';

            const formData    = new FormData(consultaForm);
            const fechaInicio = formData.get('fecha_inicio').trim();
            const fechaFin    = formData.get('fecha_fin').trim();

            // Basic client-side validation
            if (!fechaInicio || !fechaFin) {
                consultaError.textContent = 'Ambas fechas son obligatorias.';
                consultaError.style.display = 'block';
                return;
            }

            // Show loading
            loadingOverlay.style.display = 'flex';
            resultsPanel.style.display = 'block';

            // Add action to form data
            formData.append('action', 'consultar_control_base');

            // Submit form
            fetch('consulta-control-base', {
                method: 'POST',
                body: formData
            })
            .then(response => {
                if (!response.ok) {
                    return response.json().then(errData => {
                        throw new Error(errData.message || `Error ${response.status}: ${response.statusText}`);
                    }).catch(() => {
                        throw new Error(`Error ${response.status}: ${response.statusText}`);
                    });
                }
                return response.json();
            })
            .then(data => {
                if (data.success) {
                    // Update results title
                    resultsTitle.textContent = `Control Base (${data.fecha_inicio} - ${data.fecha_fin})`;

                    // Clear existing results
                    controlBaseTableBody.innerHTML = '';

                    if (data.controles_base.length === 0) {
                        // Show no data message
                        controlBaseTableBody.innerHTML = `
                            <tr>
                                <td colspan="5" class="no-data-message">
                                    No se encontraron registros de control base para el rango de fechas seleccionado.
                                </td>
                            </tr>
                        `;
                        controlBaseTableFooter.style.display = 'none';
                    } else {
                        // Populate results
                        data.controles_base.forEach(control => {
                            const row = document.createElement('tr');

                            // Determine if valor_final should be displayed
                            const valorFinalDisplay = (control.valor_final === null || control.valor_final === 0)
                                ? '-'
                                : control.valor_final_formateado;

                            row.innerHTML = `
                                <td class="text-center">
                                    <button type="button" class="btn btn-xs btn-info btn-ver-movimientos"
                                            title="Ver Movimientos"
                                            data-id="${control.id}">
                                        <i class="fa fa-list"></i>
                                    </button>
                                </td>
                                <td class="text-center">${control.fecha}</td>
                                <td class="text-end">${control.valor_inicial_formateado}</td>
                                <td class="text-end">${control.valor_actual_formateado}</td>
                                <td class="text-end">${valorFinalDisplay}</td>
                            `;
                            controlBaseTableBody.appendChild(row);
                        });

                        // Show total
                        totalRegistros.textContent = `Total: ${data.total_registros} registros`;
                        controlBaseTableFooter.style.display = 'table-footer-group';
                    }
                } else {
                    throw new Error(data.message || 'Error desconocido');
                }
            })
            .catch(error => {
                console.error('Error:', error);
                consultaError.textContent = error.message;
                consultaError.style.display = 'block';
                resultsPanel.style.display = 'none';
            })
            .finally(() => {
                loadingOverlay.style.display = 'none';
            });
        });

        // Handle clear filters
        limpiarFiltros.addEventListener('click', function() {
            consultaForm.reset();
            consultaError.style.display = 'none';
            resultsPanel.style.display = 'none';
        });

        // Handle movements modal
        document.addEventListener('click', function(event) {
            if (event.target.closest('.btn-ver-movimientos')) {
                const button        = event.target.closest('.btn-ver-movimientos');
                const idControlBase = button.getAttribute('data-id');

                // Clear previous content and errors
                controlBaseInfo.innerHTML      = '';
                movimientosContent.innerHTML   = '';
                movimientosError.style.display = 'none';

                // Show loading in modal
                controlBaseInfo.innerHTML = '<div class="text-center"><div class="loading-spinner"></div></div>';

                // Show modal
                movimientosModal.show();

                // Fetch movements data
                const formData = new FormData();
                formData.append('action', 'obtener_movimientos');
                formData.append('id_control_base', idControlBase);

                fetch('consulta-control-base', {
                    method: 'POST',
                    body: formData
                })
                .then(response => {
                    if (!response.ok) {
                        return response.json().then(errData => {
                            throw new Error(errData.message || `Error ${response.status}: ${response.statusText}`);
                        }).catch(() => {
                            throw new Error(`Error ${response.status}: ${response.statusText}`);
                        });
                    }
                    return response.json();
                })
                .then(data => {
                    if (data.success) {
                        const controlBase = data.control_base;

                        // Check if valor_final should be displayed (not null and not 0)
                        const showValorFinal = controlBase.valor_final !== null && controlBase.valor_final !== 0;

                        // Determine column classes based on whether valor_final is shown
                        const colClass = showValorFinal ? 'col-md-4' : 'col-md-6';

                        // Build the valor_final field HTML conditionally
                        const valorFinalField = showValorFinal ? `
                            <div class="${colClass} text-center">
                                <div class="control-base-field">
                                    <span class="control-base-label">Valor Final</span>
                                    <div class="control-base-value final">${controlBase.valor_final_formateado}</div>
                                </div>
                            </div>
                        ` : '';

                        controlBaseInfo.innerHTML = `
                            <div class="control-base-info-card">
                                <!-- Date Row -->
                                <div class="row">
                                    <div class="col-12 text-center">
                                        <div class="control-base-field">
                                            <span class="control-base-label">Fecha</span>
                                            <div class="control-base-value">${controlBase.fecha}</div>
                                        </div>
                                    </div>
                                </div>
                                <div class="control-base-divider"></div>
                                <!-- Values Row -->
                                <div class="row">
                                    <div class="${colClass} text-center">
                                        <div class="control-base-field">
                                            <span class="control-base-label">Valor Inicial</span>
                                            <div class="control-base-value highlight">${controlBase.valor_inicial_formateado}</div>
                                        </div>
                                    </div>
                                    <div class="${colClass} text-center">
                                        <div class="control-base-field">
                                            <span class="control-base-label">Valor Actual</span>
                                            <div class="control-base-value">${controlBase.valor_actual_formateado}</div>
                                        </div>
                                    </div>
                                    ${valorFinalField}
                                </div>
                            </div>
                        `;

                        // Display movements
                        if (data.movimientos.length === 0) {
                            movimientosContent.innerHTML = `
                                <div class="alert alert-info">
                                    <i class="fa fa-info-circle me-2"></i>
                                    No se encontraron movimientos para este control base.
                                </div>
                            `;
                        } else {
                            let movimientosHtml = `
                                <h6 class="mb-3">Movimientos (${data.total_movimientos})</h6>
                                <table class="table table-sm table-hover">
                                    <thead>
                                        <tr>
                                            <th class="text-center">Tipo</th>
                                            <th>Nota</th>
                                            <th class="text-end">Valor</th>
                                            <th class="text-center">Fecha</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                            `;

                            data.movimientos.forEach(movimiento => {
                                const tipoClass = movimiento.is_ingreso ? 'badge-ingreso' : 'badge-egreso';
                                const valorClass = movimiento.is_ingreso ? 'movement-ingreso' : 'movement-egreso';

                                // Format date to yyyy-MM-dd H:i:s format (use raw fecha field)
                                const fechaFormatted = movimiento.fecha || '-';

                                movimientosHtml += `
                                    <tr>
                                        <td class="text-center">
                                            <span class="badge ${tipoClass}">${movimiento.tipo_descripcion}</span>
                                        </td>
                                        <td>${movimiento.nota || '-'}</td>
                                        <td class="text-end ${valorClass}">${movimiento.valor_formateado}</td>
                                        <td class="text-center">${fechaFormatted}</td>
                                    </tr>
                                `;
                            });

                            movimientosHtml += `
                                    </tbody>
                                </table>
                            `;

                            movimientosContent.innerHTML = movimientosHtml;
                        }
                    } else {
                        throw new Error(data.message || 'Error desconocido');
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    controlBaseInfo.innerHTML = '';
                    movimientosContent.innerHTML = '';
                    movimientosError.textContent = error.message;
                    movimientosError.style.display = 'block';
                });
            }
        });
    });
</script>

</body>
</html>
