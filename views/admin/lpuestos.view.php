<?php
#region DOCS

/** @var Puesto[] $puestos */
/** @var CentroCosto[] $centros_costos */
/** @var string|null $success_text Success message to display */
/** @var string|null $success_display Whether to show success message ('show' or null) */
/** @var string|null $error_text Error message to display */
/** @var string|null $error_display Whether to show error message ('show' or null) */

use App\classes\Puesto;
use App\classes\CentroCosto;

#endregion DOCS
?>
<!DOCTYPE html>
<html lang="en" class="dark-mode">
<head>
	<meta charset="utf-8"/>
	<title><?php echo APP_NAME; ?> | Puestos</title>
	<meta content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no" name="viewport"/>
	<meta content="" name="description"/>
	<meta content="" name="author"/>
	
	<?php #region HEAD ?>
	<?php require_once __ROOT__ . '/views/general/head.view.php'; ?>
	<?php #endregion HEAD ?>
</head>
<body>
<!-- BEGIN #loader -->
<div id="loader" class="app-loader">
	<span class="spinner"></span>
</div>
<!-- END #loader -->

<!-- BEGIN #app -->
<div id="app" class="app app-header-fixed app-sidebar-fixed">
	<!-- #header -->
	<?php require_once __ROOT__ . '/views/general/header.view.php'; ?>
	
	<!-- #sidebar -->
	<?php require_once __ROOT__ . '/views/general/sidebar.view.php'; ?>
	
	<!-- BEGIN #content -->
	<div id="content" class="app-content">
		<?php #region PAGE HEADER ?>
		<div class="d-flex align-items-center mb-3">
			<div>
				<h4 class="mb-0">Gestión de Puestos</h4>
				<p class="mb-0 text-muted">Administra los puestos de trabajo del sistema</p>
			</div>
			<div class="ms-auto">
				<button type="button" class="btn btn-success" data-bs-toggle="modal" data-bs-target="#createPuestoModal">
					<i class="fa fa-plus-circle fa-fw me-1"></i> Crear nuevo
				</button>
			</div>
		</div>
		
		<?php #region PANEL PUESTOS ?>
		<div class="panel panel-inverse mt-3 no-border-radious">
			<div class="panel-heading no-border-radious">
				<h4 class="panel-title">
					Listado de Puestos Activos
				</h4>
			</div>
			<!-- BEGIN PANEL body -->
			<div class="p-1 table-nowrap" style="overflow: auto">
				<?php #region TABLE PUESTOS ?>
				<table class="table table-hover table-sm">
					<thead>
					<tr>
						<th class="w-70px text-center">Acciones</th>
						<th>Descripción</th>
						<th>Centro de Costo</th>
						<th class="text-center">Estado</th>
					</tr>
					</thead>
					<tbody class="fs-12px" id="puesto-table-body">
					<?php foreach ($puestos as $puesto): ?>
						<tr data-puesto-id="<?php echo $puesto->getId(); ?>">
							<td class="text-center">
								<?php // Edit Button - Triggers Modal ?>
								<button type="button" class="btn btn-xs btn-primary me-1 btn-edit-puesto"
								        title="Editar Puesto"
								        data-bs-toggle="modal"
								        data-bs-target="#editPuestoModal"
								        data-puesto-id="<?php echo $puesto->getId(); ?>"
								        data-descripcion="<?php echo htmlspecialchars($puesto->getDescripcion() ?? ''); ?>"
							        data-id-centro-costo="<?php echo $puesto->getId_centro_costo() ?? ''; ?>">
									<i class="fa fa-edit"></i>
								</button>
								<?php // Deactivate Button - Only show if active ?>
								<?php if ($puesto->isActivo()): ?>
									<button type="button" class="btn btn-xs btn-danger btn-desactivar-puesto"
									        title="Desactivar"
									        data-puesto-id="<?php echo $puesto->getId(); ?>"
									        data-descripcion="<?php echo htmlspecialchars($puesto->getDescripcion() ?? ''); ?>">
										<i class="fa fa-trash-alt"></i>
									</button>
								<?php endif; ?>
							</td>
							<td class="puesto-descripcion-cell"><?php echo htmlspecialchars($puesto->getDescripcion()); ?></td>
							<td class="puesto-centro-costo-cell"><?php echo htmlspecialchars($puesto->getNombre_centro_costo() ?? 'Sin asignar'); ?></td>
							<td class="text-center">
								<?php if ($puesto->isActivo()): ?>
									<span class="badge bg-success">Activo</span>
								<?php else: ?>
									<span class="badge bg-danger">Inactivo</span>
								<?php endif; ?>
							</td>
						</tr>
					<?php endforeach; ?>
					<?php if (empty($puestos)): ?>
						<tr>
							<td colspan="4" class="text-center">No hay puestos para mostrar.</td>
						</tr>
					<?php endif; ?>
					
					</tbody>
				</table>
				<?php #endregion TABLE PUESTOS ?>
			</div>
			<!-- END PANEL body -->
		</div>
		<?php #endregion PANEL PUESTOS ?>
		
		<?php #region Create Puesto Modal ?>
		<div class="modal fade" id="createPuestoModal" tabindex="-1" aria-labelledby="createPuestoModalLabel" aria-hidden="true">
			<div class="modal-dialog">
				<div class="modal-content">
					<form id="create-puesto-form">
						<div class="modal-header">
							<h5 class="modal-title" id="createPuestoModalLabel">Crear Puesto</h5>
							<button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
						</div>
						<div class="modal-body">
							<input type="hidden" name="action" value="crear">

							<div class="mb-3">
								<label for="create-puesto-descripcion" class="form-label">Descripción <span class="text-danger">*</span></label>
								<input type="text" class="form-control" id="create-puesto-descripcion" name="descripcion" required>
							</div>

							<div class="mb-3">
								<label for="create-puesto-centro-costo" class="form-label">Centro de Costo <span class="text-danger">*</span></label>
								<select class="form-select" id="create-puesto-centro-costo" name="id_centro_costo" required>
									<option value="">Seleccionar centro de costo</option>
									<?php foreach ($centros_costos as $centro_costo): ?>
										<option value="<?php echo $centro_costo->getId(); ?>">
											<?php echo htmlspecialchars($centro_costo->getNombre()); ?>
										</option>
									<?php endforeach; ?>
								</select>
							</div>

							<div class="alert alert-danger" id="create-puesto-error" style="display: none;"></div>
						</div>
						<div class="modal-footer">
							<button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancelar</button>
							<button type="submit" class="btn btn-primary">Guardar</button>
						</div>
					</form>
				</div>
			</div>
		</div>
		<?php #endregion Create Puesto Modal ?>
		
		<?php #region Edit Puesto Modal ?>
		<div class="modal fade" id="editPuestoModal" tabindex="-1" aria-labelledby="editPuestoModalLabel" aria-hidden="true">
			<div class="modal-dialog">
				<div class="modal-content">
					<form id="edit-puesto-form">
						<div class="modal-header">
							<h5 class="modal-title" id="editPuestoModalLabel">Editar Puesto</h5>
							<button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
						</div>
						<div class="modal-body">
							<input type="hidden" name="puestoId" id="edit-puesto-id">
							<input type="hidden" name="action" value="modificar">

							<div class="mb-3">
								<label for="edit-puesto-descripcion" class="form-label">Descripción <span class="text-danger">*</span></label>
								<input type="text" class="form-control" id="edit-puesto-descripcion" name="descripcion" required>
							</div>

							<div class="mb-3">
								<label for="edit-puesto-centro-costo" class="form-label">Centro de Costo <span class="text-muted">(No modificable)</span></label>
								<select class="form-select" id="edit-puesto-centro-costo" name="id_centro_costo" disabled>
									<option value="">Seleccionar centro de costo</option>
									<?php foreach ($centros_costos as $centro_costo): ?>
										<option value="<?php echo $centro_costo->getId(); ?>">
											<?php echo htmlspecialchars($centro_costo->getNombre()); ?>
										</option>
									<?php endforeach; ?>
								</select>
								<small class="form-text text-muted">
									<i class="fa fa-info-circle"></i> El centro de costo no puede modificarse una vez creado el puesto para mantener la integridad de los datos.
								</small>
							</div>

							<div class="alert alert-danger" id="edit-puesto-error" style="display: none;"></div>
						</div>
						<div class="modal-footer">
							<button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancelar</button>
							<button type="submit" class="btn btn-primary">Guardar cambios</button>
						</div>
					</form>
				</div>
			</div>
		</div>
		<?php #endregion Edit Puesto Modal ?>
	
	</div>
	<!-- END #content -->
	
	<?php #region Hidden Form for Deactivation ?>
	<form id="deactivate-puesto-form" method="POST" action="puestos" style="display: none;">
		<input type="hidden" name="action" value="desactivar">
		<input type="hidden" name="puestoId" id="deactivate-puesto-id">
	</form>
	<?php #endregion Hidden Form ?>
	
	<!-- BEGIN scroll-top-btn -->
	<a href="#" class="btn btn-icon btn-circle btn-success btn-scroll-to-top" data-toggle="scroll-to-top"><i class="fa fa-angle-up"></i></a>
	<!-- END scroll-top-btn -->
</div>
<!-- END #app -->

<?php #region JS ?>
<?php require_once __ROOT__ . '/views/general/core_js.view.php'; ?>

<!-- ================== BEGIN PAGE LEVEL JS ================== -->
<script>
    document.addEventListener('DOMContentLoaded', function () {
        // Use event delegation on the table body
        const tableBody = document.getElementById('puesto-table-body');
        
        // Create Puesto Modal Elements
        const createPuestoModalElement = document.getElementById('createPuestoModal');
        const createPuestoModal        = new bootstrap.Modal(createPuestoModalElement);
        const createPuestoForm         = document.getElementById('create-puesto-form');
        const createPuestoErrorDiv     = document.getElementById('create-puesto-error');
        
        // Edit Puesto Modal Elements
        const editPuestoModalElement     = document.getElementById('editPuestoModal');
        const editPuestoModal            = new bootstrap.Modal(editPuestoModalElement);
        const editPuestoForm             = document.getElementById('edit-puesto-form');
        const editPuestoIdInput          = document.getElementById('edit-puesto-id');
        const editPuestoDescripcionInput = document.getElementById('edit-puesto-descripcion');
        const editPuestoCentroCostoSelect = document.getElementById('edit-puesto-centro-costo');
        const editPuestoErrorDiv         = document.getElementById('edit-puesto-error');
        
        // Deactivate Form Elements
        const deactivatePuestoForm    = document.getElementById('deactivate-puesto-form');
        const deactivatePuestoIdInput = document.getElementById('deactivate-puesto-id');
        
        // --- Handle Table Row Clicks (Event Delegation) ---
        if (tableBody) {
            tableBody.addEventListener('click', function (event) {
                // Find the closest button if the event target is a child element (like an icon)
                const editButton       = event.target.closest('.btn-edit-puesto');
                const deactivateButton = event.target.closest('.btn-desactivar-puesto');
                
                // --- Handle Deactivate Click ---
                if (deactivateButton) {
                    event.preventDefault(); // Prevent default if it was an <a>
                    const puestoId    = deactivateButton.dataset.puestoId;
                    const descripcion = deactivateButton.dataset.descripcion;
                    
                    // Confirm before deactivating
                    swal({
                        title     : "¿Estás seguro?",
                        text      : `¿Deseas desactivar el puesto "${descripcion}"?`,
                        icon      : "warning",
                        buttons   : {
                            cancel : {
                                text      : "Cancelar",
                                value     : null,
                                visible   : true,
                                className : "btn-secondary",
                                closeModal: true,
                            },
                            confirm: {
                                text      : "Sí, desactivar",
                                value     : true,
                                visible   : true,
                                className : "btn-danger",
                                closeModal: true
                            }
                        },
                        dangerMode: true,
                    })
                        .then((willDeactivate) => {
                            if (willDeactivate) {
                                // User confirmed, submit the form
                                deactivatePuestoIdInput.value = puestoId;
                                deactivatePuestoForm.submit();
                            }
                        });
                }
                
                // --- Handle Edit Click ---
                if (editButton) {
                    event.preventDefault(); // Prevent default if it was an <a>
                    const puestoId           = editButton.dataset.puestoId;
                    const currentDescripcion = editButton.dataset.descripcion;
                    const currentIdCentroCosto = editButton.dataset.idCentroCosto;

                    // Populate the modal form
                    editPuestoIdInput.value          = puestoId;
                    editPuestoDescripcionInput.value = currentDescripcion;
                    editPuestoCentroCostoSelect.value = currentIdCentroCosto || '';
                    editPuestoErrorDiv.style.display = 'none'; // Hide previous errors
                    editPuestoErrorDiv.textContent   = '';

                    // The data-bs-toggle and data-bs-target attributes on the button handle showing the modal
                }
            });
        }
        
        // --- Handle Create Form Submission (AJAX) ---
        if (createPuestoForm) {
            createPuestoForm.addEventListener('submit', function (event) {
                event.preventDefault(); // Stop default form submission
                createPuestoErrorDiv.style.display = 'none'; // Hide error div initially
                
                const formData    = new FormData(createPuestoForm);
                const descripcion = formData.get('descripcion').trim(); // Get trimmed description
                const centroCosto = formData.get('id_centro_costo'); // Get centro de costo ID

                // Basic client-side validation
                if (!descripcion) {
                    createPuestoErrorDiv.textContent   = 'La descripción no puede estar vacía.';
                    createPuestoErrorDiv.style.display = 'block';
                    return; // Stop submission
                }

                if (!centroCosto) {
                    createPuestoErrorDiv.textContent   = 'Debe seleccionar un centro de costo.';
                    createPuestoErrorDiv.style.display = 'block';
                    return; // Stop submission
                }
                
                // Disable submit button during request
                const submitButton    = createPuestoForm.querySelector('button[type="submit"]');
                submitButton.disabled = true;
                
                fetch('puestos', {
                    method: 'POST',
                    body  : formData
                })
                    .then(response => {
                        if (!response.ok) {
                            return response.json().then(errData => {
                                throw new Error(errData.message || `Error ${response.status}: ${response.statusText}`);
                            }).catch(() => {
                                throw new Error(`Error ${response.status}: ${response.statusText}`);
                            });
                        }
                        return response.json();
                    })
                    .then(data => {
                        if (data.success) {
                            createPuestoModal.hide(); // Close modal on success
                            
                            // Reset the form for next use
                            createPuestoForm.reset();
                            
                            // Add the new row to the table
                            const tableBody = document.getElementById('puesto-table-body');
                            
                            // Remove "no data" row if it exists
                            const noDataRow = tableBody.querySelector('tr td[colspan="4"]');
                            if (noDataRow) {
                                noDataRow.closest('tr').remove();
                            }
                            
                            // Create new row HTML
                            const newRow = document.createElement('tr');
                            newRow.setAttribute('data-puesto-id', data.id);
                            
                            newRow.innerHTML = `
                                <td class="text-center">
                                    <button type="button" class="btn btn-xs btn-primary me-1 btn-edit-puesto"
                                            title="Editar Puesto"
                                            data-bs-toggle="modal"
                                            data-bs-target="#editPuestoModal"
                                            data-puesto-id="${data.id}"
                                            data-descripcion="${data.descripcion}"
                                            data-id-centro-costo="${data.id_centro_costo || ''}">
                                        <i class="fa fa-edit"></i>
                                    </button>
                                    <button type="button" class="btn btn-xs btn-danger btn-desactivar-puesto"
                                            title="Desactivar"
                                            data-puesto-id="${data.id}"
                                            data-descripcion="${data.descripcion}">
                                        <i class="fa fa-trash-alt"></i>
                                    </button>
                                </td>
                                <td class="puesto-descripcion-cell">${data.descripcion}</td>
                                <td class="puesto-centro-costo-cell">${data.nombre_centro_costo || 'Sin asignar'}</td>
                                <td class="text-center"><span class="badge bg-success">Activo</span></td>
                            `;
                            
                            // Add the new row to the table
                            tableBody.appendChild(newRow);
                            
                            showSweetAlertSuccess('Éxito', 'Puesto creado correctamente.');
                        } else {
                            // Show error message inside the modal
                            createPuestoErrorDiv.textContent   = data.message || 'Ocurrió un error al crear el Puesto.';
                            createPuestoErrorDiv.style.display = 'block';
                        }
                    })
                    .catch(error => {
                        console.error('Error creating Puesto:', error);
                        // Show error message inside the modal
                        createPuestoErrorDiv.textContent   = 'Error de red o del servidor: ' + error.message;
                        createPuestoErrorDiv.style.display = 'block';
                    })
                    .finally(() => {
                        // Re-enable submit button
                        submitButton.disabled = false;
                    });
            });
        }
        
        // --- Handle Edit Form Submission (AJAX) ---
        if (editPuestoForm) {
            editPuestoForm.addEventListener('submit', function (event) {
                event.preventDefault(); // Stop default form submission
                editPuestoErrorDiv.style.display = 'none'; // Hide error div initially

                const formData         = new FormData(editPuestoForm);
                const puestoId         = formData.get('puestoId');
                const nuevaDescripcion = formData.get('descripcion').trim(); // Get trimmed description

                // Basic client-side validation - only validate description
                if (!nuevaDescripcion) {
                    editPuestoErrorDiv.textContent   = 'La descripción no puede estar vacía.';
                    editPuestoErrorDiv.style.display = 'block';
                    return; // Stop submission
                }

                // Remove centro de costo from form data to prevent modification attempts
                formData.delete('id_centro_costo');

                // Disable submit button during request
                const submitButton    = editPuestoForm.querySelector('button[type="submit"]');
                submitButton.disabled = true;

                fetch('puestos', {
                    method: 'POST',
                    body  : formData
                })
                    .then(response => {
                        if (!response.ok) {
                            return response.json().then(errData => {
                                throw new Error(errData.message || `Error ${response.status}: ${response.statusText}`);
                            }).catch(() => {
                                throw new Error(`Error ${response.status}: ${response.statusText}`);
                            });
                        }
                        return response.json();
                    })
                    .then(data => {
                        if (data.success) {
                            editPuestoModal.hide(); // Close modal on success
                            
                            // Update the row in the table
                            const row = tableBody.querySelector(`tr[data-puesto-id="${puestoId}"]`);
                            if (row) {
                                // Update only the description cell (centro de costo remains unchanged)
                                const descCell = row.querySelector('.puesto-descripcion-cell');
                                if (descCell) {
                                    descCell.textContent = nuevaDescripcion;
                                }

                                // Update the data-descripcion attribute on the edit button
                                const editBtn = row.querySelector('.btn-edit-puesto');
                                if (editBtn) {
                                    editBtn.dataset.descripcion = nuevaDescripcion;
                                    // Keep the existing centro de costo data unchanged
                                }

                                // Update the data-descripcion attribute on the deactivate button
                                const deactivateBtn = row.querySelector('.btn-desactivar-puesto');
                                if (deactivateBtn) {
                                    deactivateBtn.dataset.descripcion = nuevaDescripcion;
                                }
                            }
                            
                            showSweetAlertSuccess('Éxito', 'Puesto actualizado correctamente.');
                        } else {
                            // Show error message inside the modal
                            editPuestoErrorDiv.textContent   = data.message || 'Ocurrió un error al guardar.';
                            editPuestoErrorDiv.style.display = 'block';
                        }
                    })
                    .catch(error => {
                        console.error('Error updating Puesto:', error);
                        // Show error message inside the modal
                        editPuestoErrorDiv.textContent   = 'Error de red o del servidor: ' + error.message;
                        editPuestoErrorDiv.style.display = 'block';
                    })
                    .finally(() => {
                        // Re-enable submit button
                        submitButton.disabled = false;
                    });
            });
        }
    });
</script>
<!-- ================== END PAGE LEVEL JS ================== -->
<?php #endregion JS ?>
</body>
</html>
