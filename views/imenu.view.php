<?php
#region region DOCS

/** @var int|null $id ID of the menu being edited, null for new menus */
/** @var string $url URL of the menu */
/** @var string $texto Text/label of the menu */
/** @var string $icono Icon class for the menu */
/** @var int $prioridad Priority order of the menu */
/** @var int $tiene_subs Whether the menu has submenus (1) or not (0) */
/** @var int $estado Status of the menu (1 = active, 0 = inactive) */
/** @var bool $is_edit_mode Whether we're in edit mode (true) or create mode (false) */
/** @var string $page_title Title of the page (Create or Edit) */
/** @var Submenu[] $submenus Array of submenu objects associated with this menu */
/** @var string $error_text Error message text if any */

/** @var string $error_display Whether to show error message ('show') or not ('') */

use App\classes\Menu;
use App\classes\Submenu;

#endregion DOCS
?>
<!DOCTYPE html>
<html lang="en" class="dark-mode">
<head>
	<meta charset="utf-8"/>
	<title><?php echo APP_NAME; ?> | <?php echo $page_title; ?></title>
	<meta content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no" name="viewport"/>
	<meta content="" name="description"/>
	<meta content="" name="author"/>

	<?php #region HEAD ?>
	<?php require_once __ROOT__ . '/views/general/head.view.php'; ?>
	<?php #endregion HEAD ?>
	<style>
        /* Style for invalid fields */
        .is-invalid {
            border-color: #dc3545 !important; /* Bootstrap's default danger color */
        }

        /* Style for validation messages */
        .invalid-feedback {
            display: none; /* Hide by default */
            width: 100%;
            margin-top: .25rem;
            font-size: .875em; /* 14px if base is 16px */
            color: #dc3545;
        }

        .is-invalid ~ .invalid-feedback {
            display: block; /* Show when input is invalid */
        }
	</style>
</head>
<body>
<!-- BEGIN #loader -->
<div id="loader" class="app-loader">
	<span class="spinner"></span>
</div>
<!-- END #loader -->

<!-- BEGIN #app -->
<div id="app" class="app app-header-fixed app-sidebar-fixed">
	<!-- #header -->
	<?php require_once __ROOT__ . '/views/general/header.view.php'; ?>

	<!-- #sidebar -->
	<?php require_once __ROOT__ . '/views/general/sidebar.view.php'; ?>

	<!-- BEGIN #content -->
	<div id="content" class="app-content">
		<?php #region region PAGE HEADER ?>
		<div class="d-flex align-items-center mb-3">
			<div>
				<h4 class="mb-0"><?php echo $page_title; ?></h4>
				<p class="mb-0 text-muted">Ingrese los detalles del menú.</p>
			</div>
			<div class="ms-auto">
				<a href="lmenus" class="btn btn-secondary"><i class="fa fa-arrow-left fa-fw me-1"></i> Volver a la Lista</a>
			</div>
		</div>
		<hr>
		<?php #endregion PAGE HEADER ?>

		<?php #region region TABS AND FORM ?>
		<!-- Tabs Navigation -->
		<ul class="nav nav-tabs nav-tabs-v2" id="menu-tabs" role="tablist">
			<li class="nav-item me-2" role="presentation">
				<a href="#info-tab-pane" class="nav-link active" id="info-tab" data-bs-toggle="tab" role="tab" aria-controls="info-tab-pane" aria-selected="true">
					<i class="fa fa-info-circle fa-fw me-1"></i> Información del Menú
				</a>
			</li>
			<li class="nav-item" role="presentation">
				<a href="#submenus-tab-pane" class="nav-link <?php echo !$is_edit_mode ? 'disabled' : ''; ?>" id="submenus-tab" data-bs-toggle="tab" role="tab" aria-controls="submenus-tab-pane" aria-selected="false" <?php echo !$is_edit_mode ? 'disabled' : ''; ?>>
					<i class="fa fa-list fa-fw me-1"></i> Submenús Asociados
				</a>
			</li>
		</ul>

		<!-- Tabs Content -->
		<div class="tab-content pt-3" id="menu-tabs-content">
			<!-- Menu Information Tab -->
			<div class="tab-pane fade show active" id="info-tab-pane" role="tabpanel" aria-labelledby="info-tab">
				<form action="imenu<?php echo $is_edit_mode ? '?id=' . $id : ''; ?>" method="POST" id="menu-form" novalidate>
					<?php #region region PANEL MENU DETAILS ?>
					<div class="panel panel-inverse no-border-radious">
						<div class="panel-heading no-border-radious">
							<h4 class="panel-title">Detalles del Menú</h4>
							<div class="panel-heading-btn">
								<a href="javascript:;" class="btn btn-xs btn-icon btn-default" data-toggle="panel-expand"><i class="fa fa-expand"></i></a>
								<a href="javascript:;" class="btn btn-xs btn-icon btn-warning" data-toggle="panel-collapse"><i class="fa fa-minus"></i></a>
							</div>
						</div>
						<div class="panel-body">
							<div class="mb-3">
								<label for="texto" class="form-label">Texto <span class="text-danger">*</span></label>
								<input type="text" class="form-control" id="texto" name="texto" value="<?php echo htmlspecialchars($texto ?? ''); ?>" required>
								<div class="invalid-feedback" id="texto-error">El texto del menú es requerido.</div>
							</div>

							<div class="mb-3">
								<label for="url" class="form-label">URL <span class="text-danger">*</span></label>
								<input type="text" class="form-control" id="url" name="url" value="<?php echo htmlspecialchars($url ?? ''); ?>" required>
								<div class="invalid-feedback" id="url-error">La URL del menú es requerida.</div>
							</div>

							<div class="mb-3">
								<label for="icono" class="form-label">Icono</label>
								<input type="text" class="form-control" id="icono" name="icono" value="<?php echo htmlspecialchars($icono ?? ''); ?>">
								<small class="text-muted">Ejemplo: "fa fa-home" o "fas fa-user"</small>
								<div class="invalid-feedback" id="icono-error">Formato de icono inválido.</div>
							</div>

							<div class="mb-3">
								<label for="prioridad" class="form-label">Prioridad</label>
								<input type="number" class="form-control" id="prioridad" name="prioridad" value="<?php echo htmlspecialchars($prioridad ?? 0); ?>" min="0">
								<div class="invalid-feedback" id="prioridad-error">La prioridad debe ser un número entero positivo.</div>
							</div>

							<div class="mb-3 form-check">
								<input type="checkbox" class="form-check-input" id="tiene_subs" name="tiene_subs" value="1" <?php echo ($tiene_subs == 1) ? 'checked' : ''; ?>>
								<label class="form-check-label" for="tiene_subs">Tiene submenús</label>
							</div>
						</div>
						<div class="panel-footer text-end">
							<button type="submit" class="btn btn-primary"><i class="fa fa-save fa-fw me-1"></i> Guardar Menú</button>
							<a href="lmenus" class="btn btn-secondary"><i class="fa fa-times fa-fw me-1"></i> Cancelar</a>
						</div>
					</div>
					<?php #endregion PANEL MENU DETAILS ?>
				</form>
			</div>

			<!-- Submenus Tab -->
			<div class="tab-pane fade" id="submenus-tab-pane" role="tabpanel" aria-labelledby="submenus-tab">
				<?php if ($is_edit_mode): ?>
					<?php #region region PANEL SUBMENUS ?>
					<div class="panel panel-inverse no-border-radious">
						<div class="panel-heading no-border-radious">
							<h4 class="panel-title">Submenús Asociados</h4>
							<div class="mb-auto">
								<button type="button" class="btn btn-md btn-success" id="btn-add-submenu">
									<i class="fa fa-plus-circle fa-fw me-1"></i> Agregar Submenú
								</button>
							</div>
						</div>
						<div class="panel-body">
							<div class="table-responsive">
								<table class="table table-hover table-sm">
									<thead>
									<tr>
										<th class="w-70px text-center">Acciones</th>
										<th>Texto</th>
										<th>URL</th>
										<th class="text-center">Prioridad</th>
									</tr>
									</thead>
									<tbody class="fs-12px" id="submenu-table-body">
									<?php foreach ($submenus as $submenu): ?>
										<tr data-submenu-id="<?php echo $submenu->getId(); ?>">
											<td class="text-center">
												<button type="button" class="btn btn-xs btn-primary me-1 btn-edit-submenu"
												        title="Editar Submenú"
												        data-submenu-id="<?php echo $submenu->getId(); ?>"
												        data-texto="<?php echo htmlspecialchars($submenu->getTexto() ?? ''); ?>"
												        data-url="<?php echo htmlspecialchars($submenu->getUrl() ?? ''); ?>"
												        data-prioridad="<?php echo $submenu->getPrioridad(); ?>">
													<i class="fa fa-edit"></i>
												</button>
												<button type="button" class="btn btn-xs btn-danger btn-delete-submenu"
												        title="Eliminar"
												        data-submenu-id="<?php echo $submenu->getId(); ?>"
												        data-texto="<?php echo htmlspecialchars($submenu->getTexto() ?? ''); ?>">
													<i class="fa fa-trash-alt"></i>
												</button>
											</td>
											<td class="submenu-texto-cell"><?php echo htmlspecialchars($submenu->getTexto()); ?></td>
											<td class="submenu-url-cell"><?php echo htmlspecialchars($submenu->getUrl()); ?></td>
											<td class="submenu-prioridad-cell text-center"><?php echo htmlspecialchars($submenu->getPrioridad()); ?></td>
										</tr>
									<?php endforeach; ?>
									<?php if (empty($submenus)): ?>
										<tr id="no-submenus-row">
											<td colspan="4" class="text-center">No hay submenús para mostrar.</td>
										</tr>
									<?php endif; ?>
									</tbody>
								</table>
							</div>
						</div>
					</div>
					<?php #endregion PANEL SUBMENUS ?>
				<?php else: ?>
					<div class="alert alert-info mt-3">
						<i class="fa fa-info-circle me-1"></i> Debe guardar el menú primero para poder agregar submenús.
					</div>
				<?php endif; ?>
			</div>
		</div>
		<?php #endregion TABS AND FORM ?>
	</div>
	<!-- END #content -->

	<!-- BEGIN scroll-top-btn -->
	<a href="#" class="btn btn-icon btn-circle btn-success btn-scroll-to-top" data-toggle="scroll-to-top"><i class="fa fa-angle-up"></i></a>
	<!-- END scroll-top-btn -->

	<?php #region region Submenu Modal ?>
	<div class="modal fade" id="submenuModal" tabindex="-1" aria-labelledby="submenuModalLabel" aria-hidden="true">
		<div class="modal-dialog">
			<div class="modal-content">
				<form id="submenu-form">
					<div class="modal-header">
						<h5 class="modal-title" id="submenuModalLabel">Agregar Submenú</h5>
						<button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
					</div>
					<div class="modal-body">
						<input type="hidden" name="id" id="submenu-id">
						<input type="hidden" name="id_menu" id="submenu-id-menu" value="<?php echo (int)$id; ?>">
						<input type="hidden" name="ajax_action" id="submenu-action" value="create_submenu">
						<!-- Debug info -->
						<div class="d-none">
							Menu ID: <?php echo (int)$id; ?>
						</div>

						<div class="mb-3">
							<label for="submenu-texto" class="form-label">Texto <span class="text-danger">*</span></label>
							<input type="text" class="form-control" id="submenu-texto" name="texto" required>
							<div class="invalid-feedback">El texto del submenú es requerido.</div>
						</div>

						<div class="mb-3">
							<label for="submenu-url" class="form-label">URL</label>
							<input type="text" class="form-control" id="submenu-url" name="url">
						</div>

						<div class="mb-3">
							<label for="submenu-prioridad" class="form-label">Prioridad</label>
							<input type="number" class="form-control" id="submenu-prioridad" name="prioridad" value="0" min="0">
						</div>

						<div id="submenu-error" class="alert alert-danger mt-2" style="display: none;"></div>
					</div>
					<div class="modal-footer">
						<button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancelar</button>
						<button type="submit" class="btn btn-primary">Guardar</button>
					</div>
				</form>
			</div>
		</div>
	</div>
	<?php #endregion Submenu Modal ?>
</div>
<!-- END #app -->

<?php #region region JS ?>
<?php require_once __ROOT__ . '/views/general/core_js.view.php'; ?>

<?php #region region CLIENT-SIDE VALIDATION SCRIPT ?>
<script>
    document.addEventListener('DOMContentLoaded', function () {
        // Sort submenu table on page load if we're in edit mode
        <?php if ($is_edit_mode): ?>
        setTimeout(() => {
            sortSubmenuTable();
        }, 100); // Small delay to ensure the table is fully loaded
        <?php endif; ?>
        // Menu form validation
        const form           = document.getElementById('menu-form');
        const textoInput     = document.getElementById('texto');
        const urlInput       = document.getElementById('url');
        const iconoInput     = document.getElementById('icono');
        const prioridadInput = document.getElementById('prioridad');

        // Error message elements
        const textoError     = document.getElementById('texto-error');
        const urlError       = document.getElementById('url-error');
        const iconoError     = document.getElementById('icono-error');
        const prioridadError = document.getElementById('prioridad-error');

        form.addEventListener('submit', function (event) {
            let isValid = true;

            // Reset previous validation states
            [textoInput, urlInput, iconoInput, prioridadInput].forEach(input => {
                input.classList.remove('is-invalid');
            });
            [textoError, urlError, iconoError, prioridadError].forEach(errorEl => {
                errorEl.textContent = ''; // Clear previous messages
            });

            // Validate Texto
            if (textoInput.value.trim() === '') {
                isValid = false;
                textoInput.classList.add('is-invalid');
                textoError.textContent = 'El texto del menú es requerido.';
            }

            // Validate URL
            if (urlInput.value.trim() === '') {
                isValid = false;
                urlInput.classList.add('is-invalid');
                urlError.textContent = 'La URL del menú es requerida.';
            }

            // Validate Icono (optional, but if provided, validate format)
            const iconoValue = iconoInput.value.trim();
            if (iconoValue !== '' && !iconoValue.match(/^[a-zA-Z0-9 -]+$/)) {
                isValid = false;
                iconoInput.classList.add('is-invalid');
                iconoError.textContent = 'El formato del icono no es válido.';
            }

            // Validate Prioridad (optional validation for numeric value)
            if (prioridadInput.value.trim() !== '' && (!Number.isInteger(Number(prioridadInput.value)) || Number(prioridadInput.value) < 0)) {
                isValid = false;
                prioridadInput.classList.add('is-invalid');
                prioridadError.textContent = 'La prioridad debe ser un número entero positivo.';
            }

            // Prevent form submission if validation fails
            if (!isValid) {
                event.preventDefault();
                event.stopPropagation();
                // Focus the first invalid field
                const firstInvalid = form.querySelector('.is-invalid');
                if (firstInvalid) {
                    firstInvalid.focus();
                }
            }
        });

        // Real-time validation feedback as user types
        [textoInput, urlInput, iconoInput, prioridadInput].forEach(input => {
            input.addEventListener('input', () => {
                // Remove invalid state when user starts typing
                if (input.classList.contains('is-invalid')) {
                    input.classList.remove('is-invalid');
                }
            });
        });

        // Submenu functionality
		<?php if ($is_edit_mode): ?>
        // Function to sort submenu table by priority
        function sortSubmenuTable() {
            const tableBody = document.getElementById('submenu-table-body');
            if (!tableBody) return;

            // Get all rows except the 'no submenus' row
            const rows = Array.from(tableBody.querySelectorAll('tr:not(#no-submenus-row)'));
            if (rows.length <= 1) return; // No need to sort if there's only one or zero rows

            // Sort rows by priority
            rows.sort((a, b) => {
                const priorityA = parseInt(a.querySelector('.submenu-prioridad-cell').textContent) || 0;
                const priorityB = parseInt(b.querySelector('.submenu-prioridad-cell').textContent) || 0;
                return priorityA - priorityB; // Ascending order
            });

            // Remove all rows from the table
            rows.forEach(row => row.remove());

            // Add rows back in sorted order
            rows.forEach(row => tableBody.appendChild(row));

            console.log('Submenu table sorted by priority');
        }

        // Elements
        const submenuModal          = new bootstrap.Modal(document.getElementById('submenuModal'));
        const submenuForm           = document.getElementById('submenu-form');
        const submenuIdInput        = document.getElementById('submenu-id');
        const submenuIdMenuInput    = document.getElementById('submenu-id-menu');
        const submenuActionInput    = document.getElementById('submenu-action');
        const submenuTextoInput     = document.getElementById('submenu-texto');
        const submenuUrlInput       = document.getElementById('submenu-url');
        const submenuPrioridadInput = document.getElementById('submenu-prioridad');
        const submenuErrorDiv       = document.getElementById('submenu-error');
        const submenuModalLabel     = document.getElementById('submenuModalLabel');
        const submenuTableBody      = document.getElementById('submenu-table-body');
        const btnAddSubmenu         = document.getElementById('btn-add-submenu');

        // Add submenu button click
        btnAddSubmenu.addEventListener('click', function () {
            // Reset form
            submenuForm.reset();
            submenuIdInput.value          = '';
            // Make sure id_menu is set correctly
            submenuIdMenuInput.value      = '<?php echo $id; ?>';
            submenuActionInput.value      = 'create_submenu';
            submenuModalLabel.textContent = 'Agregar Submenú';
            submenuErrorDiv.style.display = 'none';
            submenuErrorDiv.textContent   = '';

            // Show modal
            submenuModal.show();
        });

        // Edit submenu button click (using event delegation)
        document.addEventListener('click', function (event) {
            const editButton = event.target.closest('.btn-edit-submenu');
            if (editButton) {
                const submenuId = editButton.dataset.submenuId;
                const texto     = editButton.dataset.texto;
                const url       = editButton.dataset.url;
                const prioridad = editButton.dataset.prioridad;

                // Populate form
                submenuIdInput.value          = submenuId;
                submenuTextoInput.value       = texto;
                submenuUrlInput.value         = url;
                submenuPrioridadInput.value   = prioridad;
                submenuActionInput.value      = 'update_submenu';
                submenuModalLabel.textContent = 'Editar Submenú';
                submenuErrorDiv.style.display = 'none';
                submenuErrorDiv.textContent   = '';

                // Show modal
                submenuModal.show();
            }
        });

        // Delete submenu button click (using event delegation)
        document.addEventListener('click', function (event) {
            const deleteButton = event.target.closest('.btn-delete-submenu');
            if (deleteButton) {
                const submenuId = deleteButton.dataset.submenuId;
                const texto     = deleteButton.dataset.texto;

                // Confirm deletion
                swal({
                    title  : '¿Eliminar submenú?',
                    text   : `¿Está seguro que desea eliminar el submenú "${texto}"?`,
                    icon   : 'warning',
                    buttons: {
                        cancel : {
                            text      : 'Cancelar',
                            value     : null,
                            visible   : true,
                            className : 'btn btn-default',
                            closeModal: true,
                        },
                        confirm: {
                            text      : 'Sí, eliminar',
                            value     : true,
                            visible   : true,
                            className : 'btn btn-danger',
                            closeModal: true
                        }
                    }
                }).then((result) => {
                    if (result) {
                        // Send AJAX request to delete submenu
                        const formData = new FormData();
                        formData.append('ajax_action', 'delete_submenu');
                        formData.append('id', submenuId);
                        formData.append('id_menu', submenuIdMenuInput.value);

                        fetch('imenu', {
                            method: 'POST',
                            body  : formData
                        })
                            .then(response => response.json())
                            .then(data => {
                                if (data.success) {
                                    // Remove row from table
                                    const row = document.querySelector(`tr[data-submenu-id="${submenuId}"]`);
                                    if (row) {
                                        row.remove();
                                    }

                                    // Check if table is empty
                                    if (submenuTableBody.children.length === 0) {
                                        submenuTableBody.innerHTML = `
                                    <tr id="no-submenus-row">
                                        <td colspan="4" class="text-center">No hay submenús para mostrar.</td>
                                    </tr>
                                `;

                                        // Update tiene_subs checkbox
                                        const tieneSubsCheckbox = document.getElementById('tiene_subs');
                                        if (tieneSubsCheckbox) {
                                            tieneSubsCheckbox.checked = false;
                                        }
                                    }

                                    // Sort the table by priority (if there are still rows)
                                    if (submenuTableBody.children.length > 0 && !document.getElementById('no-submenus-row')) {
                                        sortSubmenuTable();
                                    }

                                    // Show success message
                                    showSweetAlertSuccess('Éxito', data.message);
                                } else {
                                    // Show error message
                                    showSweetAlertError('Error', data.message);
                                }
                            })
                            .catch(error => {
                                console.error('Error:', error);
                                showSweetAlertError('Error', 'Ocurrió un error al procesar la solicitud.');
                            });
                    }
                });
            }
        });

        // Submenu form submission
        submenuForm.addEventListener('submit', function (event) {
            event.preventDefault();

            // Validate form
            let isValid = true;

            // Reset validation state
            submenuTextoInput.classList.remove('is-invalid');
            submenuErrorDiv.style.display = 'none';

            // Validate Texto
            if (submenuTextoInput.value.trim() === '') {
                isValid = false;
                submenuTextoInput.classList.add('is-invalid');
            }

            if (!isValid) {
                return;
            }

            // Prepare form data
            const formData = new FormData(submenuForm);

            // Log form data for debugging
            console.log('Submenu form data:', {
                id       : submenuIdInput.value,
                id_menu  : submenuIdMenuInput.value,
                action   : submenuActionInput.value,
                texto    : submenuTextoInput.value,
                url      : submenuUrlInput.value,
                prioridad: submenuPrioridadInput.value
            });

            // Send AJAX request
            fetch('imenu', {
                method : 'POST',
                headers: {
                    'X-Requested-With': 'XMLHttpRequest'  // Add this header to identify AJAX requests
                },
                body   : formData
            })
                .then(response => {
                    if (!response.ok) {
                        throw new Error(`HTTP error! Status: ${response.status}`);
                    }
                    return response.json();
                })
                .then(data => {
                    console.log('Server response:', data);
                    if (data.success) {
                        // Hide modal
                        submenuModal.hide();

                        // Update table
                        const isNewSubmenu = submenuActionInput.value === 'create_submenu';

                        if (isNewSubmenu) {
                            // Remove 'no submenus' row if it exists
                            const noSubmenusRow = document.getElementById('no-submenus-row');
                            if (noSubmenusRow) {
                                noSubmenusRow.remove();
                            }

                            // Add new row
                            const newRow = document.createElement('tr');
                            newRow.setAttribute('data-submenu-id', data.data.id);
                            newRow.innerHTML = `
                        <td>
                            <button type="button" class="btn btn-xs btn-primary me-1 btn-edit-submenu"
                                title="Editar Submenú"
                                data-submenu-id="${data.data.id}"
                                data-texto="${data.data.texto}"
                                data-url="${data.data.url || ''}"
                                data-prioridad="${data.data.prioridad}">
                                <i class="fa fa-edit"></i>
                            </button>
                            <button type="button" class="btn btn-xs btn-danger btn-delete-submenu"
                                title="Eliminar"
                                data-submenu-id="${data.data.id}"
                                data-texto="${data.data.texto}">
                                <i class="fa fa-trash-alt"></i>
                            </button>
                        </td>
                        <td class="submenu-texto-cell">${data.data.texto}</td>
                        <td class="submenu-url-cell">${data.data.url || ''}</td>
                        <td class="submenu-prioridad-cell text-center">${data.data.prioridad}</td>
                    `;
                            submenuTableBody.appendChild(newRow);

                            // Update tiene_subs checkbox
                            const tieneSubsCheckbox = document.getElementById('tiene_subs');
                            if (tieneSubsCheckbox) {
                                tieneSubsCheckbox.checked = true;
                            }
                        } else {
                            // Update existing row
                            const row = document.querySelector(`tr[data-submenu-id="${data.data.id}"]`);
                            if (row) {
                                // Update cells
                                row.querySelector('.submenu-texto-cell').textContent     = data.data.texto;
                                row.querySelector('.submenu-url-cell').textContent       = data.data.url || '';
                                row.querySelector('.submenu-prioridad-cell').textContent = data.data.prioridad;

                                // Update edit button data attributes
                                const editButton = row.querySelector('.btn-edit-submenu');
                                if (editButton) {
                                    editButton.dataset.texto     = data.data.texto;
                                    editButton.dataset.url       = data.data.url || '';
                                    editButton.dataset.prioridad = data.data.prioridad;
                                }

                                // Update delete button data attribute
                                const deleteButton = row.querySelector('.btn-delete-submenu');
                                if (deleteButton) {
                                    deleteButton.dataset.texto = data.data.texto;
                                }
                            }
                        }

                        // Sort the table by priority
                        sortSubmenuTable();

                        // Show success message
                        showSweetAlertSuccess('Éxito', data.message);
                    } else {
                        // Show error message
                        submenuErrorDiv.textContent   = data.message;
                        submenuErrorDiv.style.display = 'block';
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    submenuErrorDiv.textContent   = 'Ocurrió un error al procesar la solicitud: ' + error.message;
                    submenuErrorDiv.style.display = 'block';
                });
        });
		<?php endif; ?>
    });
</script>
<?php #endregion CLIENT-SIDE VALIDATION SCRIPT ?>
<?php #endregion JS ?>

</body>
</html>
