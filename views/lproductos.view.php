<?php
#region DOCS

/** @var Producto[] $productos */
/** @var string|null $success_text Success message to display */
/** @var string|null $success_display Whether to show success message ('show' or null) */
/** @var string|null $error_text Error message to display */
/** @var string|null $error_display Whether to show error message ('show' or null) */

use App\classes\Producto;

#endregion DOCS
?>
<!DOCTYPE html>
<html lang="en" class="dark-mode">
<head>
	<meta charset="utf-8"/>
	<title><?php echo APP_NAME; ?> | Productos</title>
	<meta content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no" name="viewport"/>
	<meta content="" name="description"/>
	<meta content="" name="author"/>

	<?php #region HEAD ?>
	<?php require_once __ROOT__ . '/views/general/head.view.php'; ?>
	<?php #endregion HEAD ?>

	<!-- CSS for currency formatting -->
	<style>
        input.currency-input {
            text-align: right;
        }
    </style>
</head>
<body>
<!-- BEGIN #loader -->
<div id="loader" class="app-loader">
	<span class="spinner"></span>
</div>
<!-- END #loader -->

<!-- BEGIN #app -->
<div id="app" class="app app-header-fixed app-sidebar-fixed">
	<!-- #header -->
	<?php require_once __ROOT__ . '/views/general/header.view.php'; ?>

	<!-- #sidebar -->
	<?php require_once __ROOT__ . '/views/general/sidebar.view.php'; ?>

	<!-- BEGIN #content -->
	<div id="content" class="app-content">
		<?php #region PAGE HEADER ?>
		<div class="d-flex align-items-center mb-3">
			<div>
				<h4 class="mb-0">Gestión de Productos</h4>
				<p class="mb-0 text-muted">Administra los productos del sistema</p>
			</div>
			<div class="ms-auto">
				<button type="button" class="btn btn-success" data-bs-toggle="modal" data-bs-target="#createProductoModal">
					<i class="fa fa-plus-circle fa-fw me-1"></i> Crear nuevo
				</button>
			</div>
		</div>
		<?php #endregion PAGE HEADER ?>

		<?php #region SEARCH BAR ?>
		<div class="panel panel-inverse mt-3 no-border-radious">
			<div class="panel-heading no-border-radious">
				<h4 class="panel-title">
					Buscar Productos
				</h4>
			</div>
			<div class="panel-body">
				<div class="row">
					<div class="col-lg-6">
						<div class="input-group">
							<input type="text" class="form-control" id="search-input" placeholder="Buscar por descripción...">
							<button class="btn btn-outline-secondary" type="button" id="clear-search-btn">
								<i class="fa fa-times"></i>
							</button>
						</div>
					</div>
				</div>
			</div>
		</div>
		<?php #endregion SEARCH BAR ?>

		<?php #region PANEL PRODUCTOS ?>
		<div class="panel panel-inverse mt-3 no-border-radious">
			<div class="panel-heading no-border-radious">
				<h4 class="panel-title">
					Listado de Productos Activos
				</h4>
			</div>
			<!-- BEGIN PANEL body -->
			<div class="p-1 table-nowrap" style="overflow: auto">
				<?php #region TABLE PRODUCTOS ?>
				<table class="table table-hover table-sm">
					<thead>
					<tr>
						<th class="w-70px text-center">Acciones</th>
						<th>Descripción</th>
						<th class="text-center">Valor</th>
						<th class="text-center">Inventario</th>
					</tr>
					</thead>
					<tbody class="fs-12px" id="producto-table-body">
					<?php foreach ($productos as $producto): ?>
						<tr data-producto-id="<?php echo $producto->getId(); ?>">
							<td class="text-center">
								<?php // Edit Button - Triggers Modal ?>
								<button type="button" class="btn btn-xs btn-primary me-1 btn-edit-producto"
								        title="Editar Producto"
								        data-bs-toggle="modal"
								        data-bs-target="#editProductoModal"
								        data-producto-id="<?php echo $producto->getId(); ?>"
								        data-descripcion="<?php echo htmlspecialchars($producto->getDescripcion() ?? ''); ?>"
								        data-valor="<?php echo htmlspecialchars($producto->getValor()); ?>">
									<i class="fa fa-edit"></i>
								</button>
								<?php // Deactivate Button - Only show if active ?>
								<?php if ($producto->isActivo()): ?>
									<button type="button" class="btn btn-xs btn-danger btn-desactivar-producto"
									        title="Desactivar"
									        data-producto-id="<?php echo $producto->getId(); ?>"
									        data-descripcion="<?php echo htmlspecialchars($producto->getDescripcion() ?? ''); ?>">
										<i class="fa fa-trash-alt"></i>
									</button>
								<?php endif; ?>
							</td>
							<td class="producto-descripcion-cell"><?php echo htmlspecialchars($producto->getDescripcion()); ?></td>
							<td class="producto-valor-cell text-end"><?php echo format_currency_consigno($producto->getValor()); ?></td>
							<td class="text-center">
								<?php
								// Find inventory for this product
								$inventario_producto = null;
								foreach ($inventario_por_producto as $inv) {
									if ($inv['id'] == $producto->getId()) {
										$inventario_producto = $inv;
										break;
									}
								}

								if ($inventario_producto && !empty($inventario_producto['centros_costo'])):
									foreach ($inventario_producto['centros_costo'] as $centro):
										$badgeClass = $centro['cantidad'] > 0 ? 'bg-success' : 'bg-secondary';
										echo '<span class="badge ' . $badgeClass . ' me-1 mb-1">' .
											 htmlspecialchars($centro['nombre']) . ': ' . number_format($centro['cantidad'], 0, ',', '.') .
											 '</span>';
									endforeach;
								else:
									echo '<span class="badge bg-warning">Sin inventario</span>';
								endif;
								?>
							</td>
						</tr>
					<?php endforeach; ?>
					</tbody>
				</table>
				<?php #endregion TABLE PRODUCTOS ?>
			</div>
			<!-- END PANEL body -->
		</div>
		<?php #endregion PANEL PRODUCTOS ?>

		<?php #region Create Producto Modal ?>
		<div class="modal fade" id="createProductoModal" tabindex="-1" aria-labelledby="createProductoModalLabel" aria-hidden="true">
			<div class="modal-dialog">
				<div class="modal-content">
					<form id="create-producto-form">
						<div class="modal-header">
							<h5 class="modal-title" id="createProductoModalLabel">Crear Producto</h5>
							<button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
						</div>
						<div class="modal-body">
							<input type="hidden" name="action" value="crear">

							<div class="mb-3">
								<label for="create-producto-descripcion" class="form-label">Descripción: <span class="text-danger">*</span></label>
								<input type="text" class="form-control" id="create-producto-descripcion" name="descripcion" required>
							</div>

							<div class="mb-3">
								<label for="create-producto-valor" class="form-label">Valor: <span class="text-danger">*</span></label>
								<input type="text" class="form-control currency-input" id="create-producto-valor" name="valor"
								       data-type="currency" placeholder="$0" required>
							</div>

						</div>
						<div class="modal-footer">
							<button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancelar</button>
							<button type="submit" class="btn btn-primary">Guardar</button>
						</div>
					</form>
				</div>
			</div>
		</div>
		<?php #endregion Create Producto Modal ?>

		<?php #region Edit Producto Modal ?>
		<div class="modal fade" id="editProductoModal" tabindex="-1" aria-labelledby="editProductoModalLabel" aria-hidden="true">
			<div class="modal-dialog">
				<div class="modal-content">
					<form id="edit-producto-form">
						<div class="modal-header">
							<h5 class="modal-title" id="editProductoModalLabel">Editar Producto</h5>
							<button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
						</div>
						<div class="modal-body">
							<input type="hidden" name="productoId" id="edit-producto-id">
							<input type="hidden" name="action" value="modificar">

							<div class="mb-3">
								<label for="edit-producto-descripcion" class="form-label">Descripción: <span class="text-danger">*</span></label>
								<input type="text" class="form-control" id="edit-producto-descripcion" name="descripcion" required>
							</div>

							<div class="mb-3">
								<label for="edit-producto-valor" class="form-label">Valor: <span class="text-danger">*</span></label>
								<input type="text" class="form-control currency-input" id="edit-producto-valor" name="valor"
								       data-type="currency" placeholder="$0" required>
							</div>

						</div>
						<div class="modal-footer">
							<button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancelar</button>
							<button type="submit" class="btn btn-primary">Actualizar</button>
						</div>
					</form>
				</div>
			</div>
		</div>
		<?php #endregion Edit Producto Modal ?>

	</div>
	<!-- END #content -->

	<?php #region Hidden Form for Deactivation ?>
	<form id="deactivate-producto-form" method="POST" action="productos" style="display: none;">
		<input type="hidden" name="action" value="desactivar">
		<input type="hidden" name="productoId" id="deactivate-producto-id">
	</form>
	<?php #endregion Hidden Form ?>

	<!-- BEGIN scroll-top-btn -->
	<a href="#" class="btn btn-icon btn-circle btn-success btn-scroll-to-top" data-toggle="scroll-to-top"><i class="fa fa-angle-up"></i></a>
	<!-- END scroll-top-btn -->
</div>
<!-- END #app -->

<?php #region JS ?>
<?php require_once __ROOT__ . '/views/general/core_js.view.php'; ?>

<!-- ================== BEGIN PAGE LEVEL JS ================== -->
<!-- Include formatcurrency.js for currency formatting -->
<script src="<?php echo RUTA_RESOURCES; ?>js/formatcurrency.js"></script>
<script>
    // Initialize currency formatting for dynamically added elements
    $(document).ready(function() {
        // Re-initialize currency inputs when modals are shown
        $('#createProductoModal').on('shown.bs.modal', function () {
            $('input[data-type="currency"]').each(function() {
                $(this).on({
                    keyup: function() { formatCurrency($(this)); },
                    blur: function() { formatCurrency($(this), "blur"); }
                });
            });
        });

        $('#editProductoModal').on('shown.bs.modal', function () {
            $('input[data-type="currency"]').each(function() {
                $(this).on({
                    keyup: function() { formatCurrency($(this)); },
                    blur: function() { formatCurrency($(this), "blur"); }
                });
            });
        });
    });
</script>

<script>
    document.addEventListener('DOMContentLoaded', function () {
        // Get DOM elements
        const tableBody          = document.getElementById('producto-table-body');
        const createProductoForm = document.getElementById('create-producto-form');
        const editProductoForm   = document.getElementById('edit-producto-form');
        const searchInput        = document.getElementById('search-input');
        const clearSearchBtn     = document.getElementById('clear-search-btn');

        const editProductoIdInput          = document.getElementById('edit-producto-id');
        const editProductoDescripcionInput = document.getElementById('edit-producto-descripcion');
        const editProductoValorInput       = document.getElementById('edit-producto-valor');
        const deactivateProductoForm       = document.getElementById('deactivate-producto-form');
        const deactivateProductoIdInput    = document.getElementById('deactivate-producto-id');

        // Bootstrap modal instances
        const createProductoModal = new bootstrap.Modal(document.getElementById('createProductoModal'));
        const editProductoModal   = new bootstrap.Modal(document.getElementById('editProductoModal'));

        // Search functionality variables
        let searchTimeout;

        // --- Handle Success/Error Messages (from PHP redirects) ---
        <?php if (isset($success_display) && $success_display === 'show'): ?>
        showSweetAlertSuccess('¡Éxito!', '<?php echo addslashes($success_text ?? "Operación completada con éxito."); ?>');
        <?php endif; ?>

        <?php if (isset($error_display) && $error_display === 'show'): ?>
        // Check if error message contains HTML (for inventory link)
        <?php if (strpos($error_text ?? '', '<a href') !== false): ?>
        swal({
            title: 'Error',
            content: {
                element: "div",
                attributes: {
                    innerHTML: '<?php echo addslashes($error_text ?? "Ha ocurrido un error."); ?>'
                }
            },
            icon: 'error',
            button: {
                text: "Entendido",
                value: true,
                visible: true,
                className: "btn-danger",
                closeModal: true
            }
        });
        <?php else: ?>
        showSweetAlertError('Error', '<?php echo addslashes($error_text ?? "Ha ocurrido un error."); ?>');
        <?php endif; ?>
        <?php endif; ?>

        // --- Search Functionality ---
        if (searchInput) {
            searchInput.addEventListener('input', function() {
                clearTimeout(searchTimeout);
                searchTimeout = setTimeout(() => {
                    performSearch(this.value.trim());
                }, 300); // Debounce search
            });
        }

        if (clearSearchBtn) {
            clearSearchBtn.addEventListener('click', function() {
                searchInput.value = '';
                performSearch('');
            });
        }

        function performSearch(termino) {
            const formData = new FormData();
            formData.append('action', 'buscar');
            formData.append('termino', termino);

            fetch('productos', {
                method: 'POST',
                body: formData
            })
                .then(response => {
                    if (!response.ok) {
                        throw new Error(`Error ${response.status}: ${response.statusText}`);
                    }
                    return response.json();
                })
                .then(data => {
                    if (data.success) {
                        updateTable(data.productos);
                    } else {
                        showSweetAlertError('Error', data.message);
                    }
                })
                .catch(error => {
                    showSweetAlertError('Error', 'Error al realizar la búsqueda: ' + error.message);
                });
        }

        function updateTable(productos) {
            tableBody.innerHTML = '';

            productos.forEach(producto => {
                const newRow = document.createElement('tr');
                newRow.setAttribute('data-producto-id', producto.id);

                const deactivateButton = producto.is_activo
                    ? `<button type="button" class="btn btn-xs btn-danger btn-desactivar-producto"
                              title="Desactivar"
                              data-producto-id="${producto.id}"
                              data-descripcion="${producto.descripcion}">
                          <i class="fa fa-trash-alt"></i>
                       </button>`
                    : '';

                newRow.innerHTML = `
                    <td class="text-center">
                        <button type="button" class="btn btn-xs btn-primary me-1 btn-edit-producto"
                                title="Editar Producto"
                                data-bs-toggle="modal"
                                data-bs-target="#editProductoModal"
                                data-producto-id="${producto.id}"
                                data-descripcion="${producto.descripcion}"
                                data-valor="${producto.valor}">
                            <i class="fa fa-edit"></i>
                        </button>
                        ${deactivateButton}
                    </td>
                    <td class="producto-descripcion-cell">${producto.descripcion}</td>
                    <td class="producto-valor-cell text-end">${producto.valor_formateado}</td>
                    <td class="text-center">
                        <span class="badge bg-warning">Sin inventario</span>
                    </td>
                `;

                tableBody.appendChild(newRow);
            });
        }

        // Use event delegation on the table body
        if (tableBody) {
            tableBody.addEventListener('click', function (event) {
                // Find the closest button if it was clicked or a child of it was clicked
                const deactivateButton = event.target.closest('.btn-desactivar-producto');
                const editButton       = event.target.closest('.btn-edit-producto');

                // --- Handle Deactivate Click ---
                if (deactivateButton) {
                    event.preventDefault(); // Prevent default if it was an <a>
                    const productoId  = deactivateButton.dataset.productoId;
                    const descripcion = deactivateButton.dataset.descripcion;

                    // Confirm before deactivating using SweetAlert v1
                    swal({
                        title: 'Confirmar desactivación',
                        text: `¿Está seguro que desea desactivar el producto "${descripcion}"?`,
                        icon: 'warning',
                        buttons: {
                            cancel: {
                                text: "Cancelar",
                                value: null,
                                visible: true,
                                className: "btn-secondary",
                                closeModal: true
                            },
                            confirm: {
                                text: "Sí, desactivar",
                                value: true,
                                visible: true,
                                className: "btn-danger",
                                closeModal: true
                            }
                        }
                    }).then((willDelete) => {
                        if (willDelete) {
                            // Set the ID in the hidden form and submit it
                            deactivateProductoIdInput.value = productoId;
                            deactivateProductoForm.submit();
                        }
                    });
                }

                // --- Handle Edit Click ---
                if (editButton) {
                    event.preventDefault(); // Prevent default if it was an <a>
                    const productoId  = editButton.dataset.productoId;
                    const descripcion = editButton.dataset.descripcion;
                    const valor       = editButton.dataset.valor;

                    console.log('Edit button clicked with valor:', valor);

                    // Format the valor for display in the input
                    const valorFormateado = formatCurrencyValue(valor);

                    console.log('Formatted valor:', valorFormateado);

                    // Populate the modal form
                    editProductoIdInput.value          = productoId;
                    editProductoDescripcionInput.value = descripcion;
                    editProductoValorInput.value       = valorFormateado;

                    // The data-bs-toggle and data-bs-target attributes on the button handle showing the modal
                }
            });
        }

        // --- Handle Create Form Submission (AJAX) ---
        if (createProductoForm) {
            createProductoForm.addEventListener('submit', function (event) {
                event.preventDefault(); // Stop default form submission

                const formData    = new FormData(createProductoForm);
                const descripcion = formData.get('descripcion').trim();  // Get trimmed description
                const valor       = formData.get('valor').trim();        // Get trimmed valor

                // Basic client-side validation
                if (!descripcion) {
                    showSweetAlertError('Error de validación', 'La descripción no puede estar vacía.');
                    return; // Stop submission
                }

                if (!valor || valor === '$0') {
                    showSweetAlertError('Error de validación', 'El valor debe ser mayor que cero.');
                    return; // Stop submission
                }

                // Disable submit button during request
                const submitButton = createProductoForm.querySelector('button[type="submit"]');
                submitButton.disabled = true;

                fetch('productos', {
                    method: 'POST',
                    body: formData
                })
                    .then(response => {
                        if (!response.ok) {
                            return response.json().then(errData => {
                                throw new Error(errData.message || `Error ${response.status}: ${response.statusText}`);
                            }).catch(() => {
                                throw new Error(`Error ${response.status}: ${response.statusText}`);
                            });
                        }
                        return response.json();
                    })
                    .then(data => {
                        if (data.success) {
                            createProductoModal.hide(); // Close modal on success

                            // Reset form for next use
                            createProductoForm.reset();

                            // Show success message using SweetAlert
                            showSweetAlertSuccess('¡Éxito!', data.message);

                            // Create new row HTML
                            const newRow = document.createElement('tr');
                            newRow.setAttribute('data-producto-id', data.id);

                            newRow.innerHTML = `
                                <td class="text-center">
                                    <button type="button" class="btn btn-xs btn-primary me-1 btn-edit-producto"
                                            title="Editar Producto"
                                            data-bs-toggle="modal"
                                            data-bs-target="#editProductoModal"
                                            data-producto-id="${data.id}"
                                            data-descripcion="${data.descripcion}"
                                            data-valor="${parseFloat(data.valor.replace(/\$/g, '').replace(/\./g, '').replace(/,/g, '.'))}">
                                        <i class="fa fa-edit"></i>
                                    </button>
                                    <button type="button" class="btn btn-xs btn-danger btn-desactivar-producto"
                                            title="Desactivar"
                                            data-producto-id="${data.id}"
                                            data-descripcion="${data.descripcion}">
                                        <i class="fa fa-trash-alt"></i>
                                    </button>
                                </td>
                                <td class="producto-descripcion-cell">${data.descripcion}</td>
                                <td class="producto-valor-cell text-end">${data.valor}</td>
                                <td class="text-center">
                                    <span class="badge bg-warning">Sin inventario</span>
                                </td>
                            `;

                            // Add the new row to the table
                            tableBody.appendChild(newRow);
                        } else {
                            // Show error using SweetAlert
                            showSweetAlertError('Error', data.message);
                        }
                    })
                    .catch(error => {
                        // Show error using SweetAlert
                        showSweetAlertError('Error', error.message);
                    })
                    .finally(() => {
                        // Re-enable submit button
                        submitButton.disabled = false;
                    });
            });
        }

        // --- Handle Edit Form Submission (AJAX) ---
        if (editProductoForm) {
            editProductoForm.addEventListener('submit', function (event) {
                event.preventDefault(); // Stop default form submission

                const formData         = new FormData(editProductoForm);
                const productoId       = formData.get('productoId');
                const nuevaDescripcion = formData.get('descripcion').trim();  // Get trimmed description
                const nuevoValor       = formData.get('valor').trim();        // Get trimmed valor

                // Basic client-side validation
                if (!nuevaDescripcion) {
                    showSweetAlertError('Error de validación', 'La descripción no puede estar vacía.');
                    return; // Stop submission
                }

                if (!nuevoValor || nuevoValor === '$0') {
                    showSweetAlertError('Error de validación', 'El valor debe ser mayor que cero.');
                    return; // Stop submission
                }

                // Disable submit button during request
                const submitButton = editProductoForm.querySelector('button[type="submit"]');
                submitButton.disabled = true;

                fetch('productos', {
                    method: 'POST',
                    body: formData
                })
                    .then(response => {
                        if (!response.ok) {
                            return response.json().then(errData => {
                                throw new Error(errData.message || `Error ${response.status}: ${response.statusText}`);
                            }).catch(() => {
                                throw new Error(`Error ${response.status}: ${response.statusText}`);
                            });
                        }
                        return response.json();
                    })
                    .then(data => {
                        if (data.success) {
                            editProductoModal.hide(); // Close modal on success

                            // Show success message using SweetAlert
                            showSweetAlertSuccess('¡Éxito!', data.message);

                            // Update the row in the table
                            const row = document.querySelector(`tr[data-producto-id="${productoId}"]`);
                            if (row) {
                                const descripcionCell  = row.querySelector('.producto-descripcion-cell');
                                const valorCell        = row.querySelector('.producto-valor-cell');
                                const editButton       = row.querySelector('.btn-edit-producto');
                                const deactivateButton = row.querySelector('.btn-desactivar-producto');

                                if (descripcionCell) descripcionCell.textContent = nuevaDescripcion;
                                if (valorCell) valorCell.textContent = data.valor_formateado;

                                // Update data attributes for future edits
                                if (editButton) {
                                    editButton.dataset.descripcion = nuevaDescripcion;
                                    // Update the valor data attribute with the raw value
                                    // Handle Colombian Peso format correctly
                                    const cleanValor = nuevoValor.replace(/\$/g, '').replace(/\./g, '').replace(/,/g, '.');
                                    editButton.dataset.valor = parseFloat(cleanValor);
                                }

                                if (deactivateButton) {
                                    deactivateButton.dataset.descripcion = nuevaDescripcion;
                                }
                            }
                        } else {
                            // Show error using SweetAlert
                            showSweetAlertError('Error', data.message);
                        }
                    })
                    .catch(error => {
                        // Show error using SweetAlert
                        showSweetAlertError('Error', error.message);
                    })
                    .finally(() => {
                        // Re-enable submit button
                        submitButton.disabled = false;
                    });
            });
        }

        // Helper function to format currency value for display in input
        function formatCurrencyValue(value) {
            // Convert to number and format as currency
            console.log('Formatting value:', value, 'Type:', typeof value);
            let numValue;
            try {
                // Make sure we're working with a clean number
                if (typeof value === 'string') {
                    // Handle Colombian Peso format: remove $ and convert thousands separators
                    // First, remove the currency symbol
                    let cleanValue = value.replace(/\$/g, '');
                    // Then, remove all dots (thousands separators in Colombian format)
                    cleanValue = cleanValue.replace(/\./g, '');
                    // Finally, replace commas with dots for decimal points
                    cleanValue = cleanValue.replace(/,/g, '.');

                    console.log('Cleaned value for parsing:', cleanValue);
                    value = cleanValue;
                }
                numValue = parseFloat(value);
                console.log('Parsed value:', numValue);
            } catch (e) {
                console.error('Error parsing value:', e);
                return '$0';
            }

            if (isNaN(numValue)) {
                console.warn('Value is NaN after parsing');
                return '$0';
            }

            try {
                // Format with thousands separator for Colombian Peso
                // Use Math.round to avoid decimal places
                const formatted = '$' + Math.round(numValue).toLocaleString('es-CO').replace(/,/g, '.');
                console.log('Final formatted value:', formatted);
                return formatted;
            } catch (e) {
                console.error('Error formatting value:', e);
                return '$' + Math.round(numValue);
            }
        }
    });
</script>
<!-- ================== END PAGE LEVEL JS ================== -->
<?php #endregion JS ?>
</body>
</html>