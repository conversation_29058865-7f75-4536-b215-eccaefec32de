<?php #region DOCS
/** @var Empleado[] $empleados Lista de empleados activos */
/** @var string $fecha_inicio Fecha de inicio del filtro */
/** @var string $fecha_fin Fecha de fin del filtro */
/** @var int|string $id_empleado ID del empleado seleccionado */
/** @var array|null $consulta_data Datos de la consulta si se ejecutó */
/** @var string|null $success_text Mensaje de éxito para mostrar */
/** @var string|null $success_display Si se debe mostrar el mensaje de éxito ('show' o null) */
/** @var string|null $error_text Mensaje de error para mostrar */
/** @var string|null $error_display Si se debe mostrar el mensaje de error ('show' o null) */
#endregion DOCS

use App\classes\Empleado;

// Prevent direct access
if (!defined('__ROOT__')) {
    http_response_code(404);
    exit('Acceso directo no permitido');
}

// Set default values if not set
$fecha_inicio = $fecha_inicio ?? '';
$fecha_fin    = $fecha_fin ?? '';
$id_empleado  = $id_empleado ?? '';
?>

<!DOCTYPE html>
<html lang="es" class="dark-mode">
<head>
    <meta charset="utf-8"/>
    <title><?php echo APP_NAME; ?> | Consulta de Citas por Empleado</title>
    <meta content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no" name="viewport"/>
    <meta content="" name="description"/>
    <meta content="" name="author"/>

    <?php #region HEAD ?>
    <?php require_once __ROOT__ . '/views/general/head.view.php'; ?>
    <?php #endregion HEAD ?>

    <!-- Include Bootstrap Datepicker CSS -->
    <link href="<?php echo RUTA_ADM_ASSETS; ?>plugins/bootstrap-datepicker/dist/css/bootstrap-datepicker.css" rel="stylesheet"/>
</head>

<body>
<!-- BEGIN #loader -->
<div id="loader" class="app-loader">
    <span class="spinner"></span>
</div>
<!-- END #loader -->

<!-- BEGIN #app -->
<div id="app" class="app app-header-fixed app-sidebar-fixed">
    <!-- #header -->
    <?php require_once __ROOT__ . '/views/general/header.view.php'; ?>

    <!-- #sidebar -->
    <?php require_once __ROOT__ . '/views/general/sidebar.view.php'; ?>

    <!-- BEGIN #content -->
    <div id="content" class="app-content">
        <!-- BEGIN breadcrumb -->
        <ol class="breadcrumb float-xl-end">
            <li class="breadcrumb-item"><a href="dashboard">Inicio</a></li>
            <li class="breadcrumb-item active">Consulta de Citas por Empleado</li>
        </ol>
        <!-- END breadcrumb -->

        <?php #region PAGE HEADER ?>
        <div class="d-flex align-items-center mb-3">
            <div>
                <h4 class="mb-0">Consulta de Citas por Empleado</h4>
                <p class="mb-0 text-muted">Consulte las citas completadas por empleado específico</p>
            </div>
        </div>

        <hr>
        <?php #endregion PAGE HEADER ?>

        <?php #region SEARCH FILTERS ?>
        <!-- Search Filters Panel -->
        <div class="panel panel-inverse">
            <div class="panel-heading">
                <h4 class="panel-title">Filtros de Búsqueda</h4>
            </div>
            <div class="panel-body">
                <form id="consulta-form">
                    <div class="row g-3">
                        <!-- Fecha Inicio -->
                        <div class="col-md-4">
                            <label for="fecha_inicio" class="form-label">Fecha Inicio: <span class="text-danger">*</span></label>
                            <div class="input-group">
                                <input type="text" class="form-control datepicker" id="fecha_inicio" name="fecha_inicio"
                                       placeholder="yyyy-mm-dd" value="<?= htmlspecialchars($fecha_inicio) ?>" required>
                                <span class="input-group-text" id="fecha_inicio_icon" style="cursor: pointer;">
                                    <i class="fa fa-calendar"></i>
                                </span>
                            </div>
                        </div>

                        <!-- Fecha Fin -->
                        <div class="col-md-4">
                            <label for="fecha_fin" class="form-label">Fecha Fin: <span class="text-danger">*</span></label>
                            <div class="input-group">
                                <input type="text" class="form-control datepicker" id="fecha_fin" name="fecha_fin"
                                       placeholder="yyyy-mm-dd" value="<?= htmlspecialchars($fecha_fin) ?>" required>
                                <span class="input-group-text" id="fecha_fin_icon" style="cursor: pointer;">
                                    <i class="fa fa-calendar"></i>
                                </span>
                            </div>
                        </div>

                        <!-- Empleado -->
                        <div class="col-md-4">
                            <label for="id_empleado" class="form-label">Empleado: <span class="text-danger">*</span></label>
                            <select class="form-select" id="id_empleado" name="id_empleado" required>
                                <option value="">Seleccione un Empleado</option>
                                <?php foreach ($empleados as $empleado): ?>
                                    <option value="<?= $empleado->getId() ?>" <?= ($id_empleado == $empleado->getId()) ? 'selected' : '' ?>>
                                        <?= htmlspecialchars($empleado->getNombre()) ?>
                                    </option>
                                <?php endforeach; ?>
                            </select>
                        </div>
                    </div>

                    <div class="row g-3 mt-1 justify-content-end">
                        <!-- Generate Report Button -->
                        <div class="col-md-auto">
                            <button type="submit" class="btn btn-primary">
                                <i class="fa fa-search fa-fw me-1"></i> Generar Reporte
                            </button>
                        </div>
                        <!-- Clear Filters Button -->
                        <div class="col-md-auto">
                            <button type="button" class="btn btn-secondary" id="clear-filters-btn">
                                <i class="fa fa-times fa-fw me-1"></i> Limpiar Filtros
                            </button>
                        </div>
                    </div>
                </form>
            </div>
        </div>
        <?php #endregion SEARCH FILTERS ?>

        <?php #region RESULTS ?>
        <!-- Results Panel -->
        <div class="panel panel-inverse">
            <div class="panel-heading">
                <div class="d-flex align-items-center w-100">
                    <div>
                        <h4 class="panel-title">Resultados de la Consulta</h4>
                        <small id="filtros-aplicados" class="text-muted fs-6" style="display: none;"></small>
                    </div>
                    <div class="ms-auto">
                        <form method="POST" style="display: inline;" id="export-excel-form">
                            <input type="hidden" name="action" value="exportar_excel">
                            <input type="hidden" name="fecha_inicio" id="export-fecha-inicio" value="">
                            <input type="hidden" name="fecha_fin" id="export-fecha-fin" value="">
                            <input type="hidden" name="id_empleado" id="export-empleado" value="">
                            <button type="submit" class="btn btn-success btn-sm" id="export-excel-btn" style="display: none;">
                                <i class="fa fa-file-excel fa-fw me-1"></i> Exportar a Excel
                            </button>
                        </form>
                    </div>
                </div>
            </div>
            <div class="panel-body">

                    <?php #region ALERTS ?>
                    <!-- Success Alert -->
                    <?php if (isset($success_display) && $success_display === 'show'): ?>
                        <div class="alert alert-success alert-dismissible fade show" role="alert">
                            <strong>¡Éxito!</strong> <?= htmlspecialchars($success_text) ?>
                            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                        </div>
                    <?php endif; ?>

                    <!-- Error Alert -->
                    <?php if (isset($error_display) && $error_display === 'show'): ?>
                        <div class="alert alert-danger alert-dismissible fade show" role="alert">
                            <strong>Error:</strong> <?= htmlspecialchars($error_text) ?>
                            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                        </div>
                    <?php endif; ?>
                    <?php #endregion ALERTS ?>

                    <?php #region LOADING INDICATOR ?>
                    <!-- Loading Indicator -->
                    <div id="loading" class="text-center py-5" style="display: none;">
                        <i class="fa fa-spinner fa-spin fa-3x text-primary mb-3"></i>
                        <h5 class="text-primary">Cargando citas...</h5>
                    </div>
                    <?php #endregion LOADING INDICATOR ?>

                    <?php #region NO RESULTS ?>
                    <!-- No Results / Empty State -->
                    <div id="no-results" class="text-center py-5">
                        <i class="fa fa-search fa-3x text-muted mb-3"></i>
                        <h5 class="text-muted">Utilice los filtros para consultar las citas por empleado</h5>
                        <p class="text-muted">Seleccione un empleado y un rango de fechas para ver las citas completadas.</p>
                    </div>
                    <?php #endregion NO RESULTS ?>

                    <?php #region RESULTS TABLE ?>
                    <!-- Results Table -->
                    <div id="results-table-container" style="display: none;">
                        <div class="table-responsive">
                            <table class="table table-hover table-sm mb-0" id="citas-table">
                                <thead class="table-dark">
                                    <tr>
                                        <th class="text-center" style="width: 100px;">Acciones</th>
                                        <th class="text-center">#</th>
                                        <th>Centro Costo</th>
                                        <th>Puesto</th>
                                        <th>Barbero</th>
                                        <th>Método de Pago</th>
                                        <th class="text-center" style="width: 140px;">Fecha Inicio</th>
                                        <th class="text-center" style="width: 140px;">Fecha Fin</th>
                                        <th class="text-center" style="width: 100px;">Estado</th>
                                        <th class="text-end" style="width: 120px;">Total</th>
                                        <th class="text-end" style="width: 120px;">Comisión</th>
                                    </tr>
                                </thead>
                                <tbody id="citas-table-body">
                                    <!-- Results will be populated via AJAX -->
                                </tbody>
                                <tfoot id="citas-table-footer" style="display: none;">
                                    <tr class="table-info">
                                        <td colspan="9" class="text-end fw-bold">Totales:</td>
                                        <td class="text-end fw-bold" id="citas-total-valor">$0</td>
                                        <td class="text-end fw-bold" id="citas-total-comision">$0</td>
                                    </tr>
                                </tfoot>
                            </table>
                        </div>
                    </div>
                    <?php #endregion RESULTS TABLE ?>

            </div>
        </div>
        <?php #endregion RESULTS ?>

    </div>
    <!-- END #content -->
</div>
<!-- END #app -->

    <?php #region View Services Modal ?>
    <div class="modal fade" id="viewServicesModal" tabindex="-1" aria-labelledby="viewServicesModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="viewServicesModalLabel">Servicios de la Cita</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <!-- Loading indicator -->
                    <div id="services-modal-loading" class="text-center py-4">
                        <i class="fa fa-spinner fa-spin fa-3x text-primary mb-3"></i>
                        <h5 class="text-primary">Cargando servicios...</h5>
                    </div>

                    <!-- Services content -->
                    <div id="services-modal-content" style="display: none;">
                        <!-- Cita Header Information -->
                        <div class="panel panel-inverse mb-3">
                            <div class="panel-heading">
                                <h4 class="panel-title">Información de la Cita</h4>
                            </div>
                            <div class="panel-body">
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label class="form-label fw-bold text-primary">Puesto:</label>
                                            <div id="service-detail-puesto" class="form-control-plaintext text-white fs-6"></div>
                                        </div>
                                        <div class="mb-3">
                                            <label class="form-label fw-bold text-primary">Empleado:</label>
                                            <div id="service-detail-empleado" class="form-control-plaintext text-white fs-6"></div>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label class="form-label fw-bold text-primary">Método de Pago:</label>
                                            <div id="service-detail-metodo-pago" class="form-control-plaintext text-white fs-6"></div>
                                        </div>
                                        <div class="mb-3">
                                            <label class="form-label fw-bold text-primary">Estado:</label>
                                            <div id="service-detail-estado" class="form-control-plaintext text-white fs-6"></div>
                                        </div>
                                    </div>
                                </div>
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label class="form-label fw-bold text-primary">Fecha Inicio:</label>
                                            <div id="service-detail-fecha-inicio" class="form-control-plaintext text-white fs-6"></div>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label class="form-label fw-bold text-primary">Fecha Fin:</label>
                                            <div id="service-detail-fecha-fin" class="form-control-plaintext text-white fs-6"></div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Services Table -->
                        <div class="panel panel-inverse">
                            <div class="panel-heading">
                                <h4 class="panel-title">Servicios Realizados</h4>
                            </div>
                            <div class="panel-body">
                                <div class="table-responsive">
                                    <table class="table table-striped table-bordered">
                                        <thead class="table-dark">
                                            <tr>
                                                <th>Servicio</th>
                                                <th class="text-end">Valor</th>
                                            </tr>
                                        </thead>
                                        <tbody id="services-table-body">
                                            <!-- Services will be populated via JavaScript -->
                                        </tbody>
                                        <tfoot>
                                            <tr class="table-primary">
                                                <th class="text-end">Total:</th>
                                                <th class="text-end" id="services-total"></th>
                                            </tr>
                                        </tfoot>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cerrar</button>
                </div>
            </div>
        </div>
    </div>
    <?php #endregion View Services Modal ?>

<!-- Include required JS files -->
<?php require_once __ROOT__ . '/views/general/core_js.view.php'; ?>

<!-- Include Bootstrap Datepicker JS -->
<script src="<?php echo RUTA_ADM_ASSETS; ?>plugins/bootstrap-datepicker/dist/js/bootstrap-datepicker.js"></script>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Initialize datepickers
    $('.datepicker').datepicker({
        autoclose     : true,
        todayHighlight: true,
        format        : 'yyyy-mm-dd'
    });

    // Make calendar icons clickable
    document.getElementById('fecha_inicio_icon').addEventListener('click', function() {
        $('#fecha_inicio').datepicker('show');
    });

    document.getElementById('fecha_fin_icon').addEventListener('click', function() {
        $('#fecha_fin').datepicker('show');
    });

    // Form Elements
    const consultaForm          = document.getElementById('consulta-form');
    const noResults             = document.getElementById('no-results');
    const loading               = document.getElementById('loading');
    const resultsTableContainer = document.getElementById('results-table-container');
    const citasTableBody        = document.getElementById('citas-table-body');
    const clearFiltersBtn       = document.getElementById('clear-filters-btn');

    // Clear filters functionality
    clearFiltersBtn.addEventListener('click', function() {
        // Clear all filter fields
        document.getElementById('id_empleado').value  = '';
        document.getElementById('fecha_inicio').value = '';
        document.getElementById('fecha_fin').value    = '';

        // Clear results and show empty state
        noResults.innerHTML = `
            <i class="fa fa-search fa-3x text-muted mb-3"></i>
            <h5 class="text-muted">Utilice los filtros para consultar las citas por empleado</h5>
            <p class="text-muted">Seleccione un empleado y un rango de fechas para ver las citas completadas.</p>
        `;

        // Hide totals footer when clearing filters
        const citasTableFooter = document.getElementById('citas-table-footer');
        if (citasTableFooter) {
            citasTableFooter.style.display = 'none';
        }

        // Hide filter description
        const filtrosAplicados = document.getElementById('filtros-aplicados');
        if (filtrosAplicados) {
            filtrosAplicados.style.display = 'none';
        }

        // Hide export button when clearing filters
        const exportBtn = document.getElementById('export-excel-btn');
        if (exportBtn) {
            exportBtn.style.display = 'none';
        }

        noResults.style.display             = 'block';
        resultsTableContainer.style.display = 'none';
        loading.style.display               = 'none';
    });

    // Form submission
    consultaForm.addEventListener('submit', function(e) {
        e.preventDefault();

        // Get form data
        const formData = new FormData();
        formData.append('action', 'buscar_citas');
        formData.append('id_empleado', document.getElementById('id_empleado').value);
        formData.append('fecha_inicio', document.getElementById('fecha_inicio').value);
        formData.append('fecha_fin', document.getElementById('fecha_fin').value);

        // Validate required fields
        if (!document.getElementById('id_empleado').value) {
            showSweetAlertError('Error de Validación', 'Debe seleccionar un empleado.');
            return;
        }

        if (!document.getElementById('fecha_inicio').value || !document.getElementById('fecha_fin').value) {
            showSweetAlertError('Error de Validación', 'Las fechas de inicio y fin son obligatorias.');
            return;
        }

        // Show loading
        loading.style.display               = 'block';
        noResults.style.display             = 'none';
        resultsTableContainer.style.display = 'none';

        fetch('consulta-citas-empleado', {
            method: 'POST',
            body: formData
        })
        .then(response => response.json())
        .then(data => {
            loading.style.display = 'none';

            if (data.success) {
                if (data.citas.length > 0) {
                    displayResults(data.citas);
                } else {
                    showNoResults();
                }
            } else {
                showSweetAlertError('Error', data.message || 'Error al consultar las citas');
                showNoResults();
            }
        })
        .catch(error => {
            loading.style.display = 'none';
            showSweetAlertError('Error de Conexión', 'Error de conexión al consultar las citas');
            showNoResults();
            console.error('Error:', error);
        });
    });

    // Display results function
    function displayResults(citas) {
        citasTableBody.innerHTML = '';

        let totalGeneral = 0;
        let totalComision = 0;

        citas.forEach(cita => {
            const row = document.createElement('tr');

            // Estado badge
            let estadoHtml = '';
            if (cita.estado == 1) {
                estadoHtml = '<span class="badge bg-success">Terminada</span>';
            } else {
                estadoHtml = '<span class="badge bg-danger">Cancelada</span>';
            }

            // Calculate totals
            totalGeneral += parseFloat(cita.total_valor_servicios || 0);
            totalComision += parseFloat(cita.valor_comision_empleado || 0);

            // Format commission value
            const comisionFormateada = '$' + new Intl.NumberFormat('es-CO').format(parseFloat(cita.valor_comision_empleado || 0));

            row.innerHTML = `
                <td class="text-center align-middle">
                    <button class="btn btn-info btn-xs" onclick="verServiciosCita(${cita.id})" title="Ver servicios de la cita">
                        <i class="fa fa-eye"></i>
                    </button>
                </td>
                <td class="text-center align-middle">${cita.id}</td>
                <td class="align-middle">${cita.nombre_centro_costo || 'N/A'}</td>
                <td class="align-middle">${cita.descripcion_puesto || 'N/A'}</td>
                <td class="align-middle">${cita.turno_empleado_nombre || 'N/A'}</td>
                <td class="align-middle">${cita.nombre_metodo_pago || 'N/A'}</td>
                <td class="text-center align-middle">${cita.fecha_inicio_formateada}</td>
                <td class="text-center align-middle">${cita.fecha_fin_formateada}</td>
                <td class="text-center align-middle">${estadoHtml}</td>
                <td class="text-end align-middle">${cita.total_valor_formateado}</td>
                <td class="text-end align-middle">${comisionFormateada}</td>
            `;
            citasTableBody.appendChild(row);
        });

        // Show and update totals
        const citasTableFooter   = document.getElementById('citas-table-footer');
        const citasTotalValor    = document.getElementById('citas-total-valor');
        const citasTotalComision = document.getElementById('citas-total-comision');

        if (citas.length > 0) {
            // Format totals in Colombian currency format
            const totalFormateado = '$' + new Intl.NumberFormat('es-CO').format(totalGeneral);
            const comisionTotalFormateada = '$' + new Intl.NumberFormat('es-CO').format(totalComision);

            citasTotalValor.textContent = totalFormateado;
            citasTotalComision.textContent = comisionTotalFormateada;
            citasTableFooter.style.display = 'table-footer-group';
        } else {
            citasTableFooter.style.display = 'none';
        }

        // Update filter description
        updateFilterDescription();

        // Show export button and update hidden fields with current filter values
        updateExportForm();

        resultsTableContainer.style.display = 'block';
        noResults.style.display             = 'none';
    }

    // Update filter description function
    function updateFilterDescription() {
        const filtrosAplicados = document.getElementById('filtros-aplicados');
        const fechaInicio = document.getElementById('fecha_inicio').value;
        const fechaFin = document.getElementById('fecha_fin').value;
        const empleadoSelect = document.getElementById('id_empleado');
        const empleadoNombre = empleadoSelect.options[empleadoSelect.selectedIndex].text;

        if (fechaInicio && fechaFin && empleadoSelect.value) {
            filtrosAplicados.textContent = `Filtros aplicados: Fecha ${fechaInicio} a ${fechaFin}, Empleado: ${empleadoNombre}`;
            filtrosAplicados.style.display = 'block';
        } else {
            filtrosAplicados.style.display = 'none';
        }
    }

    // Show no results function
    function showNoResults() {
        noResults.innerHTML = `
            <i class="fa fa-exclamation-triangle fa-3x text-warning mb-3"></i>
            <h5 class="text-warning">No se encontraron resultados</h5>
            <p class="text-muted">No se encontraron citas completadas para los criterios de búsqueda seleccionados.</p>
        `;

        // Hide totals footer when no results
        const citasTableFooter = document.getElementById('citas-table-footer');
        if (citasTableFooter) {
            citasTableFooter.style.display = 'none';
        }

        // Hide filter description when no results
        const filtrosAplicados = document.getElementById('filtros-aplicados');
        if (filtrosAplicados) {
            filtrosAplicados.style.display = 'none';
        }

        // Hide export button when no results
        const exportBtn = document.getElementById('export-excel-btn');
        if (exportBtn) {
            exportBtn.style.display = 'none';
        }

        noResults.style.display             = 'block';
        resultsTableContainer.style.display = 'none';
    }

    // Global function for viewing cita services
    window.verServiciosCita = function(citaId) {
        const modal        = new bootstrap.Modal(document.getElementById('viewServicesModal'));
        const modalLoading = document.getElementById('services-modal-loading');
        const modalContent = document.getElementById('services-modal-content');

        // Show modal and loading state
        modal.show();
        modalLoading.style.display = 'block';
        modalContent.style.display = 'none';

        // Fetch cita services
        const formData = new FormData();
        formData.append('action', 'get_cita_services');
        formData.append('cita_id', citaId);

        fetch('consulta-citas-empleado', {
            method: 'POST',
            body: formData
        })
        .then(response => response.json())
        .then(data => {
            modalLoading.style.display = 'none';

            if (data.success) {
                populateCitaServices(data.cita, data.servicios, data.total_valor_formateado);
                modalContent.style.display = 'block';
            } else {
                showSweetAlertError('Error', data.message || 'Error al cargar los servicios de la cita');
                modal.hide();
            }
        })
        .catch(error => {
            modalLoading.style.display = 'none';
            showSweetAlertError('Error de Conexión', 'Error de conexión al cargar los servicios de la cita');
            modal.hide();
            console.error('Error:', error);
        });
    };

    // Populate cita services in modal
    function populateCitaServices(cita, servicios, totalFormateado) {
        // Populate cita details
        document.getElementById('service-detail-puesto').textContent = cita.descripcion_puesto;
        document.getElementById('service-detail-empleado').textContent = cita.nombre_empleado;
        document.getElementById('service-detail-metodo-pago').textContent = cita.nombre_metodo_pago;
        document.getElementById('service-detail-fecha-inicio').textContent = cita.fecha_inicio_formateada;
        document.getElementById('service-detail-fecha-fin').textContent = cita.fecha_fin_formateada;

        // Estado badge
        let estadoHtml = '';
        if (cita.estado == 1) {
            estadoHtml = '<span class="badge bg-success">Terminada</span>';
        } else {
            estadoHtml = '<span class="badge bg-danger">Cancelada</span>';
        }
        document.getElementById('service-detail-estado').innerHTML = estadoHtml;

        // Populate services table
        const servicesTableBody           = document.getElementById('services-table-body');
              servicesTableBody.innerHTML = '';

        servicios.forEach(servicio => {
            const row           = document.createElement('tr');
                  row.innerHTML = `
                <td>${servicio.descripcion}</td>
                <td class="text-end">${servicio.valor_formateado}</td>
            `;
            servicesTableBody.appendChild(row);
        });

        // Set total
        document.getElementById('services-total').textContent = totalFormateado;
    }

    // Function to update export form with current filter values
    function updateExportForm() {
        const exportBtn = document.getElementById('export-excel-btn');

        // Update hidden fields with current filter values
        document.getElementById('export-fecha-inicio').value = document.getElementById('fecha_inicio').value;
        document.getElementById('export-fecha-fin').value = document.getElementById('fecha_fin').value;
        document.getElementById('export-empleado').value = document.getElementById('id_empleado').value;

        // Show export button
        exportBtn.style.display = 'inline-block';
    }

    // Handle Excel Export Button
    const exportForm = document.getElementById('export-excel-form');
    const exportBtn = document.getElementById('export-excel-btn');

    exportForm.addEventListener('submit', function(e) {
        // Show loading state
        const originalText = exportBtn.innerHTML;
        exportBtn.innerHTML = '<i class="fa fa-spinner fa-spin fa-fw me-1"></i> Exportando...';
        exportBtn.disabled = true;

        // Reset button after a delay (since we're downloading a file)
        setTimeout(function() {
            exportBtn.innerHTML = originalText;
            exportBtn.disabled = false;
        }, 3000);
    });
});
</script>

</body>
</html>
