<?php
#region DOCS

/** @var MetodoPago[] $metodos_pagos */
/** @var string|null $success_text Success message to display */
/** @var string|null $success_display Whether to show success message ('show' or null) */
/** @var string|null $error_text Error message to display */

/** @var string|null $error_display Whether to show error message ('show' or null) */

use App\classes\MetodoPago;

#endregion DOCS
?>
<!DOCTYPE html>
<html lang="en" class="dark-mode">
<head>
	<meta charset="utf-8"/>
	<title><?php echo APP_NAME; ?> | Métodos de Pago</title>
	<meta content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no" name="viewport"/>
	<meta content="" name="description"/>
	<meta content="" name="author"/>
	
	<?php #region HEAD ?>
	<?php require_once __ROOT__ . '/views/general/head.view.php'; ?>
	<?php #endregion HEAD ?>
</head>
<body>
<!-- BEGIN #loader -->
<div id="loader" class="app-loader">
	<span class="spinner"></span>
</div>
<!-- END #loader -->

<!-- BEGIN #app -->
<div id="app" class="app app-header-fixed app-sidebar-fixed">
	<!-- #header -->
	<?php require_once __ROOT__ . '/views/general/header.view.php'; ?>
	
	<!-- #sidebar -->
	<?php require_once __ROOT__ . '/views/general/sidebar.view.php'; ?>
	
	<!-- BEGIN #content -->
	<div id="content" class="app-content">
		<?php #region PAGE HEADER ?>
		<div class="d-flex align-items-center mb-3">
			<div>
				<h4 class="mb-0">Gestión de Métodos de Pago</h4>
				<p class="mb-0 text-muted">Administra los métodos de pago del sistema</p>
			</div>
			<div class="ms-auto">
				<button type="button" class="btn btn-success" data-bs-toggle="modal" data-bs-target="#createMetodoPagoModal">
					<i class="fa fa-plus-circle fa-fw me-1"></i> Crear nuevo
				</button>
			</div>
		</div>
		
		<hr>
		<?php #endregion PAGE HEADER ?>
		
		<?php #region PANEL METODOS PAGOS ?>
		<div class="panel panel-inverse mt-3 no-border-radious">
			<div class="panel-heading no-border-radious">
				<h4 class="panel-title">
					Listado de Métodos de Pago Activos
				</h4>
			</div>
			<!-- BEGIN PANEL body -->
			<div class="p-1 table-nowrap" style="overflow: auto">
				<?php #region TABLE METODOS PAGOS ?>
				<table class="table table-hover table-sm">
					<thead>
					<tr>
						<th class="w-70px text-center">Acciones</th>
						<th>Descripción</th>
						<th class="text-center">Es Electrónico</th>
						<th class="text-center">Estado</th>
					</tr>
					</thead>
					<tbody class="fs-12px" id="metodo-pago-table-body">
					<?php foreach ($metodos_pagos as $metodo_pago): ?>
						<tr data-metodo-id="<?php echo $metodo_pago->getId(); ?>">
							<td class="text-center">
								<?php // Edit Button - Triggers Modal ?>
								<button type="button" class="btn btn-xs btn-primary me-1 btn-edit-metodo-pago"
								        title="Editar Método de Pago"
								        data-bs-toggle="modal"
								        data-bs-target="#editMetodoPagoModal"
								        data-metodo-id="<?php echo $metodo_pago->getId(); ?>"
								        data-descripcion="<?php echo htmlspecialchars($metodo_pago->getDescripcion() ?? ''); ?>"
								        data-electronico="<?php echo $metodo_pago->isMetodoElectronico() ? '1' : '0'; ?>">
									<i class="fa fa-edit"></i>
								</button>
								<?php // Deactivate Button - Only show if active ?>
								<?php if ($metodo_pago->isActivo()): ?>
									<button type="button" class="btn btn-xs btn-danger btn-desactivar-metodo-pago"
									        title="Desactivar"
									        data-metodo-id="<?php echo $metodo_pago->getId(); ?>"
									        data-descripcion="<?php echo htmlspecialchars($metodo_pago->getDescripcion() ?? ''); ?>">
										<i class="fa fa-trash-alt"></i>
									</button>
								<?php endif; ?>
							</td>
							<td class="metodo-pago-descripcion-cell"><?php echo htmlspecialchars($metodo_pago->getDescripcion()); ?></td>
							<td class="text-center metodo-pago-electronico-cell">
								<?php echo $metodo_pago->isMetodoElectronico() ? '<span class="badge bg-info">Sí</span>' : '<span class="badge bg-secondary">No</span>'; ?>
							</td>
							<td class="text-center">
								<?php if ($metodo_pago->isActivo()): ?>
									<span class="badge bg-success">Activo</span>
								<?php else: ?>
									<span class="badge bg-danger">Inactivo</span>
								<?php endif; ?>
							</td>
						</tr>
					<?php endforeach; ?>
					<?php if (empty($metodos_pagos)): ?>
						<tr>
							<td colspan="4" class="text-center">No hay métodos de pago para mostrar.</td>
						</tr>
					<?php endif; ?>
					
					</tbody>
				</table>
				<?php #endregion TABLE METODOS PAGOS ?>
			</div>
			<!-- END PANEL body -->
		</div>
		<?php #endregion PANEL METODOS PAGOS ?>
		
		<?php #region Create Metodo Pago Modal ?>
		<div class="modal fade" id="createMetodoPagoModal" tabindex="-1" aria-labelledby="createMetodoPagoModalLabel" aria-hidden="true">
			<div class="modal-dialog">
				<div class="modal-content">
					<form id="create-metodo-pago-form">
						<div class="modal-header">
							<h5 class="modal-title" id="createMetodoPagoModalLabel">Crear Método de Pago</h5>
							<button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
						</div>
						<div class="modal-body">
							<input type="hidden" name="action" value="crear">
							
							<div class="mb-3">
								<label for="create-metodo-pago-descripcion" class="form-label">Descripción:</label>
								<input type="text" class="form-control" id="create-metodo-pago-descripcion" name="descripcion" required>
							</div>

							<div class="form-check mb-3">
								<input class="form-check-input" type="checkbox" id="create-metodo-pago-electronico" name="es_metodo_electronico" value="1">
								<label class="form-check-label" for="create-metodo-pago-electronico">Es Método Electrónico</label>
								<small class="form-text text-muted d-block">
									<i class="fa fa-info-circle me-1"></i>Este campo sirve para clasificar los metodos de pago en los reportes financieros.
								</small>
							</div>
							
							<div id="create-metodo-pago-error" class="alert alert-danger mt-2" style="display: none;"></div>
						</div>
						<div class="modal-footer">
							<button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancelar</button>
							<button type="submit" class="btn btn-primary">Guardar</button>
						</div>
					</form>
				</div>
			</div>
		</div>
		<?php #endregion Create Metodo Pago Modal ?>
		
		<?php #region Edit Metodo Pago Modal ?>
		<div class="modal fade" id="editMetodoPagoModal" tabindex="-1" aria-labelledby="editMetodoPagoModalLabel" aria-hidden="true">
			<div class="modal-dialog">
				<div class="modal-content">
					<form id="edit-metodo-pago-form">
						<div class="modal-header">
							<h5 class="modal-title" id="editMetodoPagoModalLabel">Editar Método de Pago</h5>
							<button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
						</div>
						<div class="modal-body">
							<input type="hidden" name="metodoId" id="edit-metodo-pago-id">
							<input type="hidden" name="action" value="modificar">
							
							<div class="mb-3">
								<label for="edit-metodo-pago-descripcion" class="form-label">Descripción:</label>
								<input type="text" class="form-control" id="edit-metodo-pago-descripcion" name="descripcion" required>
							</div>

							<div class="form-check mb-3">
								<input class="form-check-input" type="checkbox" id="edit-metodo-pago-electronico" name="es_metodo_electronico" value="1">
								<label class="form-check-label" for="edit-metodo-pago-electronico">Es Método Electrónico</label>
								<small class="form-text text-muted d-block">
									<i class="fa fa-info-circle me-1"></i>Este campo sirve para clasificar los metodos de pago en los reportes financieros.
								</small>
							</div>
							
							<div id="edit-metodo-pago-error" class="alert alert-danger mt-2" style="display: none;"></div>
						</div>
						<div class="modal-footer">
							<button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancelar</button>
							<button type="submit" class="btn btn-primary">Guardar Cambios</button>
						</div>
					</form>
				</div>
			</div>
		</div>
		<?php #endregion Edit Metodo Pago Modal ?>
	
	</div>
	<!-- END #content -->
	
	<?php #region Hidden Form for Deactivation ?>
	<form id="deactivate-metodo-pago-form" method="POST" action="metodos-pagos" style="display: none;">
		<input type="hidden" name="action" value="desactivar">
		<input type="hidden" name="metodoId" id="deactivate-metodo-pago-id">
	</form>
	<?php #endregion Hidden Form ?>
	
	<!-- BEGIN scroll-top-btn -->
	<a href="#" class="btn btn-icon btn-circle btn-success btn-scroll-to-top" data-toggle="scroll-to-top"><i class="fa fa-angle-up"></i></a>
	<!-- END scroll-top-btn -->
</div>
<!-- END #app -->

<?php #region JS ?>
<?php require_once __ROOT__ . '/views/general/core_js.view.php'; ?>

<!-- ================== BEGIN PAGE LEVEL JS ================== -->
<script>
    document.addEventListener('DOMContentLoaded', function () {
        // Use event delegation on the table body
        const tableBody = document.getElementById('metodo-pago-table-body');
        
        // Create Metodo Pago Modal Elements
        const createMetodoPagoModalElement = document.getElementById('createMetodoPagoModal');
        const createMetodoPagoModal        = new bootstrap.Modal(createMetodoPagoModalElement);
        const createMetodoPagoForm         = document.getElementById('create-metodo-pago-form');
        const createMetodoPagoErrorDiv     = document.getElementById('create-metodo-pago-error');
        
        // Edit Metodo Pago Modal Elements
        const editMetodoPagoModalElement     = document.getElementById('editMetodoPagoModal');
        const editMetodoPagoModal            = new bootstrap.Modal(editMetodoPagoModalElement);
        const editMetodoPagoForm             = document.getElementById('edit-metodo-pago-form');
        const editMetodoPagoIdInput          = document.getElementById('edit-metodo-pago-id');
        const editMetodoPagoDescripcionInput = document.getElementById('edit-metodo-pago-descripcion');
        const editMetodoPagoErrorDiv         = document.getElementById('edit-metodo-pago-error');
        const editMetodoPagoElectronicoCheckbox = document.getElementById('edit-metodo-pago-electronico');
        
        if (tableBody) {
            tableBody.addEventListener('click', function (event) {
                const deactivateButton = event.target.closest('.btn-desactivar-metodo-pago');
                const editButton       = event.target.closest('.btn-edit-metodo-pago');
                
                // --- Handle Deactivate Click ---
				<?php #region JS AJAX - Handle Deactivate click ?>
                if (deactivateButton) {
                    event.preventDefault(); // Prevent default if it was an <a>
                    const metodoId    = deactivateButton.dataset.metodoId;
                    const descripcion = deactivateButton.dataset.descripcion;
                    
                    // Confirm before deactivating
                    swal({
                        title     : "¿Estás seguro?",
                        text      : `¿Deseas desactivar el método de pago "${descripcion}"?`,
                        icon      : "warning",
                        buttons   : {
                            cancel : {
                                text      : "Cancelar",
                                value     : null,
                                visible   : true,
                                className : "btn-secondary",
                                closeModal: true,
                            },
                            confirm: {
                                text      : "Sí, desactivar",
                                value     : true,
                                visible   : true,
                                className : "btn-danger",
                                closeModal: true
                            }
                        },
                        dangerMode: true,
                    })
                        .then((willDeactivate) => {
                            if (willDeactivate) {
                                // User confirmed, submit the form
                                const deactivateForm                                       = document.getElementById('deactivate-metodo-pago-form');
                                document.getElementById('deactivate-metodo-pago-id').value = metodoId;
                                deactivateForm.submit();
                            }
                        });
                }
				<?php #endregion JS AJAX - Handle Deactivate click ?>
                
                // --- Handle Edit Click ---
				<?php #region JS AJAX - Handle Edit click ?>
                if (editButton) {
                    event.preventDefault(); // Prevent default if it was an <a>
                    const metodoId           = editButton.dataset.metodoId;
                    const currentElectronico = editButton.dataset.electronico;
                    const currentDescripcion = editButton.dataset.descripcion;
                    
                    // Populate the modal form
                    editMetodoPagoIdInput.value          = metodoId;
                    editMetodoPagoDescripcionInput.value = currentDescripcion;
                    editMetodoPagoErrorDiv.style.display = 'none'; // Hide previous errors
                    editMetodoPagoElectronicoCheckbox.checked = currentElectronico === '1';
                    editMetodoPagoErrorDiv.textContent   = '';
                    
                    // The data-bs-toggle and data-bs-target attributes on the button handle showing the modal
                }
				<?php #endregion JS AJAX - Edit Metodo Pago ?>
            });
        }
        
        // --- Handle Create Form Submission (AJAX) ---
		<?php #region JS AJAX - Create Form Submission ?>
        if (createMetodoPagoForm) {
            createMetodoPagoForm.addEventListener('submit', function (event) {
                event.preventDefault(); // Stop default form submission
                createMetodoPagoErrorDiv.style.display = 'none'; // Hide error div initially
                
                const formData    = new FormData(createMetodoPagoForm);
                const descripcion = formData.get('descripcion').trim(); // Get trimmed description
                
                // Basic client-side validation
                if (!descripcion) {
                    createMetodoPagoErrorDiv.textContent   = 'La descripción no puede estar vacía.';
                    createMetodoPagoErrorDiv.style.display = 'block';
                    return; // Stop submission
                }
                
                // Disable submit button during request
                const submitButton    = createMetodoPagoForm.querySelector('button[type="submit"]');
                submitButton.disabled = true;
                
                fetch('metodos-pagos', {
                    method: 'POST',
                    body  : formData
                })
                    .then(response => {
                        if (!response.ok) {
                            return response.json().then(errData => {
                                throw new Error(errData.message || `Error ${response.status}: ${response.statusText}`);
                            }).catch(() => {
                                throw new Error(`Error ${response.status}: ${response.statusText}`);
                            });
                        }
                        return response.json();
                    })
                    .then(data => {
                        if (data.success) {
                            createMetodoPagoModal.hide(); // Close modal on success
                            
                            // Reset the form for next use
                            createMetodoPagoForm.reset();
                            
                            // Add the new row to the table
                            // const tableBody = document.getElementById('metodo-pago-table-body'); // Already defined
                            
                            // Remove "no data" row if it exists
                            const noDataRow = tableBody.querySelector('tr td[colspan="3"]');
                            if (noDataRow) {
                                noDataRow.closest('tr').remove();
                            }
                            
                            // Create new row HTML
                            const newRow = document.createElement('tr');
                            newRow.setAttribute('data-metodo-id', data.id);
                            
                            newRow.innerHTML = `
                                <td class="text-center">
                                    <button type="button" class="btn btn-xs btn-primary me-1 btn-edit-metodo-pago"
                                            title="Editar Método de Pago"
                                            data-bs-toggle="modal"
                                            data-bs-target="#editMetodoPagoModal"
                                            data-metodo-id="${data.id}"
                                            data-descripcion="${data.descripcion}"
                                            data-electronico="${data.es_metodo_electronico ? '1' : '0'}">
                                        <i class="fa fa-edit"></i>
                                    </button>
                                    <button type="button" class="btn btn-xs btn-danger btn-desactivar-metodo-pago"
                                            title="Desactivar"
                                            data-metodo-id="${data.id}"
                                            data-descripcion="${data.descripcion}">
                                        <i class="fa fa-trash-alt"></i>
                                    </button>
                                </td>
                                <td class="metodo-pago-descripcion-cell">${data.descripcion}</td>
                                <td class="text-center metodo-pago-electronico-cell">
                                    <span class="badge ${data.es_metodo_electronico ? 'bg-info' : 'bg-secondary'}">${data.es_metodo_electronico ? 'Sí' : 'No'}</span>
                                </td>
                                <td class="text-center"><span class="badge bg-success">Activo</span></td>
                            `;
                            
                            // Add the new row to the table
                            tableBody.appendChild(newRow);
                            
                            showSweetAlertSuccess('Éxito', 'Método de Pago creado correctamente.');
                        } else {
                            // Show error message inside the modal
                            createMetodoPagoErrorDiv.textContent   = data.message || 'Ocurrió un error al crear el Método de Pago.';
                            createMetodoPagoErrorDiv.style.display = 'block';
                        }
                    })
                    .catch(error => {
                        console.error('Error creating Metodo Pago:', error);
                        // Show error message inside the modal
                        createMetodoPagoErrorDiv.textContent   = 'Error de red o del servidor: ' + error.message;
                        createMetodoPagoErrorDiv.style.display = 'block';
                    })
                    .finally(() => {
                        // Re-enable submit button
                        submitButton.disabled = false;
                    });
            });
        }
		<?php #endregion JS AJAX - Create Form Submission ?>
        
        // --- Handle Edit Form Submission (AJAX) ---
		<?php #region JS AJAX - Edit Form Submission ?>
        if (editMetodoPagoForm) {
            editMetodoPagoForm.addEventListener('submit', function (event) {
                event.preventDefault(); // Stop default form submission
                editMetodoPagoErrorDiv.style.display = 'none'; // Hide error div initially
                
                const formData         = new FormData(editMetodoPagoForm);
                const metodoId         = formData.get('metodoId');
                const nuevaDescripcion = formData.get('descripcion').trim(); // Get trimmed description
                const esElectronico    = formData.get('es_metodo_electronico') ? 1 : 0;
                
                // Basic client-side validation
                if (!nuevaDescripcion) {
                    editMetodoPagoErrorDiv.textContent   = 'La descripción no puede estar vacía.';
                    editMetodoPagoErrorDiv.style.display = 'block';
                    return; // Stop submission
                }
                
                // Disable submit button during request
                const submitButton    = editMetodoPagoForm.querySelector('button[type="submit"]');
                submitButton.disabled = true;
                
                fetch('metodos-pagos', {
                    method: 'POST',
                    body  : formData
                })
                    .then(response => {
                        if (!response.ok) {
                            return response.json().then(errData => {
                                throw new Error(errData.message || `Error ${response.status}: ${response.statusText}`);
                            }).catch(() => {
                                throw new Error(`Error ${response.status}: ${response.statusText}`);
                            });
                        }
                        return response.json();
                    })
                    .then(data => {
                        if (data.success) {
                            editMetodoPagoModal.hide(); // Close modal on success
                            
                            // Update the row in the table directly
                            const tableRow = document.querySelector(`#metodo-pago-table-body tr[data-metodo-id="${metodoId}"]`);
                            if (tableRow) {
                                // Find the cell with the specific class within that row
                                const descripcionCell = tableRow.querySelector('.metodo-pago-descripcion-cell');
                                if (descripcionCell) {
                                    descripcionCell.textContent = nuevaDescripcion; // Update cell text
                                }

                                const electronicoCell = tableRow.querySelector('.metodo-pago-electronico-cell');
                                if (electronicoCell) {
                                    electronicoCell.innerHTML = `<span class="badge ${esElectronico ? 'bg-info' : 'bg-secondary'}">${esElectronico ? 'Sí' : 'No'}</span>`;
                                }
                                
                                // Also update the data attributes on the edit and delete buttons for next time
                                const editButton = tableRow.querySelector('.btn-edit-metodo-pago');
                                if (editButton) {
                                    editButton.dataset.descripcion = nuevaDescripcion;
                                    editButton.dataset.electronico = esElectronico ? '1' : '0';
                                }
                                
                                const deleteButton = tableRow.querySelector('.btn-desactivar-metodo-pago');
                                if (deleteButton) {
                                    deleteButton.dataset.descripcion = nuevaDescripcion;
                                }
                            }
                            
                            showSweetAlertSuccess('Éxito', 'Método de Pago actualizado correctamente.');
                        } else {
                            // Show error message inside the modal
                            editMetodoPagoErrorDiv.textContent   = data.message || 'Ocurrió un error al guardar.';
                            editMetodoPagoErrorDiv.style.display = 'block';
                        }
                    })
                    .catch(error => {
                        console.error('Error updating Metodo Pago:', error);
                        // Show error message inside the modal
                        editMetodoPagoErrorDiv.textContent   = 'Error de red o del servidor: ' + error.message;
                        editMetodoPagoErrorDiv.style.display = 'block';
                    })
                    .finally(() => {
                        // Re-enable submit button
                        submitButton.disabled = false;
                    });
            });
        }
		<?php #endregion JS AJAX - Edit Form Submission ?>
    });
</script>
<!-- ================== END PAGE LEVEL JS ================== -->

<?php #endregion JS ?>
</body>
</html>
