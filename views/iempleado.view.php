<?php #region docs
// No specific variables needed for this view

#endregion docs
?>
<!DOCTYPE html>
<html lang="en" class="dark-mode">
<head>
	<meta charset="utf-8"/>
	<title><?php echo APP_NAME; ?> | <PERSON>rear <PERSON>pleado</title>
	<meta content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no" name="viewport"/>
	<meta content="" name="description"/>
	<meta content="" name="author"/>

	<?php #region HEAD ?>
	<?php require_once __ROOT__ . '/views/general/head.view.php'; ?>
	<?php #endregion HEAD ?>
    <style>
        /* Style for invalid fields */
        .is-invalid {
            border-color: #dc3545 !important; /* Bootstrap's default danger color */
        }
        /* Style for validation messages */
        .invalid-feedback {
            display: none; /* Hide by default */
            width: 100%;
            margin-top: .25rem;
            font-size: .875em; /* 14px if base is 16px */
            color: #dc3545;
        }
        .is-invalid ~ .invalid-feedback {
            display: block; /* Show when input is invalid */
        }




    </style>
    <!-- Include Bootstrap Datepicker -->
    <link href="<?php echo RUTA ?>resources/adm_assets/plugins/bootstrap-datepicker/dist/css/bootstrap-datepicker.css" rel="stylesheet" />
</head>
<body>
<!-- BEGIN #loader -->
<div id="loader" class="app-loader">
	<span class="spinner"></span>
</div>
<!-- END #loader -->

<!-- BEGIN #app -->
<div id="app" class="app app-header-fixed app-sidebar-fixed">
	<!-- #header -->
	<?php require_once __ROOT__ . '/views/general/header.view.php'; ?>

	<!-- #sidebar -->
	<?php require_once __ROOT__ . '/views/general/sidebar.view.php'; ?>

	<!-- BEGIN #content -->
	<div id="content" class="app-content">
		<?php #region region PAGE HEADER ?>
        <div class="d-flex align-items-center mb-3">
            <div>
                <h4 class="mb-0">Crear Nuevo Empleado</h4>
                <p class="mb-0 text-muted">Ingrese los detalles del nuevo empleado. El empleado se creará como activo.</p>
            </div>
            <div class="ms-auto">
                <a href="lempleados" class="btn btn-secondary"><i class="fa fa-arrow-left fa-fw me-1"></i> Volver a la Lista</a>
            </div>
        </div>
		<hr>
		<?php #endregion PAGE HEADER ?>

		<?php #region region FORM ?>
		<form action="iempleado" method="POST" id="create-empleado-form" novalidate> <?php /* novalidate prevents default browser validation */ ?>
            <?php #region region PANEL EMPLEADO DETAILS ?>
            <div class="panel panel-inverse no-border-radious">
                <div class="panel-heading no-border-radious">
                    <h4 class="panel-title">Detalles del Empleado</h4>
                    <div class="panel-heading-btn">
                        <a href="javascript:;" class="btn btn-xs btn-icon btn-default" data-toggle="panel-expand"><i class="fa fa-expand"></i></a>
                        <a href="javascript:;" class="btn btn-xs btn-icon btn-warning" data-toggle="panel-collapse"><i class="fa fa-minus"></i></a>
                    </div>
                </div>
                <div class="panel-body">
                    <div class="mb-3">
                        <label for="nombre" class="form-label">Nombre <span class="text-danger">*</span></label>
                        <input type="text" class="form-control" id="nombre" name="nombre" value="<?php echo htmlspecialchars($nombre ?? ''); ?>" required>
                        <div class="invalid-feedback" id="nombre-error">El nombre es requerido.</div>
                    </div>

                    <div class="mb-3">
                        <label for="email" class="form-label">Correo <span class="text-danger">*</span></label>
                        <input type="email" class="form-control" id="email" name="email" value="<?php echo htmlspecialchars($email ?? ''); ?>" required>
                        <div class="invalid-feedback" id="email-error">El formato del email no es válido.</div>
                    </div>

                    <div class="mb-3">
                        <label for="telefono" class="form-label">Teléfono</label>
                        <input type="text" class="form-control" id="telefono" name="telefono" value="<?php echo htmlspecialchars($telefono ?? ''); ?>">
                    </div>

                    <div class="mb-3">
                        <label for="direccion" class="form-label">Dirección</label>
                        <input type="text" class="form-control" id="direccion" name="direccion" value="<?php echo htmlspecialchars($direccion ?? ''); ?>">
                    </div>

                    <div class="mb-3">
                        <label for="fecha_ingreso" class="form-label">Fecha Ingreso</label>
                        <div class="input-group">
                            <input type="text" class="form-control datepicker" id="fecha_ingreso" name="fecha_ingreso"
                                   value="<?php echo htmlspecialchars($fecha_ingreso ?? ''); ?>"
                                   data-date-format="yyyy-mm-dd">
                            <span class="input-group-text date-icon" id="fecha_ingreso_icon">
                                <i class="fa fa-calendar"></i>
                            </span>
                        </div>
                        <div class="invalid-feedback" id="fecha-ingreso-error">El formato de la fecha debe ser YYYY-MM-DD.</div>
                    </div>

                    <div class="mb-3">
                        <label for="porc_comision" class="form-label">% Comisión <span class="text-danger">*</span></label>
                        <div class="input-group">
                            <input type="number" class="form-control" id="porc_comision" name="porc_comision"
                                   value="<?php echo htmlspecialchars($porc_comision ?? '0.00'); ?>"
                                   step="0.01" min="0" max="100" placeholder="0.00" required>
                            <span class="input-group-text">%</span>
                        </div>
                        <div class="invalid-feedback" id="porc-comision-error">El porcentaje de comisión debe estar entre 0 y 100.</div>
                    </div>

                    <?php #region region USUARIO SECTION ?>
                    <hr class="my-4">
                    <h5 class="mb-3">Datos de Usuario</h5>
                    <p class="text-muted small mb-3">Se creará automáticamente un usuario con perfil "Barbero" asociado a este empleado.</p>

                    <div class="mb-3">
                        <label for="username" class="form-label">Nombre de Usuario <span class="text-danger">*</span></label>
                        <input type="text" class="form-control" id="username" name="username"
                               value="<?php echo htmlspecialchars($username ?? ''); ?>"
                               style="text-transform: uppercase;" required>
                        <div class="invalid-feedback" id="username-error">El nombre de usuario es requerido.</div>
                        <small class="text-muted">El nombre de usuario se convertirá automáticamente a mayúsculas.</small>
                    </div>

                    <div class="row mb-3">
                        <div class="col-md-6">
                            <label for="clave" class="form-label">Contraseña (Clave) <span class="text-danger">*</span></label>
                            <input type="password" class="form-control" id="clave" name="clave" required>
                            <div class="invalid-feedback" id="clave-error">La contraseña es requerida.</div>
                        </div>
                        <div class="col-md-6">
                            <label for="confirm_clave" class="form-label">Confirmar Contraseña <span class="text-danger">*</span></label>
                            <input type="password" class="form-control" id="confirm_clave" name="confirm_clave" required>
                            <div class="invalid-feedback" id="confirm_clave-error">Por favor confirme la contraseña.</div>
                        </div>
                    </div>
                    <?php #endregion USUARIO SECTION ?>
                </div>
                <div class="panel-footer text-end">
                    <button type="submit" class="btn btn-primary"><i class="fa fa-save fa-fw me-1"></i> Guardar Empleado</button>
                    <a href="lempleados" class="btn btn-secondary"><i class="fa fa-times fa-fw me-1"></i> Cancelar</a>
                </div>
            </div>
            <?php #endregion PANEL EMPLEADO DETAILS ?>
		</form>
		<?php #endregion FORM ?>
	</div>
	<!-- END #content -->

	<!-- BEGIN scroll-top-btn -->
	<a href="#" class="btn btn-icon btn-circle btn-success btn-scroll-to-top" data-toggle="scroll-to-top"><i class="fa fa-angle-up"></i></a>
	<!-- END scroll-top-btn -->
</div>
<!-- END #app -->

<?php #region region JS ?>
<?php require_once __ROOT__ . '/views/general/core_js.view.php'; ?>

<!-- Include Bootstrap Datepicker JS -->
<script src="<?php echo RUTA ?>resources/adm_assets/plugins/bootstrap-datepicker/dist/js/bootstrap-datepicker.js"></script>

<?php #region region CLIENT-SIDE VALIDATION & INPUT HANDLING SCRIPT ?>
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Initialize datepicker
    $('.datepicker').datepicker({
        autoclose: true,
        todayHighlight: true,
        format: 'yyyy-mm-dd'
    });

    // Make calendar icon clickable
    document.getElementById('fecha_ingreso_icon').addEventListener('click', function() {
        $('#fecha_ingreso').datepicker('show');
    });

    // --- Force Username to Uppercase ---
    usernameInput.addEventListener('input', function() {
        // Store the current cursor position
        const start = this.selectionStart;
        const end = this.selectionEnd;
        // Convert value to uppercase
        this.value = this.value.toUpperCase();
        // Restore the cursor position
        this.setSelectionRange(start, end);
    });

    const form = document.getElementById('create-empleado-form');
    const nombreInput = document.getElementById('nombre');
    const emailInput = document.getElementById('email');
    const fechaIngresoInput = document.getElementById('fecha_ingreso');
    const porcComisionInput = document.getElementById('porc_comision');
    const usernameInput = document.getElementById('username');
    const claveInput = document.getElementById('clave');
    const confirmClaveInput = document.getElementById('confirm_clave');

    // Error message elements
    const nombreError = document.getElementById('nombre-error');
    const emailError = document.getElementById('email-error');
    const fechaIngresoError = document.getElementById('fecha-ingreso-error');
    const porcComisionError = document.getElementById('porc-comision-error');
    const usernameError = document.getElementById('username-error');
    const claveError = document.getElementById('clave-error');
    const confirmClaveError = document.getElementById('confirm_clave-error');

    form.addEventListener('submit', function(event) {
        let isValid = true;

        // Reset previous validation states
        [nombreInput, emailInput, fechaIngresoInput, porcComisionInput, usernameInput, claveInput, confirmClaveInput].forEach(input => {
            input.classList.remove('is-invalid');
        });

        // Validate Nombre
        if (nombreInput.value.trim() === '') {
            isValid = false;
            nombreInput.classList.add('is-invalid');
            nombreError.textContent = 'El nombre es requerido.';
        }

        // Validate Email (required field)
        if (emailInput.value.trim() === '') {
            isValid = false;
            emailInput.classList.add('is-invalid');
            emailError.textContent = 'El correo es requerido.';
        } else if (!isValidEmail(emailInput.value)) {
            isValid = false;
            emailInput.classList.add('is-invalid');
            emailError.textContent = 'El formato del correo no es válido.';
        }

        // Validate Fecha Ingreso format if provided
        if (fechaIngresoInput.value.trim() !== '' && !isValidDate(fechaIngresoInput.value)) {
            isValid = false;
            fechaIngresoInput.classList.add('is-invalid');
            fechaIngresoError.textContent = 'El formato de la fecha debe ser YYYY-MM-DD.';
        }

        // Validate Porcentaje Comisión (now mandatory)
        if (porcComisionInput.value.trim() === '') {
            isValid = false;
            porcComisionInput.classList.add('is-invalid');
            porcComisionError.textContent = 'El porcentaje de comisión es requerido.';
        } else {
            const porcComision = parseFloat(porcComisionInput.value);
            if (isNaN(porcComision) || porcComision <= 0 || porcComision > 100) {
                isValid = false;
                porcComisionInput.classList.add('is-invalid');
                porcComisionError.textContent = 'El porcentaje de comisión debe ser mayor que 0 y no mayor que 100.';
            }
        }

        // Validate Username
        if (usernameInput.value.trim() === '') {
            isValid = false;
            usernameInput.classList.add('is-invalid');
            usernameError.textContent = 'El nombre de usuario es requerido.';
        }

        // Validate Clave (Password)
        if (claveInput.value === '') {
            isValid = false;
            claveInput.classList.add('is-invalid');
            claveError.textContent = 'La contraseña es requerida.';
        }

        // Validate Confirm Clave (Confirm Password)
        if (confirmClaveInput.value === '') {
            isValid = false;
            confirmClaveInput.classList.add('is-invalid');
            confirmClaveError.textContent = 'Por favor confirme la contraseña.';
        } else if (claveInput.value !== confirmClaveInput.value) {
            isValid = false;
            confirmClaveInput.classList.add('is-invalid');
            confirmClaveError.textContent = 'Las contraseñas no coinciden.';
        }

        // Prevent form submission if validation fails
        if (!isValid) {
            event.preventDefault();
            event.stopPropagation();
            // Optional: focus the first invalid field
            const firstInvalid = form.querySelector('.is-invalid');
            if (firstInvalid) {
                firstInvalid.focus();
            }
        }
    });

    // Helper function to validate email format
    function isValidEmail(email) {
        const re = /^(([^<>()\[\]\\.,;:\s@"]+(\.[^<>()\[\]\\.,;:\s@"]+)*)|(".+"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,}))$/;
        return re.test(email.toLowerCase());
    }

    // Helper function to validate date format (YYYY-MM-DD)
    function isValidDate(dateString) {
        // First check for the pattern
        if(!/^\d{4}-\d{2}-\d{2}$/.test(dateString)) return false;

        // Parse the date parts to integers
        const parts = dateString.split("-");
        const year = parseInt(parts[0], 10);
        const month = parseInt(parts[1], 10);
        const day = parseInt(parts[2], 10);

        // Check the ranges of month and day
        if(year < 1000 || year > 3000 || month == 0 || month > 12) return false;

        const monthLength = [31, 28, 31, 30, 31, 30, 31, 31, 30, 31, 30, 31];

        // Adjust for leap years
        if(year % 400 == 0 || (year % 100 != 0 && year % 4 == 0)) monthLength[1] = 29;

        // Check the range of the day
        return day > 0 && day <= monthLength[month - 1];
    }

    // Optional: Real-time validation feedback as user types (or on blur)
    [nombreInput, emailInput, fechaIngresoInput, porcComisionInput, usernameInput, claveInput, confirmClaveInput].forEach(input => {
        input.addEventListener('input', () => {
            // Simple check to remove invalid state when user starts typing
            if (input.classList.contains('is-invalid')) {
                input.classList.remove('is-invalid');
            }
        });
    });

    // --- Handle Success/Error Messages (from PHP redirects) ---
    <?php if (isset($success_display) && $success_display === 'show'): ?>
    showSweetAlertSuccess('¡Éxito!', '<?php echo addslashes($success_text ?? "Operación completada con éxito."); ?>');
    <?php endif; ?>

    <?php if (isset($error_display) && $error_display === 'show'): ?>
    showSweetAlertError('Error', '<?php echo addslashes($error_text ?? "Ha ocurrido un error."); ?>');
    <?php endif; ?>
});
</script>
<?php #endregion CLIENT-SIDE VALIDATION & INPUT HANDLING SCRIPT ?>
<?php #endregion JS ?>

</body>
</html>
