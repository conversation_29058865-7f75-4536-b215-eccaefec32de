<?php
#region region DOCS

/** @var Proveedor[] $proveedores */

use App\classes\Proveedor;

#endregion DOCS
?>
<!DOCTYPE html>
<html lang="en" class="dark-mode">
<head>
	<meta charset="utf-8"/>
	<title><?php echo APP_NAME; ?> | Proveedores</title>
	<meta content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no" name="viewport"/>
	<meta content="" name="description"/>
	<meta content="" name="author"/>

	<?php #region HEAD ?>
	<?php require_once __ROOT__ . '/views/general/head.view.php'; ?>
	<?php #endregion HEAD ?>
</head>
<body>
<!-- BEGIN #loader -->
<div id="loader" class="app-loader">
	<span class="spinner"></span>
</div>
<!-- END #loader -->

<!-- BEGIN #app -->
<div id="app" class="app app-header-fixed app-sidebar-fixed">
	<!-- #header -->
	<?php require_once __ROOT__ . '/views/general/header.view.php'; ?>

	<!-- #sidebar -->
	<?php require_once __ROOT__ . '/views/general/sidebar.view.php'; ?>

	<!-- BEGIN #content -->
	<div id="content" class="app-content">
		<?php #region region PAGE HEADER ?>
		<div class="d-flex align-items-center mb-3">
			<div>
				<h4 class="mb-0">Gestión de Proveedores</h4>
				<p class="mb-0 text-muted">Administra los proveedores del sistema</p>
			</div>
			<div class="ms-auto">
				<a href="iproveedor" class="btn btn-success"><i class="fa fa-plus-circle fa-fw me-1"></i> Crear nuevo</a>
			</div>
		</div>

		<hr>
		<?php #endregion PAGE HEADER ?>

		<?php #region region PANEL PROVEEDORES ?>
		<div class="panel panel-inverse mt-3 no-border-radious">
			<div class="panel-heading no-border-radious">
				<h4 class="panel-title">
					Listado de Proveedores Activos
				</h4>
			</div>
			<!-- BEGIN PANEL body -->
			<div class="p-1 table-nowrap" style="overflow: auto">
				<?php #region region TABLE PROVEEDORES ?>
				<table class="table table-hover table-sm">
					<thead>
					<tr>
						<th>Acciones</th>
						<th>Nombre</th>
						<th>NIT</th>
						<th>Teléfono</th>
						<th>Correo</th>
						<th>Dirección</th>
					</tr>
					</thead>
					<tbody class="fs-12px" id="proveedor-table-body">
					<?php foreach ($proveedores as $proveedor): ?>
						<tr data-proveedor-id="<?php echo $proveedor->getId(); ?>">
							<td>
								<?php // Edit Button - Redirects to eproveedor.php ?>
								<a href="eproveedor?id=<?php echo $proveedor->getId(); ?>"
								   class="btn btn-xs btn-primary me-1"
								   title="Editar Proveedor">
									<i class="fa fa-edit"></i>
								</a>
								<?php // Deactivate Button - Only show if active ?>
								<?php if ($proveedor->isActivo()): ?>
									<button type="button" class="btn btn-xs btn-danger btn-desactivar-proveedor"
									        title="Desactivar"
									        data-proveedor-id="<?php echo $proveedor->getId(); ?>"
									        data-nombre="<?php echo htmlspecialchars($proveedor->getNombre() ?? ''); ?>">
										<i class="fa fa-trash-alt"></i>
									</button>
								<?php endif; ?>
							</td>
							<td><?php echo htmlspecialchars($proveedor->getNombre()); ?></td>
							<td><?php echo htmlspecialchars($proveedor->getNit() ?? ''); ?></td>
							<td><?php echo htmlspecialchars($proveedor->getTelefono() ?? ''); ?></td>
							<td><?php echo htmlspecialchars($proveedor->getCorreo() ?? ''); ?></td>
							<td><?php echo htmlspecialchars($proveedor->getDireccion() ?? ''); ?></td>
						</tr>
					<?php endforeach; ?>
					</tbody>
				</table>
				<?php #endregion TABLE PROVEEDORES ?>
			</div>
			<!-- END PANEL body -->
		</div>
		<?php #endregion PANEL PROVEEDORES ?>
	</div>
	<!-- END #content -->

	<!-- BEGIN scroll-top-btn -->
	<a href="#" class="btn btn-icon btn-circle btn-success btn-scroll-to-top" data-toggle="scroll-to-top"><i class="fa fa-angle-up"></i></a>
	<!-- END scroll-top-btn -->
</div>
<!-- END #app -->

<?php #region region JS ?>
<?php require_once __ROOT__ . '/views/general/core_js.view.php'; ?>

<script>
    document.addEventListener('DOMContentLoaded', function () {
        // Use event delegation on the table body
        const tableBody = document.getElementById('proveedor-table-body');

        if (tableBody) {
            tableBody.addEventListener('click', function (event) {
                const deactivateButton = event.target.closest('.btn-desactivar-proveedor');

                if (deactivateButton) {
                    event.preventDefault();

                    const proveedorId = deactivateButton.getAttribute('data-proveedor-id');
                    const proveedorNombre = deactivateButton.getAttribute('data-nombre');

                    // Show confirmation dialog using SweetAlert
                    swal({
                        title: '¿Está seguro?',
                        text: `¿Desea desactivar el proveedor "${proveedorNombre}"?`,
                        icon: 'warning',
                        buttons: {
                            cancel: {
                                text: 'Cancelar',
                                value: null,
                                visible: true,
                                className: 'btn-secondary',
                                closeModal: true,
                            },
                            confirm: {
                                text: 'Sí, desactivar',
                                value: true,
                                visible: true,
                                className: 'btn-danger',
                                closeModal: true
                            }
                        },
                        dangerMode: true,
                    }).then((willDeactivate) => {
                        if (willDeactivate) {
                            // Create and submit form for deactivation
                            const form = document.createElement('form');
                            form.method = 'POST';
                            form.action = 'lproveedores';

                            const actionInput = document.createElement('input');
                            actionInput.type = 'hidden';
                            actionInput.name = 'action';
                            actionInput.value = 'desactivar';

                            const idInput = document.createElement('input');
                            idInput.type = 'hidden';
                            idInput.name = 'id';
                            idInput.value = proveedorId;

                            form.appendChild(actionInput);
                            form.appendChild(idInput);
                            document.body.appendChild(form);
                            form.submit();
                        }
                    });
                }
            });
        }
    });
</script>

<?php #endregion JS ?>

</body>
</html>
