<?php
#region DOCS

/** @var array $citas_para_cierre Lista de citas listas para cierre de caja */
/** @var array $ventas_para_cierre Lista de ventas listas para cierre de caja */
/** @var array $ordenes_compra_para_cierre Lista de órdenes de compra listas para cierre de caja */
/** @var object $centro_costo Centro de costo seleccionado */
/** @var string|null $success_text Success message to display */
/** @var string|null $success_display Whether to show success message ('show' or null) */
/** @var string|null $error_text Error message to display */
/** @var string|null $error_display Whether to show error message ('show' or null) */

#endregion DOCS
?>
<!DOCTYPE html>
<html lang="en" class="dark-mode">
<head>
	<meta charset="utf-8"/>
	<title><?php echo APP_NAME; ?> | Cierre de Caja</title>
	<meta content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no" name="viewport"/>
	<meta content="" name="description"/>
	<meta content="" name="author"/>

	<?php #region HEAD ?>
	<?php require_once __ROOT__ . '/views/general/head.view.php'; ?>
	<?php #endregion HEAD ?>
</head>
<body>
<!-- BEGIN #loader -->
<div id="loader" class="app-loader">
	<span class="spinner"></span>
</div>
<!-- END #loader -->

<!-- BEGIN #app -->
<div id="app" class="app app-header-fixed app-sidebar-fixed">
	<!-- #header -->
	<?php require_once __ROOT__ . '/views/general/header.view.php'; ?>

	<!-- #sidebar -->
	<?php require_once __ROOT__ . '/views/general/sidebar.view.php'; ?>

	<!-- BEGIN #content -->
	<div id="content" class="app-content">
        <!-- BEGIN container -->
        <div class="container">
            <!-- BEGIN row -->
            <div class="row justify-content-center">
                <!-- BEGIN col-12 -->
                <div class="col-12">

            <?php #region PAGE HEADER ?>
            <div class="d-flex align-items-center mb-3">
                <div>
                    <h4 class="mb-0">Cierre de Caja</h4>
                    <p class="mb-0 text-muted">
                        Centro de Costo: <strong><?php echo htmlspecialchars($centro_costo->getNombre() ?? 'No seleccionado'); ?></strong>
                    </p>
                </div>
                <div class="ms-auto">
                    <a href="dashboard" class="btn btn-secondary">
                        <i class="fa fa-arrow-left fa-fw me-1"></i> Volver al Dashboard
                    </a>
                </div>
            </div>

            <hr>
            <?php #endregion PAGE HEADER ?>

            <?php #region INFO NOTE ?>
            <div class="alert alert-info">
                <i class="fa fa-info-circle fa-fw me-2"></i>
                <strong>Nota:</strong> Todas las transacciones que se muestran serán incluidas en este cierre.
                Luego de terminar la operación, estos documentos no podrán ser editados para mantener la consistencia en el sistema.
            </div>
            <?php #endregion INFO NOTE ?>

            <?php #region ERROR/SUCCESS MESSAGES ?>
            <?php if (isset($error_display) && $error_display === 'show'): ?>
                <div class="alert alert-danger alert-dismissible fade show" role="alert">
                    <i class="fa fa-exclamation-triangle me-2"></i>
                    <?php echo htmlspecialchars($error_text); ?>
                    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                </div>
            <?php endif; ?>

            <?php if (isset($success_display) && $success_display === 'show'): ?>
                <div class="alert alert-success alert-dismissible fade show" role="alert">
                    <i class="fa fa-check-circle me-2"></i>
                    <?php echo htmlspecialchars($success_text); ?>
                    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                </div>
            <?php endif; ?>
            <?php #endregion ERROR/SUCCESS MESSAGES ?>

            <?php if (!isset($error_display) || $error_display !== 'show'): ?>
            <!-- BEGIN citas-panel -->
            <div class="panel panel-inverse">
                <div class="panel-heading">
                    <h4 class="panel-title">
                        Citas Finalizadas Listas para Cierre
                        <span class="badge bg-primary ms-2"><?php echo count($citas_para_cierre); ?></span>
                    </h4>
                </div>
                <div class="panel-body p-0">
                    <?php if (empty($citas_para_cierre)): ?>
                        <!-- No results message -->
                        <div class="text-center p-4">
                            <i class="fa fa-check-circle fa-3x text-success mb-3"></i>
                            <h5 class="text-muted">No hay citas pendientes para cierre</h5>
                            <p class="text-muted">Todas las citas del centro de costo han sido procesadas o no hay citas finalizadas.</p>
                        </div>
                    <?php else: ?>
                        <!-- Results table -->
                        <div style="overflow-x: auto;">
                            <table class="table table-hover table-sm mb-0" id="citas-table">
                                <thead class="table-dark">
                                    <tr>
                                        <th class="text-center" style="width: 100px;">Acciones</th>
                                        <th style="width: 60px;">#</th>
                                        <th style="width: 140px;">Fecha Inicio</th>
                                        <th style="width: 140px;">Fecha Fin</th>
                                        <th>Empleado</th>
                                        <th>Puesto</th>
                                        <th>Método de Pago</th>
                                        <th class="text-end" style="width: 120px;">Total Servicios</th>
                                        <th class="text-end" style="width: 120px;">Comisión</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php 
                                    $total_general_servicios = 0;
                                    $total_general_comisiones = 0;
                                    foreach ($citas_para_cierre as $cita): 
                                        $total_servicios = $cita->getTotal_valor_servicios() ?? 0;
                                        $comision = $cita->getValor_comision_empleado() ?? 0;
                                        $total_general_servicios += $total_servicios;
                                        $total_general_comisiones += $comision;
                                    ?>
                                        <tr>
                                            <td class="text-center">
                                                <button class="btn btn-info btn-xs" onclick="verDetallesCita(<?php echo $cita->getId(); ?>)" title="Ver detalles">
                                                    <i class="fa fa-eye"></i>
                                                </button>
                                            </td>
                                            <td><?php echo $cita->getId(); ?></td>
                                            <td><?php echo date('Y-m-d H:i', strtotime($cita->getFecha_inicio())); ?></td>
                                            <td><?php echo date('Y-m-d H:i', strtotime($cita->getFecha_fin())); ?></td>
                                            <td><?php echo htmlspecialchars($cita->getNombre_empleado() ?? 'N/A'); ?></td>
                                            <td><?php echo htmlspecialchars($cita->getDescripcion_puesto() ?? 'N/A'); ?></td>
                                            <td><?php echo htmlspecialchars($cita->getNombre_metodo_pago() ?? 'N/A'); ?></td>
                                            <td class="text-end"><?php echo format_currency_consigno($total_servicios); ?></td>
                                            <td class="text-end"><?php echo format_currency_consigno($comision); ?></td>
                                        </tr>
                                    <?php endforeach; ?>
                                </tbody>
                                <tfoot>
                                    <tr class="table-info">
                                        <th colspan="7" class="text-end fw-bold">Totales:</th>
                                        <th class="text-end fw-bold"><?php echo format_currency_consigno($total_general_servicios); ?></th>
                                        <th class="text-end fw-bold"><?php echo format_currency_consigno($total_general_comisiones); ?></th>
                                    </tr>
                                </tfoot>
                            </table>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
            <!-- END citas-panel -->

            <!-- BEGIN ventas-panel -->
            <div class="panel panel-inverse mt-4">
                <div class="panel-heading">
                    <h4 class="panel-title">
                        Ventas Listas para Cierre
                        <span class="badge bg-primary ms-2"><?php echo count($ventas_para_cierre ?? []); ?></span>
                    </h4>
                </div>
                <div class="panel-body p-0">
                    <?php if (empty($ventas_para_cierre ?? [])): ?>
                        <!-- No results message -->
                        <div class="text-center p-4">
                            <i class="fa fa-check-circle fa-3x text-success mb-3"></i>
                            <h5 class="text-muted">No hay ventas pendientes para cierre</h5>
                            <p class="text-muted">Todas las ventas del centro de costo han sido procesadas o no hay ventas activas.</p>
                        </div>
                    <?php else: ?>
                        <!-- Results table -->
                        <div style="overflow-x: auto;">
                            <table class="table table-hover table-sm mb-0" id="ventas-table">
                                <thead class="table-dark">
                                    <tr>
                                        <th class="text-center" style="width: 100px;">Acciones</th>
                                        <th style="width: 60px;">#</th>
                                        <th style="width: 140px;">Fecha</th>
                                        <th>Cliente</th>
                                        <th>Método de Pago</th>
                                        <th class="text-end" style="width: 120px;">Valor Total</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php
                                    $total_general_ventas = 0;
                                    foreach (($ventas_para_cierre ?? []) as $venta):
                                        $valor_total = $venta->getValor_total() ?? 0;
                                        $total_general_ventas += $valor_total;
                                    ?>
                                        <tr>
                                            <td class="text-center">
                                                <button class="btn btn-info btn-xs" onclick="verDetallesVenta(<?php echo $venta->getId(); ?>)" title="Ver detalles">
                                                    <i class="fa fa-eye"></i>
                                                </button>
                                            </td>
                                            <td><?php echo $venta->getId(); ?></td>
                                            <td><?php echo date('Y-m-d H:i', strtotime($venta->getFecha())); ?></td>
                                            <td><?php echo htmlspecialchars($venta->getCliente_nombre() ?? 'Sin cliente'); ?></td>
                                            <td><?php echo htmlspecialchars($venta->getMetodo_pago_nombre() ?? 'N/A'); ?></td>
                                            <td class="text-end"><?php echo format_currency_consigno($valor_total); ?></td>
                                        </tr>
                                    <?php endforeach; ?>
                                </tbody>
                                <tfoot>
                                    <tr class="table-success">
                                        <th colspan="5" class="text-end fw-bold">Total Ventas:</th>
                                        <th class="text-end fw-bold"><?php echo format_currency_consigno($total_general_ventas); ?></th>
                                    </tr>
                                </tfoot>
                            </table>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
            <!-- END ventas-panel -->

            <!-- BEGIN ordenes-compra-panel -->
            <div class="panel panel-inverse mt-4">
                <div class="panel-heading">
                    <h4 class="panel-title">
                        Órdenes de Compra Listas para Cierre
                        <span class="badge bg-primary ms-2"><?php echo count($ordenes_compra_para_cierre ?? []); ?></span>
                    </h4>
                </div>
                <div class="panel-body p-0">
                    <?php if (empty($ordenes_compra_para_cierre ?? [])): ?>
                        <!-- No results message -->
                        <div class="text-center p-4">
                            <i class="fa fa-check-circle fa-3x text-success mb-3"></i>
                            <h5 class="text-muted">No hay órdenes de compra pendientes para cierre</h5>
                            <p class="text-muted">Todas las órdenes de compra del centro de costo han sido procesadas o no hay órdenes activas.</p>
                        </div>
                    <?php else: ?>
                        <!-- Results table -->
                        <div style="overflow-x: auto;">
                            <table class="table table-hover table-sm mb-0" id="ordenes-compra-table">
                                <thead class="table-dark">
                                    <tr>
                                        <th class="text-center" style="width: 100px;">Acciones</th>
                                        <th style="width: 60px;">#</th>
                                        <th style="width: 140px;">Fecha</th>
                                        <th>Proveedor</th>
                                        <th style="width: 120px;">Núm. Referencia</th>
                                        <th class="text-end" style="width: 120px;">Valor Total</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php
                                    $total_general_ordenes_compra = 0;
                                    foreach (($ordenes_compra_para_cierre ?? []) as $orden):
                                        $valor_total = $orden->getValor_total() ?? 0;
                                        $total_general_ordenes_compra += $valor_total;
                                    ?>
                                        <tr>
                                            <td class="text-center">
                                                <button class="btn btn-info btn-xs" onclick="verDetallesOrdenCompra(<?php echo $orden->getId(); ?>)" title="Ver detalles">
                                                    <i class="fa fa-eye"></i>
                                                </button>
                                            </td>
                                            <td><?php echo $orden->getId(); ?></td>
                                            <td><?php echo date('Y-m-d', strtotime($orden->getFecha())); ?></td>
                                            <td><?php echo htmlspecialchars($orden->getProveedor_nombre() ?? 'Sin proveedor'); ?></td>
                                            <td><?php echo htmlspecialchars($orden->getN_referencia_proveedor() ?? ''); ?></td>
                                            <td class="text-end"><?php echo format_currency_consigno($valor_total); ?></td>
                                        </tr>
                                    <?php endforeach; ?>
                                </tbody>
                                <tfoot>
                                    <tr class="table-success">
                                        <th colspan="5" class="text-end fw-bold">Total Órdenes de Compra:</th>
                                        <th class="text-end fw-bold"><?php echo format_currency_consigno($total_general_ordenes_compra); ?></th>
                                    </tr>
                                </tfoot>
                            </table>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
            <!-- END ordenes-compra-panel -->

            <!-- BEGIN gastos-operativos-panel -->
            <div class="panel panel-inverse mt-4">
                <div class="panel-heading">
                    <h4 class="panel-title">
                        Gastos Operativos Listos para Cierre
                        <span class="badge bg-primary ms-2"><?php echo count($gastos_operativos_para_cierre ?? []); ?></span>
                    </h4>
                </div>
                <div class="panel-body p-0">
                    <?php if (empty($gastos_operativos_para_cierre ?? [])): ?>
                        <!-- No results message -->
                        <div class="text-center p-4">
                            <i class="fa fa-check-circle fa-3x text-success mb-3"></i>
                            <h5 class="text-muted">No hay gastos operativos pendientes para cierre</h5>
                            <p class="text-muted">Todos los gastos operativos han sido procesados o no hay gastos activos.</p>
                        </div>
                    <?php else: ?>
                        <!-- Results table -->
                        <div style="overflow-x: auto;">
                            <table class="table table-hover table-sm mb-0" id="gastos-operativos-table">
                                <thead class="table-dark">
                                    <tr>
                                        <th style="width: 60px;">#</th>
                                        <th style="width: 140px;">Fecha</th>
                                        <th>Descripción</th>
                                        <th class="text-end" style="width: 120px;">Valor</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php
                                    $total_general_gastos_operativos = 0;
                                    foreach (($gastos_operativos_para_cierre ?? []) as $gasto):
                                        $valor = $gasto->getValor() ?? 0;
                                        $total_general_gastos_operativos += $valor;
                                    ?>
                                        <tr>
                                            <td><?php echo $gasto->getId(); ?></td>
                                            <td><?php echo date('Y-m-d', strtotime($gasto->getFecha())); ?></td>
                                            <td><?php echo htmlspecialchars($gasto->getDescripcion() ?? 'N/A'); ?></td>
                                            <td class="text-end"><?php echo format_currency_consigno($valor); ?></td>
                                        </tr>
                                    <?php endforeach; ?>
                                </tbody>
                                <tfoot>
                                    <tr class="table-success">
                                        <th colspan="3" class="text-end fw-bold">Total Gastos Operativos:</th>
                                        <th class="text-end fw-bold"><?php echo format_currency_consigno($total_general_gastos_operativos); ?></th>
                                    </tr>
                                </tfoot>
                            </table>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
            <!-- END gastos-operativos-panel -->

            <!-- BEGIN generar-cierre-section -->
            <?php if (!empty($citas_para_cierre) || !empty($ventas_para_cierre) || !empty($ordenes_compra_para_cierre) || !empty($gastos_operativos_para_cierre)): ?>
            <div class="text-center mt-4">
                <button type="button" class="btn btn-success btn-lg" id="btn-generar-cierre">
                    <i class="fa fa-cash-register me-2"></i>
                    Generar Cierre
                </button>
                <p class="text-muted mt-2">
                    <small>Al generar el cierre, todos los documentos mostrados serán incluidos y no podrán ser editados posteriormente.</small>
                </p>
            </div>
            <?php else: ?>
            <div class="text-center mt-4">
                <div class="alert alert-info">
                    <i class="fa fa-info-circle me-2"></i>
                    No hay documentos disponibles para generar un cierre.
                </div>
            </div>
            <?php endif; ?>
            <!-- END generar-cierre-section -->
            <?php endif; ?>

        </div>
        <!-- END col-12 -->
    </div>
    <!-- END row -->
</div>
<!-- END container -->
</div>
<!-- END #content -->
</div>
<!-- END #app -->

<!-- Modal for Cita Details -->
<div class="modal fade" id="citaDetallesModal" tabindex="-1" aria-labelledby="citaDetallesModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-xl">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="citaDetallesModalLabel">Detalles de la Cita</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <!-- Loading indicator -->
                <div id="cita-modal-loading" class="text-center py-4">
                    <i class="fa fa-spinner fa-spin fa-3x text-primary mb-3"></i>
                    <h5 class="text-primary">Cargando detalles...</h5>
                </div>

                <!-- Cita details content -->
                <div id="cita-modal-content" style="display: none;">
                    <!-- Cita Header Information -->
                    <div class="panel panel-inverse mb-3">
                        <div class="panel-heading">
                            <h4 class="panel-title">Información General</h4>
                        </div>
                        <div class="panel-body">
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label class="form-label fw-bold text-primary">Número de Cita:</label>
                                        <div id="detail-numero-cita" class="form-control-plaintext text-white fs-5"></div>
                                    </div>
                                    <div class="mb-3">
                                        <label class="form-label fw-bold text-primary">Fecha Inicio:</label>
                                        <div id="detail-fecha-inicio" class="form-control-plaintext text-white fs-5"></div>
                                    </div>
                                    <div class="mb-3">
                                        <label class="form-label fw-bold text-primary">Fecha Fin:</label>
                                        <div id="detail-fecha-fin" class="form-control-plaintext text-white fs-5"></div>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label class="form-label fw-bold text-primary">Empleado:</label>
                                        <div id="detail-empleado" class="form-control-plaintext text-white fs-5"></div>
                                    </div>
                                    <div class="mb-3">
                                        <label class="form-label fw-bold text-primary">Puesto:</label>
                                        <div id="detail-puesto" class="form-control-plaintext text-white fs-5"></div>
                                    </div>
                                    <div class="mb-3">
                                        <label class="form-label fw-bold text-primary">Método de Pago:</label>
                                        <div id="detail-metodo-pago" class="form-control-plaintext text-white fs-5"></div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Servicios Table -->
                    <div class="panel panel-inverse">
                        <div class="panel-heading">
                            <h4 class="panel-title">Servicios Realizados</h4>
                        </div>
                        <div class="panel-body">
                            <div class="table-responsive">
                                <table class="table table-hover table-sm mb-0" id="cita-servicios-table">
                                    <thead class="table-dark">
                                        <tr>
                                            <th>Servicio</th>
                                            <th class="text-end" style="width: 120px;">Valor</th>
                                        </tr>
                                    </thead>
                                    <tbody id="cita-servicios-table-body">
                                        <!-- Services will be populated via AJAX -->
                                    </tbody>
                                    <tfoot>
                                        <tr class="table-info">
                                            <th class="text-end fw-bold">Total Servicios:</th>
                                            <th class="text-end fw-bold" id="detail-total-servicios"></th>
                                        </tr>
                                        <tr class="table-success">
                                            <th class="text-end fw-bold">Comisión Empleado:</th>
                                            <th class="text-end fw-bold" id="detail-comision-empleado"></th>
                                        </tr>
                                    </tfoot>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>

                <div id="cita-detalles-error" style="display: none;" class="alert alert-danger">
                    <i class="fa fa-exclamation-triangle"></i>
                    <span id="cita-detalles-error-message">Error al cargar los detalles</span>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cerrar</button>
            </div>
        </div>
    </div>
</div>

<!-- Modal for Venta Details -->
<div class="modal fade" id="ventaDetallesModal" tabindex="-1" aria-labelledby="ventaDetallesModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-xl">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="ventaDetallesModalLabel">Detalles de la Venta</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <!-- Loading indicator -->
                <div id="venta-modal-loading" class="text-center py-4">
                    <i class="fa fa-spinner fa-spin fa-3x text-primary mb-3"></i>
                    <h5 class="text-primary">Cargando detalles...</h5>
                </div>

                <!-- Venta details content -->
                <div id="venta-modal-content" style="display: none;">
                    <!-- Venta Header Information -->
                    <div class="panel panel-inverse mb-3">
                        <div class="panel-heading">
                            <h4 class="panel-title">Información General</h4>
                        </div>
                        <div class="panel-body">
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label class="form-label fw-bold text-primary">Número de Venta:</label>
                                        <div id="detail-numero-venta" class="form-control-plaintext text-white fs-5"></div>
                                    </div>
                                    <div class="mb-3">
                                        <label class="form-label fw-bold text-primary">Fecha:</label>
                                        <div id="detail-fecha-venta" class="form-control-plaintext text-white fs-5"></div>
                                    </div>
                                    <div class="mb-3">
                                        <label class="form-label fw-bold text-primary">Cliente:</label>
                                        <div id="detail-cliente-venta" class="form-control-plaintext text-white fs-5"></div>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label class="form-label fw-bold text-primary">Centro de Costo:</label>
                                        <div id="detail-centro-costo-venta" class="form-control-plaintext text-white fs-5"></div>
                                    </div>
                                    <div class="mb-3">
                                        <label class="form-label fw-bold text-primary">Método de Pago:</label>
                                        <div id="detail-metodo-pago-venta" class="form-control-plaintext text-white fs-5"></div>
                                    </div>
                                    <div class="mb-3">
                                        <label class="form-label fw-bold text-primary">Estado:</label>
                                        <div id="detail-estado-venta" class="form-control-plaintext text-white fs-5"></div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Productos Table -->
                    <div class="panel panel-inverse">
                        <div class="panel-heading">
                            <h4 class="panel-title">Productos Vendidos</h4>
                        </div>
                        <div class="panel-body">
                            <div class="table-responsive">
                                <table class="table table-hover table-sm mb-0" id="venta-detalles-table">
                                    <thead class="table-dark">
                                        <tr>
                                            <th>Producto</th>
                                            <th class="text-center" style="width: 100px;">Cantidad</th>
                                            <th class="text-end" style="width: 120px;">Valor Unit.</th>
                                            <th class="text-end" style="width: 120px;">Valor Total</th>
                                        </tr>
                                    </thead>
                                    <tbody id="venta-detalles-table-body">
                                        <!-- Details will be populated via AJAX -->
                                    </tbody>
                                    <tfoot>
                                        <tr class="table-success">
                                            <th colspan="3" class="text-end fw-bold">Total Venta:</th>
                                            <th class="text-end fw-bold" id="detail-total-venta"></th>
                                        </tr>
                                    </tfoot>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>

                <div id="venta-detalles-error" style="display: none;" class="alert alert-danger">
                    <i class="fa fa-exclamation-triangle"></i>
                    <span id="venta-detalles-error-message">Error al cargar los detalles</span>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cerrar</button>
            </div>
        </div>
    </div>
</div>

<!-- Modal for Orden Compra Details -->
<div class="modal fade" id="ordenCompraDetallesModal" tabindex="-1" aria-labelledby="ordenCompraDetallesModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-xl">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="ordenCompraDetallesModalLabel">Detalles de la Orden de Compra</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <!-- Loading indicator -->
                <div id="orden-compra-modal-loading" class="text-center py-4">
                    <i class="fa fa-spinner fa-spin fa-3x text-primary mb-3"></i>
                    <h5 class="text-primary">Cargando detalles...</h5>
                </div>

                <!-- Orden Compra details content -->
                <div id="orden-compra-modal-content" style="display: none;">
                    <!-- Orden Compra Header Information -->
                    <div class="panel panel-inverse mb-3">
                        <div class="panel-heading">
                            <h4 class="panel-title">Información General</h4>
                        </div>
                        <div class="panel-body">
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label class="form-label fw-bold text-primary">Número de Orden:</label>
                                        <div id="detail-numero-orden-compra" class="form-control-plaintext text-white fs-5"></div>
                                    </div>
                                    <div class="mb-3">
                                        <label class="form-label fw-bold text-primary">Fecha:</label>
                                        <div id="detail-fecha-orden-compra" class="form-control-plaintext text-white fs-5"></div>
                                    </div>
                                    <div class="mb-3">
                                        <label class="form-label fw-bold text-primary">Proveedor:</label>
                                        <div id="detail-proveedor-orden-compra" class="form-control-plaintext text-white fs-5"></div>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label class="form-label fw-bold text-primary">Centro de Costo:</label>
                                        <div id="detail-centro-costo-orden-compra" class="form-control-plaintext text-white fs-5"></div>
                                    </div>
                                    <div class="mb-3">
                                        <label class="form-label fw-bold text-primary">Usuario:</label>
                                        <div id="detail-usuario-orden-compra" class="form-control-plaintext text-white fs-5"></div>
                                    </div>
                                    <div class="mb-3">
                                        <label class="form-label fw-bold text-primary">Núm. Referencia:</label>
                                        <div id="detail-numero-referencia-orden-compra" class="form-control-plaintext text-white fs-5"></div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Productos Table -->
                    <div class="panel panel-inverse">
                        <div class="panel-heading">
                            <h4 class="panel-title">Detalle de Productos</h4>
                        </div>
                        <div class="panel-body">
                            <div class="table-responsive">
                                <table class="table table-hover table-sm mb-0" id="orden-compra-detalles-table">
                                    <thead class="table-dark">
                                        <tr>
                                            <th>Producto</th>
                                            <th class="text-center" style="width: 100px;">Cantidad</th>
                                            <th class="text-end" style="width: 120px;">Valor Unit.</th>
                                            <th class="text-end" style="width: 120px;">Valor Total</th>
                                        </tr>
                                    </thead>
                                    <tbody id="orden-compra-detalles-table-body">
                                        <!-- Details will be populated via AJAX -->
                                    </tbody>
                                    <tfoot>
                                        <tr class="table-success">
                                            <th colspan="3" class="text-end fw-bold">Total Orden:</th>
                                            <th class="text-end fw-bold" id="detail-total-orden-compra"></th>
                                        </tr>
                                    </tfoot>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>

                <div id="orden-compra-detalles-error" style="display: none;" class="alert alert-danger">
                    <i class="fa fa-exclamation-triangle"></i>
                    <span id="orden-compra-detalles-error-message">Error al cargar los detalles</span>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cerrar</button>
            </div>
        </div>
    </div>
</div>

<!-- Modal for Cierre Confirmation -->
<div class="modal fade" id="confirmarCierreModal" tabindex="-1" aria-labelledby="confirmarCierreModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="confirmarCierreModalLabel">
                    <i class="fa fa-exclamation-triangle text-warning me-2"></i>
                    Confirmar Cierre de Caja
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <div class="alert alert-warning">
                    <i class="fa fa-info-circle me-2"></i>
                    <strong>¡Atención!</strong> Confirme que desea cerrar la caja.
                </div>
                <p class="mb-3">
                    ¿Está seguro de que desea generar el cierre de caja?
                </p>
                <p class="text-muted mb-0">
                    <small>
                        Al confirmar, todos los documentos mostrados serán incluidos en el cierre y no podrán ser editados posteriormente.
                    </small>
                </p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                    <i class="fa fa-times me-1"></i>
                    Cancelar
                </button>
                <button type="button" class="btn btn-success" id="btn-confirmar-cierre">
                    <i class="fa fa-check me-1"></i>
                    Confirmar Cierre
                </button>
            </div>
        </div>
    </div>
</div>

<!-- ================== BEGIN core-js ================== -->
<?php require_once __ROOT__ . '/views/general/core_js.view.php'; ?>
<!-- ================== END core-js ================== -->

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Function to view appointment details
    window.verDetallesCita = function(citaId) {
        const modal      = new bootstrap.Modal(document.getElementById('citaDetallesModal'));
        const loadingDiv = document.getElementById('cita-modal-loading');
        const contentDiv = document.getElementById('cita-modal-content');
        const errorDiv   = document.getElementById('cita-detalles-error');

        // Show modal and loading state
        modal.show();
        loadingDiv.style.display = 'block';
        contentDiv.style.display = 'none';
        errorDiv.style.display   = 'none';

        // Fetch appointment details
        const formData = new FormData();
        formData.append('action', 'get_cita_details');
        formData.append('cita_id', citaId);

        fetch('cerrar-caja', {
            method: 'POST',
            body: formData
        })
        .then(response => response.json())
        .then(data => {
            loadingDiv.style.display = 'none';

            if (data.success) {
                // Populate header information
                document.getElementById('detail-numero-cita').textContent  = data.cita.id;
                document.getElementById('detail-fecha-inicio').textContent = data.cita.fecha_inicio_formateada;
                document.getElementById('detail-fecha-fin').textContent    = data.cita.fecha_fin_formateada;
                document.getElementById('detail-empleado').textContent     = data.cita.empleado_nombre || 'N/A';
                document.getElementById('detail-puesto').textContent       = data.cita.puesto_descripcion || 'N/A';
                document.getElementById('detail-metodo-pago').textContent  = data.cita.metodo_pago_nombre || 'N/A';

                // Populate services table
                const serviciosTableBody = document.getElementById('cita-servicios-table-body');
                serviciosTableBody.innerHTML = '';

                data.servicios.forEach(servicio => {
                    const row = document.createElement('tr');
                    row.innerHTML = `
                        <td>${servicio.descripcion}</td>
                        <td class="text-end">${servicio.valor_formateado}</td>
                    `;
                    serviciosTableBody.appendChild(row);
                });

                // Populate totals
                document.getElementById('detail-total-servicios').textContent = data.total_servicios_formateado;
                document.getElementById('detail-comision-empleado').textContent = data.cita.valor_comision_formateado;

                contentDiv.style.display = 'block';
            } else {
                document.getElementById('cita-detalles-error-message').textContent = data.message || 'Error al cargar los detalles';
                errorDiv.style.display = 'block';
            }
        })
        .catch(error => {
            loadingDiv.style.display = 'none';
            document.getElementById('cita-detalles-error-message').textContent = 'Error de conexión al cargar los detalles';
            errorDiv.style.display = 'block';
            console.error('Error:', error);
        });
    };

    // Function to view venta details
    window.verDetallesVenta = function(ventaId) {
        const modal      = new bootstrap.Modal(document.getElementById('ventaDetallesModal'));
        const loadingDiv = document.getElementById('venta-modal-loading');
        const contentDiv = document.getElementById('venta-modal-content');
        const errorDiv   = document.getElementById('venta-detalles-error');

        // Show modal and loading state
        modal.show();
        loadingDiv.style.display = 'block';
        contentDiv.style.display = 'none';
        errorDiv.style.display = 'none';

        // Fetch venta details
        const formData = new FormData();
        formData.append('action', 'get_venta_details');
        formData.append('venta_id', ventaId);

        fetch('cerrar-caja', {
            method: 'POST',
            body: formData
        })
        .then(response => response.json())
        .then(data => {
            loadingDiv.style.display = 'none';

            if (data.success) {
                // Populate header information
                document.getElementById('detail-numero-venta').textContent       = data.venta.id;
                document.getElementById('detail-fecha-venta').textContent        = data.venta.fecha_formateada;
                document.getElementById('detail-cliente-venta').textContent      = data.venta.cliente_nombre || 'Sin cliente';
                document.getElementById('detail-centro-costo-venta').textContent = data.venta.centro_costo_nombre || 'N/A';
                document.getElementById('detail-metodo-pago-venta').textContent  = data.venta.metodo_pago_nombre || 'N/A';
                document.getElementById('detail-estado-venta').textContent       = data.venta.estado_texto || 'N/A';

                // Populate details table
                const detallesTableBody = document.getElementById('venta-detalles-table-body');
                detallesTableBody.innerHTML = '';

                data.detalles.forEach(detalle => {
                    const row = document.createElement('tr');
                    row.innerHTML = `
                        <td>${detalle.producto_descripcion}</td>
                        <td class="text-center">${detalle.cantidad}</td>
                        <td class="text-end">${detalle.valor_formateado}</td>
                        <td class="text-end">${detalle.valor_total_formateado}</td>
                    `;
                    detallesTableBody.appendChild(row);
                });

                // Populate total
                document.getElementById('detail-total-venta').textContent = data.total_detalles_formateado;

                contentDiv.style.display = 'block';
            } else {
                document.getElementById('venta-detalles-error-message').textContent = data.message || 'Error al cargar los detalles';
                errorDiv.style.display = 'block';
            }
        })
        .catch(error => {
            loadingDiv.style.display = 'none';
            document.getElementById('venta-detalles-error-message').textContent = 'Error de conexión al cargar los detalles';
            errorDiv.style.display = 'block';
            console.error('Error:', error);
        });
    };

    // Function to view orden compra details
    window.verDetallesOrdenCompra = function(ordenId) {
        const modal      = new bootstrap.Modal(document.getElementById('ordenCompraDetallesModal'));
        const loadingDiv = document.getElementById('orden-compra-modal-loading');
        const contentDiv = document.getElementById('orden-compra-modal-content');
        const errorDiv   = document.getElementById('orden-compra-detalles-error');

        // Show modal and loading state
        modal.show();
        loadingDiv.style.display = 'block';
        contentDiv.style.display = 'none';
        errorDiv.style.display = 'none';

        // Fetch orden compra details
        const formData = new FormData();
        formData.append('action', 'get_orden_compra_details');
        formData.append('orden_id', ordenId);

        fetch('cerrar-caja', {
            method: 'POST',
            body: formData
        })
        .then(response => response.json())
        .then(data => {
            loadingDiv.style.display = 'none';

            if (data.success) {
                // Populate header information
                document.getElementById('detail-numero-orden-compra').textContent            = data.orden.id;
                document.getElementById('detail-fecha-orden-compra').textContent             = data.orden.fecha_formateada;
                document.getElementById('detail-proveedor-orden-compra').textContent         = data.orden.proveedor_nombre || 'Sin proveedor';
                document.getElementById('detail-centro-costo-orden-compra').textContent      = data.orden.centro_costo_nombre || 'N/A';
                document.getElementById('detail-usuario-orden-compra').textContent           = data.orden.usuario_nombre || 'N/A';
                document.getElementById('detail-numero-referencia-orden-compra').textContent = data.orden.n_referencia_proveedor || '';

                // Populate details table
                const detallesTableBody = document.getElementById('orden-compra-detalles-table-body');
                detallesTableBody.innerHTML = '';

                data.detalles.forEach(detalle => {
                    const row = document.createElement('tr');
                    row.innerHTML = `
                        <td>${detalle.producto_descripcion}</td>
                        <td class="text-center">${detalle.cantidad}</td>
                        <td class="text-end">${detalle.valor_formateado}</td>
                        <td class="text-end">${detalle.valor_total_formateado}</td>
                    `;
                    detallesTableBody.appendChild(row);
                });

                // Populate total
                document.getElementById('detail-total-orden-compra').textContent = data.total_detalles_formateado;

                contentDiv.style.display = 'block';
            } else {
                document.getElementById('orden-compra-detalles-error-message').textContent = data.message || 'Error al cargar los detalles';
                errorDiv.style.display = 'block';
            }
        })
        .catch(error => {
            loadingDiv.style.display = 'none';
            document.getElementById('orden-compra-detalles-error-message').textContent = 'Error de conexión al cargar los detalles';
            errorDiv.style.display = 'block';
            console.error('Error:', error);
        });
    };

    // Function to show confirmation modal
    window.generarCierre = function() {
        // Show confirmation modal
        const modal = new bootstrap.Modal(document.getElementById('confirmarCierreModal'));
        modal.show();
    };

    // Function to actually generate the cierre (called from modal)
    window.procesarGenerarCierre = function() {
        // Hide the confirmation modal
        const modal = bootstrap.Modal.getInstance(document.getElementById('confirmarCierreModal'));
        modal.hide();

        // Disable button and show loading
        const btnGenerar = document.getElementById('btn-generar-cierre');
        if (!btnGenerar) {
            alert('Error: No se pudo encontrar el botón de generar cierre');
            return;
        }

        const originalText = btnGenerar.innerHTML;
        btnGenerar.disabled = true;
        btnGenerar.innerHTML = '<i class="fa fa-spinner fa-spin me-2"></i>Generando cierre...';

        // Prepare form data
        const formData = new FormData();
        formData.append('action', 'generar_cierre');

        // Send request
        fetch('cerrar-caja', {
            method: 'POST',
            body: formData
        })
        .then(response => {
            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }
            return response.json();
        })
        .then(data => {
            if (data.success) {
                // Redirect to view cierre page with created parameter
                window.location.href = 'ver-cierre?id=' + data.id_cierre + '&created=1';
            } else {
                alert('Error al generar el cierre: ' + (data.message || 'Error desconocido'));
                // Restore button
                btnGenerar.disabled = false;
                btnGenerar.innerHTML = originalText;
            }
        })
        .catch(error => {
            alert('Error de conexión al generar el cierre: ' + error.message);
            // Restore button
            btnGenerar.disabled = false;
            btnGenerar.innerHTML = originalText;
        });
    };

    // Add event listeners
    const btnGenerar = document.getElementById('btn-generar-cierre');
    if (btnGenerar) {
        btnGenerar.addEventListener('click', generarCierre);
    }

    const btnConfirmarCierre = document.getElementById('btn-confirmar-cierre');
    if (btnConfirmarCierre) {
        btnConfirmarCierre.addEventListener('click', procesarGenerarCierre);
    }
});
</script>

</body>
</html>
