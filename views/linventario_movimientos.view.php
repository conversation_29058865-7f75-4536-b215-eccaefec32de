<?php
/**
 * Vista para consulta de movimientos de inventario
 * Página de solo lectura con filtros obligatorios por rango de fechas
 */
?>

<!DOCTYPE html>
<html lang="en" class="dark-mode">
<head>
	<meta charset="utf-8"/>
	<title><?php echo APP_NAME; ?> | Consulta Movimientos de Inventario</title>
	<meta content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no" name="viewport"/>
	<meta content="" name="description"/>
	<meta content="" name="author"/>

	<?php #region HEAD ?>
	<?php require_once __ROOT__ . '/views/general/head.view.php'; ?>
	<?php #endregion HEAD ?>
    
    <!-- ================== BEGIN page-css ================== -->
    <link href="<?php echo RUTA_ADM_ASSETS; ?>plugins/bootstrap-datepicker/dist/css/bootstrap-datepicker.css" rel="stylesheet"/>
    <!-- ================== END page-css ================== -->
</head>
<body>
<!-- BEGIN #loader -->
<div id="loader" class="app-loader">
    <span class="spinner"></span>
</div>
<!-- END #loader -->

<!-- BEGIN #app -->
<div id="app" class="app app-header-fixed app-sidebar-fixed">
    <!-- BEGIN #header -->
    <?php require_once __ROOT__ . '/views/general/header.view.php'; ?>
    <!-- END #header -->

    <!-- BEGIN #sidebar -->
    <?php require_once __ROOT__ . '/views/general/sidebar.view.php'; ?>
    <!-- END #sidebar -->

    <!-- BEGIN #content -->
    <div id="content" class="app-content">
        <?php #region PAGE HEADER ?>
        <div class="d-flex align-items-center mb-3">
            <div>
                <h4 class="mb-0">Consulta Movimientos de Inventario</h4>
                <p class="mb-0 text-muted">Consulta histórica de movimientos</p>
            </div>
        </div>
        <?php #endregion PAGE HEADER ?>

        <div class="row">
            <div class="col-12">

                <?php #region FILTERS ?>
                <div class="panel panel-inverse no-border-radious">
                    <div class="panel-heading no-border-radious">
                        <h4 class="panel-title">Filtros de Consulta</h4>
                    </div>
                    <div class="panel-body">
                        <form id="consulta-form">
                            <div class="row">
                                <!-- Fecha de Inicio Filter (Mandatory) -->
                                <div class="col-md-3">
                                    <div class="mb-3">
                                        <label for="fecha_inicio" class="form-label">
                                            <strong>Fecha de Inicio <span style="color: red;">*</span></strong>
                                        </label>
                                        <div class="input-group">
                                            <input type="text" class="form-control datepicker" id="fecha_inicio" name="fecha_inicio"
                                                    placeholder="yyyy-mm-dd" required>
                                            <span class="input-group-text" id="fecha_inicio_icon" style="cursor: pointer;">
                                                <i class="fa fa-calendar"></i>
                                            </span>
                                        </div>
                                    </div>
                                </div>

                                <!-- Fecha de Fin Filter (Mandatory) -->
                                <div class="col-md-3">
                                    <div class="mb-3">
                                        <label for="fecha_fin" class="form-label">
                                            <strong>Fecha de Fin <span style="color: red;">*</span></strong>
                                        </label>
                                        <div class="input-group">
                                            <input type="text" class="form-control datepicker" id="fecha_fin" name="fecha_fin"
                                                    placeholder="yyyy-mm-dd" required>
                                            <span class="input-group-text" id="fecha_fin_icon" style="cursor: pointer;">
                                                <i class="fa fa-calendar"></i>
                                            </span>
                                        </div>
                                    </div>
                                </div>

                                <!-- Usuario Filter (Optional) -->
                                <div class="col-md-3">
                                    <div class="mb-3">
                                        <label for="id_usuario" class="form-label">
                                            Usuario
                                        </label>
                                        <select class="form-select" id="id_usuario" name="id_usuario">
                                            <option value="">Todos los usuarios</option>
                                            <?php foreach ($usuarios as $usuario): ?>
                                                <option value="<?php echo $usuario->getId(); ?>">
                                                    <?php echo htmlspecialchars($usuario->getNombre()); ?>
                                                </option>
                                            <?php endforeach; ?>
                                        </select>
                                    </div>
                                </div>

                                <!-- Tipo Filter (Optional) -->
                                <div class="col-md-3">
                                    <div class="mb-3">
                                        <label for="tipo" class="form-label">
                                            Tipo
                                        </label>
                                        <select class="form-select" id="tipo" name="tipo">
                                            <option value="">Todos los tipos</option>
                                            <option value="ingreso">Ingreso</option>
                                            <option value="egreso">Egreso</option>
                                        </select>
                                    </div>
                                </div>
                            </div>

                            <div class="row">
                                <div class="col-12">
                                    <button type="submit" class="btn btn-primary" id="search-btn">
                                        <i class="fa fa-search me-1"></i> Consultar Movimientos
                                    </button>
                                    <button type="button" class="btn btn-secondary" id="limpiar-filtros">
                                        <i class="fa fa-times me-1"></i> Limpiar
                                    </button>
                                </div>
                            </div>
                        </form>
                    </div>
                </div>
                <?php #endregion FILTERS ?>

                <?php #region RESULTS ?>
                <div class="panel panel-inverse mt-3 no-border-radious">
                    <div class="panel-heading no-border-radious">
                        <h4 class="panel-title">Resultados de la Consulta</h4>
                    </div>
                    <div>
                        <!-- No selection message -->
                        <div id="no-selection-message" class="text-center py-4">
                            <i class="fa fa-info-circle fa-2x text-muted mb-2"></i>
                            <p class="text-muted">Seleccione un rango de fechas para consultar los movimientos de inventario.</p>
                        </div>

                        <!-- Loading message -->
                        <div id="loading-message" class="text-center py-4" style="display: none;">
                            <div class="spinner-border text-primary" role="status">
                                <span class="visually-hidden">Cargando...</span>
                            </div>
                            <p class="text-muted mt-2">Cargando movimientos...</p>
                        </div>

                        <!-- Error message -->
                        <div id="error-message" class="alert alert-danger" role="alert" style="display: none;">
                            <strong>Error en la consulta!</strong> <span id="error-text"></span>
                        </div>

                        <!-- Results table -->
                        <div id="results-table-container" style="overflow-x: auto; display: none;">
                            <table class="table table-hover table-sm mb-0" id="movimientos-table">
                                <thead class="table-dark">
                                    <tr>
                                        <th class="text-center">Fecha</th>
                                        <th class="text-center">Tipo</th>
                                        <th>Centro de Costo</th>
                                        <th>Producto</th>
                                        <th class="text-center">Cantidad</th>
                                        <th>Usuario</th>
                                        <th>Nota</th>
                                    </tr>
                                </thead>
                                <tbody id="movimientos-table-body">
                                    <!-- Results will be populated via AJAX -->
                                </tbody>
                            </table>
                        </div>

                        <!-- No results message -->
                        <div id="no-results-message" class="text-center py-4" style="display: none;">
                            <i class="fa fa-search fa-2x text-muted mb-2"></i>
                            <p class="text-muted">No se encontraron movimientos para el rango de fechas especificado.</p>
                        </div>
                    </div>
                </div>
                <?php #endregion RESULTS ?>

            </div>
            <!-- END col-12 -->
        </div>
        <!-- END row -->
    </div>
    <!-- END #content -->

    <!-- BEGIN scroll-top-btn -->
    <a href="#" class="btn btn-icon btn-circle btn-success btn-scroll-to-top" data-toggle="scroll-to-top"><i class="fa fa-angle-up"></i></a>
    <!-- END scroll-top-btn -->
</div>
<!-- END #app -->

<?php #region JS ?>
<?php require_once __ROOT__ . '/views/general/core_js.view.php'; ?>

<!-- Include Bootstrap Datepicker JS -->
<script src="<?php echo RUTA_ADM_ASSETS; ?>plugins/bootstrap-datepicker/dist/js/bootstrap-datepicker.js"></script>

<!-- ================== BEGIN PAGE LEVEL JS ================== -->
<script>
$(document).ready(function() {
    // Initialize datepickers
    $('.datepicker').datepicker({
        autoclose: true,
        todayHighlight: true,
        format: 'yyyy-mm-dd'
    });

    // Make calendar icons clickable
    document.getElementById('fecha_inicio_icon').addEventListener('click', function() {
        $('#fecha_inicio').datepicker('show');
    });

    document.getElementById('fecha_fin_icon').addEventListener('click', function() {
        $('#fecha_fin').datepicker('show');
    });

    // Get DOM elements
    const consultaForm = document.getElementById('consulta-form');
    const limpiarFiltrosBtn = document.getElementById('limpiar-filtros');

    const noSelectionMessage = document.getElementById('no-selection-message');
    const loadingMessage = document.getElementById('loading-message');
    const errorMessage = document.getElementById('error-message');
    const errorText = document.getElementById('error-text');
    const resultsTableContainer = document.getElementById('results-table-container');
    const noResultsMessage = document.getElementById('no-results-message');
    const movimientosTableBody = document.getElementById('movimientos-table-body');

    // Form submit handler
    consultaForm.addEventListener('submit', function(e) {
        e.preventDefault();

        const fechaInicio = document.getElementById('fecha_inicio').value;
        const fechaFin = document.getElementById('fecha_fin').value;

        if (!fechaInicio || !fechaFin) {
            showSweetAlertError('Error', 'Ambas fechas son obligatorias.');
            return;
        }

        searchMovimientos();
    });

    // Clear button handler
    limpiarFiltrosBtn.addEventListener('click', function() {
        consultaForm.reset();
        showNoSelectionState();
    });

    // Search function
    function searchMovimientos() {
        const formData = new FormData();
        formData.append('action', 'consultar_movimientos');
        formData.append('fecha_inicio', document.getElementById('fecha_inicio').value);
        formData.append('fecha_fin', document.getElementById('fecha_fin').value);
        formData.append('id_usuario', document.getElementById('id_usuario').value);
        formData.append('tipo', document.getElementById('tipo').value);

        showLoadingState();

        fetch('movimientos-inventario', {
            method: 'POST',
            body: formData
        })
        .then(response => {
            if (!response.ok) {
                throw new Error(`Error ${response.status}: ${response.statusText}`);
            }
            return response.json();
        })
        .then(data => {
            if (data.success) {
                displayResults(data.data);
            } else {
                showSweetAlertError('Error', data.message);
                showErrorState(data.message);
            }
        })
        .catch(error => {
            showSweetAlertError('Error', 'Error al consultar movimientos: ' + error.message);
            showErrorState('Error de conexión: ' + error.message);
        });
    }

    // Display results in table
    function displayResults(movimientos) {
        movimientosTableBody.innerHTML = '';

        if (movimientos.length === 0) {
            showNoResultsState();
            return;
        }

        movimientos.forEach(movimiento => {
            const row = document.createElement('tr');

            // Format date to yyyy-MM-dd HH:mm:ss
            const fechaFormateada = formatDateTime(movimiento.fecha);

            row.innerHTML = `
                <td class="text-center align-middle">${fechaFormateada}</td>
                <td class="text-center align-middle">
                    <span class="badge ${movimiento.tipo === 'ingreso' ? 'bg-success' : 'bg-danger'}">
                        ${movimiento.tipo.charAt(0).toUpperCase() + movimiento.tipo.slice(1)}
                    </span>
                </td>
                <td class="align-middle">${movimiento.nombre_centro_costo || 'N/A'}</td>
                <td class="align-middle">${movimiento.descripcion_producto || 'N/A'}</td>
                <td class="align-middle text-center">${movimiento.cantidad}</td>
                <td class="align-middle">${movimiento.nombre_usuario || 'N/A'}</td>
                <td class="align-middle">${movimiento.nota || ''}</td>
            `;

            movimientosTableBody.appendChild(row);
        });

        showResultsState();
    }

    // Format date to yyyy-MM-dd HH:mm:ss
    function formatDateTime(dateString) {
        if (!dateString) return '';

        try {
            const date = new Date(dateString);
            const year = date.getFullYear();
            const month = String(date.getMonth() + 1).padStart(2, '0');
            const day = String(date.getDate()).padStart(2, '0');
            const hours = String(date.getHours()).padStart(2, '0');
            const minutes = String(date.getMinutes()).padStart(2, '0');
            const seconds = String(date.getSeconds()).padStart(2, '0');

            return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
        } catch (error) {
            return dateString;
        }
    }

    // State management functions
    function showNoSelectionState() {
        noSelectionMessage.style.display = 'block';
        loadingMessage.style.display = 'none';
        errorMessage.style.display = 'none';
        resultsTableContainer.style.display = 'none';
        noResultsMessage.style.display = 'none';
    }

    function showLoadingState() {
        noSelectionMessage.style.display = 'none';
        loadingMessage.style.display = 'block';
        errorMessage.style.display = 'none';
        resultsTableContainer.style.display = 'none';
        noResultsMessage.style.display = 'none';
    }

    function showResultsState() {
        noSelectionMessage.style.display = 'none';
        loadingMessage.style.display = 'none';
        errorMessage.style.display = 'none';
        resultsTableContainer.style.display = 'block';
        noResultsMessage.style.display = 'none';
    }

    function showNoResultsState() {
        noSelectionMessage.style.display = 'none';
        loadingMessage.style.display = 'none';
        errorMessage.style.display = 'none';
        resultsTableContainer.style.display = 'none';
        noResultsMessage.style.display = 'block';
    }

    function showErrorState(message) {
        noSelectionMessage.style.display = 'none';
        loadingMessage.style.display = 'none';
        errorMessage.style.display = 'block';
        errorText.textContent = message;
        resultsTableContainer.style.display = 'none';
        noResultsMessage.style.display = 'none';
    }

    // Initialize with no selection state
    showNoSelectionState();
});
</script>
<!-- ================== END PAGE LEVEL JS ================== -->
<?php #endregion JS ?>

</body>
</html>
