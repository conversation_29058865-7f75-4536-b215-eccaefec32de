<?php
#region DOCS

/** @var CentroCosto[] $centros_costos */
/** @var Usuario[] $usuarios */
/** @var string|null $success_text Success message to display */
/** @var string|null $success_display Whether to show success message ('show' or null) */
/** @var string|null $error_text Error message to display */

/** @var string|null $error_display Whether to show error message ('show' or null) */

use App\classes\CentroCosto;
use App\classes\Usuario;

#endregion DOCS
?>
<!DOCTYPE html>
<html lang="en" class="dark-mode">
<head>
	<meta charset="utf-8"/>
	<title><?php echo APP_NAME; ?> | Centros de Costos</title>
	<meta content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no" name="viewport"/>
	<meta content="" name="description"/>
	<meta content="" name="author"/>

	<?php #region HEAD ?>
	<?php require_once __ROOT__ . '/views/general/head.view.php'; ?>
	<?php #endregion HEAD ?>

	<style>
		/* Row hover effect for better UX */
		#usuarios-table-body tr:hover {
			background-color: rgba(49, 130, 206, 0.1);
		}

		#usuarios-table-body tr {
			cursor: pointer;
			transition: background-color 0.2s ease;
		}

		/* Bootstrap checkbox styling enhancements for dark theme */
		.form-check-input {
			background-color: #2d3748;
			border-color: #4a5568;
		}

		.form-check-input:checked {
			background-color: #3182ce;
			border-color: #3182ce;
		}

		.form-check-input:focus {
			border-color: #3182ce;
			box-shadow: 0 0 0 0.25rem rgba(49, 130, 206, 0.25);
		}

		.form-check-input:hover {
			border-color: #3182ce;
		}
	</style>
</head>
<body>
<!-- BEGIN #loader -->
<div id="loader" class="app-loader">
	<span class="spinner"></span>
</div>
<!-- END #loader -->

<!-- BEGIN #app -->
<div id="app" class="app app-header-fixed app-sidebar-fixed">
	<!-- #header -->
	<?php require_once __ROOT__ . '/views/general/header.view.php'; ?>

	<!-- #sidebar -->
	<?php require_once __ROOT__ . '/views/general/sidebar.view.php'; ?>

	<!-- BEGIN #content -->
	<div id="content" class="app-content">
		<?php #region PAGE HEADER ?>
		<div class="d-flex align-items-center mb-3">
			<div>
				<h4 class="mb-0">Gestión de Centros de Costos</h4>
				<p class="mb-0 text-muted">Administra los centros de costos del sistema</p>
			</div>
			<div class="ms-auto">
				<button type="button" class="btn btn-success" data-bs-toggle="modal" data-bs-target="#createCentroCostoModal">
					<i class="fa fa-plus-circle fa-fw me-1"></i> Crear nuevo
				</button>
			</div>
		</div>

		<hr>
		<?php #endregion PAGE HEADER ?>

		<?php #region PANEL CENTROS COSTOS ?>
		<div class="panel panel-inverse mt-3 no-border-radious">
			<div class="panel-heading no-border-radious">
				<h4 class="panel-title">
					Listado de Centros de Costos Activos
				</h4>
			</div>
			<!-- BEGIN PANEL body -->
			<div class="p-1 table-nowrap" style="overflow: auto">
				<?php #region TABLE CENTROS COSTOS ?>
				<table class="table table-hover table-sm">
					<thead>
					<tr>
						<th class="w-150px text-center">Acciones</th>
						<th>Nombre</th>
						<th class="text-center"># Usuarios</th>
						<th class="text-center"># Empleados</th>
						<th class="text-center">Estado</th>
					</tr>
					</thead>
					<tbody class="fs-12px" id="centro-costo-table-body">
					<?php foreach ($centros_costos as $centro_costo): ?>
						<tr data-centro-id="<?php echo $centro_costo->getId(); ?>">
							<td class="text-center">
								<?php // Edit Button - Triggers Modal ?>
								<button type="button" class="btn btn-xs btn-primary me-1 btn-edit-centro-costo"
								        title="Editar Centro de Costo"
								        data-bs-toggle="modal"
								        data-bs-target="#editCentroCostoModal"
								        data-centro-id="<?php echo $centro_costo->getId(); ?>"
								        data-nombre="<?php echo htmlspecialchars($centro_costo->getNombre() ?? ''); ?>">
									<i class="fa fa-edit"></i>
								</button>
								<?php // Gestionar Usuarios Button ?>
								<button type="button" class="btn btn-xs btn-info me-1 btn-gestionar-usuarios"
								        title="Asociar Usuarios"
								        data-bs-toggle="modal"
								        data-bs-target="#gestionarUsuariosModal"
								        data-centro-id="<?php echo $centro_costo->getId(); ?>"
								        data-centro-nombre="<?php echo htmlspecialchars($centro_costo->getNombre() ?? ''); ?>">
									<i class="fa fa-users"></i>
								</button>
								<?php // Gestionar Empleados Button ?>
								<button type="button" class="btn btn-xs btn-warning me-1 btn-gestionar-empleados"
								        title="Asociar Empleados"
								        data-bs-toggle="modal"
								        data-bs-target="#gestionarEmpleadosModal"
								        data-centro-id="<?php echo $centro_costo->getId(); ?>"
								        data-centro-nombre="<?php echo htmlspecialchars($centro_costo->getNombre() ?? ''); ?>">
									<i class="fa fa-user-tie"></i>
								</button>
								<?php // Deactivate Button - Only show if active ?>
								<?php if ($centro_costo->isActivo()): ?>
									<button type="button" class="btn btn-xs btn-danger btn-desactivar-centro-costo"
									        title="Desactivar"
									        data-centro-id="<?php echo $centro_costo->getId(); ?>"
									        data-nombre="<?php echo htmlspecialchars($centro_costo->getNombre() ?? ''); ?>">
										<i class="fa fa-trash-alt"></i>
									</button>
								<?php endif; ?>
							</td>
							<td class="centro-costo-nombre-cell"><?php echo htmlspecialchars($centro_costo->getNombre()); ?></td>
							<td class="text-center">
								<span class="badge bg-info"><?php echo $centro_costo->user_count ?? 0; ?></span>
							</td>
							<td class="text-center">
								<span class="badge bg-warning"><?php echo $centro_costo->employee_count ?? 0; ?></span>
							</td>
							<td class="text-center">
								<?php if ($centro_costo->isActivo()): ?>
									<span class="badge bg-success">Activo</span>
								<?php else: ?>
									<span class="badge bg-danger">Inactivo</span>
								<?php endif; ?>
							</td>
						</tr>
					<?php endforeach; ?>
					<?php if (empty($centros_costos)): ?>
						<tr>
							<td colspan="4" class="text-center">No hay centros de costos para mostrar.</td>
						</tr>
					<?php endif; ?>

					</tbody>
				</table>
				<?php #endregion TABLE CENTROS COSTOS ?>
			</div>
			<!-- END PANEL body -->
		</div>
		<?php #endregion PANEL CENTROS COSTOS ?>

		<?php #region Create Centro Costo Modal ?>
		<div class="modal fade" id="createCentroCostoModal" tabindex="-1" aria-labelledby="createCentroCostoModalLabel" aria-hidden="true">
			<div class="modal-dialog">
				<div class="modal-content">
					<form id="create-centro-costo-form">
						<div class="modal-header">
							<h5 class="modal-title" id="createCentroCostoModalLabel">Crear Centro de Costo</h5>
							<button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
						</div>
						<div class="modal-body">
							<input type="hidden" name="action" value="crear">

							<div class="mb-3">
								<label for="create-centro-costo-nombre" class="form-label">Nombre:</label>
								<input type="text" class="form-control" id="create-centro-costo-nombre" name="nombre" required>
							</div>

							<div id="create-centro-costo-error" class="alert alert-danger mt-2" style="display: none;"></div>
						</div>
						<div class="modal-footer">
							<button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancelar</button>
							<button type="submit" class="btn btn-success">Crear</button>
						</div>
					</form>
				</div>
			</div>
		</div>
		<?php #endregion Create Centro Costo Modal ?>

		<?php #region Edit Centro Costo Modal ?>
		<div class="modal fade" id="editCentroCostoModal" tabindex="-1" aria-labelledby="editCentroCostoModalLabel" aria-hidden="true">
			<div class="modal-dialog">
				<div class="modal-content">
					<form id="edit-centro-costo-form">
						<div class="modal-header">
							<h5 class="modal-title" id="editCentroCostoModalLabel">Editar Centro de Costo</h5>
							<button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
						</div>
						<div class="modal-body">
							<input type="hidden" name="centroId" id="edit-centro-costo-id">
							<input type="hidden" name="action" value="modificar">

							<div class="mb-3">
								<label for="edit-centro-costo-nombre" class="form-label">Nombre:</label>
								<input type="text" class="form-control" id="edit-centro-costo-nombre" name="nombre" required>
							</div>

							<div id="edit-centro-costo-error" class="alert alert-danger mt-2" style="display: none;"></div>
						</div>
						<div class="modal-footer">
							<button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancelar</button>
							<button type="submit" class="btn btn-primary">Guardar Cambios</button>
						</div>
					</form>
				</div>
			</div>
		</div>
		<?php #endregion Edit Centro Costo Modal ?>

		<?php #region Asociar Usuarios Modal ?>
		<div class="modal fade" id="gestionarUsuariosModal" tabindex="-1" aria-labelledby="asociarUsuariosModalLabel" aria-hidden="true">
			<div class="modal-dialog modal-lg">
				<div class="modal-content">
					<div class="modal-header">
						<h5 class="modal-title" id="asociarUsuariosModalLabel">Asociar usuarios</h5>
						<button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
					</div>
					<div class="modal-body">
						<p class="text-muted mb-3">Seleccione los usuarios que desea asociar al centro de costo <strong id="centro-nombre-display"></strong>:</p>

						<div id="usuarios-loading" class="text-center" style="display: none;">
							<div class="spinner-border text-primary" role="status">
								<span class="visually-hidden">Cargando...</span>
							</div>
							<p class="mt-2">Cargando usuarios...</p>
						</div>

						<div id="usuarios-container" style="display: none;">
							<div class="panel panel-inverse">
								<div class="panel-heading">
									<h4 class="panel-title">
										Usuarios Disponibles
									</h4>
								</div>
								<div class="panel-body p-0">
									<div class="table-responsive">
										<table class="table table-hover table-sm table-dark mb-0">
											<thead>
												<tr>
													<th width="60px" class="text-center align-middle">
														<div class="form-check">
															<input type="checkbox" id="select-all-usuarios" class="form-check-input" title="Seleccionar/Deseleccionar todos">
														</div>
													</th>
													<th class="align-middle">Nombre</th>
													<th class="align-middle">Username</th>
												</tr>
											</thead>
											<tbody id="usuarios-table-body">
												<!-- Usuarios will be loaded here via AJAX -->
											</tbody>
										</table>
									</div>
								</div>
							</div>
						</div>

						<div id="usuarios-error" class="alert alert-danger mt-3" style="display: none;"></div>
					</div>
					<div class="modal-footer">
						<button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancelar</button>
						<button type="button" class="btn btn-success" id="guardar-usuarios-btn">
							<i class="fa fa-save me-1"></i> Guardar Cambios
						</button>
					</div>
				</div>
			</div>
		</div>
		<?php #endregion Asociar Usuarios Modal ?>

		<?php #region Asociar Empleados Modal ?>
		<div class="modal fade" id="gestionarEmpleadosModal" tabindex="-1" aria-labelledby="asociarEmpleadosModalLabel" aria-hidden="true">
			<div class="modal-dialog modal-lg">
				<div class="modal-content">
					<div class="modal-header">
						<h5 class="modal-title" id="asociarEmpleadosModalLabel">Asociar empleados</h5>
						<button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
					</div>
					<div class="modal-body">
						<p class="text-muted mb-3">Seleccione los empleados que desea asociar al centro de costo <strong id="centro-nombre-empleados-display"></strong>:</p>

						<div id="empleados-loading" class="text-center">
							<i class="fa fa-spinner fa-spin fa-2x"></i>
							<p class="mt-2">Cargando empleados...</p>
						</div>

						<div id="empleados-container" style="display: none;">
							<div class="panel panel-inverse">
								<div class="panel-heading">
									<h4 class="panel-title">
										Empleados Disponibles
									</h4>
								</div>
								<div class="panel-body p-0">
									<div class="table-responsive">
										<table class="table table-hover table-sm table-dark mb-0">
											<thead>
												<tr>
													<th width="60px" class="text-center align-middle">
														<div class="form-check">
															<input type="checkbox" id="select-all-empleados" class="form-check-input" title="Seleccionar/Deseleccionar todos">
														</div>
													</th>
													<th class="align-middle">Nombre</th>
													<th class="align-middle">Email</th>
												</tr>
											</thead>
											<tbody id="empleados-table-body">
												<!-- Empleados will be loaded here via AJAX -->
											</tbody>
										</table>
									</div>
								</div>
							</div>
						</div>

						<div id="empleados-error" class="alert alert-danger mt-3" style="display: none;"></div>
					</div>
					<div class="modal-footer">
						<button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancelar</button>
						<button type="button" class="btn btn-success" id="guardar-empleados-btn">
							<i class="fa fa-save me-1"></i> Guardar Cambios
						</button>
					</div>
				</div>
			</div>
		</div>
		<?php #endregion Asociar Empleados Modal ?>

	</div>
	<!-- END #content -->

	<?php #region Hidden Form for Deactivation ?>
	<form id="deactivate-centro-costo-form" method="POST" action="centros-costos" style="display: none;">
		<input type="hidden" name="action" value="desactivar">
		<input type="hidden" name="centroId" id="deactivate-centro-costo-id">
	</form>
	<?php #endregion Hidden Form ?>

	<!-- BEGIN scroll-top-btn -->
	<a href="#" class="btn btn-icon btn-circle btn-success btn-scroll-to-top" data-toggle="scroll-to-top"><i class="fa fa-angle-up"></i></a>
	<!-- END scroll-top-btn -->
</div>
<!-- END #app -->

<?php #region JS ?>
<?php require_once __ROOT__ . '/views/general/core_js.view.php'; ?>

<!-- ================== BEGIN PAGE LEVEL JS ================== -->
<!-- Assuming SweetAlert is loaded in core_js or globally -->
<!-- Assuming SweetAlert helper functions (showSweetAlertSuccess/Error) are available -->
<script>
    document.addEventListener('DOMContentLoaded', function () {
        // Use event delegation on the table body
        const tableBody = document.getElementById('centro-costo-table-body');

        // Create Centro Costo Modal Elements
        const createCentroCostoModalElement = document.getElementById('createCentroCostoModal');
        const createCentroCostoModal        = new bootstrap.Modal(createCentroCostoModalElement);
        const createCentroCostoForm         = document.getElementById('create-centro-costo-form');
        const createCentroCostoErrorDiv     = document.getElementById('create-centro-costo-error');

        // Edit Centro Costo Modal Elements
        const editCentroCostoModalElement = document.getElementById('editCentroCostoModal');
        const editCentroCostoModal        = new bootstrap.Modal(editCentroCostoModalElement);
        const editCentroCostoForm         = document.getElementById('edit-centro-costo-form');
        const editCentroCostoIdInput      = document.getElementById('edit-centro-costo-id');
        const editCentroCostoNombreInput  = document.getElementById('edit-centro-costo-nombre');
        const editCentroCostoErrorDiv     = document.getElementById('edit-centro-costo-error');

        // Gestionar Usuarios Modal Elements
        const gestionarUsuariosModalElement = document.getElementById('gestionarUsuariosModal');
        const gestionarUsuariosModal        = new bootstrap.Modal(gestionarUsuariosModalElement);
        const centroNombreDisplay           = document.getElementById('centro-nombre-display');
        const usuariosLoading               = document.getElementById('usuarios-loading');
        const usuariosContainer             = document.getElementById('usuarios-container');
        const usuariosTableBody             = document.getElementById('usuarios-table-body');
        const usuariosErrorDiv              = document.getElementById('usuarios-error');
        const guardarUsuariosBtn            = document.getElementById('guardar-usuarios-btn');
        const selectAllUsuarios             = document.getElementById('select-all-usuarios');

        // Gestionar Empleados Modal Elements
        const gestionarEmpleadosModalElement = document.getElementById('gestionarEmpleadosModal');
        const gestionarEmpleadosModal        = new bootstrap.Modal(gestionarEmpleadosModalElement);
        const centroNombreEmpleadosDisplay   = document.getElementById('centro-nombre-empleados-display');
        const empleadosLoading               = document.getElementById('empleados-loading');
        const empleadosContainer             = document.getElementById('empleados-container');
        const empleadosTableBody             = document.getElementById('empleados-table-body');
        const empleadosErrorDiv              = document.getElementById('empleados-error');
        const guardarEmpleadosBtn            = document.getElementById('guardar-empleados-btn');
        const selectAllEmpleados             = document.getElementById('select-all-empleados');

        let currentCentroId = null;

        if (tableBody) {
            tableBody.addEventListener('click', function (event) {
                const deactivateButton = event.target.closest('.btn-desactivar-centro-costo');
                const editButton       = event.target.closest('.btn-edit-centro-costo');
                const gestionarUsuariosButton = event.target.closest('.btn-gestionar-usuarios');
                const gestionarEmpleadosButton = event.target.closest('.btn-gestionar-empleados');

                // --- Handle Deactivate Click ---
				<?php #region JS AJAX -- Deactivate Centro Costo ?>
                if (deactivateButton) {
                    event.preventDefault();
                    const centroId     = deactivateButton.dataset.centroId;
                    const centroNombre = deactivateButton.dataset.nombre || 'este centro de costo';

                    swal({
                        title     : "Confirmar Desactivación",
                        text      : `¿Seguro que quieres desactivar el centro de costo '${centroNombre}'?`,
                        icon      : "warning",
                        buttons   : {
                            cancel : {text: "Cancelar", value: null, visible: true, className: "", closeModal: true},
                            confirm: {text: "Confirmar", value: true, visible: true, className: "btn-danger", closeModal: true}
                        },
                        dangerMode: true,
                    })
                        .then((willDelete) => {
                            if (willDelete) {
                                document.getElementById('deactivate-centro-costo-id').value = centroId;
                                document.getElementById('deactivate-centro-costo-form').submit();
                            }
                        });
                }
				<?php #endregion JS AJAX -- Deactivate Centro Costo ?>

                // --- Handle Gestionar Usuarios Click ---
				<?php #region JS AJAX - Handle Gestionar Usuarios click ?>
                if (gestionarUsuariosButton) {
                    event.preventDefault();
                    const centroId = gestionarUsuariosButton.dataset.centroId;
                    const centroNombre = gestionarUsuariosButton.dataset.centroNombre;

                    currentCentroId = centroId;
                    centroNombreDisplay.textContent = centroNombre;

                    // Reset modal state
                    usuariosErrorDiv.style.display = 'none';
                    usuariosContainer.style.display = 'none';
                    usuariosLoading.style.display = 'block';

                    // Load users for this cost center
                    loadUsuariosCentro(centroId);
                }
				<?php #endregion JS AJAX - Handle Gestionar Usuarios click ?>

                // --- Handle Gestionar Empleados Click ---
				<?php #region JS AJAX - Handle Gestionar Empleados click ?>
                if (gestionarEmpleadosButton) {
                    event.preventDefault();
                    const centroId = gestionarEmpleadosButton.dataset.centroId;
                    const centroNombre = gestionarEmpleadosButton.dataset.centroNombre;

                    currentCentroId = centroId;
                    centroNombreEmpleadosDisplay.textContent = centroNombre;

                    // Reset modal state
                    empleadosErrorDiv.style.display = 'none';
                    empleadosContainer.style.display = 'none';
                    empleadosLoading.style.display = 'block';

                    // Load employees for this cost center
                    loadEmpleadosCentro(centroId);
                }
				<?php #endregion JS AJAX - Handle Gestionar Empleados click ?>

                // --- Handle Edit Click ---
				<?php #region JS AJAX - Handle Edit click ?>
                if (editButton) {
                    event.preventDefault(); // Prevent default if it was an <a>
                    const centroId      = editButton.dataset.centroId;
                    const currentNombre = editButton.dataset.nombre;

                    // Populate the modal form
                    editCentroCostoIdInput.value          = centroId;
                    editCentroCostoNombreInput.value      = currentNombre;
                    editCentroCostoErrorDiv.style.display = 'none'; // Hide previous errors
                    editCentroCostoErrorDiv.textContent   = '';

                    // The data-bs-toggle and data-bs-target attributes on the button handle showing the modal
                }
				<?php #endregion JS AJAX - Edit Centro Costo ?>
            });
        }

        // --- Handle Create Form Submission (AJAX) ---
		<?php #region JS AJAX - Create Form Submission ?>
        if (createCentroCostoForm) {
            createCentroCostoForm.addEventListener('submit', function (event) {
                event.preventDefault(); // Stop default form submission
                createCentroCostoErrorDiv.style.display = 'none'; // Hide error div initially

                const formData = new FormData(createCentroCostoForm);
                const nombre   = formData.get('nombre').trim(); // Get trimmed name

                // Basic client-side validation
                if (!nombre) {
                    createCentroCostoErrorDiv.textContent   = 'El nombre no puede estar vacío.';
                    createCentroCostoErrorDiv.style.display = 'block';
                    return; // Stop submission
                }

                // Disable submit button during request
                const submitButton    = createCentroCostoForm.querySelector('button[type="submit"]');
                submitButton.disabled = true;

                fetch('centros-costos', {
                    method: 'POST',
                    body  : formData
                })
                    .then(response => {
                        if (!response.ok) {
                            return response.json().then(errData => {
                                throw new Error(errData.message || `Error ${response.status}: ${response.statusText}`);
                            }).catch(() => {
                                throw new Error(`Error ${response.status}: ${response.statusText}`);
                            });
                        }
                        return response.json();
                    })
                    .then(data => {
                        if (data.success) {
                            createCentroCostoModal.hide(); // Close modal on success

                            // Reset the form for next use
                            createCentroCostoForm.reset();

                            // Add the new row to the table
                            const tableBody = document.getElementById('centro-costo-table-body');

                            // Remove "no data" row if it exists
                            const noDataRow = tableBody.querySelector('tr td[colspan="4"]');
                            if (noDataRow) {
                                noDataRow.closest('tr').remove();
                            }

                            // Create new row HTML
                            const newRow = document.createElement('tr');
                            newRow.setAttribute('data-centro-id', data.id);

                            newRow.innerHTML = `
                                <td class="text-center">
                                    <button type="button" class="btn btn-xs btn-primary me-1 btn-edit-centro-costo"
                                            title="Editar Centro de Costo"
                                            data-bs-toggle="modal"
                                            data-bs-target="#editCentroCostoModal"
                                            data-centro-id="${data.id}"
                                            data-nombre="${data.nombre}">
                                        <i class="fa fa-edit"></i>
                                    </button>
                                    <button type="button" class="btn btn-xs btn-info me-1 btn-gestionar-usuarios"
                                            title="Asociar Usuarios"
                                            data-bs-toggle="modal"
                                            data-bs-target="#gestionarUsuariosModal"
                                            data-centro-id="${data.id}"
                                            data-centro-nombre="${data.nombre}">
                                        <i class="fa fa-users"></i>
                                    </button>
                                    <button type="button" class="btn btn-xs btn-warning me-1 btn-gestionar-empleados"
                                            title="Asociar Empleados"
                                            data-bs-toggle="modal"
                                            data-bs-target="#gestionarEmpleadosModal"
                                            data-centro-id="${data.id}"
                                            data-centro-nombre="${data.nombre}">
                                        <i class="fa fa-user-tie"></i>
                                    </button>
                                    <button type="button" class="btn btn-xs btn-danger btn-desactivar-centro-costo"
                                            title="Desactivar"
                                            data-centro-id="${data.id}"
                                            data-nombre="${data.nombre}">
                                        <i class="fa fa-trash-alt"></i>
                                    </button>
                                </td>
                                <td class="centro-costo-nombre-cell">${data.nombre}</td>
                                <td class="text-center"><span class="badge bg-info">0</span></td>
                                <td class="text-center"><span class="badge bg-warning">0</span></td>
                                <td class="text-center"><span class="badge bg-success">Activo</span></td>
                            `;

                            // Add the new row to the table
                            tableBody.appendChild(newRow);

                            showSweetAlertSuccess('Éxito', 'Centro de Costo creado correctamente.');
                        } else {
                            // Show error message inside the modal
                            createCentroCostoErrorDiv.textContent   = data.message || 'Ocurrió un error al crear el Centro de Costo.';
                            createCentroCostoErrorDiv.style.display = 'block';
                        }
                    })
                    .catch(error => {
                        console.error('Error creating Centro Costo:', error);
                        // Show error message inside the modal
                        createCentroCostoErrorDiv.textContent   = 'Error de red o del servidor: ' + error.message;
                        createCentroCostoErrorDiv.style.display = 'block';
                    })
                    .finally(() => {
                        // Re-enable submit button
                        submitButton.disabled = false;
                    });
            });
        }
		<?php #endregion JS AJAX - Create Form Submission ?>

        // --- Handle Edit Form Submission (AJAX) ---
		<?php #region JS AJAX - Edit Form Submission ?>
        if (editCentroCostoForm) {
            editCentroCostoForm.addEventListener('submit', function (event) {
                event.preventDefault(); // Stop default form submission
                editCentroCostoErrorDiv.style.display = 'none'; // Hide error div initially

                const formData  = new FormData(editCentroCostoForm);
                const centroId  = formData.get('centroId');
                const newNombre = formData.get('nombre').trim(); // Get trimmed name

                // Basic client-side validation
                if (!newNombre) {
                    editCentroCostoErrorDiv.textContent   = 'El nombre no puede estar vacío.';
                    editCentroCostoErrorDiv.style.display = 'block';
                    return; // Stop submission
                }

                // Disable submit button during request
                const submitButton    = editCentroCostoForm.querySelector('button[type="submit"]');
                submitButton.disabled = true;

                fetch('centros-costos', {
                    method: 'POST',
                    body  : formData
                })
                    .then(response => {
                        if (!response.ok) {
                            return response.json().then(errData => {
                                throw new Error(errData.message || `Error ${response.status}: ${response.statusText}`);
                            }).catch(() => {
                                throw new Error(`Error ${response.status}: ${response.statusText}`);
                            });
                        }
                        return response.json();
                    })
                    .then(data => {
                        if (data.success) {
                            editCentroCostoModal.hide(); // Close modal on success

                            // Update the row in the table directly
                            const tableRow = document.querySelector(`#centro-costo-table-body tr[data-centro-id="${centroId}"]`);
                            if (tableRow) {
                                // Find the cell with the specific class within that row
                                const nombreCell = tableRow.querySelector('.centro-costo-nombre-cell');
                                if (nombreCell) {
                                    nombreCell.textContent = newNombre; // Update cell text
                                }

                                // Also update the data attributes on the edit, gestionar usuarios and delete buttons for next time
                                const editButton = tableRow.querySelector('.btn-edit-centro-costo');
                                if (editButton) {
                                    editButton.dataset.nombre = newNombre;
                                }

                                const gestionarUsuariosButton = tableRow.querySelector('.btn-gestionar-usuarios');
                                if (gestionarUsuariosButton) {
                                    gestionarUsuariosButton.dataset.centroNombre = newNombre;
                                }

                                const gestionarEmpleadosButton = tableRow.querySelector('.btn-gestionar-empleados');
                                if (gestionarEmpleadosButton) {
                                    gestionarEmpleadosButton.dataset.centroNombre = newNombre;
                                }

                                const deleteButton = tableRow.querySelector('.btn-desactivar-centro-costo');
                                if (deleteButton) {
                                    deleteButton.dataset.nombre = newNombre;
                                }
                            }

                            showSweetAlertSuccess('Éxito', 'Centro de Costo actualizado correctamente.');
                        } else {
                            // Show error message inside the modal
                            editCentroCostoErrorDiv.textContent   = data.message || 'Ocurrió un error al guardar.';
                            editCentroCostoErrorDiv.style.display = 'block';
                        }
                    })
                    .catch(error => {
                        console.error('Error updating Centro Costo:', error);
                        // Show error message inside the modal
                        editCentroCostoErrorDiv.textContent   = 'Error de red o del servidor: ' + error.message;
                        editCentroCostoErrorDiv.style.display = 'block';
                    })
                    .finally(() => {
                        // Re-enable submit button
                        submitButton.disabled = false;
                    });
            });
        }
		<?php #endregion JS AJAX - Edit Form Submission ?>

        // --- Load Users Function ---
		<?php #region JS AJAX - Load Users ?>
        function loadUsuariosCentro(centroId) {
            fetch(`centros-costos?action=get_usuarios_centro&centroId=${centroId}`, {
                method: 'GET'
            })
                .then(response => {
                    if (!response.ok) {
                        throw new Error(`Error ${response.status}: ${response.statusText}`);
                    }
                    return response.json();
                })
                .then(data => {
                    if (data.success) {
                        renderUsuarios(data.usuarios, data.centro_usuarios);
                        usuariosLoading.style.display = 'none';
                        usuariosContainer.style.display = 'block';
                    } else {
                        throw new Error(data.message || 'Error al cargar usuarios');
                    }
                })
                .catch(error => {
                    console.error('Error loading users:', error);
                    usuariosLoading.style.display = 'none';
                    usuariosErrorDiv.textContent = 'Error al cargar usuarios: ' + error.message;
                    usuariosErrorDiv.style.display = 'block';
                });
        }

        function renderUsuarios(usuarios, centroUsuarios) {
            usuariosTableBody.innerHTML = '';

            usuarios.forEach((usuario, index) => {
                const isChecked = centroUsuarios.includes(usuario.id);
                const row = document.createElement('tr');
                row.style.cursor = 'pointer';
                row.dataset.usuarioId = usuario.id;

                const checkboxId = `usuario-checkbox-${index}`;

                row.innerHTML = `
                    <td class="text-center align-middle">
                        <div class="form-check">
                            <input type="checkbox" id="${checkboxId}" class="form-check-input usuario-checkbox" value="${usuario.id}" ${isChecked ? 'checked' : ''}>
                        </div>
                    </td>
                    <td class="align-middle">${usuario.nombre}</td>
                    <td class="align-middle">${usuario.username}</td>
                `;

                // Add click handler to toggle checkbox when clicking anywhere on the row
                row.addEventListener('click', function(e) {
                    // Don't toggle if clicking on the checkbox or form-check container
                    if (!e.target.closest('.form-check') && !e.target.classList.contains('form-check-input')) {
                        const checkbox = row.querySelector('.usuario-checkbox');
                        checkbox.checked = !checkbox.checked;
                        updateSelectAllUsuariosState();
                    }
                });

                // Add change handler to checkbox
                const checkbox = row.querySelector('.usuario-checkbox');
                checkbox.addEventListener('change', updateSelectAllUsuariosState);

                usuariosTableBody.appendChild(row);
            });

            updateSelectAllUsuariosState();
        }

        function updateSelectAllUsuariosState() {
            const checkboxes = usuariosTableBody.querySelectorAll('.usuario-checkbox');
            const checkedBoxes = usuariosTableBody.querySelectorAll('.usuario-checkbox:checked');

            if (checkboxes.length === 0) {
                selectAllUsuarios.indeterminate = false;
                selectAllUsuarios.checked = false;
            } else if (checkedBoxes.length === checkboxes.length) {
                selectAllUsuarios.indeterminate = false;
                selectAllUsuarios.checked = true;
            } else if (checkedBoxes.length > 0) {
                selectAllUsuarios.indeterminate = true;
                selectAllUsuarios.checked = false;
            } else {
                selectAllUsuarios.indeterminate = false;
                selectAllUsuarios.checked = false;
            }
        }
		<?php #endregion JS AJAX - Load Users ?>

        // --- Load Employees Function ---
		<?php #region JS AJAX - Load Employees ?>
        function loadEmpleadosCentro(centroId) {
            fetch(`centros-costos?action=get_empleados_centro&centro_id=${centroId}`, {
                method: 'GET'
            })
                .then(response => {
                    if (!response.ok) {
                        throw new Error(`Error ${response.status}: ${response.statusText}`);
                    }
                    return response.json();
                })
                .then(data => {
                    if (data.success) {
                        renderEmpleados(data.empleados, data.centro_empleados);
                        empleadosLoading.style.display = 'none';
                        empleadosContainer.style.display = 'block';
                    } else {
                        throw new Error(data.message || 'Error al cargar empleados');
                    }
                })
                .catch(error => {
                    console.error('Error loading employees:', error);
                    empleadosLoading.style.display = 'none';
                    empleadosErrorDiv.textContent = 'Error al cargar empleados: ' + error.message;
                    empleadosErrorDiv.style.display = 'block';
                });
        }

        function renderEmpleados(empleados, centroEmpleados) {
            empleadosTableBody.innerHTML = '';

            empleados.forEach((empleado, index) => {
                const isChecked = centroEmpleados.includes(empleado.id);
                const row = document.createElement('tr');
                row.style.cursor = 'pointer';
                row.dataset.empleadoId = empleado.id;

                const checkboxId = `empleado-checkbox-${index}`;

                row.innerHTML = `
                    <td class="text-center align-middle">
                        <div class="form-check">
                            <input type="checkbox" id="${checkboxId}" class="form-check-input empleado-checkbox" value="${empleado.id}" ${isChecked ? 'checked' : ''}>
                        </div>
                    </td>
                    <td class="align-middle">${empleado.nombre}</td>
                    <td class="align-middle">${empleado.email || ''}</td>
                `;

                // Add click handler to toggle checkbox when clicking anywhere on the row
                row.addEventListener('click', function(e) {
                    // Don't toggle if clicking on the checkbox or form-check container
                    if (!e.target.closest('.form-check') && !e.target.classList.contains('form-check-input')) {
                        const checkbox = row.querySelector('.empleado-checkbox');
                        checkbox.checked = !checkbox.checked;
                        updateSelectAllEmpleadosState();
                    }
                });

                // Add change handler to checkbox
                const checkbox = row.querySelector('.empleado-checkbox');
                checkbox.addEventListener('change', updateSelectAllEmpleadosState);

                empleadosTableBody.appendChild(row);
            });

            updateSelectAllEmpleadosState();
        }

        function updateSelectAllEmpleadosState() {
            const checkboxes = empleadosTableBody.querySelectorAll('.empleado-checkbox');
            const checkedBoxes = empleadosTableBody.querySelectorAll('.empleado-checkbox:checked');

            if (checkboxes.length === 0) {
                selectAllEmpleados.indeterminate = false;
                selectAllEmpleados.checked = false;
            } else if (checkedBoxes.length === checkboxes.length) {
                selectAllEmpleados.indeterminate = false;
                selectAllEmpleados.checked = true;
            } else if (checkedBoxes.length > 0) {
                selectAllEmpleados.indeterminate = true;
                selectAllEmpleados.checked = false;
            } else {
                selectAllEmpleados.indeterminate = false;
                selectAllEmpleados.checked = false;
            }
        }
		<?php #endregion JS AJAX - Load Employees ?>

        // --- Select All Checkbox Handler ---
        if (selectAllUsuarios) {
            selectAllUsuarios.addEventListener('change', function() {
                const checkboxes = usuariosTableBody.querySelectorAll('.usuario-checkbox');
                checkboxes.forEach(checkbox => {
                    checkbox.checked = selectAllUsuarios.checked;
                });
            });
        }

        if (selectAllEmpleados) {
            selectAllEmpleados.addEventListener('change', function() {
                const checkboxes = empleadosTableBody.querySelectorAll('.empleado-checkbox');
                checkboxes.forEach(checkbox => {
                    checkbox.checked = selectAllEmpleados.checked;
                });
            });
        }

        // --- Save Changes Button Handler ---
		<?php #region JS AJAX - Save Users ?>
        if (guardarUsuariosBtn) {
            guardarUsuariosBtn.addEventListener('click', function() {
                if (!currentCentroId) {
                    usuariosErrorDiv.textContent = 'Error: No se ha seleccionado un centro de costo.';
                    usuariosErrorDiv.style.display = 'block';
                    return;
                }

                // Get selected user IDs
                const selectedUsuarios = [];
                const checkboxes = usuariosTableBody.querySelectorAll('.usuario-checkbox:checked');
                checkboxes.forEach(checkbox => {
                    selectedUsuarios.push(checkbox.value);
                });

                // Disable button during request
                guardarUsuariosBtn.disabled = true;
                const originalText = guardarUsuariosBtn.innerHTML;
                guardarUsuariosBtn.innerHTML = '<i class="fa fa-spinner fa-spin me-1"></i> Guardando...';

                // Prepare form data
                const formData = new FormData();
                formData.append('action', 'sync_usuarios_centro');
                formData.append('centroId', currentCentroId);
                formData.append('usuarios', JSON.stringify(selectedUsuarios));

                fetch('centros-costos', {
                    method: 'POST',
                    body: formData
                })
                    .then(response => {
                        if (!response.ok) {
                            return response.json().then(errData => {
                                throw new Error(errData.message || `Error ${response.status}: ${response.statusText}`);
                            }).catch(() => {
                                throw new Error(`Error ${response.status}: ${response.statusText}`);
                            });
                        }
                        return response.json();
                    })
                    .then(data => {
                        if (data.success) {
                            gestionarUsuariosModal.hide();

                            // Update the user count in the main table
                            updateUserCountInMainTable(currentCentroId);

                            showSweetAlertSuccess('Éxito', 'Usuarios actualizados correctamente.');
                        } else {
                            usuariosErrorDiv.textContent = data.message || 'Error al guardar los cambios.';
                            usuariosErrorDiv.style.display = 'block';
                        }
                    })
                    .catch(error => {
                        console.error('Error saving users:', error);
                        usuariosErrorDiv.textContent = 'Error de red o del servidor: ' + error.message;
                        usuariosErrorDiv.style.display = 'block';
                    })
                    .finally(() => {
                        // Re-enable button
                        guardarUsuariosBtn.disabled = false;
                        guardarUsuariosBtn.innerHTML = originalText;
                    });
            });
        }
		<?php #endregion JS AJAX - Save Users ?>

        // --- Save Employees Changes Button Handler ---
		<?php #region JS AJAX - Save Employees ?>
        if (guardarEmpleadosBtn) {
            guardarEmpleadosBtn.addEventListener('click', function() {
                if (!currentCentroId) {
                    empleadosErrorDiv.textContent = 'Error: No se ha seleccionado un centro de costo.';
                    empleadosErrorDiv.style.display = 'block';
                    return;
                }

                // Get selected employee IDs
                const selectedEmpleados = [];
                const checkboxes = empleadosTableBody.querySelectorAll('.empleado-checkbox:checked');
                checkboxes.forEach(checkbox => {
                    selectedEmpleados.push(checkbox.value);
                });

                // Disable button during request
                guardarEmpleadosBtn.disabled = true;
                const originalText = guardarEmpleadosBtn.innerHTML;
                guardarEmpleadosBtn.innerHTML = '<i class="fa fa-spinner fa-spin me-1"></i> Guardando...';

                // Prepare form data
                const formData = new FormData();
                formData.append('action', 'sync_empleados_centro');
                formData.append('centroId', currentCentroId);
                formData.append('empleados', JSON.stringify(selectedEmpleados));

                fetch('centros-costos', {
                    method: 'POST',
                    body: formData
                })
                    .then(response => {
                        if (!response.ok) {
                            return response.json().then(errData => {
                                throw new Error(errData.message || `Error ${response.status}: ${response.statusText}`);
                            }).catch(() => {
                                throw new Error(`Error ${response.status}: ${response.statusText}`);
                            });
                        }
                        return response.json();
                    })
                    .then(data => {
                        if (data.success) {
                            gestionarEmpleadosModal.hide();

                            // Update the employee count in the main table
                            updateEmployeeCountInMainTable(currentCentroId);

                            showSweetAlertSuccess('Éxito', 'Empleados actualizados correctamente.');
                        } else {
                            empleadosErrorDiv.textContent = data.message || 'Error al guardar los cambios.';
                            empleadosErrorDiv.style.display = 'block';
                        }
                    })
                    .catch(error => {
                        console.error('Error saving employees:', error);
                        empleadosErrorDiv.textContent = 'Error de red o del servidor: ' + error.message;
                        empleadosErrorDiv.style.display = 'block';
                    })
                    .finally(() => {
                        // Re-enable button
                        guardarEmpleadosBtn.disabled = false;
                        guardarEmpleadosBtn.innerHTML = originalText;
                    });
            });
        }
		<?php #endregion JS AJAX - Save Employees ?>

        // --- Update User Count Function ---
        function updateUserCountInMainTable(centroId) {
            // Count the currently selected checkboxes in the modal
            const checkedBoxes = usuariosTableBody.querySelectorAll('.usuario-checkbox:checked');
            const userCount = checkedBoxes.length;

            // Find the corresponding row in the main table
            const tableRow = document.querySelector(`#centro-costo-table-body tr[data-centro-id="${centroId}"]`);
            if (tableRow) {
                // Find the user count cell (third cell, after actions and name)
                const userCountCell = tableRow.children[2]; // 0: actions, 1: name, 2: user count, 3: employee count, 4: status
                if (userCountCell) {
                    // Update the badge content with the new count
                    const badge = userCountCell.querySelector('.badge');
                    if (badge) {
                        badge.textContent = userCount;
                    }
                }
            }
        }

        // --- Update Employee Count Function ---
        function updateEmployeeCountInMainTable(centroId) {
            // Count the currently selected checkboxes in the modal
            const checkedBoxes = empleadosTableBody.querySelectorAll('.empleado-checkbox:checked');
            const employeeCount = checkedBoxes.length;

            // Find the corresponding row in the main table
            const tableRow = document.querySelector(`#centro-costo-table-body tr[data-centro-id="${centroId}"]`);
            if (tableRow) {
                // Find the employee count cell (fourth cell, after actions, name, and user count)
                const employeeCountCell = tableRow.children[3]; // 0: actions, 1: name, 2: user count, 3: employee count, 4: status
                if (employeeCountCell) {
                    // Update the badge content with the new count
                    const badge = employeeCountCell.querySelector('.badge');
                    if (badge) {
                        badge.textContent = employeeCount;
                    }
                }
            }
        }

    });
</script>
<!-- ================== END PAGE LEVEL JS ================== -->

<?php #endregion JS ?>

</body>
</html>
