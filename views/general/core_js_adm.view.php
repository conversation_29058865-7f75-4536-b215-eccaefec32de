<!-- ================== BEGIN core-js ================== -->
<script src="<?php echo RUTA ?>resources/adm_assets/js/vendor.min.js"></script>
<script src="<?php echo RUTA ?>resources/adm_assets/js/app.min.js"></script>
<script src="<?php echo RUTA ?>resources/js/fab2.js"></script>
<!-- ================== END core-js ================== -->

<?php #region region JS sweet alert import ?>
<script src="<?php echo RUTA ?>resources/adm_assets/plugins/sweetalert/dist/sweetalert.min.js"></script>
<?php #endregion JS sweet alert import ?>
<?php #region region JS sweet alert success ?>
<?php if (!empty($success_text)): ?>
    <script type="text/javascript">
        swal({
            text: "<?php echo $success_text; ?>",
            icon: 'success',
            buttons: {
                confirm: {
                    text: 'Ok',
                    value: true,
                    visible: true,
                    className: 'btn btn-success',
                    closeModal: true
                }
            }
        });
    </script>
<?php endif; ?>
<?php #endregion JS sweet alert success ?>
<?php #region region JS sweet alert error ?>
<?php if (!empty($error_text)): ?>
    <script type="text/javascript">
        swal({
            text: "<?php echo $error_text; ?>",
            icon: 'error',
            buttons: {
                confirm: {
                    text: 'Ok',
                    value: true,
                    visible: true,
                    className: 'btn btn-danger',
                    closeModal: true
                }
            }
        });
    </script>
<?php endif; ?>
<?php #endregion JS sweet alert error ?>