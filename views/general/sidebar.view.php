<?php #region docs
/** @var Usuario $logged_usuario_info */
/** @var array $menu_permitido_list */
/** @var array $user_centros_costos */
/** @var CentroCosto|null $selected_centro_costo */
/** @var string $selected_centro_costo_nombre */

use App\classes\Usuario;
use App\classes\Menu;
use App\classes\Submenu;
use App\classes\CentroCosto;

#endregion docs
?>

<!-- Toast Notifications CSS -->
<link href="<?php echo RUTA; ?>resources/css/toast-notifications.css" rel="stylesheet" />

<!-- BEGIN #sidebar -->
<div id="sidebar" class="app-sidebar">
	<!-- BEGIN scrollbar -->
	<div class="app-sidebar-content" data-scrollbar="true" data-height="100%">
		<!-- BEGIN menu -->
		<div class="menu fs-15px spacing-08em">
			<div class="menu-profile">
				<a href="#" class="menu-profile-link" data-toggle="app-sidebar-profile" data-target="#appSidebarProfileMenu">
					<div class="menu-profile-info">
						<div class="d-flex align-items-center">
							<div class="flex-grow-1">
								<?php echo limpiar_datos($logged_usuario_info->getNombre()); ?>
							</div>
						</div>
					</div>
				</a>
			</div>

			<!-- Centro de Costo Selector -->
			<?php if (!empty($user_centros_costos)): ?>
			<div class="menu-item centro-costo-selector">
				<div class="menu-text">
					<div class="d-flex align-items-center justify-content-between">
						<small class="text-muted">Centro de Costo:</small>
					</div>
					<?php if (count($user_centros_costos) > 1): ?>
					<!-- Multiple centro de costos - show dropdown -->
					<select id="centroCostoSelect" class="form-select form-select-sm mt-1" style="font-size: 0.8rem;">
						<?php foreach ($user_centros_costos as $centro): ?>
						<option value="<?php echo $centro->getId(); ?>"
							<?php echo ($selected_centro_costo && $centro->getId() === $selected_centro_costo->getId()) ? 'selected' : ''; ?>>
							<?php echo htmlspecialchars($centro->getNombre()); ?>
						</option>
						<?php endforeach; ?>
					</select>
					<?php else: ?>
					<!-- Single centro de costo - show as text -->
					<div class="text-white-50" style="font-size: 0.8rem;" id="centroCostoDisplay">
						<?php echo htmlspecialchars($selected_centro_costo_nombre); ?>
					</div>
					<?php endif; ?>
				</div>
			</div>
			<?php elseif (empty($user_centros_costos)): ?>
			<!-- No centro de costos assigned -->
			<div class="menu-item centro-costo-selector">
				<div class="menu-text">
					<div class="d-flex align-items-center justify-content-between">
						<small class="text-muted">Centro de Costo:</small>
					</div>
					<div class="text-warning" style="font-size: 0.8rem;">
						<i class="fa fa-exclamation-triangle me-1"></i>
						Sin asignar
					</div>
				</div>
			</div>
			<?php endif; ?>
			<div class="menu-header">Menu</div>
			<?php #region region menu item dashboard ?>
			<div class="menu-item">
				<a href="<?php echo RUTA ?>dashboard" class="menu-link">
					<div class="menu-icon">
						<i class="fab fa-simplybuilt"></i>
					</div>
					<div class="menu-text">Dashboard</div>
				</a>
			</div>
			<?php #endregion menu item dashboard ?>
			<?php #region region dynamic menus ?>
			<?php foreach ($menu_permitido_list as $menu): ?>
				<?php if ($menu->hasSubs() && !empty($menu->submenus)): ?>
				<!-- Menu with submenus -->
				<div class="menu-item has-sub">
					<a href="#" class="menu-link">
						<div class="menu-icon">
							<i class="<?php echo htmlspecialchars($menu->getIcono() ?: 'fa fa-circle'); ?>"></i>
						</div>
						<div class="menu-text"><?php echo htmlspecialchars($menu->getTexto()); ?></div>
						<div class="menu-caret"></div>
					</a>
					<div class="menu-submenu">
						<?php foreach ($menu->submenus as $submenu): ?>
						<div class="menu-item">
							<a href="<?php echo RUTA . htmlspecialchars($submenu->getUrl()); ?>" class="menu-link">
								<div class="menu-text"><?php echo htmlspecialchars($submenu->getTexto()); ?></div>
							</a>
						</div>
						<?php endforeach; ?>
					</div>
				</div>
				<?php else: ?>
				<!-- Regular menu item without submenus -->
				<div class="menu-item">
					<a href="<?php echo RUTA . htmlspecialchars($menu->getUrl()); ?>" class="menu-link">
						<div class="menu-icon">
							<i class="<?php echo htmlspecialchars($menu->getIcono() ?: 'fa fa-circle'); ?>"></i>
						</div>
						<div class="menu-text"><?php echo htmlspecialchars($menu->getTexto()); ?></div>
					</a>
				</div>
				<?php endif; ?>
			<?php endforeach; ?>
			<?php #endregion dynamic menus ?>
			<?php #region region menu item cerrar sesion ?>
			<div class="menu-item">
				<a href="<?php echo RUTA ?>cerrar" class="menu-link">
					<div class="menu-icon">
						<i class="fa fa-power-off"></i>
					</div>
					<div class="menu-text">Cerrar sesión</div>
				</a>
			</div>
			<?php #endregion menu item cerrar sesion ?>

			<!-- BEGIN minify-button -->
			<div class="menu-item d-flex">
				<a href="#" class="app-sidebar-minify-btn ms-auto" data-toggle="app-sidebar-minify"><i class="fa fa-angle-double-left"></i></a>
			</div>
			<!-- END minify-button -->
		</div>
		<!-- END menu -->
	</div>
	<!-- END scrollbar -->
</div>
<div class="app-sidebar-bg"></div>
<div class="app-sidebar-mobile-backdrop"><a href="#" data-dismiss="app-sidebar-mobile" class="stretched-link"></a></div>
<!-- END #sidebar -->

<!-- Toast Container for Centro de Costo Notifications -->
<div class="toast-container position-fixed top-0 end-0 p-3" style="z-index: 1055;">
    <div id="centro-costo-toast-notification" class="toast" role="alert" aria-live="assertive" aria-atomic="true">
        <div class="toast-header">
            <i id="centro-costo-toast-icon" class="fa fa-check-circle text-success me-2"></i>
            <strong id="centro-costo-toast-title" class="me-auto">Centro de Costo</strong>
            <button type="button" class="btn-close" data-bs-dismiss="toast" aria-label="Close"></button>
        </div>
        <div class="toast-body" id="centro-costo-toast-message">
            Mensaje de notificación
        </div>
    </div>
</div>

<!-- Centro de Costo Switching JavaScript -->
<script>
document.addEventListener('DOMContentLoaded', function() {
    const centroCostoSelect = document.getElementById('centroCostoSelect');

    // Toast notification function for centro de costo switching
    function showCentroCostoToastNotification(type, title, message) {
        const toast = document.getElementById('centro-costo-toast-notification');
        const toastIcon = document.getElementById('centro-costo-toast-icon');
        const toastTitle = document.getElementById('centro-costo-toast-title');
        const toastMessage = document.getElementById('centro-costo-toast-message');

        // Configure toast based on type
        if (type === 'success') {
            toastIcon.className = 'fa fa-check-circle text-success me-2';
            toast.className = 'toast border-success';
        } else if (type === 'error') {
            toastIcon.className = 'fa fa-exclamation-circle text-danger me-2';
            toast.className = 'toast border-danger';
        } else if (type === 'warning') {
            toastIcon.className = 'fa fa-exclamation-triangle text-warning me-2';
            toast.className = 'toast border-warning';
        } else {
            toastIcon.className = 'fa fa-info-circle text-info me-2';
            toast.className = 'toast border-info';
        }

        toastTitle.textContent = title;
        toastMessage.textContent = message;

        // Show toast with appropriate settings
        const bsToast = new bootstrap.Toast(toast, {
            autohide: type === 'success', // Auto-hide success, manual dismiss for errors
            delay: type === 'success' ? 3000 : 0 // 3 seconds for success, no auto-hide for errors
        });
        bsToast.show();
    }

    if (centroCostoSelect) {
        centroCostoSelect.addEventListener('change', function() {
            const selectedCentroCostoId = this.value;

            if (!selectedCentroCostoId) {
                return;
            }

            // Show loading state
            const originalText = this.options[this.selectedIndex].text;
            this.disabled = true;

            // Make AJAX request to switch centro de costo
            fetch('<?php echo RUTA; ?>switch-centro-costo', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    centro_costo_id: parseInt(selectedCentroCostoId)
                })
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    // Show success toast notification
                    showCentroCostoToastNotification('success', 'Centro de Costo', data.message);

                    // Reload page to reflect changes
                    setTimeout(() => {
                        window.location.reload();
                    }, 1500);
                } else {
                    // Show error toast notification
                    showCentroCostoToastNotification('error', 'Error', data.message || 'Error al cambiar centro de costo');

                    // Reset select to previous value
                    this.disabled = false;
                }
            })
            .catch(error => {
                console.error('Error:', error);

                // Show error toast notification
                showCentroCostoToastNotification('error', 'Error de Conexión', 'Error de conexión al cambiar centro de costo');

                // Reset select
                this.disabled = false;
            });
        });
    }
});
</script>

<style>
.centro-costo-selector {
    padding: 0.5rem 1rem;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    margin-bottom: 0.5rem;
}

.centro-costo-selector .menu-text {
    color: #fff;
}

.centro-costo-selector .form-select {
    background-color: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    color: #fff;
}

.centro-costo-selector .form-select:focus {
    background-color: rgba(255, 255, 255, 0.15);
    border-color: rgba(255, 255, 255, 0.3);
    box-shadow: 0 0 0 0.2rem rgba(255, 255, 255, 0.1);
    color: #fff;
}

.centro-costo-selector .form-select option {
    background-color: #2d3748;
    color: #fff;
}
</style>
