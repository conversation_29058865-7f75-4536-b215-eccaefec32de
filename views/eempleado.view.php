<?php #region DOCS
/** @var Empleado $empleado */
/** @var int $id */
/** @var string $nombre */
/** @var string $email */
/** @var string $telefono */
/** @var string $direccion */
/** @var string $fecha_ingreso */
/** @var float $porc_comision */
/** @var Servicio[] $servicios Lista de todos los servicios activos */
/** @var Servicio[] $servicios_empleado Lista de servicios asociados al empleado */
/** @var string|null $success_text Mensaje de éxito para mostrar */
/** @var string|null $success_display Si se debe mostrar el mensaje de éxito ('show' o null) */
/** @var string|null $error_text Mensaje de error para mostrar */
/** @var string|null $error_display Si se debe mostrar el mensaje de error ('show' o null) */

use App\classes\Empleado;
use App\classes\Servicio;

#endregion DOCS
?>
<!DOCTYPE html>
<html lang="en" class="dark-mode">
<head>
	<meta charset="utf-8"/>
	<title><?php echo APP_NAME; ?> | Editar Empleado</title>
	<meta content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no" name="viewport"/>
	<meta content="" name="description"/>
	<meta content="" name="author"/>

	<?php #region HEAD ?>
	<?php require_once __ROOT__ . '/views/general/head.view.php'; ?>
	<?php #endregion HEAD ?>
    <style>
        /* Style for invalid fields */
        .is-invalid {
            border-color: #dc3545 !important; /* Bootstrap's default danger color */
        }
        /* Style for validation messages */
        .invalid-feedback {
            display: none; /* Hide by default */
            width: 100%;
            margin-top: .25rem;
            font-size: .875em; /* 14px if base is 16px */
            color: #dc3545;
        }
        .is-invalid ~ .invalid-feedback {
            display: block; /* Show when input is invalid */
        }
    </style>
    <!-- Include Bootstrap Datepicker -->
    <link href="<?php echo RUTA ?>resources/adm_assets/plugins/bootstrap-datepicker/dist/css/bootstrap-datepicker.css" rel="stylesheet" />
</head>
<body>
<!-- BEGIN #loader -->
<div id="loader" class="app-loader">
	<span class="spinner"></span>
</div>
<!-- END #loader -->

<!-- BEGIN #app -->
<div id="app" class="app app-header-fixed app-sidebar-fixed">
	<!-- #header -->
	<?php require_once __ROOT__ . '/views/general/header.view.php'; ?>

	<!-- #sidebar -->
	<?php require_once __ROOT__ . '/views/general/sidebar.view.php'; ?>

	<!-- BEGIN #content -->
	<div id="content" class="app-content">
		<?php #region region PAGE HEADER ?>
        <div class="d-flex align-items-center mb-3">
            <div>
                <h4 class="mb-0">Editar Empleado</h4>
                <p class="mb-0 text-muted">Modifique los detalles del empleado.</p>
            </div>
            <div class="ms-auto">
                <a href="lempleados" class="btn btn-secondary"><i class="fa fa-arrow-left fa-fw me-1"></i> Volver a la Lista</a>
            </div>
        </div>
		<hr>
		<?php #endregion PAGE HEADER ?>

		<?php #region region TABS NAVIGATION ?>
		<!-- Tabs Navigation -->
		<ul class="nav nav-tabs nav-tabs-v2" id="empleado-tabs" role="tablist">
			<li class="nav-item me-2" role="presentation">
				<a href="#info-tab-pane" class="nav-link active" id="info-tab" data-bs-toggle="tab" role="tab" aria-controls="info-tab-pane" aria-selected="true">
					<i class="fa fa-info-circle fa-fw me-1"></i> Información General
				</a>
			</li>
			<li class="nav-item" role="presentation">
				<a href="#servicios-tab-pane" class="nav-link" id="servicios-tab" data-bs-toggle="tab" role="tab" aria-controls="servicios-tab-pane" aria-selected="false">
					<i class="fa fa-list fa-fw me-1"></i> Servicios
				</a>
			</li>
		</ul>
		<?php #endregion TABS NAVIGATION ?>

		<?php #region region TABS CONTENT ?>
		<!-- Tabs Content -->
		<div class="tab-content pt-3" id="empleado-tabs-content">
			<!-- Información General Tab -->
			<div class="tab-pane fade show active" id="info-tab-pane" role="tabpanel" aria-labelledby="info-tab">
				<?php #region region FORM ?>
				<form action="eempleado?id=<?php echo $id; ?>" method="POST" id="edit-empleado-form" novalidate> <?php /* novalidate prevents default browser validation */ ?>
					<?php #region region PANEL EMPLEADO DETAILS ?>
					<div class="panel panel-inverse no-border-radious">
						<div class="panel-heading no-border-radious">
							<h4 class="panel-title">Detalles del Empleado</h4>
							<div class="panel-heading-btn">
								<a href="javascript:;" class="btn btn-xs btn-icon btn-default" data-toggle="panel-expand"><i class="fa fa-expand"></i></a>
								<a href="javascript:;" class="btn btn-xs btn-icon btn-warning" data-toggle="panel-collapse"><i class="fa fa-minus"></i></a>
							</div>
						</div>
						<div class="panel-body">
							<div class="mb-3">
								<label for="nombre" class="form-label">Nombre <span class="text-danger">*</span></label>
								<input type="text" class="form-control" id="nombre" name="nombre" value="<?php echo htmlspecialchars($nombre ?? ''); ?>" required>
								<div class="invalid-feedback" id="nombre-error">El nombre es requerido.</div>
							</div>

							<div class="mb-3">
								<label for="email" class="form-label">Correo <span class="text-danger">*</span></label>
								<input type="email" class="form-control" id="email" name="email" value="<?php echo htmlspecialchars($email ?? ''); ?>" required>
								<div class="invalid-feedback" id="email-error">El formato del correo no es válido.</div>
							</div>

							<div class="mb-3">
								<label for="telefono" class="form-label">Teléfono</label>
								<input type="text" class="form-control" id="telefono" name="telefono" value="<?php echo htmlspecialchars($telefono ?? ''); ?>">
							</div>

							<div class="mb-3">
								<label for="direccion" class="form-label">Dirección</label>
								<input type="text" class="form-control" id="direccion" name="direccion" value="<?php echo htmlspecialchars($direccion ?? ''); ?>">
							</div>

							<div class="mb-3">
								<label for="fecha_ingreso" class="form-label">Fecha Ingreso</label>
								<div class="input-group">
									<input type="text" class="form-control datepicker" id="fecha_ingreso" name="fecha_ingreso"
										   value="<?php echo htmlspecialchars($fecha_ingreso ?? ''); ?>"
										   data-date-format="yyyy-mm-dd">
									<span class="input-group-text date-icon" id="fecha_ingreso_icon">
										<i class="fa fa-calendar"></i>
									</span>
								</div>
								<div class="invalid-feedback" id="fecha-ingreso-error">El formato de la fecha debe ser YYYY-MM-DD.</div>
							</div>

							<div class="mb-3">
								<label for="porc_comision" class="form-label">% Comisión <span class="text-danger">*</span></label>
								<div class="input-group">
									<input type="number" class="form-control" id="porc_comision" name="porc_comision"
										   value="<?php echo htmlspecialchars($porc_comision ?? '0.00'); ?>"
										   step="0.01" min="0" max="100" placeholder="0.00" required>
									<span class="input-group-text">%</span>
								</div>
								<div class="invalid-feedback" id="porc-comision-error">El porcentaje de comisión debe estar entre 0 y 100.</div>
							</div>
						</div>
						<div class="panel-footer text-end">
							<button type="submit" class="btn btn-primary"><i class="fa fa-save fa-fw me-1"></i> Guardar Cambios</button>
							<a href="lempleados" class="btn btn-secondary"><i class="fa fa-times fa-fw me-1"></i> Cancelar</a>
						</div>
					</div>
					<?php #endregion PANEL EMPLEADO DETAILS ?>
				</form>
				<?php #endregion FORM ?>
			</div>

			<!-- Servicios Tab -->
			<div class="tab-pane fade" id="servicios-tab-pane" role="tabpanel" aria-labelledby="servicios-tab">
				<?php #region region SERVICIOS FORM ?>
				<form id="servicios-form" novalidate>
					<input type="hidden" name="action" value="guardar_servicios">
					<input type="hidden" name="ajax" value="true">
					<input type="hidden" name="empleado_id" value="<?php echo $id; ?>">

					<?php #region region PANEL SERVICIOS ?>
					<div class="panel panel-inverse no-border-radious">
						<div class="panel-heading no-border-radious">
							<h4 class="panel-title">Servicios Asociados</h4>
							<div class="panel-heading-btn">
								<a href="javascript:;" class="btn btn-xs btn-icon btn-default" data-toggle="panel-expand"><i class="fa fa-expand"></i></a>
								<a href="javascript:;" class="btn btn-xs btn-icon btn-warning" data-toggle="panel-collapse"><i class="fa fa-minus"></i></a>
							</div>
						</div>
						<div class="panel-body">
							<p class="mb-3">Seleccione los servicios que este empleado puede proporcionar:</p>

							<div class="row">
								<?php
								// Create an array of IDs for easy checking
								$servicios_empleado_ids = array_map(function($servicio) {
									return $servicio->getId();
								}, $servicios_empleado);

								// Display all services in a grid
								foreach ($servicios as $servicio):
									$checked = in_array($servicio->getId(), $servicios_empleado_ids) ? 'checked' : '';
								?>
								<div class="col-md-6 col-lg-4 mb-3">
									<div class="form-check">
										<input class="form-check-input" type="checkbox" name="servicios[]"
											   value="<?php echo $servicio->getId(); ?>"
											   id="servicio-<?php echo $servicio->getId(); ?>"
											   <?php echo $checked; ?>>
										<label class="form-check-label" for="servicio-<?php echo $servicio->getId(); ?>">
											<?php echo htmlspecialchars($servicio->getDescripcion()); ?>
											<span class="text-muted ms-2">
												(<?php echo format_currency_consigno($servicio->getValor()); ?>)
											</span>
										</label>
									</div>
								</div>
								<?php endforeach; ?>
							</div>

							<div id="servicios-error" class="alert alert-danger mt-3" style="display: none;"></div>
						</div>
						<div class="panel-footer text-end">
							<button type="submit" class="btn btn-primary" id="btn-guardar-servicios">
								<i class="fa fa-save fa-fw me-1"></i> Guardar Servicios
							</button>
						</div>
					</div>
					<?php #endregion PANEL SERVICIOS ?>
				</form>
				<?php #endregion SERVICIOS FORM ?>
			</div>
		</div>
		<?php #endregion TABS CONTENT ?>
	</div>
	<!-- END #content -->

	<!-- BEGIN scroll-top-btn -->
	<a href="#" class="btn btn-icon btn-circle btn-success btn-scroll-to-top" data-toggle="scroll-to-top"><i class="fa fa-angle-up"></i></a>
	<!-- END scroll-top-btn -->
</div>
<!-- END #app -->

<?php #region region JS ?>
<?php require_once __ROOT__ . '/views/general/core_js.view.php'; ?>

<!-- Include Bootstrap Datepicker JS -->
<script src="<?php echo RUTA ?>resources/adm_assets/plugins/bootstrap-datepicker/dist/js/bootstrap-datepicker.js"></script>

<?php #region region CLIENT-SIDE VALIDATION & INPUT HANDLING SCRIPT ?>
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Initialize datepicker
    $('.datepicker').datepicker({
        autoclose: true,
        todayHighlight: true,
        format: 'yyyy-mm-dd'
    });

    // Make calendar icon clickable
    document.getElementById('fecha_ingreso_icon').addEventListener('click', function() {
        $('#fecha_ingreso').datepicker('show');
    });

    // --- General Information Tab Validation ---
    const form = document.getElementById('edit-empleado-form');
    const nombreInput = document.getElementById('nombre');
    const emailInput = document.getElementById('email');
    const fechaIngresoInput = document.getElementById('fecha_ingreso');
    const porcComisionInput = document.getElementById('porc_comision');

    // Error message elements
    const nombreError = document.getElementById('nombre-error');
    const emailError = document.getElementById('email-error');
    const fechaIngresoError = document.getElementById('fecha-ingreso-error');
    const porcComisionError = document.getElementById('porc-comision-error');

    if (form) {
        form.addEventListener('submit', function(event) {
            let isValid = true;

            // Reset previous validation states
            [nombreInput, emailInput, fechaIngresoInput, porcComisionInput].forEach(input => {
                input.classList.remove('is-invalid');
            });

            // Validate Nombre
            if (nombreInput.value.trim() === '') {
                isValid = false;
                nombreInput.classList.add('is-invalid');
                nombreError.textContent = 'El nombre es requerido.';
            }

            // Validate Email (required field)
            if (emailInput.value.trim() === '') {
                isValid = false;
                emailInput.classList.add('is-invalid');
                emailError.textContent = 'El correo es requerido.';
            } else if (!isValidEmail(emailInput.value)) {
                isValid = false;
                emailInput.classList.add('is-invalid');
                emailError.textContent = 'El formato del correo no es válido.';
            }

            // Validate Fecha Ingreso format if provided
            if (fechaIngresoInput.value.trim() !== '' && !isValidDate(fechaIngresoInput.value)) {
                isValid = false;
                fechaIngresoInput.classList.add('is-invalid');
                fechaIngresoError.textContent = 'El formato de la fecha debe ser YYYY-MM-DD.';
            }

            // Validate Porcentaje Comisión (now mandatory)
            if (porcComisionInput.value.trim() === '') {
                isValid = false;
                porcComisionInput.classList.add('is-invalid');
                porcComisionError.textContent = 'El porcentaje de comisión es requerido.';
            } else {
                const porcComision = parseFloat(porcComisionInput.value);
                if (isNaN(porcComision) || porcComision <= 0 || porcComision > 100) {
                    isValid = false;
                    porcComisionInput.classList.add('is-invalid');
                    porcComisionError.textContent = 'El porcentaje de comisión debe ser mayor que 0 y no mayor que 100.';
                }
            }

            // Prevent form submission if validation fails
            if (!isValid) {
                event.preventDefault();
                event.stopPropagation();
                // Optional: focus the first invalid field
                const firstInvalid = form.querySelector('.is-invalid');
                if (firstInvalid) {
                    firstInvalid.focus();
                }
            }
        });

        // Optional: Real-time validation feedback as user types (or on blur)
        [nombreInput, emailInput, fechaIngresoInput, porcComisionInput].forEach(input => {
            input.addEventListener('input', () => {
                // Simple check to remove invalid state when user starts typing
                if (input.classList.contains('is-invalid')) {
                    input.classList.remove('is-invalid');
                }
            });
        });
    }

    // --- Services Tab AJAX Form Submission ---
    const serviciosForm = document.getElementById('servicios-form');
    const serviciosError = document.getElementById('servicios-error');
    const btnGuardarServicios = document.getElementById('btn-guardar-servicios');

    if (serviciosForm) {
        serviciosForm.addEventListener('submit', function(event) {
            event.preventDefault();

            // Show loading state
            if (btnGuardarServicios) {
                btnGuardarServicios.disabled = true;
                btnGuardarServicios.innerHTML = '<i class="fa fa-spinner fa-spin fa-fw me-1"></i> Guardando...';
            }

            // Hide previous error messages
            if (serviciosError) {
                serviciosError.style.display = 'none';
                serviciosError.textContent = '';
            }

            // Get form data
            const formData = new FormData(serviciosForm);

            // Ensure employee_id is included and has a value
            const empleadoId = '<?php echo $id; ?>';
            console.log('Employee ID from PHP: ' + empleadoId);

            // Always set the employee_id explicitly to ensure it's included
            // Convert to number to ensure it's treated as an integer
            formData.set('empleado_id', parseInt(empleadoId, 10));

            // Debug: Log form data
            console.log('Form data being sent:');
            for (let pair of formData.entries()) {
                console.log(pair[0] + ': ' + pair[1]);
            }

            // Send AJAX request
            fetch('eempleado', {
                method: 'POST',
                body: formData,
                headers: {
                    'X-Requested-With': 'XMLHttpRequest',
                    'Accept': 'application/json'
                }
            })
            .then(response => {
                // Check if response is OK
                if (!response.ok) {
                    throw new Error(`HTTP error! Status: ${response.status}`);
                }

                // Check content type
                const contentType = response.headers.get('content-type');
                if (!contentType || !contentType.includes('application/json')) {
                    // If not JSON, get the text and throw an error with the response text
                    return response.text().then(text => {
                        throw new Error(`Expected JSON but got ${contentType}\n\nResponse:\n${text.substring(0, 500)}...`);
                    });
                }

                return response.json();
            })
            .then(data => {
                // Reset button state
                if (btnGuardarServicios) {
                    btnGuardarServicios.disabled = false;
                    btnGuardarServicios.innerHTML = '<i class="fa fa-save fa-fw me-1"></i> Guardar Servicios';
                }

                if (data.success) {
                    // Show success message
                    showSweetAlertSuccess('¡Éxito!', data.message);
                } else {
                    // Show error message
                    if (serviciosError) {
                        serviciosError.textContent = data.message;
                        serviciosError.style.display = 'block';
                    }
                    showSweetAlertError('Error', data.message);
                }
            })
            .catch(error => {
                // Reset button state
                if (btnGuardarServicios) {
                    btnGuardarServicios.disabled = false;
                    btnGuardarServicios.innerHTML = '<i class="fa fa-save fa-fw me-1"></i> Guardar Servicios';
                }

                // Show detailed error message
                const errorMessage = error.message || 'Error de conexión. Por favor, intente nuevamente.';

                if (serviciosError) {
                    serviciosError.textContent = errorMessage;
                    serviciosError.style.display = 'block';
                }

                // Show a simpler error message in the alert
                showSweetAlertError('Error', 'Error al guardar servicios. Por favor, intente nuevamente.');

                // Log the full error to console for debugging
                console.error('Error:', error);
            });
        });
    }

    // Helper function to validate email format
    function isValidEmail(email) {
        const re = /^(([^<>()\[\]\\.,;:\s@"]+(\.[^<>()\[\]\\.,;:\s@"]+)*)|(".+"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,}))$/;
        return re.test(email.toLowerCase());
    }

    // Helper function to validate date format (YYYY-MM-DD)
    function isValidDate(dateString) {
        // First check for the pattern
        if(!/^\d{4}-\d{2}-\d{2}$/.test(dateString)) return false;

        // Parse the date parts to integers
        const parts = dateString.split("-");
        const year = parseInt(parts[0], 10);
        const month = parseInt(parts[1], 10);
        const day = parseInt(parts[2], 10);

        // Check the ranges of month and day
        if(year < 1000 || year > 3000 || month == 0 || month > 12) return false;

        const monthLength = [31, 28, 31, 30, 31, 30, 31, 31, 30, 31, 30, 31];

        // Adjust for leap years
        if(year % 400 == 0 || (year % 100 != 0 && year % 4 == 0)) monthLength[1] = 29;

        // Check the range of the day
        return day > 0 && day <= monthLength[month - 1];
    }

    // --- Handle Success/Error Messages (from PHP redirects) ---
    <?php if (isset($success_display) && $success_display === 'show'): ?>
    showSweetAlertSuccess('¡Éxito!', '<?php echo addslashes($success_text ?? "Operación completada con éxito."); ?>');
    <?php endif; ?>

    <?php if (isset($error_display) && $error_display === 'show'): ?>
    showSweetAlertError('Error', '<?php echo addslashes($error_text ?? "Ha ocurrido un error."); ?>');
    <?php endif; ?>
});
</script>
<?php #endregion CLIENT-SIDE VALIDATION & INPUT HANDLING SCRIPT ?>
<?php #endregion JS ?>

</body>
</html>
