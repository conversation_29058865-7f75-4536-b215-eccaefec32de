<?php #region docs
// No specific variables needed for this view

#endregion docs
?>
<!DOCTYPE html>
<html lang="en" class="dark-mode">
<head>
	<meta charset="utf-8"/>
	<title><?php echo APP_NAME; ?> | <PERSON><PERSON>r Proveedor</title>
	<meta content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no" name="viewport"/>
	<meta content="" name="description"/>
	<meta content="" name="author"/>

	<?php #region HEAD ?>
	<?php require_once __ROOT__ . '/views/general/head.view.php'; ?>
	<?php #endregion HEAD ?>
    <style>
        /* Style for invalid fields */
        .is-invalid {
            border-color: #dc3545 !important; /* Bootstrap's default danger color */
        }
        /* Style for validation messages */
        .invalid-feedback {
            display   : none;     /* Hide by default */
            width     : 100%;
            margin-top: .25rem;
            font-size : .875em;   /* 14px if base is 16px */
            color     : #dc3545;
        }
        .is-invalid ~ .invalid-feedback {
            display: block; /* Show when input is invalid */
        }
        /* Style for mandatory fields */
        .required-field {
            color      : #dc3545;
            font-weight: bold;
        }
    </style>
</head>
<body>
<!-- BEGIN #loader -->
<div id="loader" class="app-loader">
	<span class="spinner"></span>
</div>
<!-- END #loader -->

<!-- BEGIN #app -->
<div id="app" class="app app-header-fixed app-sidebar-fixed">
	<!-- #header -->
	<?php require_once __ROOT__ . '/views/general/header.view.php'; ?>

	<!-- #sidebar -->
	<?php require_once __ROOT__ . '/views/general/sidebar.view.php'; ?>

	<!-- BEGIN #content -->
	<div id="content" class="app-content">
		<?php #region region PAGE HEADER ?>
        <div class="d-flex align-items-center mb-3">
            <div>
                <h4 class="mb-0">Crear Nuevo Proveedor</h4>
                <p class="mb-0 text-muted">Ingrese los detalles del nuevo proveedor. El proveedor se creará como activo.</p>
            </div>
            <div class="ms-auto">
                <a href="lproveedores" class="btn btn-secondary"><i class="fa fa-arrow-left fa-fw me-1"></i> Volver a la Lista</a>
            </div>
        </div>
		<hr>
		<?php #endregion PAGE HEADER ?>

		<?php #region region FORM ?>
		<form action="iproveedor" method="POST" id="create-proveedor-form" novalidate> <?php /* novalidate prevents default browser validation */ ?>
            <?php #region region PANEL PROVEEDOR DETAILS ?>
            <div class="panel panel-inverse no-border-radious">
                <div class="panel-heading no-border-radious">
                    <h4 class="panel-title">Detalles del Proveedor</h4>
                    <div class="panel-heading-btn">
                        <a href="javascript:;" class="btn btn-xs btn-icon btn-default" data-toggle="panel-expand"><i class="fa fa-expand"></i></a>
                        <a href="javascript:;" class="btn btn-xs btn-icon btn-warning" data-toggle="panel-collapse"><i class="fa fa-minus"></i></a>
                    </div>
                </div>
                <div class="panel-body">
                    <div class="mb-3">
                        <label for="nombre" class="form-label">Nombre <span class="required-field">*</span></label>
                        <input type="text" class="form-control" id="nombre" name="nombre"
                               value="<?php echo htmlspecialchars($_POST['nombre'] ?? ''); ?>" required>
                        <div class="invalid-feedback" id="nombre-error">El nombre es requerido.</div>
                    </div>

                    <div class="mb-3">
                        <label for="nit" class="form-label">NIT</label>
                        <input type="text" class="form-control" id="nit" name="nit"
                               value="<?php echo htmlspecialchars($_POST['nit'] ?? ''); ?>">
                        <div class="invalid-feedback" id="nit-error"></div>
                    </div>

                    <div class="mb-3">
                        <label for="telefono" class="form-label">Teléfono</label>
                        <input type="text" class="form-control" id="telefono" name="telefono"
                               value="<?php echo htmlspecialchars($_POST['telefono'] ?? ''); ?>">
                        <div class="invalid-feedback" id="telefono-error"></div>
                    </div>

                    <div class="mb-3">
                        <label for="correo" class="form-label">Correo Electrónico</label>
                        <input type="email" class="form-control" id="correo" name="correo"
                               value="<?php echo htmlspecialchars($_POST['correo'] ?? ''); ?>">
                        <div class="invalid-feedback" id="correo-error">El formato del correo no es válido.</div>
                    </div>

                    <div class="mb-3">
                        <label for="direccion" class="form-label">Dirección</label>
                        <textarea class="form-control" id="direccion" name="direccion" rows="3"><?php echo htmlspecialchars($_POST['direccion'] ?? ''); ?></textarea>
                        <div class="invalid-feedback" id="direccion-error"></div>
                    </div>
                </div>
                <div class="panel-footer text-end">
                    <button type="submit" class="btn btn-primary"><i class="fa fa-save fa-fw me-1"></i> Crear Proveedor</button>
                    <a href="lproveedores" class="btn btn-secondary"><i class="fa fa-times fa-fw me-1"></i> Cancelar</a>
                </div>
            </div>
            <?php #endregion PANEL PROVEEDOR DETAILS ?>
		</form>
		<?php #endregion FORM ?>
	</div>
	<!-- END #content -->

	<!-- BEGIN scroll-top-btn -->
	<a href="#" class="btn btn-icon btn-circle btn-success btn-scroll-to-top" data-toggle="scroll-to-top"><i class="fa fa-angle-up"></i></a>
	<!-- END scroll-top-btn -->
</div>
<!-- END #app -->

<?php #region region JS ?>
<?php require_once __ROOT__ . '/views/general/core_js.view.php'; ?>

<?php #region region CLIENT-SIDE VALIDATION & INPUT HANDLING SCRIPT ?>
<script>
document.addEventListener('DOMContentLoaded', function() {
    const form = document.getElementById('create-proveedor-form');
    const nombreInput = document.getElementById('nombre');
    const correoInput = document.getElementById('correo');

    // Error message elements
    const nombreError = document.getElementById('nombre-error');
    const correoError = document.getElementById('correo-error');

    // Email validation function
    function isValidEmail(email) {
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        return emailRegex.test(email);
    }

    if (form) {
        form.addEventListener('submit', function(event) {
            let isValid = true;

            // Reset previous validation states
            [nombreInput, correoInput].forEach(input => {
                input.classList.remove('is-invalid');
            });

            // Validate Nombre (required)
            if (nombreInput.value.trim() === '') {
                isValid = false;
                nombreInput.classList.add('is-invalid');
                nombreError.textContent = 'El nombre es requerido.';
            }

            // Validate Email format if provided
            if (correoInput.value.trim() !== '' && !isValidEmail(correoInput.value)) {
                isValid = false;
                correoInput.classList.add('is-invalid');
                correoError.textContent = 'El formato del correo no es válido.';
            }

            // Prevent form submission if validation fails
            if (!isValid) {
                event.preventDefault();
                event.stopPropagation();
                // Focus the first invalid field
                const firstInvalid = form.querySelector('.is-invalid');
                if (firstInvalid) {
                    firstInvalid.focus();
                }
            }
        });

        // Real-time validation feedback as user types
        [nombreInput, correoInput].forEach(input => {
            input.addEventListener('input', () => {
                // Remove invalid state when user starts typing
                if (input.classList.contains('is-invalid')) {
                    input.classList.remove('is-invalid');
                }
            });
        });
    }
});
</script>
<?php #endregion CLIENT-SIDE VALIDATION ?>

<?php #endregion JS ?>

</body>
</html>
