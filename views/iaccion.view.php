<?php
#region DOCS

/** @var int|null $id ID of the action being edited, null for new actions */
/** @var string $nombre Name of the action */
/** @var string $grupo Group of the action */
/** @var int|null $id_menu ID of the menu this action belongs to, null if not associated with a menu */
/** @var int|null $id_submenu ID of the submenu this action belongs to, null if not associated with a submenu */
/** @var int $estado Status of the action (1 = active, 0 = inactive) */
/** @var bool $is_edit_mode Whether we're in edit mode (true) or create mode (false) */
/** @var Menu[] $menus Array of menu objects for dropdown selection */
/** @var Submenu[] $submenus Array of submenu objects for dropdown selection */
/** @var string $error_text Error message text if any */
/** @var string $error_display Whether to show error message ('show') or not ('') */

use App\classes\Accion;
use App\classes\Menu;
use App\classes\Submenu;

#endregion DOCS
?>
<!DOCTYPE html>
<html lang="en" class="dark-mode">
<head>
	<meta charset="utf-8"/>
	<title><?php echo APP_NAME; ?> | <?php echo $is_edit_mode ? 'Editar' : 'Crear'; ?> Acción</title>
	<meta content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no" name="viewport"/>
	<meta content="" name="description"/>
	<meta content="" name="author"/>

	<?php #region HEAD ?>
	<?php require_once __ROOT__ . '/views/general/head.view.php'; ?>
	<?php #endregion HEAD ?>
    <style>
        /* Style for invalid fields */
        .is-invalid {
            border-color: #dc3545 !important; /* Bootstrap's default danger color */
        }
        /* Style for validation messages */
        .invalid-feedback {
            display: none; /* Hide by default */
            width: 100%;
            margin-top: .25rem;
            font-size: .875em; /* 14px if base is 16px */
            color: #dc3545;
        }
        .is-invalid ~ .invalid-feedback {
            display: block; /* Show when input is invalid */
        }
    </style>
</head>
<body>
<!-- BEGIN #loader -->
<div id="loader" class="app-loader">
	<span class="spinner"></span>
</div>
<!-- END #loader -->

<!-- BEGIN #app -->
<div id="app" class="app app-header-fixed app-sidebar-fixed">
	<!-- #header -->
	<?php require_once __ROOT__ . '/views/general/header.view.php'; ?>

	<!-- #sidebar -->
	<?php require_once __ROOT__ . '/views/general/sidebar.view.php'; ?>

	<!-- BEGIN #content -->
	<div id="content" class="app-content">
		<?php #region region PAGE HEADER ?>
        <div class="d-flex align-items-center mb-3">
            <div>
                <h4 class="mb-0"><?php echo $is_edit_mode ? 'Editar' : 'Crear Nueva'; ?> Acción</h4>
                <p class="mb-0 text-muted">Ingrese los detalles de la acción.</p>
            </div>
            <div class="ms-auto">
                <a href="lacciones" class="btn btn-secondary"><i class="fa fa-arrow-left fa-fw me-1"></i> Volver a la Lista</a>
            </div>
        </div>
		<hr>
		<?php #endregion PAGE HEADER ?>

		<?php #region region FORM ?>
		<form action="iaccion" method="POST" id="accion-form" novalidate>
            <?php if ($is_edit_mode): ?>
            <input type="hidden" name="id" value="<?php echo $id; ?>">
            <?php endif; ?>

            <?php #region region PANEL ACCION DETAILS ?>
            <div class="panel panel-inverse no-border-radious">
                <div class="panel-heading no-border-radious">
                    <h4 class="panel-title">Detalles de la Acción</h4>
                    <div class="panel-heading-btn">
                        <a href="javascript:;" class="btn btn-xs btn-icon btn-default" data-toggle="panel-expand"><i class="fa fa-expand"></i></a>
                        <a href="javascript:;" class="btn btn-xs btn-icon btn-warning" data-toggle="panel-collapse"><i class="fa fa-minus"></i></a>
                    </div>
                </div>
                <div class="panel-body">

                    <div class="mb-3">
                        <label for="nombre" class="form-label">Nombre <span class="text-danger">*</span></label>
                        <input type="text" class="form-control" id="nombre" name="nombre" value="<?php echo htmlspecialchars($nombre ?? ''); ?>" required>
                        <div class="invalid-feedback" id="nombre-error">El nombre de la acción es requerido.</div>
                    </div>

                    <div class="mb-3">
                        <label for="grupo" class="form-label">Grupo</label>
                        <input type="text" class="form-control" id="grupo" name="grupo" value="<?php echo htmlspecialchars($grupo ?? ''); ?>">
                        <div class="invalid-feedback" id="grupo-error">Formato de grupo inválido.</div>
                    </div>

                    <div class="row mb-3">
                        <div class="col-md-6">
                            <label for="id_menu" class="form-label">Menú</label>
                            <select class="form-select" id="id_menu" name="id_menu">
                                <option value="">-- Seleccione un menú --</option>
                                <?php foreach ($menus as $menu): ?>
                                <option value="<?php echo $menu->getId(); ?>" <?php echo ($id_menu == $menu->getId()) ? 'selected' : ''; ?>>
                                    <?php echo htmlspecialchars($menu->getTexto()); ?>
                                </option>
                                <?php endforeach; ?>
                            </select>
                        </div>
                        <div class="col-md-6">
                            <label for="id_submenu" class="form-label">Submenú</label>
                            <select class="form-select" id="id_submenu" name="id_submenu" <?php echo empty($id_menu) ? 'disabled' : ''; ?>>
                                <option value="">-- Seleccione un submenú --</option>
                                <?php foreach ($submenus as $submenu): ?>
                                <option value="<?php echo $submenu->getId(); ?>" <?php echo ($id_submenu == $submenu->getId()) ? 'selected' : ''; ?>>
                                    <?php echo htmlspecialchars($submenu->getTexto()); ?>
                                </option>
                                <?php endforeach; ?>
                            </select>
                        </div>
                    </div>

                    <?php if ($is_edit_mode): ?>
                    <div class="mb-3">
                        <label class="form-label">Estado</label>
                        <div class="form-check form-switch">
                            <input type="checkbox" class="form-check-input" id="estado" name="estado" value="1" <?php echo ($estado == 1) ? 'checked' : ''; ?>>
                            <label class="form-check-label" for="estado">Activo</label>
                        </div>
                    </div>
                    <?php endif; ?>

                </div>
                <div class="panel-footer text-end">
                    <button type="submit" class="btn btn-primary"><i class="fa fa-save fa-fw me-1"></i> Guardar Acción</button>
                    <a href="lacciones" class="btn btn-secondary"><i class="fa fa-times fa-fw me-1"></i> Cancelar</a>
                </div>
            </div>
            <?php #endregion PANEL ACCION DETAILS ?>
		</form>
		<?php #endregion FORM ?>
	</div>
	<!-- END #content -->

	<!-- BEGIN scroll-top-btn -->
	<a href="#" class="btn btn-icon btn-circle btn-success btn-scroll-to-top" data-toggle="scroll-to-top"><i class="fa fa-angle-up"></i></a>
	<!-- END scroll-top-btn -->
</div>
<!-- END #app -->

<?php #region region JS ?>
<?php require_once __ROOT__ . '/views/general/core_js.view.php'; ?>

<?php #region region CLIENT-SIDE VALIDATION & INPUT HANDLING SCRIPT ?>
<script>
document.addEventListener('DOMContentLoaded', function() {
    const form = document.getElementById('accion-form');
    const nombreInput = document.getElementById('nombre');
    const grupoInput = document.getElementById('grupo');
    const menuSelect = document.getElementById('id_menu');
    const submenuSelect = document.getElementById('id_submenu');

    // Error message elements
    const nombreError = document.getElementById('nombre-error');
    const grupoError = document.getElementById('grupo-error');

    // Handle menu change to update submenu options
    menuSelect.addEventListener('change', function() {
        const menuId = this.value;

        // Reset submenu dropdown
        submenuSelect.innerHTML = '<option value="">-- Seleccione un submenú --</option>';

        if (menuId) {
            // Enable submenu dropdown
            submenuSelect.disabled = false;

            // Fetch submenus for the selected menu
            fetch(`get_submenus?id_menu=${menuId}`)
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        // Add options to submenu dropdown
                        data.submenus.forEach(submenu => {
                            const option = document.createElement('option');
                            option.value = submenu.id;
                            option.textContent = submenu.texto;
                            submenuSelect.appendChild(option);
                        });
                    } else {
                        console.error('Error loading submenus:', data.message);
                    }
                })
                .catch(error => {
                    console.error('Error fetching submenus:', error);
                });
        } else {
            // Disable submenu dropdown if no menu is selected
            submenuSelect.disabled = true;
        }
    });

    // Form validation
    form.addEventListener('submit', function(event) {
        let isValid = true;

        // Reset previous validation states
        [nombreInput, grupoInput].forEach(input => {
            input.classList.remove('is-invalid');
        });
        [nombreError, grupoError].forEach(errorEl => {
            errorEl.textContent = ''; // Clear previous messages
        });

        // Validate Nombre
        if (nombreInput.value.trim() === '') {
            isValid = false;
            nombreInput.classList.add('is-invalid');
            nombreError.textContent = 'El nombre de la acción es requerido.';
        }

        // Prevent form submission if validation fails
        if (!isValid) {
            event.preventDefault();
            event.stopPropagation();
            // Focus the first invalid field
            const firstInvalid = form.querySelector('.is-invalid');
            if (firstInvalid) {
                firstInvalid.focus();
            }
        }
    });

    // Real-time validation feedback as user types
    [nombreInput, grupoInput].forEach(input => {
        input.addEventListener('input', () => {
            // Remove invalid state when user starts typing
            if (input.classList.contains('is-invalid')) {
                input.classList.remove('is-invalid');
            }
        });
    });
});
</script>
<?php #endregion CLIENT-SIDE VALIDATION & INPUT HANDLING SCRIPT ?>
<?php #endregion JS ?>

</body>
</html>
