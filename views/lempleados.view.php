<?php
#region region DOCS

/** @var Empleado[] $empleados */

use App\classes\Empleado;

#endregion DOCS
?>
<!DOCTYPE html>
<html lang="en" class="dark-mode">
<head>
	<meta charset="utf-8"/>
	<title><?php echo APP_NAME; ?> | Empleados</title>
	<meta content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no" name="viewport"/>
	<meta content="" name="description"/>
	<meta content="" name="author"/>

	<?php #region HEAD ?>
	<?php require_once __ROOT__ . '/views/general/head.view.php'; ?>
	<?php #endregion HEAD ?>
</head>
<body>
<!-- BEGIN #loader -->
<div id="loader" class="app-loader">
	<span class="spinner"></span>
</div>
<!-- END #loader -->

<!-- BEGIN #app -->
<div id="app" class="app app-header-fixed app-sidebar-fixed">
	<!-- #header -->
	<?php require_once __ROOT__ . '/views/general/header.view.php'; ?>

	<!-- #sidebar -->
	<?php require_once __ROOT__ . '/views/general/sidebar.view.php'; ?>

	<!-- BEGIN #content -->
	<div id="content" class="app-content">
		<?php #region region PAGE HEADER ?>
		<div class="d-flex align-items-center mb-3">
			<div>
				<h4 class="mb-0">Gestión de Empleados</h4>
				<p class="mb-0 text-muted">Administra los empleados del sistema</p>
			</div>
			<div class="ms-auto">
				<a href="iempleado" class="btn btn-success"><i class="fa fa-plus-circle fa-fw me-1"></i> Crear nuevo</a>
			</div>
		</div>

		<hr>
		<?php #endregion PAGE HEADER ?>

		<?php #region region PANEL EMPLEADOS ?>
		<div class="panel panel-inverse mt-3 no-border-radious">
			<div class="panel-heading no-border-radious">
				<h4 class="panel-title">
					Listado de Empleados Activos
				</h4>
			</div>
			<!-- BEGIN PANEL body -->
			<div class="p-1 table-nowrap" style="overflow: auto">
				<?php #region region TABLE EMPLEADOS ?>
				<table class="table table-hover table-sm">
					<thead>
					<tr>
						<th class="text-center">Acciones</th>
						<th>Nombre</th>
						<th>Correo</th>
						<th>Teléfono</th>
						<th>Dirección</th>
						<th>Fecha Ingreso</th>
						<th class="text-end">% Comisión</th>
					</tr>
					</thead>
					<tbody class="fs-12px" id="empleado-table-body">
					<?php foreach ($empleados as $empleado): ?>
						<tr data-empleado-id="<?php echo $empleado->getId(); ?>">
							<td class="text-center">
								<?php // Edit Button - Redirects to eempleado.php ?>
								<a href="eempleado?id=<?php echo $empleado->getId(); ?>" class="btn btn-xs btn-primary me-1" title="Editar Empleado">
									<i class="fa fa-edit"></i>
								</a>
								<?php // Deactivate Button - Only show if active ?>
								<?php if ($empleado->isActivo()): ?>
									<button type="button" class="btn btn-xs btn-danger btn-desactivar-empleado"
									        title="Desactivar"
									        data-empleadoid="<?php echo $empleado->getId(); ?>"
									        data-nombre="<?php echo htmlspecialchars($empleado->getNombre() ?? ''); ?>">
										<i class="fa fa-trash-alt"></i>
									</button>
								<?php endif; ?>
							</td>
							<td class="empleado-nombre-cell"><?php echo htmlspecialchars($empleado->getNombre()); ?></td>
							<td><?php echo htmlspecialchars($empleado->getEmail() ?? ''); ?></td>
							<td><?php echo htmlspecialchars($empleado->getTelefono() ?? ''); ?></td>
							<td><?php echo htmlspecialchars($empleado->getDireccion() ?? ''); ?></td>
							<td><?php echo $empleado->getFecha_ingreso() ? date('Y-m-d', strtotime($empleado->getFecha_ingreso())) : ''; ?></td>
							<td class="text-end"><?php echo $empleado->getPorc_comision() !== null ? number_format($empleado->getPorc_comision(), 2) . '%' : '0.00%'; ?></td>
						</tr>
					<?php endforeach; ?>
					<?php if (empty($empleados)): ?>
						<tr>
							<td colspan="7" class="text-center">No hay empleados para mostrar.</td>
						</tr>
					<?php endif; ?>

					</tbody>
				</table>
				<?php #endregion TABLE EMPLEADOS ?>
			</div>
			<!-- END PANEL body -->
		</div>
		<?php #endregion PANEL EMPLEADOS ?>

	</div>
	<!-- END #content -->

	<?php #region region Hidden Form for Deactivation ?>
	<form id="deactivate-empleado-form" method="POST" action="lempleados" style="display: none;">
		<input type="hidden" name="action" value="desactivar">
		<input type="hidden" name="empleadoId" id="deactivate-empleado-id">
	</form>
	<?php #endregion Hidden Form ?>

	<!-- BEGIN scroll-top-btn -->
	<a href="#" class="btn btn-icon btn-circle btn-success btn-scroll-to-top" data-toggle="scroll-to-top"><i class="fa fa-angle-up"></i></a>
	<!-- END scroll-top-btn -->
</div>
<!-- END #app -->

<?php #region region JS ?>
<?php require_once __ROOT__ . '/views/general/core_js.view.php'; ?>

<!-- ================== BEGIN PAGE LEVEL JS ================== -->
<!-- Include Bootstrap Datepicker -->
<link href="<?php echo RUTA ?>resources/adm_assets/plugins/bootstrap-datepicker/dist/css/bootstrap-datepicker.css" rel="stylesheet" />
<script src="<?php echo RUTA ?>resources/adm_assets/plugins/bootstrap-datepicker/dist/js/bootstrap-datepicker.js"></script>

<script>
    document.addEventListener('DOMContentLoaded', function () {
        // Use event delegation on the table body
        const tableBody = document.getElementById('empleado-table-body');

        if (tableBody) {
            tableBody.addEventListener('click', function (event) {
                const deactivateButton = event.target.closest('.btn-desactivar-empleado');

                // --- Handle Deactivate Click ---
                if (deactivateButton) {
                    event.preventDefault(); // Prevent default if it was an <a>
                    const empleadoId = deactivateButton.dataset.empleadoid;
                    const empleadoNombre = deactivateButton.dataset.nombre;

                    // Use SweetAlert confirmation dialog
                    swal({
                        title: '¿Estás seguro?',
                        text: `¿Deseas desactivar al empleado ${empleadoNombre}?`,
                        icon: 'warning',
                        buttons: {
                            cancel: {
                                text: 'Cancelar',
                                value: null,
                                visible: true,
                                className: 'btn btn-default',
                                closeModal: true
                            },
                            confirm: {
                                text: 'Sí, desactivar',
                                value: true,
                                visible: true,
                                className: 'btn btn-danger',
                                closeModal: true
                            }
                        }
                    }).then((result) => {
                        if (result) {
                            // Set the ID in the hidden form and submit
                            document.getElementById('deactivate-empleado-id').value = empleadoId;
                            document.getElementById('deactivate-empleado-form').submit();
                        }
                    });
                }
            });
        }

        // --- Handle Success/Error Messages (from PHP redirects) ---
        <?php if (isset($success_display) && $success_display === 'show'): ?>
        showSweetAlertSuccess('¡Éxito!', '<?php echo addslashes($success_text ?? "Operación completada con éxito."); ?>');
        <?php endif; ?>

        <?php if (isset($error_display) && $error_display === 'show'): ?>
        showSweetAlertError('Error', '<?php echo addslashes($error_text ?? "Ha ocurrido un error."); ?>');
        <?php endif; ?>
    });
</script>
<!-- ================== END PAGE LEVEL JS ================== -->
<?php #endregion JS ?>

</body>
</html>
