<?php
#region DOCS

/** @var Servicio[] $servicios */
/** @var string|null $success_text Success message to display */
/** @var string|null $success_display Whether to show success message ('show' or null) */
/** @var string|null $error_text Error message to display */
/** @var string|null $error_display Whether to show error message ('show' or null) */

use App\classes\Servicio;

#endregion DOCS
?>
<!DOCTYPE html>
<html lang="en" class="dark-mode">
<head>
	<meta charset="utf-8"/>
	<title><?php echo APP_NAME; ?> | Servicios</title>
	<meta content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no" name="viewport"/>
	<meta content="" name="description"/>
	<meta content="" name="author"/>

	<?php #region HEAD ?>
	<?php require_once __ROOT__ . '/views/general/head.view.php'; ?>
	<?php #endregion HEAD ?>

	<!-- CSS for currency formatting -->
	<style>
        input.currency-input {
            text-align: right;
        }
    </style>
</head>
<body>
<!-- BEGIN #loader -->
<div id="loader" class="app-loader">
	<span class="spinner"></span>
</div>
<!-- END #loader -->

<!-- BEGIN #app -->
<div id="app" class="app app-header-fixed app-sidebar-fixed">
	<!-- #header -->
	<?php require_once __ROOT__ . '/views/general/header.view.php'; ?>

	<!-- #sidebar -->
	<?php require_once __ROOT__ . '/views/general/sidebar.view.php'; ?>

	<!-- BEGIN #content -->
	<div id="content" class="app-content">
		<?php #region PAGE HEADER ?>
		<div class="d-flex align-items-center mb-3">
			<div>
				<h4 class="mb-0">Gestión de Servicios</h4>
				<p class="mb-0 text-muted">Administra los servicios del sistema</p>
			</div>
			<div class="ms-auto">
				<button type="button" class="btn btn-success" data-bs-toggle="modal" data-bs-target="#createServicioModal">
					<i class="fa fa-plus-circle fa-fw me-1"></i> Crear nuevo
				</button>
			</div>
		</div>
		<?php #endregion PAGE HEADER ?>

		<?php #region PANEL SERVICIOS ?>
		<div class="panel panel-inverse mt-3 no-border-radious">
			<div class="panel-heading no-border-radious">
				<h4 class="panel-title">
					Listado de Servicios Activos
				</h4>
			</div>
			<!-- BEGIN PANEL body -->
			<div class="p-1 table-nowrap" style="overflow: auto">
				<?php #region TABLE SERVICIOS ?>
				<table class="table table-hover table-sm">
					<thead>
					<tr>
						<th class="w-70px text-center">Acciones</th>
						<th>Descripción</th>
						<th class="text-center">Valor</th>
						<th class="text-center">Duración (min)</th>
					</tr>
					</thead>
					<tbody class="fs-12px" id="servicio-table-body">
					<?php foreach ($servicios as $servicio): ?>
						<tr data-servicio-id="<?php echo $servicio->getId(); ?>">
							<td class="text-center">
								<?php // Edit Button - Triggers Modal ?>
								<button type="button" class="btn btn-xs btn-primary me-1 btn-edit-servicio"
								        title="Editar Servicio"
								        data-bs-toggle="modal"
								        data-bs-target="#editServicioModal"
								        data-servicio-id="<?php echo $servicio->getId(); ?>"
								        data-descripcion="<?php echo htmlspecialchars($servicio->getDescripcion() ?? ''); ?>"
								        data-valor="<?php echo htmlspecialchars($servicio->getValor()); ?>"
									        data-duracion="<?php echo htmlspecialchars($servicio->getDuracion() ?? ''); ?>">
									<i class="fa fa-edit"></i>
								</button>
								<?php // Deactivate Button - Only show if active ?>
								<?php if ($servicio->isActivo()): ?>
									<button type="button" class="btn btn-xs btn-danger btn-desactivar-servicio"
									        title="Desactivar"
									        data-servicio-id="<?php echo $servicio->getId(); ?>"
									        data-descripcion="<?php echo htmlspecialchars($servicio->getDescripcion() ?? ''); ?>">
										<i class="fa fa-trash-alt"></i>
									</button>
								<?php endif; ?>
							</td>
							<td class="servicio-descripcion-cell"><?php echo htmlspecialchars($servicio->getDescripcion()); ?></td>
							<td class="servicio-valor-cell text-end"><?php echo format_currency_consigno($servicio->getValor()); ?></td>
								<td class="servicio-duracion-cell text-center"><?php echo $servicio->getDuracion() ? htmlspecialchars($servicio->getDuracion()) : '-'; ?></td>
						</tr>
					<?php endforeach; ?>
					</tbody>
				</table>
				<?php #endregion TABLE SERVICIOS ?>
			</div>
			<!-- END PANEL body -->
		</div>
		<?php #endregion PANEL SERVICIOS ?>

		<?php #region Create Servicio Modal ?>
		<div class="modal fade" id="createServicioModal" tabindex="-1" aria-labelledby="createServicioModalLabel" aria-hidden="true">
			<div class="modal-dialog">
				<div class="modal-content">
					<form id="create-servicio-form">
						<div class="modal-header">
							<h5 class="modal-title" id="createServicioModalLabel">Crear Servicio</h5>
							<button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
						</div>
						<div class="modal-body">
							<input type="hidden" name="action" value="crear">

							<div class="mb-3">
								<label for="create-servicio-descripcion" class="form-label">Descripción:</label>
								<input type="text" class="form-control" id="create-servicio-descripcion" name="descripcion" required>
							</div>

							<div class="mb-3">
								<label for="create-servicio-valor" class="form-label">Valor:</label>
								<input type="text" class="form-control currency-input" id="create-servicio-valor" name="valor"
								       data-type="currency" placeholder="$0" required>
							</div>

							<div class="mb-3">
								<label for="create-servicio-duracion" class="form-label">Duración (minutos):</label>
								<input type="number" class="form-control" id="create-servicio-duracion" name="duracion"
								       min="1" placeholder="30">
							</div>


						</div>
						<div class="modal-footer">
							<button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancelar</button>
							<button type="submit" class="btn btn-primary">Guardar</button>
						</div>
					</form>
				</div>
			</div>
		</div>
		<?php #endregion Create Servicio Modal ?>

		<?php #region Edit Servicio Modal ?>
		<div class="modal fade" id="editServicioModal" tabindex="-1" aria-labelledby="editServicioModalLabel" aria-hidden="true">
			<div class="modal-dialog">
				<div class="modal-content">
					<form id="edit-servicio-form">
						<div class="modal-header">
							<h5 class="modal-title" id="editServicioModalLabel">Editar Servicio</h5>
							<button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
						</div>
						<div class="modal-body">
							<input type="hidden" name="servicioId" id="edit-servicio-id">
							<input type="hidden" name="action" value="modificar">

							<div class="mb-3">
								<label for="edit-servicio-descripcion" class="form-label">Descripción:</label>
								<input type="text" class="form-control" id="edit-servicio-descripcion" name="descripcion" required>
							</div>

							<div class="mb-3">
								<label for="edit-servicio-valor" class="form-label">Valor:</label>
								<input type="text" class="form-control currency-input" id="edit-servicio-valor" name="valor"
								       data-type="currency" placeholder="$0" required>
							</div>

							<div class="mb-3">
								<label for="edit-servicio-duracion" class="form-label">Duración (minutos):</label>
								<input type="number" class="form-control" id="edit-servicio-duracion" name="duracion"
								       min="1" placeholder="30">
							</div>


						</div>
						<div class="modal-footer">
							<button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancelar</button>
							<button type="submit" class="btn btn-primary">Actualizar</button>
						</div>
					</form>
				</div>
			</div>
		</div>
		<?php #endregion Edit Servicio Modal ?>

	</div>
	<!-- END #content -->

	<?php #region Hidden Form for Deactivation ?>
	<form id="deactivate-servicio-form" method="POST" action="servicios" style="display: none;">
		<input type="hidden" name="action" value="desactivar">
		<input type="hidden" name="servicioId" id="deactivate-servicio-id">
	</form>
	<?php #endregion Hidden Form ?>

	<!-- BEGIN scroll-top-btn -->
	<a href="#" class="btn btn-icon btn-circle btn-success btn-scroll-to-top" data-toggle="scroll-to-top"><i class="fa fa-angle-up"></i></a>
	<!-- END scroll-top-btn -->
</div>
<!-- END #app -->

<?php #region JS ?>
<?php require_once __ROOT__ . '/views/general/core_js.view.php'; ?>

<!-- ================== BEGIN PAGE LEVEL JS ================== -->
<!-- Include formatcurrency.js for currency formatting -->
<script src="<?php echo RUTA_RESOURCES; ?>js/formatcurrency.js"></script>
<script>
    // Initialize currency formatting for dynamically added elements
    $(document).ready(function() {
        // Re-initialize currency inputs when modals are shown
        $('#createServicioModal').on('shown.bs.modal', function () {
            $('input[data-type="currency"]').each(function() {
                $(this).on({
                    keyup: function() { formatCurrency($(this)); },
                    blur: function() { formatCurrency($(this), "blur"); }
                });
            });
        });

        $('#editServicioModal').on('shown.bs.modal', function () {
            $('input[data-type="currency"]').each(function() {
                $(this).on({
                    keyup: function() { formatCurrency($(this)); },
                    blur: function() { formatCurrency($(this), "blur"); }
                });
            });
        });
    });
</script>

<script>
    document.addEventListener('DOMContentLoaded', function () {
        // Get DOM elements
        const tableBody = document.getElementById('servicio-table-body');
        const createServicioForm = document.getElementById('create-servicio-form');
        const editServicioForm = document.getElementById('edit-servicio-form');

        const editServicioIdInput = document.getElementById('edit-servicio-id');
        const editServicioDescripcionInput = document.getElementById('edit-servicio-descripcion');
        const editServicioValorInput = document.getElementById('edit-servicio-valor');
        const editServicioDuracionInput = document.getElementById('edit-servicio-duracion');
        const deactivateServicioForm = document.getElementById('deactivate-servicio-form');
        const deactivateServicioIdInput = document.getElementById('deactivate-servicio-id');

        // Bootstrap modal instances
        const createServicioModal = new bootstrap.Modal(document.getElementById('createServicioModal'));
        const editServicioModal = new bootstrap.Modal(document.getElementById('editServicioModal'));

        // Use event delegation on the table body
        if (tableBody) {
            tableBody.addEventListener('click', function (event) {
                // Find the closest button if it was clicked or a child of it was clicked
                const deactivateButton = event.target.closest('.btn-desactivar-servicio');
                const editButton = event.target.closest('.btn-edit-servicio');

                // --- Handle Deactivate Click ---
                if (deactivateButton) {
                    event.preventDefault(); // Prevent default if it was an <a>
                    const servicioId = deactivateButton.dataset.servicioId;
                    const descripcion = deactivateButton.dataset.descripcion;

                    // Confirm before deactivating using SweetAlert2
                    swal({
                        title: 'Confirmar desactivación',
                        text: `¿Está seguro que desea desactivar el servicio "${descripcion}"?`,
                        icon: 'warning',
                        buttons: {
                            cancel: {
                                text: 'Cancelar',
                                value: null,
                                visible: true,
                                className: 'btn btn-default',
                                closeModal: true
                            },
                            confirm: {
                                text: 'Sí, desactivar',
                                value: true,
                                visible: true,
                                className: 'btn btn-danger',
                                closeModal: true
                            }
                        }
                    }).then((result) => {
                        if (result) {
                            // Set the ID in the hidden form and submit it
                            deactivateServicioIdInput.value = servicioId;
                            deactivateServicioForm.submit();
                        }
                    });
                }

                // --- Handle Edit Click ---
                if (editButton) {
                    event.preventDefault(); // Prevent default if it was an <a>
                    const servicioId = editButton.dataset.servicioId;
                    const descripcion = editButton.dataset.descripcion;
                    const valor = editButton.dataset.valor;
                    const duracion = editButton.dataset.duracion || '';

                    console.log('Edit button clicked with valor:', valor);

                    // Format the valor for display in the input
                    const valorFormateado = formatCurrencyValue(valor);

                    console.log('Formatted valor:', valorFormateado);

                    // Populate the modal form
                    editServicioIdInput.value = servicioId;
                    editServicioDescripcionInput.value = descripcion;
                    editServicioValorInput.value = valorFormateado;
                    editServicioDuracionInput.value = duracion;

                    // The data-bs-toggle and data-bs-target attributes on the button handle showing the modal
                }
            });
        }

        // --- Handle Create Form Submission (AJAX) ---
        if (createServicioForm) {
            createServicioForm.addEventListener('submit', function (event) {
                event.preventDefault(); // Stop default form submission
                // Form submission handling

                const formData = new FormData(createServicioForm);
                const descripcion = formData.get('descripcion').trim(); // Get trimmed description
                const valor = formData.get('valor').trim(); // Get trimmed valor

                // Basic client-side validation
                if (!descripcion) {
                    showSweetAlertError('Error de validación', 'La descripción no puede estar vacía.');
                    return; // Stop submission
                }

                if (!valor || valor === '$0') {
                    showSweetAlertError('Error de validación', 'El valor debe ser mayor que cero.');
                    return; // Stop submission
                }

                // Disable submit button during request
                const submitButton = createServicioForm.querySelector('button[type="submit"]');
                submitButton.disabled = true;

                fetch('servicios', {
                    method: 'POST',
                    body: formData
                })
                    .then(response => {
                        if (!response.ok) {
                            return response.json().then(errData => {
                                throw new Error(errData.message || `Error ${response.status}: ${response.statusText}`);
                            }).catch(() => {
                                throw new Error(`Error ${response.status}: ${response.statusText}`);
                            });
                        }
                        return response.json();
                    })
                    .then(data => {
                        if (data.success) {
                            createServicioModal.hide(); // Close modal on success

                            // Reset form for next use
                            createServicioForm.reset();

                            // Show success message using SweetAlert2
                            showSweetAlertSuccess('¡Éxito!', data.message);

                            // Create new row HTML
                            const newRow = document.createElement('tr');
                            newRow.setAttribute('data-servicio-id', data.id);

                            newRow.innerHTML = `
                                <td class="text-center">
                                    <button type="button" class="btn btn-xs btn-primary me-1 btn-edit-servicio"
                                            title="Editar Servicio"
                                            data-bs-toggle="modal"
                                            data-bs-target="#editServicioModal"
                                            data-servicio-id="${data.id}"
                                            data-descripcion="${data.descripcion}"
                                            data-valor="${parseFloat(data.valor.replace(/\$/g, '').replace(/\./g, '').replace(/,/g, '.'))}"
                                            data-duracion="${data.duracion || ''}">
                                        <i class="fa fa-edit"></i>
                                    </button>
                                    <button type="button" class="btn btn-xs btn-danger btn-desactivar-servicio"
                                            title="Desactivar"
                                            data-servicio-id="${data.id}"
                                            data-descripcion="${data.descripcion}">
                                        <i class="fa fa-trash-alt"></i>
                                    </button>
                                </td>
                                <td class="servicio-descripcion-cell">${data.descripcion}</td>
                                <td class="servicio-valor-cell text-end">${data.valor}</td>
                                <td class="servicio-duracion-cell text-center">${data.duracion || '-'}</td>
                            `;

                            // Add the new row to the table
                            tableBody.appendChild(newRow);
                        } else {
                            // Show error using SweetAlert2
                            showSweetAlertError('Error', data.message);
                        }
                    })
                    .catch(error => {
                        // Show error using SweetAlert2
                        showSweetAlertError('Error', error.message);
                    })
                    .finally(() => {
                        // Re-enable submit button
                        submitButton.disabled = false;
                    });
            });
        }

        // --- Handle Edit Form Submission (AJAX) ---
        if (editServicioForm) {
            editServicioForm.addEventListener('submit', function (event) {
                event.preventDefault(); // Stop default form submission
                // Form submission handling

                const formData = new FormData(editServicioForm);
                const servicioId = formData.get('servicioId');
                const nuevaDescripcion = formData.get('descripcion').trim(); // Get trimmed description
                const nuevoValor = formData.get('valor').trim(); // Get trimmed valor

                // Basic client-side validation
                if (!nuevaDescripcion) {
                    showSweetAlertError('Error de validación', 'La descripción no puede estar vacía.');
                    return; // Stop submission
                }

                if (!nuevoValor || nuevoValor === '$0') {
                    showSweetAlertError('Error de validación', 'El valor debe ser mayor que cero.');
                    return; // Stop submission
                }

                // Disable submit button during request
                const submitButton = editServicioForm.querySelector('button[type="submit"]');
                submitButton.disabled = true;

                fetch('servicios', {
                    method: 'POST',
                    body: formData
                })
                    .then(response => {
                        if (!response.ok) {
                            return response.json().then(errData => {
                                throw new Error(errData.message || `Error ${response.status}: ${response.statusText}`);
                            }).catch(() => {
                                throw new Error(`Error ${response.status}: ${response.statusText}`);
                            });
                        }
                        return response.json();
                    })
                    .then(data => {
                        if (data.success) {
                            editServicioModal.hide(); // Close modal on success

                            // Show success message using SweetAlert2
                            showSweetAlertSuccess('¡Éxito!', data.message);

                            // Update the row in the table
                            const row = document.querySelector(`tr[data-servicio-id="${servicioId}"]`);
                            if (row) {
                                const descripcionCell = row.querySelector('.servicio-descripcion-cell');
                                const valorCell = row.querySelector('.servicio-valor-cell');
                                const duracionCell = row.querySelector('.servicio-duracion-cell');
                                const editButton = row.querySelector('.btn-edit-servicio');
                                const deactivateButton = row.querySelector('.btn-desactivar-servicio');

                                if (descripcionCell) descripcionCell.textContent = nuevaDescripcion;
                                if (valorCell) valorCell.textContent = data.valor_formateado;
                                // Update the duration cell with the new value
                                if (duracionCell) duracionCell.textContent = data.duracion ? data.duracion : '-';

                                // Update data attributes for future edits
                                if (editButton) {
                                    editButton.dataset.descripcion = nuevaDescripcion;
                                    // Update the valor data attribute with the raw value
                                    // Handle Colombian Peso format correctly
                                    const cleanValor = nuevoValor.replace(/\$/g, '').replace(/\./g, '').replace(/,/g, '.');
                                    editButton.dataset.valor = parseFloat(cleanValor);
                                    // Update the duracion data attribute
                                    editButton.dataset.duracion = data.duracion || '';
                                    // This will be formatted when the edit modal is opened
                                }

                                if (deactivateButton) {
                                    deactivateButton.dataset.descripcion = nuevaDescripcion;
                                }
                            }
                        } else {
                            // Show error using SweetAlert2
                            showSweetAlertError('Error', data.message);
                        }
                    })
                    .catch(error => {
                        // Show error using SweetAlert2
                        showSweetAlertError('Error', error.message);
                    })
                    .finally(() => {
                        // Re-enable submit button
                        submitButton.disabled = false;
                    });
            });
        }

        // Helper function to format currency value for display in input
        function formatCurrencyValue(value) {
            // Convert to number and format as currency
            console.log('Formatting value:', value, 'Type:', typeof value);
            let numValue;
            try {
                // Make sure we're working with a clean number
                if (typeof value === 'string') {
                    // Handle Colombian Peso format: remove $ and convert thousands separators
                    // First, remove the currency symbol
                    let cleanValue = value.replace(/\$/g, '');
                    // Then, remove all dots (thousands separators in Colombian format)
                    cleanValue = cleanValue.replace(/\./g, '');
                    // Finally, replace commas with dots for decimal points
                    cleanValue = cleanValue.replace(/,/g, '.');

                    console.log('Cleaned value for parsing:', cleanValue);
                    value = cleanValue;
                }
                numValue = parseFloat(value);
                console.log('Parsed value:', numValue);
            } catch (e) {
                console.error('Error parsing value:', e);
                return '$0';
            }

            if (isNaN(numValue)) {
                console.warn('Value is NaN after parsing');
                return '$0';
            }

            try {
                // Format with thousands separator for Colombian Peso
                // Use Math.round to avoid decimal places
                const formatted = '$' + Math.round(numValue).toLocaleString('es-CO').replace(/,/g, '.');
                console.log('Final formatted value:', formatted);
                return formatted;
            } catch (e) {
                console.error('Error formatting value:', e);
                return '$' + Math.round(numValue);
            }
        }
    });
</script>
<!-- ================== END PAGE LEVEL JS ================== -->
<?php #endregion JS ?>
</body>
</html>
