<?php
/**
 * Vista para el reporte de ventas y comisiones
 *
 * Variables disponibles:
 * @var CentroCosto[] $centros_costos Lista de centros de costo activos
 * @var array|null $reporte_data Datos del reporte generado
 * @var string $fecha_inicio Fecha de inicio del reporte
 * @var string $fecha_fin Fecha de fin del reporte
 * @var int|null $id_centro_costo ID del centro de costo seleccionado
 * @var string $centro_costo_nombre Nombre del centro de costo seleccionado
 * @var string|null $success_text Mensaje de éxito a mostrar
 * @var string|null $success_display Estado de visualización de éxito ('show' o null)
 * @var string|null $error_text Mensaje de error a mostrar
 * @var string|null $error_display Estado de visualización de error ('show' o null)
 */

use App\classes\CentroCosto;
?>

<!DOCTYPE html>
<html lang="en" class="dark-mode">
<head>
	<meta charset="utf-8"/>
	<title><?php echo APP_NAME; ?> | Reporte de Ventas y Comisiones</title>
	<meta content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no" name="viewport"/>
	<meta content="" name="description"/>
	<meta content="" name="author"/>

	<?php #region HEAD ?>
	<?php require_once __ROOT__ . '/views/general/head.view.php'; ?>
	<?php #endregion HEAD ?>

	<!-- Include Bootstrap Datepicker CSS -->
	<link href="<?php echo RUTA_ADM_ASSETS; ?>plugins/bootstrap-datepicker/dist/css/bootstrap-datepicker.css" rel="stylesheet"/>
</head>

<body>
<!-- BEGIN #loader -->
<div id="loader" class="app-loader">
	<span class="spinner"></span>
</div>
<!-- END #loader -->

<!-- BEGIN #app -->
<div id="app" class="app app-header-fixed app-sidebar-fixed">
	<!-- #header -->
	<?php require_once __ROOT__ . '/views/general/header.view.php'; ?>

	<!-- #sidebar -->
	<?php require_once __ROOT__ . '/views/general/sidebar.view.php'; ?>

        <!-- BEGIN #content -->
        <div id="content" class="app-content">

                        <?php #region PAGE HEADER ?>
                        <div class="d-flex align-items-center mb-3">
                            <div>
                                <h4 class="mb-0">Reporte de Ventas y Comisiones</h4>
                                <p class="mb-0 text-muted">Consulta detallada de ventas y comisiones por empleado y método de pago</p>
                            </div>
                        </div>

                        <hr>
                        <?php #endregion PAGE HEADER ?>

                        <?php #region FILTERS PANEL ?>
                        <!-- Filters Panel -->
                        <div class="panel panel-inverse">
                            <div class="panel-heading">
                                <h4 class="panel-title">Filtros de Consulta</h4>
                            </div>
                            <div class="panel-body">
                                <form method="POST" id="reporte-form">
                                    <input type="hidden" name="action" value="generar_reporte">

                                    <div class="row g-3">
                                        <!-- Fecha Inicio -->
                                        <div class="col-md-3">
                                            <label for="fecha_inicio" class="form-label">Fecha de Inicio <span class="text-danger">*</span></label>
                                            <div class="input-group">
                                                <input type="text" class="form-control datepicker" id="fecha_inicio" name="fecha_inicio"
                                                       placeholder="yyyy-mm-dd" required autocomplete="off" value="<?php echo htmlspecialchars($fecha_inicio); ?>">
                                                <span class="input-group-text" id="fecha_inicio_icon" style="cursor: pointer;">
                                                    <i class="fa fa-calendar"></i>
                                                </span>
                                            </div>
                                        </div>

                                        <!-- Fecha Fin -->
                                        <div class="col-md-3">
                                            <label for="fecha_fin" class="form-label">Fecha de Fin <span class="text-danger">*</span></label>
                                            <div class="input-group">
                                                <input type="text" class="form-control datepicker" id="fecha_fin" name="fecha_fin"
                                                       placeholder="yyyy-mm-dd" required autocomplete="off" value="<?php echo htmlspecialchars($fecha_fin); ?>">
                                                <span class="input-group-text" id="fecha_fin_icon" style="cursor: pointer;">
                                                    <i class="fa fa-calendar"></i>
                                                </span>
                                            </div>
                                        </div>

                                        <!-- Centro de Costo -->
                                        <div class="col-md-4">
                                            <label for="id_centro_costo" class="form-label">Centro de Costo <span class="text-danger">*</span></label>
                                            <select class="form-select" id="id_centro_costo" name="id_centro_costo" required>
                                                <option value="">Seleccione un centro de costo...</option>
                                                <?php foreach ($centros_costos as $centro): ?>
                                                    <option value="<?php echo $centro->getId(); ?>"
                                                            <?php echo ($id_centro_costo == $centro->getId()) ? 'selected' : ''; ?>>
                                                        <?php echo htmlspecialchars($centro->getNombre()); ?>
                                                    </option>
                                                <?php endforeach; ?>
                                            </select>
                                        </div>

                                        <!-- Botón Generar -->
                                        <div class="col-md-2 d-flex align-items-end">
                                            <button type="submit" class="btn btn-primary w-100">
                                                <i class="fa fa-chart-bar fa-fw me-1"></i> Generar Reporte
                                            </button>
                                        </div>
                                    </div>
                                </form>
                            </div>
                        </div>
                        <?php #endregion FILTERS PANEL ?>

                        <?php #region RESULTS ?>
                        <?php if ($reporte_data !== null && !empty($reporte_data['empleados'])): ?>
                        <!-- Results Panel -->
                        <div class="panel panel-inverse">
                            <div class="panel-heading">
                                <div class="d-flex align-items-center w-100">
                                    <div>
                                        <h4 class="panel-title mb-0">
                                            Reporte de Ventas y Comisiones <br>
                                            <span class="text-muted fs-6">
                                                <?php echo htmlspecialchars($centro_costo_nombre); ?> |
                                                <?php echo date('Y-m-d', strtotime($fecha_inicio)); ?> -
                                                <?php echo date('Y-m-d', strtotime($fecha_fin)); ?>
                                            </span>
                                        </h4>
                                    </div>
                                    <div class="ms-auto">
                                        <form method="POST" style="display: inline;">
                                            <input type="hidden" name="action" value="exportar_excel">
                                            <input type="hidden" name="fecha_inicio" value="<?php echo htmlspecialchars($fecha_inicio); ?>">
                                            <input type="hidden" name="fecha_fin" value="<?php echo htmlspecialchars($fecha_fin); ?>">
                                            <input type="hidden" name="id_centro_costo" value="<?php echo htmlspecialchars($id_centro_costo); ?>">
                                            <button type="submit" class="btn btn-success btn-sm">
                                                <i class="fa fa-file-excel fa-fw me-1"></i> Exportar a Excel
                                            </button>
                                        </form>
                                    </div>
                                </div>
                            </div>
                            <div>
                                <div class="table-responsive">
                                    <table class="table table-hover table-sm mb-0" id="reporte-table">
                                <thead class="table-dark">
                                    <tr>
                                        <th>Barberos:</th>
                                        <?php foreach ($reporte_data['empleados'] as $empleado_id => $empleado_nombre): ?>
                                            <th class="text-center"><?php echo htmlspecialchars($empleado_nombre); ?></th>
                                        <?php endforeach; ?>
                                        <th class="text-center"><strong>Total</strong></th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php 
                                    $total_electronicos    = 0;
                                    $total_no_electronicos = 0;
                                    $totales_por_empleado  = [];
                                    
                                    // Inicializar totales por empleado
                                    foreach ($reporte_data['empleados'] as $empleado_id => $empleado_nombre) {
                                        $totales_por_empleado[$empleado_id] = 0;
                                    }
                                    ?>
                                    
                                    <?php if (!empty($reporte_data['metodos_electronicos'])): ?>
                                        <!-- Métodos de Pago Electrónicos -->
                                        <?php foreach ($reporte_data['metodos_electronicos'] as $metodo_id => $metodo): ?>
                                            <tr>
                                                <td><strong><?php echo htmlspecialchars($metodo['descripcion']); ?></strong></td>
                                                <?php 
                                                $total_fila = 0;
                                                foreach ($reporte_data['empleados'] as $empleado_id => $empleado_nombre): 
                                                    $valor = $reporte_data['matriz_ventas'][$metodo_id][$empleado_id] ?? 0;
                                                    $total_fila += $valor;
                                                    $totales_por_empleado[$empleado_id] += $valor;
                                                ?>
                                                    <td class="text-end">$<?php echo number_format($valor, 0, ',', '.'); ?></td>
                                                <?php endforeach; ?>
                                                <td class="text-end"><strong>$<?php echo number_format($total_fila, 0, ',', '.'); ?></strong></td>
                                            </tr>
                                            <?php $total_electronicos += $total_fila; ?>
                                        <?php endforeach; ?>
                                        
                                        <!-- Subtotal Transferencia -->
                                        <tr class="text-info">
                                            <td><strong>Total Transferencia</strong></td>
                                            <?php foreach ($reporte_data['empleados'] as $empleado_id => $empleado_nombre): ?>
                                                <?php 
                                                $total_empleado_electronico = 0;
                                                foreach ($reporte_data['metodos_electronicos'] as $metodo_id => $metodo) {
                                                    $total_empleado_electronico += $reporte_data['matriz_ventas'][$metodo_id][$empleado_id] ?? 0;
                                                }
                                                ?>
                                                <td class="text-end"><strong>$<?php echo number_format($total_empleado_electronico, 0, ',', '.'); ?></strong></td>
                                            <?php endforeach; ?>
                                            <td class="text-end"><strong>$<?php echo number_format($total_electronicos, 0, ',', '.'); ?></strong></td>
                                        </tr>
                                    <?php endif; ?>
                                    
                                    <?php if (!empty($reporte_data['metodos_no_electronicos'])): ?>
                                        <!-- Métodos de Pago No Electrónicos -->
                                        <?php foreach ($reporte_data['metodos_no_electronicos'] as $metodo_id => $metodo): ?>
                                            <tr>
                                                <td><strong><?php echo htmlspecialchars($metodo['descripcion']); ?></strong></td>
                                                <?php
                                                $total_fila = 0;
                                                foreach ($reporte_data['empleados'] as $empleado_id => $empleado_nombre):
                                                    $valor = $reporte_data['matriz_ventas'][$metodo_id][$empleado_id] ?? 0;
                                                    $total_fila += $valor;
                                                    $totales_por_empleado[$empleado_id] += $valor;
                                                ?>
                                                    <td class="text-end">$<?php echo number_format($valor, 0, ',', '.'); ?></td>
                                                <?php endforeach; ?>
                                                <td class="text-end"><strong>$<?php echo number_format($total_fila, 0, ',', '.'); ?></strong></td>
                                            </tr>
                                            <?php $total_no_electronicos += $total_fila; ?>
                                        <?php endforeach; ?>
                                    <?php endif; ?>
                                    
                                    <?php 
                                    $total_ventas = $total_electronicos + $total_no_electronicos;
                                    $ganancias = $total_ventas - $reporte_data['total_comisiones'];
                                    ?>
                                    
                                    <!-- Totales Finales -->
                                    <tr class="text-info">
                                        <td><strong>Total ventas</strong></td>
                                        <?php foreach ($reporte_data['empleados'] as $empleado_id => $empleado_nombre): ?>
                                            <td class="text-end"><strong>$<?php echo number_format($totales_por_empleado[$empleado_id], 0, ',', '.'); ?></strong></td>
                                        <?php endforeach; ?>
                                        <td class="text-end"><strong>$<?php echo number_format($total_ventas, 0, ',', '.'); ?></strong></td>
                                    </tr>
                                    
                                    <tr class="text-info">
                                        <td><strong>Nomina</strong></td>
                                        <td colspan="<?php echo count($reporte_data['empleados']) + 1; ?>" class="text-end">
                                            <strong>$<?php echo number_format($reporte_data['total_comisiones'], 0, ',', '.'); ?></strong>
                                        </td>
                                    </tr>
                                    
                                    <tr class="text-success">
                                        <td><strong>Ganancias</strong></td>
                                        <td colspan="<?php echo count($reporte_data['empleados']) + 1; ?>" class="text-end">
                                            <strong>$<?php echo number_format($ganancias, 0, ',', '.'); ?></strong>
                                        </td>
                                    </tr>
                                                </tbody>
                                            </table>
                                        </div>
                                    </div>
                                </div>

                        <?php elseif ($reporte_data !== null): ?>
                        <!-- Empty State Panel -->
                        <div class="panel panel-inverse">
                            <div class="panel-body text-center py-5">
                                <i class="fa fa-chart-bar fa-3x text-muted mb-3"></i>
                                <h5 class="text-muted">No hay datos para mostrar</h5>
                                <p class="text-muted">No se encontraron citas finalizadas para los filtros seleccionados.</p>
                            </div>
                        </div>

                        <?php else: ?>
                        <!-- Initial State Panel -->
                        <div class="panel panel-inverse">
                            <div class="panel-body text-center py-5">
                                <i class="fa fa-chart-bar fa-3x text-muted mb-3"></i>
                                <h5 class="text-muted">Genere un reporte para ver los resultados</h5>
                                <p class="text-muted">Complete los filtros de consulta y haga clic en "Generar Reporte" para ver los datos.</p>
                            </div>
                        </div>
                        <?php endif; ?>
                        <?php #endregion RESULTS ?>

        </div>
        <!-- END #content -->

        <!-- BEGIN scroll-top-btn -->
        <a href="javascript:;" class="btn btn-icon btn-circle btn-theme btn-scroll-to-top" data-toggle="scroll-to-top">
            <i class="fa fa-angle-up"></i>
        </a>
        <!-- END scroll-top-btn -->
    </div>
    <!-- END #app -->

    <?php #region JS ?>
    <?php require_once __ROOT__ . '/views/general/core_js.view.php'; ?>

    <!-- Include Bootstrap Datepicker JS -->
    <script src="<?php echo RUTA_ADM_ASSETS; ?>plugins/bootstrap-datepicker/dist/js/bootstrap-datepicker.js"></script>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Initialize datepickers
    $('.datepicker').datepicker({
        autoclose     : true,
        todayHighlight: true,
        format        : 'yyyy-mm-dd'
    });

    // Make calendar icons clickable
    document.getElementById('fecha_inicio_icon').addEventListener('click', function() {
        $('#fecha_inicio').datepicker('show');
    });

    document.getElementById('fecha_fin_icon').addEventListener('click', function() {
        $('#fecha_fin').datepicker('show');
    });

    // Form validation
    document.getElementById('reporte-form').addEventListener('submit', function(e) {
        const fechaInicio = document.getElementById('fecha_inicio').value;
        const fechaFin = document.getElementById('fecha_fin').value;
        const centroCosto = document.getElementById('id_centro_costo').value;

        if (!fechaInicio || !fechaFin || !centroCosto) {
            e.preventDefault();
            showSweetAlertError('Error de Validación', 'Todos los campos son obligatorios.');
            return false;
        }

        if (new Date(fechaInicio) > new Date(fechaFin)) {
            e.preventDefault();
            showSweetAlertError('Error de Validación', 'La fecha de inicio no puede ser mayor que la fecha de fin.');
            return false;
        }
    });

    // Helper function to validate date format (YYYY-MM-DD)
    function isValidDate(dateString) {
        if(!/^\d{4}-\d{2}-\d{2}$/.test(dateString)) return false;
        const parts = dateString.split("-");
        const year = parseInt(parts[0], 10);
        const month = parseInt(parts[1], 10);
        const day = parseInt(parts[2], 10);
        if(year < 1000 || year > 3000 || month == 0 || month > 12) return false;
        const monthLength = [31, 28, 31, 30, 31, 30, 31, 31, 30, 31, 30, 31];
        if(year % 400 == 0 || (year % 100 != 0 && year % 4 == 0)) monthLength[1] = 29;
        return day > 0 && day <= monthLength[month - 1];
    }

    // Handle Excel Export Button
    const exportForm = document.querySelector('input[name="action"][value="exportar_excel"]');
    if (exportForm) {
        const form = exportForm.closest('form');
        const button = form.querySelector('button[type="submit"]');

        form.addEventListener('submit', function(e) {
            // Show loading state
            const originalText = button.innerHTML;
            button.innerHTML = '<i class="fa fa-spinner fa-spin fa-fw me-1"></i> Exportando...';
            button.disabled = true;

            // Reset button after a delay (since we're downloading a file)
            setTimeout(function() {
                button.innerHTML = originalText;
                button.disabled = false;
            }, 3000);
        });
    }

    // Handle Success/Error Messages from PHP
    <?php if (isset($success_display) && $success_display === 'show'): ?>
    showSweetAlertSuccess('¡Éxito!', '<?php echo addslashes($success_text ?? "Operación completada con éxito."); ?>');
    <?php endif; ?>

    <?php if (isset($error_display) && $error_display === 'show'): ?>
    showSweetAlertError('Error', '<?php echo addslashes($error_text ?? "Ha ocurrido un error."); ?>');
    <?php endif; ?>
});
</script>

</body>

</html>
