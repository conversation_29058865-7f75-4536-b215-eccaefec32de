<?php
#region region DOCS

/** @var Usuario[] $usuarios */
/** @var Perfil[] $perfiles */
/** @var Empleado[] $empleados */

use App\classes\Usuario;
use App\classes\Perfil;
use App\classes\Empleado;

#endregion DOCS
?>
<!DOCTYPE html>
<html lang="en" class="dark-mode">
<head>
	<meta charset="utf-8"/>
	<title><?php echo APP_NAME; ?> | Usuarios</title>
	<meta content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no" name="viewport"/>
	<meta content="" name="description"/>
	<meta content="" name="author"/>

	<?php #region HEAD ?>
	<?php require_once __ROOT__ . '/views/general/head.view.php'; ?>
	<?php #endregion HEAD ?>
</head>
<body>
<!-- BEGIN #loader -->
<div id="loader" class="app-loader">
	<span class="spinner"></span>
</div>
<!-- END #loader -->

<!-- BEGIN #app -->
<div id="app" class="app app-header-fixed app-sidebar-fixed">
	<!-- #header -->
	<?php require_once __ROOT__ . '/views/general/header.view.php'; ?>

	<!-- #sidebar -->
	<?php require_once __ROOT__ . '/views/general/sidebar.view.php'; ?>

	<!-- BEGIN #content -->
	<div id="content" class="app-content">
		<?php #region region PAGE HEADER ?>
		<div class="d-flex align-items-center mb-3">
			<div>
				<h4 class="mb-0">Gestión de Usuarios</h4>
				<p class="mb-0 text-muted">Administra los usuarios del sistema</p>
			</div>
			<div class="ms-auto">
				<a href="iusuario" class="btn btn-success"><i class="fa fa-plus-circle fa-fw me-1"></i> Crear nuevo</a>
			</div>
		</div>

		<hr>
		<?php #endregion PAGE HEADER ?>

		<?php #region region PANEL USUARIOS ?>
		<div class="panel panel-inverse mt-3 no-border-radious">
			<div class="panel-heading no-border-radious">
				<h4 class="panel-title">
					Listado de Usuarios Activos
				</h4>
			</div>
			<!-- BEGIN PANEL body -->
			<div class="p-1 table-nowrap" style="overflow: auto">
				<?php #region region TABLE USUARIOS ?>
				<table class="table table-hover table-sm">
					<thead>
					<tr>
						<th>Acciones</th>
						<th>Nombre</th>
						<th>Username</th>
						<th>Perfil</th>
						<th>Barbero asociado</th>
					</tr>
					</thead>
					<tbody class="fs-12px" id="user-table-body">
					<?php foreach ($usuarios as $usuario): ?>
						<tr data-user-id="<?php echo $usuario->getId(); ?>">
							<td>
								<?php // Edit Button - Triggers Modal ?>
								<button type="button" class="btn btn-xs btn-primary me-1 btn-edit-usuario"
								        title="Editar Usuario"
								        data-bs-toggle="modal"
								        data-bs-target="#editUserModal"
								        data-userid="<?php echo $usuario->getId(); ?>"
								        data-nombre="<?php echo htmlspecialchars($usuario->getNombre() ?? ''); ?>"
								        data-id-perfil="<?php echo $usuario->getId_perfil() ?? ''; ?>"
								        data-id-empleado="<?php echo $usuario->getId_empleado() ?? ''; ?>"
								        data-nombre-empleado="<?php echo htmlspecialchars($usuario->getNombre_empleado() ?? ''); ?>">
									<i class="fa fa-edit"></i>
								</button>
								<?php // Change Password Button - Triggers Modal ?>
								<button type="button" class="btn btn-xs btn-warning me-1 btn-change-password"
								        title="Cambiar Contraseña"
								        data-bs-toggle="modal"
								        data-bs-target="#changePasswordModal"
								        data-userid="<?php echo $usuario->getId(); ?>"
								        data-username="<?php echo htmlspecialchars($usuario->getUsername() ?? ''); ?>">
									<i class="fa fa-key"></i>
								</button>
								<?php // Deactivate Button - Only show if active ?>
								<?php if ($usuario->isActivo()): ?>
									<button type="button" class="btn btn-xs btn-danger btn-desactivar-usuario"
									        title="Desactivar"
									        data-userid="<?php echo $usuario->getId(); ?>"
									        data-username="<?php echo htmlspecialchars($usuario->getUsername() ?? ''); ?>">
										<i class="fa fa-trash-alt"></i>
									</button>
								<?php endif; ?>
							</td>
							<td class="user-nombre-cell"><?php echo htmlspecialchars($usuario->getNombre()); ?></td>
							<td><?php echo htmlspecialchars($usuario->getUsername()); ?></td>
							<td class="user-perfil-cell"><?php echo htmlspecialchars($usuario->getNombre_perfil() ?? 'Sin perfil'); ?></td>
							<td class="user-empleado-cell"><?php echo htmlspecialchars($usuario->getNombre_empleado() ?? 'No asignado'); ?></td>
						</tr>
					<?php endforeach; ?>
					<?php if (empty($usuarios)): ?>
						<tr>
							<td colspan="5" class="text-center">No hay usuarios para mostrar.</td>
						</tr>
					<?php endif; ?>

					</tbody>
				</table>
				<?php #endregion TABLE USUARIOS ?>
			</div>
			<!-- END PANEL body -->
		</div>
		<?php #endregion PANEL USUARIOS ?>
		<?php #region region Edit User Modal ?>
		<div class="modal fade" id="editUserModal" tabindex="-1" aria-labelledby="editUserModalLabel" aria-hidden="true">
			<div class="modal-dialog">
				<div class="modal-content">
					<form id="edit-user-form">
						<div class="modal-header">
							<h5 class="modal-title" id="editUserModalLabel">Editar Usuario</h5>
							<button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
						</div>
						<div class="modal-body">
							<input type="hidden" name="userId" id="edit-user-id">
							<input type="hidden" name="action" value="modificar">

							<div class="mb-3">
								<label for="edit-user-nombre" class="form-label">Nombre:</label>
								<input type="text" class="form-control" id="edit-user-nombre" name="nombre" required>
							</div>

							<div class="mb-3">
								<label for="edit-user-perfil" class="form-label">Perfil:</label>
								<select class="form-select" id="edit-user-perfil" name="id_perfil" required>
									<option value="">Seleccione un perfil</option>
									<?php foreach ($perfiles as $perfil): ?>
										<option value="<?php echo $perfil->getId(); ?>">
											<?php echo htmlspecialchars($perfil->getNombre()); ?>
										</option>
									<?php endforeach; ?>
								</select>
								<div class="invalid-feedback">Debe seleccionar un perfil.</div>
							</div>

							<div class="mb-3">
								<label for="edit-user-empleado" class="form-label">Barbero:</label>
								<select class="form-select" id="edit-user-empleado" name="id_empleado">
									<option value="">Seleccione un empleado</option>
									<?php foreach ($empleados as $empleado): ?>
										<option value="<?php echo $empleado->getId(); ?>">
											<?php echo htmlspecialchars($empleado->getNombre()); ?>
										</option>
									<?php endforeach; ?>
								</select>
								<div class="invalid-feedback" id="edit-user-empleado-error">Debe seleccionar un empleado para el perfil Barbero.</div>
								<small class="text-muted">Asociar este usuario con un empleado existente.</small>
							</div>

							<div id="edit-user-error" class="alert alert-danger mt-2" style="display: none;"></div>
						</div>
						<div class="modal-footer">
							<button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancelar</button>
							<button type="submit" class="btn btn-primary">Guardar Cambios</button>
						</div>
					</form>
				</div>
			</div>
		</div>
		<?php #endregion Edit User Modal ?>

		<?php #region region Change Password Modal ?>
		<div class="modal fade" id="changePasswordModal" tabindex="-1" aria-labelledby="changePasswordModalLabel" aria-hidden="true">
			<div class="modal-dialog">
				<div class="modal-content">
					<form id="change-password-form">
						<div class="modal-header">
							<h5 class="modal-title" id="changePasswordModalLabel">Cambiar Contraseña</h5>
							<button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
						</div>
						<div class="modal-body">
							<input type="hidden" name="userId" id="change-password-user-id">
							<input type="hidden" name="action" value="cambiar_clave">

							<div class="mb-3">
								<label for="change-password-username" class="form-label">Usuario:</label>
								<input type="text" class="form-control" id="change-password-username" readonly>
							</div>

							<div class="mb-3">
								<label for="change-password-new" class="form-label">Nueva Contraseña: <span class="text-danger">*</span></label>
								<input type="password" class="form-control" id="change-password-new" name="nueva_clave" required>
								<div class="invalid-feedback">La nueva contraseña es requerida.</div>
							</div>

							<div class="mb-3">
								<label for="change-password-confirm" class="form-label">Confirmar Contraseña: <span class="text-danger">*</span></label>
								<input type="password" class="form-control" id="change-password-confirm" name="confirmar_clave" required>
								<div class="invalid-feedback">Debe confirmar la contraseña.</div>
							</div>

							<div id="change-password-error" class="alert alert-danger mt-2" style="display: none;"></div>
						</div>
						<div class="modal-footer">
							<button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancelar</button>
							<button type="submit" class="btn btn-warning">Cambiar Contraseña</button>
						</div>
					</form>
				</div>
			</div>
		</div>
		<?php #endregion Change Password Modal ?>

	</div>
	<!-- END #content -->

	<?php #region region Hidden Form for Deactivation ?>
	<form id="deactivate-user-form" method="POST" action="lusuarios" style="display: none;">
		<input type="hidden" name="action" value="desactivar">
		<input type="hidden" name="userId" id="deactivate-user-id">
	</form>
	<?php #endregion Hidden Form ?>

	<!-- BEGIN scroll-top-btn -->
	<a href="#" class="btn btn-icon btn-circle btn-success btn-scroll-to-top" data-toggle="scroll-to-top"><i class="fa fa-angle-up"></i></a>
	<!-- END scroll-top-btn -->
</div>
<!-- END #app -->

<?php #region region JS ?>
<?php require_once __ROOT__ . '/views/general/core_js.view.php'; ?>

<!-- ================== BEGIN PAGE LEVEL JS ================== -->
<!-- Assuming SweetAlert is loaded in core_js or globally -->
<!-- Assuming SweetAlert helper functions (showSweetAlertSuccess/Error) are available -->
<script>
    document.addEventListener('DOMContentLoaded', function () {
        // Use event delegation on the table body
        const tableBody            = document.getElementById('user-table-body');
        const editUserModalElement = document.getElementById('editUserModal');
        const editUserModal        = new bootstrap.Modal(editUserModalElement); // Initialize Bootstrap Modal
        const editUserForm         = document.getElementById('edit-user-form');
        const editUserIdInput      = document.getElementById('edit-user-id');
        const editUserNombreInput  = document.getElementById('edit-user-nombre');
        const editUserErrorDiv     = document.getElementById('edit-user-error');

        // Store profile and employee data for validation in edit modal
        const perfiles = <?php echo json_encode(array_map(function($p) { return ['id' => $p->getId(), 'nombre' => $p->getNombre()]; }, $perfiles)); ?>;
        const empleados = <?php echo json_encode(array_map(function($e) { return ['id' => $e->getId(), 'nombre' => $e->getNombre()]; }, $empleados)); ?>;

        if (tableBody) {
            tableBody.addEventListener('click', function (event) {
                const deactivateButton = event.target.closest('.btn-desactivar-usuario');
                const editButton       = event.target.closest('.btn-edit-usuario');
                const changePasswordButton = event.target.closest('.btn-change-password');

                // --- Handle Deactivate Click ---
				<?php #region region JS AJAX -- Deactivate user ?>
                if (deactivateButton) {
                    event.preventDefault();
                    const userId   = deactivateButton.dataset.userid;
                    const userName = deactivateButton.dataset.username || 'este usuario';

                    swal({
                        title     : "Confirmar Desactivación",
                        text      : `¿Seguro que quieres desactivar al usuario '${userName}'?`,
                        icon      : "warning",
                        buttons   : {
                            cancel : {text: "Cancelar", value: null, visible: true, className: "", closeModal: true},
                            confirm: {text: "Confirmar", value: true, visible: true, className: "btn-danger", closeModal: true}
                        },
                        dangerMode: true,
                    })
                        .then((willDelete) => {
                            if (willDelete) {
                                document.getElementById('deactivate-user-id').value = userId;
                                document.getElementById('deactivate-user-form').submit();
                            }
                        });
                }
				<?php #endregion JS AJAX -- Deactivate user ?>

                // --- Handle Edit Click ---
				<?php #region region JS AJAX - Handle Edit click ?>
                if (editButton) {
                    event.preventDefault(); // Prevent default if it was an <a>
                    const userId        = editButton.dataset.userid;
                    const currentNombre = editButton.dataset.nombre;
                    const currentPerfil = editButton.dataset.idPerfil;
                    const currentEmpleado = editButton.dataset.idEmpleado;
                    const currentEmpleadoNombre = editButton.dataset.nombreEmpleado;

                    // Populate the modal form
                    editUserIdInput.value          = userId;
                    editUserNombreInput.value      = currentNombre;
                    editUserErrorDiv.style.display = 'none'; // Hide previous errors
                    editUserErrorDiv.textContent   = '';

                    // Set the selected perfil
                    const editUserPerfilSelect = document.getElementById('edit-user-perfil');
                    if (editUserPerfilSelect) {
                        editUserPerfilSelect.value = currentPerfil;
                    }

                    // Set the empleado ID if available
                    const editUserEmpleadoSelect = document.getElementById('edit-user-empleado');
                    if (editUserEmpleadoSelect) {
                        editUserEmpleadoSelect.value = currentEmpleado || '';

                        // If no matching option is found but we have an ID, create a new option
                        if (currentEmpleado && !Array.from(editUserEmpleadoSelect.options).some(option => option.value === currentEmpleado)) {
                            const newOption = new Option(currentEmpleadoNombre || `Empleado ID: ${currentEmpleado}`, currentEmpleado);
                            editUserEmpleadoSelect.add(newOption);
                            editUserEmpleadoSelect.value = currentEmpleado;
                        }
                    }

                    // The data-bs-toggle and data-bs-target attributes on the button handle showing the modal
                    // If they weren't there, you'd call: editUserModal.show();
                }
				<?php #endregion JS AJAX - Edit user ?>

                // --- Handle Change Password Click ---
				<?php #region region JS AJAX - Handle Change Password click ?>
                if (changePasswordButton) {
                    event.preventDefault();
                    const userId = changePasswordButton.dataset.userid;
                    const username = changePasswordButton.dataset.username;

                    // Populate the modal form
                    document.getElementById('change-password-user-id').value = userId;
                    document.getElementById('change-password-username').value = username;

                    // Clear form fields and errors
                    document.getElementById('change-password-new').value = '';
                    document.getElementById('change-password-confirm').value = '';
                    document.getElementById('change-password-error').style.display = 'none';

                    // Clear validation classes
                    document.getElementById('change-password-new').classList.remove('is-invalid');
                    document.getElementById('change-password-confirm').classList.remove('is-invalid');
                }
				<?php #endregion JS AJAX - Change Password ?>
            });
        }

        // Real-time validation for edit modal profile-employee consistency
        function validateEditModalProfileEmployeeConsistency() {
            const editUserPerfilSelect = document.getElementById('edit-user-perfil');
            const editUserEmpleadoSelect = document.getElementById('edit-user-empleado');
            const editUserEmpleadoError = document.getElementById('edit-user-empleado-error');

            if (!editUserPerfilSelect || !editUserEmpleadoSelect) return;

            const selectedPerfilId = editUserPerfilSelect.value;
            const selectedEmpleadoId = editUserEmpleadoSelect.value;

            // Clear previous errors
            editUserPerfilSelect.classList.remove('is-invalid');
            editUserEmpleadoSelect.classList.remove('is-invalid');

            if (selectedPerfilId !== '') {
                const selectedPerfil = perfiles.find(p => p.id == selectedPerfilId);
                const esBarbero = selectedPerfil && selectedPerfil.nombre.toLowerCase() === 'barbero';

                if (esBarbero && selectedEmpleadoId === '') {
                    editUserEmpleadoSelect.classList.add('is-invalid');
                    if (editUserEmpleadoError) {
                        editUserEmpleadoError.textContent = 'Los usuarios con perfil "Barbero" deben tener un empleado asociado.';
                    }
                }
            }

            if (selectedEmpleadoId !== '') {
                const selectedPerfil = perfiles.find(p => p.id == selectedPerfilId);
                const esBarbero = selectedPerfil && selectedPerfil.nombre.toLowerCase() === 'barbero';

                if (!esBarbero) {
                    editUserPerfilSelect.classList.add('is-invalid');
                } else {
                    // Check if employee already has a user (AJAX call)
                    checkEmployeeAvailabilityForEdit(selectedEmpleadoId, editUserIdInput.value);
                }
            }
        }

        // Function to check if employee already has a user (for edit modal)
        function checkEmployeeAvailabilityForEdit(empleadoId, currentUserId) {
            if (!empleadoId) return;

            fetch('lusuarios', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/x-www-form-urlencoded',
                },
                body: `action=check_employee&id_empleado=${empleadoId}`
            })
            .then(response => response.json())
            .then(data => {
                const editUserEmpleadoSelect = document.getElementById('edit-user-empleado');
                const editUserEmpleadoError = document.getElementById('edit-user-empleado-error');

                if (data.has_user) {
                    // Note: The server-side validation will handle excluding the current user
                    // This is just for immediate feedback
                    editUserEmpleadoSelect.classList.add('is-invalid');
                    if (editUserEmpleadoError) {
                        editUserEmpleadoError.textContent = 'Este empleado ya está asociado a otro usuario.';
                    }
                }
            })
            .catch(error => {
                console.error('Error checking employee availability:', error);
            });
        }

        // Add real-time validation for edit modal
        const editUserPerfilSelect = document.getElementById('edit-user-perfil');
        const editUserEmpleadoSelect = document.getElementById('edit-user-empleado');

        if (editUserPerfilSelect) {
            editUserPerfilSelect.addEventListener('change', validateEditModalProfileEmployeeConsistency);
        }
        if (editUserEmpleadoSelect) {
            editUserEmpleadoSelect.addEventListener('change', validateEditModalProfileEmployeeConsistency);
        }

        // --- Handle Edit Form Submission (AJAX) ---
		<?php #region region JS AJAX - Edit Form Submission ?>
        if (editUserForm) {
            editUserForm.addEventListener('submit', function (event) {
                event.preventDefault(); // Stop default form submission
                editUserErrorDiv.style.display = 'none'; // Hide error div initially

                const formData  = new FormData(editUserForm);
                const userId    = formData.get('userId');
                const newNombre = formData.get('nombre').trim(); // Get trimmed name
                const idPerfil  = formData.get('id_perfil');
                let idEmpleado = formData.get('id_empleado');

                // Handle empty employee selection
                if (idEmpleado === '') {
                    idEmpleado = null;
                }

                // Basic client-side validation
                let isValid = true;
                let errorMessage = '';

                if (!newNombre) {
                    isValid = false;
                    errorMessage = 'El nombre no puede estar vacío.';
                }

                if (!idPerfil) {
                    isValid = false;
                    errorMessage = errorMessage ? errorMessage + ' El perfil es requerido.' : 'El perfil es requerido.';
                }

                // Business Rule Validation: Bidirectional Profile-Employee Validation
                if (idPerfil) {
                    const selectedPerfil = perfiles.find(p => p.id == idPerfil);
                    const esBarbero = selectedPerfil && selectedPerfil.nombre.toLowerCase() === 'barbero';

                    // Rule: If profile is "Barbero", MUST have an employee
                    if (esBarbero && !idEmpleado) {
                        isValid = false;
                        errorMessage = errorMessage ? errorMessage + ' Los usuarios con perfil "Barbero" deben tener un empleado asociado.' : 'Los usuarios con perfil "Barbero" deben tener un empleado asociado.';
                    }

                    // Rule: If employee is selected, profile MUST be "Barbero"
                    if (idEmpleado && !esBarbero) {
                        isValid = false;
                        errorMessage = errorMessage ? errorMessage + ' Solo los usuarios con perfil "Barbero" pueden tener un empleado asociado.' : 'Solo los usuarios con perfil "Barbero" pueden tener un empleado asociado.';
                    }
                }

                if (!isValid) {
                    editUserErrorDiv.textContent = errorMessage;
                    editUserErrorDiv.style.display = 'block';
                    return; // Stop submission
                }


                // Disable submit button during request? (Optional)
                const submitButton    = editUserForm.querySelector('button[type="submit"]');
                submitButton.disabled = true;


                fetch('lusuarios', { // Post to the same controller page
                    method: 'POST',
                    body  : formData // FormData handles content type automatically
                })
                    .then(response => {
                        // Check if response is ok (status 200-299) AND is JSON
                        if (!response.ok) {
                            // Try to parse error from JSON, otherwise use status text
                            return response.json().then(errData => {
                                throw new Error(errData.message || `Error ${response.status}: ${response.statusText}`);
                            }).catch(() => {
                                // If response wasn't JSON or parsing failed
                                throw new Error(`Error ${response.status}: ${response.statusText}`);
                            });
                        }
                        return response.json(); // Parse JSON body
                    })
                    .then(data => {
                        if (data.success) {
                            editUserModal.hide(); // Close modal on success

                            // Update the row in the table directly
                            const tableRow = document.querySelector(`#user-table-body tr[data-user-id="${userId}"]`);
                            if (tableRow) {
                                // Find the cells with the specific classes within that row
                                const nombreCell = tableRow.querySelector('.user-nombre-cell');
                                if (nombreCell) {
                                    nombreCell.textContent = newNombre; // Update cell text
                                }

                                // Update the perfil cell if it exists in the response
                                if (data.nombre_perfil) {
                                    const perfilCell = tableRow.querySelector('.user-perfil-cell');
                                    if (perfilCell) {
                                        perfilCell.textContent = data.nombre_perfil;
                                    }
                                }

                                // Also update the data attributes on the edit button for next time
                                const editButton = tableRow.querySelector('.btn-edit-usuario');
                                if (editButton) {
                                    editButton.dataset.nombre = newNombre;
                                    editButton.dataset.idPerfil = idPerfil;
                                }
                            }

                            showSweetAlertSuccess('Éxito', 'Nombre actualizado correctamente.');

                        } else {
                            // Show error message inside the modal
                            editUserErrorDiv.textContent   = data.message || 'Ocurrió un error al guardar.';
                            editUserErrorDiv.style.display = 'block';
                        }
                    })
                    .catch(error => {
                        console.error('Error updating user name:', error);
                        // Show error message inside the modal
                        editUserErrorDiv.textContent   = 'Error de red o del servidor: ' + error.message;
                        editUserErrorDiv.style.display = 'block';
                    })
                    .finally(() => {
                        // Re-enable submit button
                        submitButton.disabled = false;
                    });
            });
        }
		<?php #endregion JS AJAX - Edit Form Submission ?>

        // --- Handle Change Password Form Submission (AJAX) ---
		<?php #region region JS AJAX - Change Password Form Submission ?>
        const changePasswordForm = document.getElementById('change-password-form');
        const changePasswordModal = new bootstrap.Modal(document.getElementById('changePasswordModal'));

        if (changePasswordForm) {
            changePasswordForm.addEventListener('submit', function (event) {
                event.preventDefault(); // Stop default form submission

                const changePasswordErrorDiv = document.getElementById('change-password-error');
                changePasswordErrorDiv.style.display = 'none'; // Hide error div initially

                const formData = new FormData(changePasswordForm);
                const userId = formData.get('userId');
                const nuevaClave = formData.get('nueva_clave').trim();
                const confirmarClave = formData.get('confirmar_clave').trim();

                // Basic client-side validation
                let isValid = true;
                let errorMessage = '';

                // Clear previous validation classes
                document.getElementById('change-password-new').classList.remove('is-invalid');
                document.getElementById('change-password-confirm').classList.remove('is-invalid');

                if (!nuevaClave) {
                    isValid = false;
                    errorMessage = 'La nueva contraseña es requerida.';
                    document.getElementById('change-password-new').classList.add('is-invalid');
                }

                if (!confirmarClave) {
                    isValid = false;
                    errorMessage = errorMessage ? errorMessage + ' Debe confirmar la contraseña.' : 'Debe confirmar la contraseña.';
                    document.getElementById('change-password-confirm').classList.add('is-invalid');
                }

                if (nuevaClave && confirmarClave && nuevaClave !== confirmarClave) {
                    isValid = false;
                    errorMessage = 'Las contraseñas no coinciden.';
                    document.getElementById('change-password-new').classList.add('is-invalid');
                    document.getElementById('change-password-confirm').classList.add('is-invalid');
                }

                if (!isValid) {
                    changePasswordErrorDiv.textContent = errorMessage;
                    changePasswordErrorDiv.style.display = 'block';
                    return; // Stop submission
                }

                // Disable submit button during request
                const submitButton = changePasswordForm.querySelector('button[type="submit"]');
                submitButton.disabled = true;

                fetch('lusuarios', { // Post to the same controller page
                    method: 'POST',
                    body: formData // FormData handles content type automatically
                })
                .then(response => {
                    if (!response.ok) {
                        return response.json().then(errData => {
                            throw new Error(errData.message || `Error ${response.status}: ${response.statusText}`);
                        }).catch(() => {
                            throw new Error(`Error ${response.status}: ${response.statusText}`);
                        });
                    }
                    return response.json();
                })
                .then(data => {
                    if (data.success) {
                        changePasswordModal.hide(); // Close modal on success
                        showSweetAlertSuccess('Éxito', 'Contraseña actualizada correctamente.');
                    } else {
                        // Show error message inside the modal
                        changePasswordErrorDiv.textContent = data.message || 'Ocurrió un error al cambiar la contraseña.';
                        changePasswordErrorDiv.style.display = 'block';
                    }
                })
                .catch(error => {
                    console.error('Error changing password:', error);
                    // Show error message inside the modal
                    changePasswordErrorDiv.textContent = 'Error de red o del servidor: ' + error.message;
                    changePasswordErrorDiv.style.display = 'block';
                })
                .finally(() => {
                    // Re-enable submit button
                    submitButton.disabled = false;
                });
            });
        }
		<?php #endregion JS AJAX - Change Password Form Submission ?>
    });
</script>
<!-- ================== END PAGE LEVEL JS ================== -->

<?php #endregion JS ?>

</body>
</html>