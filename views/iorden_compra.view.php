<?php
/**
 * Vista para la creación de órdenes de compra
 *
 * Variables disponibles:
 * @var array $proveedores Lista de proveedores activos
 * @var array $centros_costos Lista de centros de costo activos
 * @var string $error_display Estado de visualización de errores ('show' o 'hide')
 * @var string $error_text Texto del mensaje de error
 * @var string $success_display Estado de visualización de éxito ('show' o 'hide')
 * @var string $success_text Texto del mensaje de éxito
 */
?>

<!DOCTYPE html>
<html lang="en" class="dark-mode">
<head>
	<meta charset="utf-8"/>
	<title><?php echo APP_NAME; ?> | Crear <PERSON> Co<PERSON></title>
	<meta content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no" name="viewport"/>
	<meta content="" name="description"/>
	<meta content="" name="author"/>

	<?php #region HEAD ?>
	<?php require_once __ROOT__ . '/views/general/head.view.php'; ?>
	<?php #endregion HEAD ?>

	<!-- Include Bootstrap Datepicker CSS -->
	<link href="<?php echo RUTA_ADM_ASSETS; ?>plugins/bootstrap-datepicker/dist/css/bootstrap-datepicker.css" rel="stylesheet"/>

	<!-- Custom styles for this page -->
	<style>
		/* Extra small button class for table actions */
		.btn-xs {
			padding: 0.25rem 0.4rem;
			font-size: 0.75rem;
			line-height: 1.2;
			border-radius: 0.2rem;
		}

		/* Enhanced table styling for dark theme */
		.table-dark th, .table-dark td {
			border-color: #555;
		}

		/* Improved form spacing */
		.form-text.text-muted {
			margin-top: 0.25rem;
		}

		/* Fix autocomplete dropdown positioning and visibility */
		#producto-results {
			position: absolute !important;
			top: 100% !important;
			left: 0 !important;
			right: 0 !important;
			width: 100% !important;
			z-index: 1050 !important;
			background-color: #fff !important;
			border: 1px solid #ced4da !important;
			border-radius: 0.375rem !important;
			box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15) !important;
			margin-top: 2px !important;
		}

		/* Ensure dropdown items are visible and properly styled */
		#producto-results .dropdown-item {
			color: #212529 !important;
			background-color: transparent !important;
			padding: 0.5rem 1rem !important;
			text-decoration: none !important;
			display: block !important;
			width: 100% !important;
			clear: both !important;
			font-weight: 400 !important;
			line-height: 1.5 !important;
			white-space: nowrap !important;
			border: 0 !important;
		}

		#producto-results .dropdown-item:hover,
		#producto-results .dropdown-item:focus {
			background-color: #e9ecef !important;
			color: #16181b !important;
		}

		/* Ensure parent container has relative positioning */
		.position-relative {
			position: relative !important;
		}
	</style>
</head>

<body>
<!-- BEGIN #loader -->
<div id="loader" class="app-loader">
	<span class="spinner"></span>
</div>
<!-- END #loader -->

<!-- BEGIN #app -->
<div id="app" class="app app-header-fixed app-sidebar-fixed">
	<!-- #header -->
	<?php require_once __ROOT__ . '/views/general/header.view.php'; ?>

	<!-- #sidebar -->
	<?php require_once __ROOT__ . '/views/general/sidebar.view.php'; ?>

	<!-- BEGIN #content -->
	<div id="content" class="app-content">

					<?php #region PAGE HEADER ?>
					<div class="d-flex align-items-center mb-3">
						<div>
                                <h4 class="mb-0">Crear Orden de Compra</h4>
                                <p class="mb-0 text-muted">Registro de nueva orden de compra con productos</p>
                            </div>
                            <div class="ms-auto">
                                <a href="ordenes-compra" class="btn btn-secondary">
                                    <i class="fa fa-arrow-left fa-fw me-1"></i> Volver a Consulta
                                </a>
                            </div>
                        </div>

                        <hr>
                        <?php #endregion PAGE HEADER ?>

                        <!-- BEGIN orden-form -->
                        <form id="orden-form" class="needs-validation" novalidate>
                            <?php #region BASIC INFO ?>
                            <!-- Basic Information Panel -->
                            <div class="panel panel-inverse">
                                <div class="panel-heading">
                                    <h4 class="panel-title">Información Básica</h4>
                                </div>
                                <div class="panel-body">
                                    <div class="row g-3">
                                        <!-- Proveedor -->
                                        <div class="col-md-4">
                                            <label for="id_proveedor" class="form-label">Proveedor <span class="text-danger">*</span></label>
                                            <select class="form-select" id="id_proveedor" name="id_proveedor" required>
                                                <option value="">Seleccione un proveedor</option>
                                                <?php foreach ($proveedores as $proveedor): ?>
                                                    <option value="<?= $proveedor->getId() ?>"><?= htmlspecialchars($proveedor->getNombre()) ?></option>
                                                <?php endforeach; ?>
                                            </select>
                                            <div class="invalid-feedback">
                                                Por favor seleccione un proveedor.
                                            </div>
                                        </div>

                                        <!-- Centro de Costo -->
                                        <div class="col-md-8">
                                            <label for="id_centro_costo" class="form-label">Centro de Costo <span class="text-danger">*</span></label>
                                            <select class="form-select" id="id_centro_costo" name="id_centro_costo" required>
                                                <option value="">Seleccione un centro de costo</option>
                                                <?php foreach ($centros_costos as $centro): ?>
                                                    <option value="<?= $centro->getId() ?>"><?= htmlspecialchars($centro->getNombre()) ?></option>
                                                <?php endforeach; ?>
                                            </select>
                                            <div class="invalid-feedback">
                                                Por favor seleccione un centro de costo.
                                            </div>
                                            <small class="form-text text-muted">
                                                <i class="fa fa-info-circle me-1"></i>
                                                El centro de costo se deshabilitará después de agregar productos para mantener la consistencia de la orden.
                                            </small>
                                        </div>



                                        <!-- Numero Referencia Proveedor -->
                                        <div class="col-md-12">
                                            <label for="n_referencia_proveedor" class="form-label">Número de Referencia del Proveedor</label>
                                            <input type="text" class="form-control" id="n_referencia_proveedor" name="n_referencia_proveedor"
                                                placeholder="Número de referencia o factura del proveedor (opcional)">
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <?php #endregion BASIC INFO ?>

                            <?php #region PRODUCTS ?>
                            <!-- BEGIN productos-panel -->
                            <div class="panel panel-inverse">
                                <div class="panel-heading">
                                    <h4 class="panel-title">Productos de la Orden</h4>
                                </div>
                                <div class="panel-body">
                                    <!-- Add Product Section -->
                                    <div class="mb-4" id="add-product-section" style="display: none;">
                                        <!-- Buscar Producto - Full Width Row -->
                                        <div class="row g-3 mb-3">
                                            <div class="col-12">
                                                <label for="producto_search" class="form-label">Buscar Producto</label>
                                                <div class="position-relative">
                                                    <input type="text" class="form-control" id="producto_search"
                                                        placeholder="Buscar producto por descripción..." autocomplete="off">
                                                    <div id="producto-results" class="dropdown-menu w-100" style="max-height: 200px; overflow-y: auto;"></div>
                                                </div>
                                            </div>
                                        </div>

                                        <!-- Cantidad, Valor Unitario, Valor Total - Same Row -->
                                        <div class="row g-3 mb-3">
                                            <div class="col-md-4">
                                                <label for="cantidad_producto" class="form-label">Cantidad</label>
                                                <input type="number" class="form-control" id="cantidad_producto" min="1" value="1">
                                            </div>
                                            <div class="col-md-4">
                                                <label for="valor_unitario" class="form-label">Valor Unitario</label>
                                                <input type="text" class="form-control currency-input" id="valor_unitario"
                                                    data-type="currency" placeholder="$0">
                                            </div>
                                            <div class="col-md-4">
                                                <label for="valor_total_producto" class="form-label">Valor Total</label>
                                                <input type="text" class="form-control" id="valor_total_producto" readonly>
                                            </div>
                                        </div>

                                        <!-- Add Button - Full Width Row -->
                                        <div class="row g-3">
                                            <div class="col-12">
                                                <button type="button" class="btn btn-success w-100" id="btn-agregar-producto" disabled>
                                                    <i class="fa fa-plus me-1"></i> Agregar Producto al Detalle
                                                </button>
                                            </div>
                                        </div>
                                    </div>

                                    <!-- Empty products message -->
                                    <div id="empty-products" class="text-center py-4">
                                        <i class="fa fa-box fa-3x text-muted mb-3"></i>
                                        <h5 class="text-muted">Seleccione un centro de costo para agregar productos</h5>
                                        <p class="text-muted">Los productos aparecerán aquí una vez que seleccione un centro de costo.</p>
                                    </div>

                                    <!-- Products table -->
                                    <div id="productos-table-container" style="display: none;">
                                        <table class="table table-hover table-sm" style="background-color: #2a2a2a; border: 1px solid #444;">
                                            <thead class="table-dark">
                                                <tr>
                                                    <th style="width: 80px;" class="align-middle">Acciones</th>
                                                    <th class="align-middle">Producto</th>
                                                    <th style="width: 100px;" class="align-middle">Cantidad</th>
                                                    <th style="width: 120px;" class="text-end align-middle">Valor Unit.</th>
                                                    <th style="width: 120px;" class="text-end align-middle">Valor Total</th>
                                                </tr>
                                            </thead>
                                            <tbody id="productos-table-body" style="background-color: #333;">
                                                <!-- Products will be added here dynamically -->
                                            </tbody>
                                            <tfoot class="table-dark">
                                                <tr>
                                                    <th colspan="4" class="text-end">Total Orden:</th>
                                                    <th id="total-orden" class="text-end">$0</th>
                                                </tr>
                                            </tfoot>
                                        </table>
                                    </div>
                                </div>
                            </div>
                            <!-- END productos-panel -->
                            <?php #endregion PRODUCTS ?>

                            <?php #region FORM ACTIONS ?>
                            <!-- Form Actions -->
                            <div class="panel panel-inverse">
                                <div class="panel-body">
                                    <div class="row">
                                        <div class="col-12 text-end">
                                            <button type="button" class="btn btn-secondary me-2" id="btn-limpiar"
                                                title="Limpiar todos los datos del formulario y reiniciar">
                                                <i class="fa fa-eraser fa-fw me-1"></i> Limpiar
                                            </button>
                                            <button type="submit" class="btn btn-success" id="btn-guardar" disabled>
                                                <i class="fa fa-save fa-fw me-1"></i> Guardar Orden
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <?php #endregion FORM ACTIONS ?>
                        </form>
                        <!-- END orden-form -->

        </div>
        <!-- END #content -->

        <!-- BEGIN scroll-top-btn -->
        <a href="javascript:;" class="btn btn-icon btn-circle btn-theme btn-scroll-to-top" data-toggle="scroll-to-top">
            <i class="fa fa-angle-up"></i>
        </a>
        <!-- END scroll-top-btn -->
    </div>
    <!-- END #app -->

    <?php #region JS ?>
    <?php require_once __ROOT__ . '/views/general/core_js.view.php'; ?>

    <!-- Include Bootstrap Datepicker JS -->
    <script src="<?php echo RUTA_ADM_ASSETS; ?>plugins/bootstrap-datepicker/dist/js/bootstrap-datepicker.js"></script>

    <!-- Include formatcurrency.js for currency formatting -->
    <script src="<?php echo RUTA_RESOURCES; ?>js/formatcurrency.js"></script>
    <?php #endregion JS ?>

<script>
// Initialize currency formatting for dynamically added elements
$(document).ready(function() {
    // Initialize currency inputs
    $('input[data-type="currency"]').each(function() {
        $(this).on({
            keyup: function() { formatCurrency($(this)); },
            blur: function() { formatCurrency($(this), "blur"); }
        });
    });
});

document.addEventListener('DOMContentLoaded', function() {
    initOrdenCreation();
});

function initOrdenCreation() {
    // Elements
    const ordenForm               = document.getElementById('orden-form');
    const centroCostoSelect       = document.getElementById('id_centro_costo');
    const addProductSection       = document.getElementById('add-product-section');
    const emptyProducts           = document.getElementById('empty-products');
    const productosTableContainer = document.getElementById('productos-table-container');
    const productosTableBody      = document.getElementById('productos-table-body');
    const totalOrdenElement       = document.getElementById('total-orden');
    const btnGuardar              = document.getElementById('btn-guardar');
    const btnLimpiar              = document.getElementById('btn-limpiar');

    // Product search elements
    const productoSearch     = document.getElementById('producto_search');
    const productoResults    = document.getElementById('producto-results');
    const cantidadProducto   = document.getElementById('cantidad_producto');
    const valorUnitario      = document.getElementById('valor_unitario');
    const valorTotalProducto = document.getElementById('valor_total_producto');
    const btnAgregarProducto = document.getElementById('btn-agregar-producto');

    // State
    let selectedProduct = null;
    let ordenDetalles   = [];
    let searchTimeout   = null;

    // Initialize
    updateUI();

    // Event Listeners
    centroCostoSelect.addEventListener('change', function() {
        if (this.value) {
            addProductSection.style.display = 'block';
            emptyProducts.style.display = 'none';
            // Clear products when centro costo changes and there are products
            if (ordenDetalles.length > 0) {
                if (confirm('Cambiar el centro de costo eliminará todos los productos agregados. ¿Continuar?')) {
                    clearProducts();
                } else {
                    // Revert selection
                    this.value = ordenDetalles.length > 0 ? ordenDetalles[0].id_centro_costo : '';
                    return;
                }
            }
        } else {
            addProductSection.style.display = 'none';
            if (ordenDetalles.length === 0) {
                emptyProducts.style.display = 'block';
            }
        }
        updateUI();
    });

    // Product search
    productoSearch.addEventListener('input', function() {
        const termino = this.value.trim();

        clearTimeout(searchTimeout);

        if (termino.length >= 2) {
            searchTimeout = setTimeout(() => {
                searchProductos(termino);
            }, 300);
        } else {
            productoResults.style.display = 'none';
        }
    });

    // Hide results when clicking outside
    document.addEventListener('click', function(e) {
        if (!productoSearch.contains(e.target) && !productoResults.contains(e.target)) {
            productoResults.style.display = 'none';
        }
    });

    // Calculate total when quantity or value changes
    cantidadProducto.addEventListener('input', calculateProductTotal);
    valorUnitario.addEventListener('input', calculateProductTotal);

    function calculateProductTotal() {
        const cantidad = parseInt(cantidadProducto.value) || 0;
        const valor = parseCurrencyValue(valorUnitario.value) || 0;
        const total = cantidad * valor;

        valorTotalProducto.value = formatCurrency(total);
        updateAddButton();
    }

    function updateAddButton() {
        const hasProduct = selectedProduct !== null;
        const hasQuantity = parseInt(cantidadProducto.value) > 0;
        const hasValue = parseCurrencyValue(valorUnitario.value) >= 0;

        btnAgregarProducto.disabled = !(hasProduct && hasQuantity && hasValue);
    }

    btnAgregarProducto.addEventListener('click', addProductToOrden);

    // Form submission
    ordenForm.addEventListener('submit', function(e) {
        e.preventDefault();
        if (this.checkValidity() && ordenDetalles.length > 0) {
            saveOrden();
        } else {
            this.classList.add('was-validated');
            if (ordenDetalles.length === 0) {
                showError('Debe agregar al menos un producto a la orden de compra.');
            }
        }
    });

    btnLimpiar.addEventListener('click', function() {
        if (confirm('¿Está seguro de que desea limpiar todos los datos?')) {
            clearForm();
        }
    });

    // Search productos function
    function searchProductos(termino) {
        const formData = new FormData();
        formData.append('action', 'search_productos');
        formData.append('termino', termino);

        fetch('crear-orden-compra', {
            method: 'POST',
            body: formData
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                displayProductoResults(data.productos);
            }
        })
        .catch(error => console.error('Error:', error));
    }

    function displayProductoResults(productos) {
        productoResults.innerHTML = '';

        // Filter out already added products
        const availableProducts = productos.filter(p =>
            !ordenDetalles.some(d => d.id_producto === p.id)
        );

        if (availableProducts.length > 0) {
            availableProducts.forEach(producto => {
                const item = document.createElement('a');
                item.className = 'dropdown-item';
                item.href = '#';
                item.innerHTML = `${producto.descripcion}`;
                item.addEventListener('click', function(e) {
                    e.preventDefault();
                    selectProducto(producto);
                });
                productoResults.appendChild(item);
            });
            productoResults.style.display = 'block';
        } else {
            productoResults.style.display = 'none';
        }
    }

    function selectProducto(producto) {
        selectedProduct = producto;
        productoSearch.value = producto.descripcion;
        // Remove automatic valor unitario pre-loading - let user enter manually
        valorUnitario.value = '';
        productoResults.style.display = 'none';
        calculateProductTotal();
        valorUnitario.focus(); // Focus on valor unitario instead of cantidad
    }

    function addProductToOrden() {
        if (!selectedProduct) return;

        const cantidad = parseInt(cantidadProducto.value);
        const valor = parseCurrencyValue(valorUnitario.value);
        const valorTotal = cantidad * valor;

        const detalle = {
            id_producto: selectedProduct.id,
            descripcion: selectedProduct.descripcion,
            cantidad: cantidad,
            valor: valor,
            valor_formateado: formatCurrency(valor),
            valor_total: valorTotal,
            valor_total_formateado: formatCurrency(valorTotal)
        };

        ordenDetalles.push(detalle);
        updateProductsTable();
        clearProductForm();
        updateUI();
    }

    function updateProductsTable() {
        productosTableBody.innerHTML = '';
        let totalOrden = 0;

        ordenDetalles.forEach((detalle, index) => {
            totalOrden += detalle.valor_total;

            const row = document.createElement('tr');
            row.innerHTML = `
                <td class="align-middle">
                    <button type="button" class="btn btn-danger btn-xs" onclick="removeProduct(${index})" title="Eliminar producto">
                        <i class="fa fa-trash"></i>
                    </button>
                </td>
                <td class="align-middle">${detalle.descripcion}</td>
                <td class="align-middle">
                    <input type="number" class="form-control form-control-sm" value="${detalle.cantidad}"
                           min="1" onchange="updateQuantity(${index}, this.value)">
                </td>
                <td class="text-end align-middle">${detalle.valor_formateado}</td>
                <td class="text-end align-middle">${detalle.valor_total_formateado}</td>
            `;
            productosTableBody.appendChild(row);
        });

        totalOrdenElement.textContent = formatCurrency(totalOrden);
    }

    function clearProductForm() {
        selectedProduct = null;
        productoSearch.value = '';
        cantidadProducto.value = '1';
        valorUnitario.value = '';
        valorTotalProducto.value = '';
        updateAddButton();
    }

    function clearProducts() {
        ordenDetalles = [];
        updateProductsTable();
        updateUI();
    }

    function clearForm() {
        ordenForm.reset();
        ordenForm.classList.remove('was-validated');
        clearProducts();
        clearProductForm();
        addProductSection.style.display = 'none';
        emptyProducts.style.display = 'block';
        updateUI();
    }

    function updateUI() {
        const hasProducts = ordenDetalles.length > 0;
        const hasCentroCosto = centroCostoSelect.value !== '';

        btnGuardar.disabled = !hasProducts;

        if (hasProducts) {
            emptyProducts.style.display = 'none';
            productosTableContainer.style.display = 'block';
            // Disable centro costo if products exist
            centroCostoSelect.disabled = true;
        } else {
            centroCostoSelect.disabled = false;
            productosTableContainer.style.display = 'none';
            if (!hasCentroCosto) {
                emptyProducts.style.display = 'block';
            }
        }
    }

    function saveOrden() {
        const formData = new FormData();
        formData.append('action', 'create_orden_compra');
        formData.append('id_proveedor', document.getElementById('id_proveedor').value);
        formData.append('id_centro_costo', centroCostoSelect.value);
        formData.append('n_referencia_proveedor', document.getElementById('n_referencia_proveedor').value);
        formData.append('detalles', JSON.stringify(ordenDetalles));

        btnGuardar.disabled = true;
        btnGuardar.innerHTML = '<i class="fa fa-spinner fa-spin"></i> Guardando...';

        fetch('crear-orden-compra', {
            method: 'POST',
            body: formData
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                swal({
                    title: "¡Éxito!",
                    text: data.message,
                    icon: "success",
                    button: {
                        text: "Aceptar",
                        value: true,
                        visible: true,
                        className: "btn-success",
                        closeModal: true
                    }
                }).then(() => {
                    window.location.href = 'ordenes-compra';
                });
            } else {
                swal({
                    title: "Error",
                    text: data.message,
                    icon: "error",
                    button: {
                        text: "Cerrar",
                        value: null,
                        visible: true,
                        className: "btn-danger",
                        closeModal: true
                    }
                });
                btnGuardar.disabled = false;
                btnGuardar.innerHTML = '<i class="fa fa-save"></i> Guardar Orden';
            }
        })
        .catch(error => {
            swal({
                title: "Error de Conexión",
                text: 'Error de conexión al guardar orden de compra',
                icon: "error",
                button: {
                    text: "Cerrar",
                    value: null,
                    visible: true,
                    className: "btn-danger",
                    closeModal: true
                }
            });
            btnGuardar.disabled = false;
            btnGuardar.innerHTML = '<i class="fa fa-save"></i> Guardar Orden';
            console.error('Error:', error);
        });
    }

    // Global functions for table actions
    window.removeProduct = function(index) {
        const producto = ordenDetalles[index];
        swal({
            title: 'Confirmar eliminación',
            text: `¿Está seguro que desea eliminar "${producto.descripcion}" del detalle?`,
            icon: 'warning',
            buttons: {
                cancel: {
                    text: 'Cancelar',
                    value: null,
                    visible: true,
                    className: 'btn btn-default',
                    closeModal: true
                },
                confirm: {
                    text: 'Sí, eliminar',
                    value: true,
                    visible: true,
                    className: 'btn btn-danger',
                    closeModal: true
                }
            }
        }).then((result) => {
            if (result) {
                ordenDetalles.splice(index, 1);
                updateProductsTable();
                updateUI();
                // Show success toast notification
                swal({
                    title: 'Producto eliminado',
                    text: `"${producto.descripcion}" ha sido eliminado del detalle`,
                    icon: 'success',
                    timer: 2000,
                    button: false
                });
            }
        });
    };

    window.updateQuantity = function(index, newQuantity) {
        const cantidad = parseInt(newQuantity) || 1;
        ordenDetalles[index].cantidad = cantidad;
        ordenDetalles[index].valor_total = ordenDetalles[index].valor * cantidad;
        ordenDetalles[index].valor_total_formateado = formatCurrency(ordenDetalles[index].valor_total);
        updateProductsTable();
    };

    function formatCurrency(amount) {
        return '$' + new Intl.NumberFormat('es-CO').format(amount);
    }

    function parseCurrencyValue(value) {
        if (!value) return 0;
        // Handle Colombian Peso format: remove $ and convert thousands separators
        let cleanValue = value.toString().replace(/\$/g, '');
        // Remove all dots (thousands separators in Colombian format)
        cleanValue = cleanValue.replace(/\./g, '');
        // Replace commas with dots for decimal points
        cleanValue = cleanValue.replace(/,/g, '.');
        return parseFloat(cleanValue) || 0;
    }


}
</script>

</body>

</html>
