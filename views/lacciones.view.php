<?php
#region region DOCS

/** @var Accion[] $acciones */
/** @var string $filtro_nombre Filtro por nombre de acción */
/** @var string $filtro_grupo Filtro por grupo de acción */
/** @var array $grupos_unicos Lista de grupos únicos para el filtro */

use App\classes\Accion;

#endregion DOCS
?>
<!DOCTYPE html>
<html lang="en" class="dark-mode">
<head>
	<meta charset="utf-8"/>
	<title><?php echo APP_NAME; ?> | Acciones</title>
	<meta content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no" name="viewport"/>
	<meta content="" name="description"/>
	<meta content="" name="author"/>

	<?php #region HEAD ?>
	<?php require_once __ROOT__ . '/views/general/head.view.php'; ?>
	<?php #endregion HEAD ?>
</head>
<body>
<!-- BEGIN #loader -->
<div id="loader" class="app-loader">
	<span class="spinner"></span>
</div>
<!-- END #loader -->

<!-- BEGIN #app -->
<div id="app" class="app app-header-fixed app-sidebar-fixed">
	<!-- #header -->
	<?php require_once __ROOT__ . '/views/general/header.view.php'; ?>

	<!-- #sidebar -->
	<?php require_once __ROOT__ . '/views/general/sidebar.view.php'; ?>

	<!-- BEGIN #content -->
	<div id="content" class="app-content">
		<?php #region region PAGE HEADER ?>
		<div class="d-flex align-items-center mb-3">
			<div>
				<h4 class="mb-0">Gestión de Acciones</h4>
				<p class="mb-0 text-muted">Administra las acciones del sistema</p>
			</div>
			<div class="ms-auto">
				<a href="iaccion" class="btn btn-success"><i class="fa fa-plus-circle fa-fw me-1"></i> Crear nueva</a>
			</div>
		</div>

		<hr>
		<?php #endregion PAGE HEADER ?>

		<?php #region region FILTER FORM ?>
		<div class="mb-3">
			<form action="lacciones" method="GET" class="row g-2">
				<div class="col-md-4">
					<div class="input-group">
						<input type="text" class="form-control" placeholder="Filtrar por nombre" name="filtro_nombre" value="<?php echo htmlspecialchars($filtro_nombre ?? ''); ?>">
					</div>
				</div>
				<div class="col-md-4">
					<div class="input-group">
						<select class="form-select" name="filtro_grupo">
							<option value="">-- Todos los grupos --</option>
							<?php foreach ($grupos_unicos as $grupo): ?>
								<option value="<?php echo htmlspecialchars($grupo); ?>" <?php echo ($filtro_grupo == $grupo) ? 'selected' : ''; ?>>
									<?php echo htmlspecialchars($grupo); ?>
								</option>
							<?php endforeach; ?>
						</select>
					</div>
				</div>
				<div class="col-md-4">
					<button class="btn btn-primary me-1" type="submit"><i class="fa fa-search"></i> Filtrar</button>
					<?php if (!empty($filtro_nombre) || !empty($filtro_grupo)): ?>
						<a href="lacciones" class="btn btn-secondary"><i class="fa fa-times"></i> Limpiar</a>
					<?php endif; ?>
				</div>
			</form>
		</div>
		<?php #endregion FILTER FORM ?>

		<?php #region region PANEL ACCIONES ?>
		<div class="panel panel-inverse mt-3 no-border-radious">
			<div class="panel-heading no-border-radious">
				<h4 class="panel-title">
					Listado de Acciones
				</h4>
			</div>
			<!-- BEGIN PANEL body -->
			<div class="p-1 table-nowrap" style="overflow: auto">
				<?php #region region TABLE ACCIONES ?>
				<table class="table table-hover table-sm">
					<thead>
					<tr>
						<th class="w-70px text-center">Acciones</th>
						<th>Nombre</th>
						<th>Grupo</th>
						<th>Menú</th>
						<th>Submenú</th>
						<th class="text-center">Estado</th>
					</tr>
					</thead>
					<tbody class="fs-12px" id="accion-table-body">
					<?php foreach ($acciones as $accion): ?>
						<tr data-accion-id="<?php echo $accion->getId(); ?>">
							<td class="text-center">
								<?php // Edit Button ?>
								<a href="iaccion?id=<?php echo $accion->getId(); ?>" class="btn btn-xs btn-primary me-1" title="Editar Acción">
									<i class="fa fa-edit"></i>
								</a>
								<?php // Deactivate/Activate Button ?>
								<?php if ($accion->isActiva()): ?>
									<button type="button" class="btn btn-xs btn-danger btn-desactivar-accion"
									        title="Desactivar"
									        data-accionid="<?php echo $accion->getId(); ?>"
									        data-nombre="<?php echo htmlspecialchars($accion->getNombre() ?? ''); ?>">
										<i class="fa fa-trash-alt"></i>
									</button>
								<?php else: ?>
									<button type="button" class="btn btn-xs btn-success btn-activar-accion"
									        title="Activar"
									        data-accionid="<?php echo $accion->getId(); ?>"
									        data-nombre="<?php echo htmlspecialchars($accion->getNombre() ?? ''); ?>">
										<i class="fa fa-check"></i>
									</button>
								<?php endif; ?>
							</td>
							<td><?php echo htmlspecialchars($accion->getNombre()); ?></td>
							<td><?php echo htmlspecialchars($accion->getGrupo() ?? 'N/A'); ?></td>
							<td><?php echo htmlspecialchars($accion->getNombreMenu() ?? 'N/A'); ?></td>
							<td><?php echo htmlspecialchars($accion->getNombreSubmenu() ?? 'N/A'); ?></td>
							<td class="text-center">
								<?php if ($accion->isActiva()): ?>
									<span class="badge bg-success">Activa</span>
								<?php else: ?>
									<span class="badge bg-danger">Inactiva</span>
								<?php endif; ?>
							</td>
						</tr>
					<?php endforeach; ?>
					<?php if (empty($acciones)): ?>
						<tr>
							<td colspan="6" class="text-center">No hay acciones para mostrar.</td>
						</tr>
					<?php endif; ?>

					</tbody>
				</table>
				<?php #endregion TABLE ACCIONES ?>
			</div>
			<!-- END PANEL body -->
		</div>
		<?php #endregion PANEL ACCIONES ?>
	</div>
	<!-- END #content -->

	<?php #region region Hidden Forms for Activation/Deactivation ?>
	<form id="deactivate-accion-form" method="POST" action="lacciones" style="display: none;">
		<input type="hidden" name="action" value="desactivar">
		<input type="hidden" name="accionId" id="deactivate-accion-id">
	</form>

	<form id="activate-accion-form" method="POST" action="lacciones" style="display: none;">
		<input type="hidden" name="action" value="activar">
		<input type="hidden" name="accionId" id="activate-accion-id">
	</form>
	<?php #endregion Hidden Forms ?>

	<!-- BEGIN scroll-top-btn -->
	<a href="#" class="btn btn-icon btn-circle btn-success btn-scroll-to-top" data-toggle="scroll-to-top"><i class="fa fa-angle-up"></i></a>
	<!-- END scroll-top-btn -->
</div>
<!-- END #app -->

<?php #region region JS ?>
<?php require_once __ROOT__ . '/views/general/core_js.view.php'; ?>

<!-- ================== BEGIN PAGE LEVEL JS ================== -->
<script>
    document.addEventListener('DOMContentLoaded', function () {
        // Use event delegation on the table body
        const tableBody = document.getElementById('accion-table-body');
        const deactivateForm = document.getElementById('deactivate-accion-form');
        const activateForm = document.getElementById('activate-accion-form');
        const deactivateAccionIdInput = document.getElementById('deactivate-accion-id');
        const activateAccionIdInput = document.getElementById('activate-accion-id');

        if (tableBody) {
            tableBody.addEventListener('click', function (event) {
                const deactivateButton = event.target.closest('.btn-desactivar-accion');
                const activateButton = event.target.closest('.btn-activar-accion');

                // --- Handle Deactivate Click ---
                if (deactivateButton) {
                    event.preventDefault();
                    const accionId = deactivateButton.dataset.accionid;
                    const accionNombre = deactivateButton.dataset.nombre || 'esta acción';

                    swal({
                        title: "Confirmar Desactivación",
                        text: `¿Seguro que quieres desactivar la acción '${accionNombre}'?`,
                        icon: "warning",
                        buttons: {
                            cancel: {text: "Cancelar", value: null, visible: true, className: "", closeModal: true},
                            confirm: {text: "Confirmar", value: true, visible: true, className: "btn-danger", closeModal: true}
                        },
                        dangerMode: true,
                    })
                    .then((willDeactivate) => {
                        if (willDeactivate) {
                            // Submit the form
                            deactivateAccionIdInput.value = accionId;
                            deactivateForm.submit();
                        }
                    });
                }

                // --- Handle Activate Click ---
                if (activateButton) {
                    event.preventDefault();
                    const accionId = activateButton.dataset.accionid;
                    const accionNombre = activateButton.dataset.nombre || 'esta acción';

                    swal({
                        title: "Confirmar Activación",
                        text: `¿Seguro que quieres activar la acción '${accionNombre}'?`,
                        icon: "info",
                        buttons: {
                            cancel: {text: "Cancelar", value: null, visible: true, className: "", closeModal: true},
                            confirm: {text: "Confirmar", value: true, visible: true, className: "btn-success", closeModal: true}
                        }
                    })
                    .then((willActivate) => {
                        if (willActivate) {
                            // Submit the form
                            activateAccionIdInput.value = accionId;
                            activateForm.submit();
                        }
                    });
                }
            });
        }

        // Display flash messages if they exist
        <?php if (isset($_SESSION['flash_message_success'])): ?>
        swal({
            title: "Éxito",
            text: "<?php echo $_SESSION['flash_message_success']; ?>",
            icon: "success",
            button: "Aceptar"
        });
        <?php unset($_SESSION['flash_message_success']); ?>
        <?php endif; ?>

        <?php if (isset($_SESSION['flash_message_error'])): ?>
        swal({
            title: "Error",
            text: "<?php echo $_SESSION['flash_message_error']; ?>",
            icon: "error",
            button: "Aceptar"
        });
        <?php unset($_SESSION['flash_message_error']); ?>
        <?php endif; ?>
    });
</script>
<!-- ================== END PAGE LEVEL JS ================== -->
<?php #endregion JS ?>
</body>
</html>
