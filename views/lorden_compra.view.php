<?php
/**
 * Vista para la consulta de órdenes de compra
 *
 * Variables disponibles:
 * @var array $proveedores Lista de proveedores activos
 * @var array $centros_costos Lista de centros de costo activos
 * @var string $error_display Estado de visualización de errores ('show' o 'hide')
 * @var string $error_text Texto del mensaje de error
 * @var string $success_display Estado de visualización de éxito ('show' o 'hide')
 * @var string $success_text Texto del mensaje de éxito
 */
?>

<!DOCTYPE html>
<html lang="en" class="dark-mode">
<head>
	<meta charset="utf-8"/>
	<title><?php echo APP_NAME; ?> | Consulta de Órdenes de Compra</title>
	<meta content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no" name="viewport"/>
	<meta content="" name="description"/>
	<meta content="" name="author"/>

	<?php #region HEAD ?>
	<?php require_once __ROOT__ . '/views/general/head.view.php'; ?>
	<?php #endregion HEAD ?>

	<!-- Include Bootstrap Datepicker CSS -->
	<link href="<?php echo RUTA_ADM_ASSETS; ?>plugins/bootstrap-datepicker/dist/css/bootstrap-datepicker.css" rel="stylesheet"/>
</head>

<body>
<!-- BEGIN #loader -->
<div id="loader" class="app-loader">
	<span class="spinner"></span>
</div>
<!-- END #loader -->

<!-- BEGIN #app -->
<div id="app" class="app app-header-fixed app-sidebar-fixed">
	<!-- #header -->
	<?php require_once __ROOT__ . '/views/general/header.view.php'; ?>

	<!-- #sidebar -->
	<?php require_once __ROOT__ . '/views/general/sidebar.view.php'; ?>

        <!-- BEGIN #content -->
        <div id="content" class="app-content">

                        <?php #region PAGE HEADER ?>
                        <div class="d-flex align-items-center mb-3">
                            <div>
                                <h4 class="mb-0">Consulta de Órdenes de Compra</h4>
                                <p class="mb-0 text-muted">Gestión y consulta de órdenes de compra realizadas</p>
                            </div>
                            <div class="ms-auto">
                                <a href="crear-orden-compra" class="btn btn-success">
                                    <i class="fa fa-plus-circle fa-fw me-1"></i> Crear Nueva Orden
                                </a>
                            </div>
                        </div>

                        <hr>
                        <?php #endregion PAGE HEADER ?>

                        <?php #region SEARCH FILTERS ?>
                        <!-- Search Filters Panel -->
                        <div class="panel panel-inverse">
                            <div class="panel-heading">
                                <h4 class="panel-title">Filtros de Búsqueda</h4>
                            </div>
                            <div class="panel-body">
                                <form id="search-form">
                                    <div class="row g-3">
                                        <!-- Numero de Orden Filter -->
                                        <div class="col-md-2">
                                            <label for="numero_orden" class="form-label">Num. de orden</label>
                                            <input type="text" class="form-control" id="numero_orden" name="numero_orden"
                                                placeholder="ID de orden...">
                                        </div>

                                        <!-- Proveedor Filter -->
                                        <div class="col-md-3">
                                            <label for="termino_proveedor" class="form-label">Proveedor</label>
                                            <input type="text" class="form-control" id="termino_proveedor" name="termino_proveedor"
                                                placeholder="Buscar por nombre de proveedor...">
                                        </div>

                                        <!-- Date From -->
                                        <div class="col-md-2">
                                            <label for="fecha_desde" class="form-label">Fecha Desde</label>
                                            <div class="input-group">
                                                <input type="text" class="form-control datepicker" id="fecha_desde" name="fecha_desde"
                                                    placeholder="yyyy-mm-dd" autocomplete="off">
                                                <span class="input-group-text" id="fecha_desde_icon" style="cursor: pointer;">
                                                    <i class="fa fa-calendar"></i>
                                                </span>
                                            </div>
                                        </div>

                                        <!-- Date To -->
                                        <div class="col-md-2">
                                            <label for="fecha_hasta" class="form-label">Fecha Hasta</label>
                                            <div class="input-group">
                                                <input type="text" class="form-control datepicker" id="fecha_hasta" name="fecha_hasta"
                                                    placeholder="yyyy-mm-dd" autocomplete="off">
                                                <span class="input-group-text" id="fecha_hasta_icon" style="cursor: pointer;">
                                                    <i class="fa fa-calendar"></i>
                                                </span>
                                            </div>
                                        </div>

                                        <!-- Centro de Costo Filter -->
                                        <div class="col-md-3">
                                            <label for="id_centro_costo" class="form-label">Centro de Costo</label>
                                            <select class="form-select" id="id_centro_costo" name="id_centro_costo">
                                                <option value="">Todos los centros de costo</option>
                                                <?php foreach ($centros_costos as $centro): ?>
                                                    <option value="<?= $centro->getId() ?>"><?= htmlspecialchars($centro->getNombre()) ?></option>
                                                <?php endforeach; ?>
                                            </select>
                                        </div>

                                        <!-- Numero Referencia Filter -->
                                        <div class="col-md-2">
                                            <label for="numero_referencia" class="form-label">Núm. Referencia</label>
                                            <input type="text" class="form-control" id="numero_referencia" name="numero_referencia"
                                                placeholder="Referencia...">
                                        </div>
                                    </div>

                                    <div class="row mt-3">
                                        <div class="col-12">
                                            <button type="submit" class="btn btn-primary">
                                                <i class="fa fa-search fa-fw me-1"></i> Buscar
                                            </button>
                                            <button type="button" class="btn btn-secondary" id="btn-limpiar">
                                                <i class="fa fa-eraser fa-fw me-1"></i> Limpiar
                                            </button>
                                        </div>
                                    </div>
                                </form>
                            </div>
                        </div>
                        <?php #endregion SEARCH FILTERS ?>

                        <?php #region RESULTS ?>
                        <!-- Results Panel -->
                        <div class="panel panel-inverse">
                            <div class="panel-heading">
                                <h4 class="panel-title">Resultados de la Búsqueda</h4>
                            </div>
                            <div class="panel-body">
                                <!-- No results message -->
                                <div id="no-results" class="text-center py-4">
                                    <i class="fa fa-search fa-3x text-muted mb-3"></i>
                                    <h5 class="text-muted">Utilice los filtros para buscar órdenes de compra</h5>
                                    <p class="text-muted">Los resultados aparecerán aquí una vez que realice una búsqueda.</p>
                                </div>

                                <!-- Loading indicator -->
                                <div id="loading" class="text-center py-4" style="display: none;">
                                    <i class="fa fa-spinner fa-spin fa-3x text-primary mb-3"></i>
                                    <h5 class="text-primary">Buscando órdenes de compra...</h5>
                                </div>

                                <!-- Results table -->
                                <div id="results-table-container" style="overflow-x: auto; display: none;">
                                    <table class="table table-hover table-sm mb-0" id="ordenes-table">
                                        <thead class="table-dark">
                                            <tr>
                                                <th class="text-center" style="width: 120px;">Acciones</th>
                                                <th style="width: 60px;">#</th>
                                                <th style="width: 120px;">Fecha</th>
                                                <th>Proveedor</th>
                                                <th>Centro de Costo</th>
                                                <th style="width: 120px;">Núm. Referencia</th>
                                                <th class="text-end" style="width: 120px;">Valor Total</th>
                                            </tr>
                                        </thead>
                                        <tbody id="ordenes-table-body">
                                            <!-- Results will be populated via AJAX -->
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                        <?php #endregion RESULTS ?>

        </div>
        <!-- END #content -->

        <?php #region View Details Modal ?>
        <div class="modal fade" id="viewDetailsModal" tabindex="-1" aria-labelledby="viewDetailsModalLabel" aria-hidden="true">
            <div class="modal-dialog modal-xl">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title" id="viewDetailsModalLabel">Detalles de la Orden de Compra</h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                    </div>
                    <div class="modal-body">
                        <!-- Loading indicator -->
                        <div id="modal-loading" class="text-center py-4">
                            <i class="fa fa-spinner fa-spin fa-3x text-primary mb-3"></i>
                            <h5 class="text-primary">Cargando detalles...</h5>
                        </div>

                        <!-- Order details content -->
                        <div id="modal-content" style="display: none;">
                            <!-- Order Header Information -->
                            <div class="panel panel-inverse mb-3">
                                <div class="panel-heading">
                                    <h4 class="panel-title">Información General</h4>
                                </div>
                                <div class="panel-body">
                                    <div class="row">
                                        <div class="col-md-6">
                                            <div class="mb-3">
                                                <label class="form-label fw-bold text-primary">Número de Orden:</label>
                                                <div id="detail-numero-orden" class="form-control-plaintext text-white fs-5"></div>
                                            </div>
                                            <div class="mb-3">
                                                <label class="form-label fw-bold text-primary">Fecha:</label>
                                                <div id="detail-fecha" class="form-control-plaintext text-white fs-5"></div>
                                            </div>
                                            <div class="mb-3">
                                                <label class="form-label fw-bold text-primary">Proveedor:</label>
                                                <div id="detail-proveedor" class="form-control-plaintext text-white fs-5"></div>
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="mb-3">
                                                <label class="form-label fw-bold text-primary">Centro de Costo:</label>
                                                <div id="detail-centro-costo" class="form-control-plaintext text-white fs-5"></div>
                                            </div>
                                            <div class="mb-3">
                                                <label class="form-label fw-bold text-primary">Número de Referencia:</label>
                                                <div id="detail-numero-referencia" class="form-control-plaintext text-white fs-5"></div>
                                            </div>
                                            <div class="mb-3">
                                                <label class="form-label fw-bold text-primary">Usuario:</label>
                                                <div id="detail-usuario" class="form-control-plaintext text-white fs-5"></div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Order Details Table -->
                            <div class="panel panel-inverse">
                                <div class="panel-heading">
                                    <h4 class="panel-title">Detalle de Productos</h4>
                                </div>
                                <div class="panel-body">
                                    <div class="table-responsive">
                                        <table class="table table-hover table-sm mb-0" id="details-table">
                                            <thead class="table-dark">
                                                <tr>
                                                    <th>Producto</th>
                                                    <th class="text-center" style="width: 100px;">Cantidad</th>
                                                    <th class="text-end" style="width: 120px;">Valor Unitario</th>
                                                    <th class="text-end" style="width: 120px;">Valor Total</th>
                                                </tr>
                                            </thead>
                                            <tbody id="details-table-body">
                                                <!-- Details will be populated via AJAX -->
                                            </tbody>
                                            <tfoot>
                                                <tr class="table-info">
                                                    <th colspan="3" class="text-end fw-bold">Total General:</th>
                                                    <th class="text-end fw-bold" id="detail-total-general"></th>
                                                </tr>
                                            </tfoot>
                                        </table>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cerrar</button>
                    </div>
                </div>
            </div>
        </div>
        <?php #endregion View Details Modal ?>

        <!-- BEGIN scroll-top-btn -->
        <a href="javascript:;" class="btn btn-icon btn-circle btn-theme btn-scroll-to-top" data-toggle="scroll-to-top">
            <i class="fa fa-angle-up"></i>
        </a>
        <!-- END scroll-top-btn -->
    </div>
    <!-- END #app -->

    <?php #region JS ?>
    <?php require_once __ROOT__ . '/views/general/core_js.view.php'; ?>

    <!-- Include Bootstrap Datepicker JS -->
    <script src="<?php echo RUTA_ADM_ASSETS; ?>plugins/bootstrap-datepicker/dist/js/bootstrap-datepicker.js"></script>

    <!-- Include SweetAlert JS -->
    <script src="<?php echo RUTA_ADM_ASSETS; ?>plugins/sweetalert/dist/sweetalert.min.js"></script>
    <?php #endregion JS ?>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Initialize datepickers
    $('.datepicker').datepicker({
        autoclose     : true,
        todayHighlight: true,
        format        : 'yyyy-mm-dd'
    });

    // Make calendar icons clickable
    document.getElementById('fecha_desde_icon').addEventListener('click', function() {
        $('#fecha_desde').datepicker('show');
    });

    document.getElementById('fecha_hasta_icon').addEventListener('click', function() {
        $('#fecha_hasta').datepicker('show');
    });

    // Elements
    const searchForm            = document.getElementById('search-form');
    const btnLimpiar            = document.getElementById('btn-limpiar');
    const noResults             = document.getElementById('no-results');
    const loading               = document.getElementById('loading');
    const resultsTableContainer = document.getElementById('results-table-container');
    const ordenesTableBody      = document.getElementById('ordenes-table-body');

    // Search form submission
    searchForm.addEventListener('submit', function(e) {
        e.preventDefault();
        searchOrdenes();
    });

    // Clear button
    btnLimpiar.addEventListener('click', function() {
        searchForm.reset();
        noResults.style.display = 'block';
        resultsTableContainer.style.display = 'none';
    });

    // Search function
    function searchOrdenes() {
        const formData = new FormData(searchForm);
        formData.append('action', 'search_ordenes_compra');

        // Show loading
        noResults.style.display = 'none';
        resultsTableContainer.style.display = 'none';
        loading.style.display = 'block';

        fetch('ordenes-compra', {
            method: 'POST',
            body: formData
        })
        .then(response => response.json())
        .then(data => {
            loading.style.display = 'none';

            if (data.success) {
                if (data.ordenes.length > 0) {
                    displayResults(data.ordenes);
                } else {
                    showNoResults();
                }
            } else {
                swal({
                    title: "Error",
                    text: data.message || 'Error al buscar órdenes de compra',
                    icon: "error",
                    button: {
                        text: "Cerrar",
                        value: null,
                        visible: true,
                        className: "btn-danger",
                        closeModal: true
                    }
                });
                showNoResults();
            }
        })
        .catch(error => {
            loading.style.display = 'none';
            swal({
                title: "Error de Conexión",
                text: 'Error de conexión al buscar órdenes de compra',
                icon: "error",
                button: {
                    text: "Cerrar",
                    value: null,
                    visible: true,
                    className: "btn-danger",
                    closeModal: true
                }
            });
            showNoResults();
            console.error('Error:', error);
        });
    }

    // Display results in table
    function displayResults(ordenes) {
        ordenesTableBody.innerHTML = '';

        ordenes.forEach(orden => {
            const row = document.createElement('tr');
            row.innerHTML = `
                <td class="text-center align-middle">
                    <button class="btn btn-info btn-xs me-1" onclick="verDetallesOrden(${orden.id})" title="Ver detalles de la orden">
                        <i class="fa fa-eye"></i>
                    </button>
                    <button class="btn btn-danger btn-xs" onclick="eliminarOrden(${orden.id})" title="Eliminar orden de compra">
                        <i class="fa fa-trash"></i>
                    </button>
                </td>
                <td class="align-middle">${orden.id}</td>
                <td class="align-middle">${orden.fecha_formateada}</td>
                <td class="align-middle">${orden.proveedor_nombre}</td>
                <td class="align-middle">${orden.centro_costo_nombre}</td>
                <td class="align-middle">${orden.n_referencia_proveedor}</td>
                <td class="text-end align-middle">${orden.valor_total_formateado}</td>
            `;
            ordenesTableBody.appendChild(row);
        });

        resultsTableContainer.style.display = 'block';
        noResults.style.display = 'none';
    }

    // Show no results message
    function showNoResults() {
        noResults.innerHTML = `
            <i class="fa fa-search fa-3x text-muted mb-3"></i>
            <h5 class="text-muted">No se encontraron órdenes de compra</h5>
            <p class="text-muted">Intente modificar los criterios de búsqueda.</p>
        `;
        noResults.style.display = 'block';
        resultsTableContainer.style.display = 'none';
    }

    // Global function for deleting orden
    window.eliminarOrden = function(ordenId) {
        swal({
            title: "¿Está seguro?",
            text: "Esta acción eliminará la orden de compra y corregirá el inventario. No se puede deshacer.",
            icon: "warning",
            buttons: {
                cancel: {
                    text      : "Cancelar",
                    value     : null,
                    visible   : true,
                    className : "btn-secondary",
                    closeModal: true,
                },
                confirm: {
                    text      : "Sí, eliminar",
                    value     : true,
                    visible   : true,
                    className : "btn-danger",
                    closeModal: true
                }
            }
        }).then((willDelete) => {
            if (willDelete) {
                deleteOrden(ordenId);
            }
        });
    };

    // Delete orden function
    function deleteOrden(ordenId) {
        const formData = new FormData();
        formData.append('action', 'delete_orden_compra');
        formData.append('orden_id', ordenId);

        fetch('ordenes-compra', {
            method: 'POST',
            body: formData
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                swal({
                    title: "¡Éxito!",
                    text: data.message,
                    icon: "success",
                    button: {
                        text: "Aceptar",
                        value: true,
                        visible: true,
                        className: "btn-success",
                        closeModal: true
                    }
                });
                // Refresh search results
                searchOrdenes();
            } else {
                swal({
                    title: "Error",
                    text: data.message,
                    icon: "error",
                    button: {
                        text: "Cerrar",
                        value: null,
                        visible: true,
                        className: "btn-danger",
                        closeModal: true
                    }
                });
            }
        })
        .catch(error => {
            swal({
                title: "Error de Conexión",
                text: 'Error de conexión al eliminar orden de compra',
                icon: "error",
                button: {
                    text: "Cerrar",
                    value: null,
                    visible: true,
                    className: "btn-danger",
                    closeModal: true
                }
            });
            console.error('Error:', error);
        });
    }

    // Global function for viewing order details
    window.verDetallesOrden = function(ordenId) {
        const modal = new bootstrap.Modal(document.getElementById('viewDetailsModal'));
        const modalLoading = document.getElementById('modal-loading');
        const modalContent = document.getElementById('modal-content');

        // Show modal and loading state
        modal.show();
        modalLoading.style.display = 'block';
        modalContent.style.display = 'none';

        // Fetch order details
        const formData = new FormData();
        formData.append('action', 'get_orden_details');
        formData.append('orden_id', ordenId);

        fetch('ordenes-compra', {
            method: 'POST',
            body: formData
        })
        .then(response => response.json())
        .then(data => {
            modalLoading.style.display = 'none';

            if (data.success) {
                populateOrderDetails(data.orden, data.detalles);
                modalContent.style.display = 'block';
            } else {
                swal({
                    title: "Error",
                    text: data.message || 'Error al cargar los detalles de la orden',
                    icon: "error",
                    button: {
                        text: "Cerrar",
                        value: null,
                        visible: true,
                        className: "btn-danger",
                        closeModal: true
                    }
                });
                modal.hide();
            }
        })
        .catch(error => {
            modalLoading.style.display = 'none';
            swal({
                title: "Error de Conexión",
                text: 'Error de conexión al cargar los detalles de la orden',
                icon: "error",
                button: {
                    text: "Cerrar",
                    value: null,
                    visible: true,
                    className: "btn-danger",
                    closeModal: true
                }
            });
            modal.hide();
            console.error('Error:', error);
        });
    };

    // Function to populate order details in modal
    function populateOrderDetails(orden, detalles) {
        // Populate header information
        document.getElementById('detail-numero-orden').textContent = orden.id;
        document.getElementById('detail-fecha').textContent = orden.fecha_formateada;
        document.getElementById('detail-proveedor').textContent = orden.proveedor_nombre;
        document.getElementById('detail-centro-costo').textContent = orden.centro_costo_nombre;
        document.getElementById('detail-numero-referencia').textContent = orden.n_referencia_proveedor || 'N/A';
        document.getElementById('detail-usuario').textContent = orden.usuario_nombre;
        document.getElementById('detail-total-general').textContent = orden.valor_total_formateado;

        // Populate details table
        const detailsTableBody = document.getElementById('details-table-body');
        detailsTableBody.innerHTML = '';

        detalles.forEach(detalle => {
            const row = document.createElement('tr');
            row.innerHTML = `
                <td class="align-middle">${detalle.producto_descripcion}</td>
                <td class="text-center align-middle">${detalle.cantidad}</td>
                <td class="text-end align-middle">${detalle.valor_formateado}</td>
                <td class="text-end align-middle">${detalle.valor_total_formateado}</td>
            `;
            detailsTableBody.appendChild(row);
        });
    }


});
</script>

</body>

</html>
