<?php
#region region DOCS

/** @var Perfil[] $perfiles */

use App\classes\Perfil;

#endregion DOCS
?>
<!DOCTYPE html>
<html lang="en" class="dark-mode">
<head>
	<meta charset="utf-8"/>
	<title><?php echo APP_NAME; ?> | Perfiles</title>
	<meta content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no" name="viewport"/>
	<meta content="" name="description"/>
	<meta content="" name="author"/>
	
	<?php #region HEAD ?>
	<?php require_once __ROOT__ . '/views/general/head.view.php'; ?>
	<?php #endregion HEAD ?>
</head>
<body>
<!-- BEGIN #loader -->
<div id="loader" class="app-loader">
	<span class="spinner"></span>
</div>
<!-- END #loader -->

<!-- BEGIN #app -->
<div id="app" class="app app-header-fixed app-sidebar-fixed">
	<!-- #header -->
	<?php require_once __ROOT__ . '/views/general/header.view.php'; ?>
	
	<!-- #sidebar -->
	<?php require_once __ROOT__ . '/views/general/sidebar.view.php'; ?>
	
	<!-- BEGIN #content -->
	<div id="content" class="app-content">
		<?php #region region PAGE HEADER ?>
		<div class="d-flex align-items-center mb-3">
			<div>
				<h4 class="mb-0">Gestión de Perfiles</h4>
				<p class="mb-0 text-muted">Administra los perfiles del sistema</p>
			</div>
			<div class="ms-auto">
				<button type="button" class="btn btn-success" data-bs-toggle="modal" data-bs-target="#createPerfilModal">
					<i class="fa fa-plus-circle fa-fw me-1"></i> Crear nuevo
				</button>
			</div>
		</div>
		
		<hr>
		<?php #endregion PAGE HEADER ?>
		
		<?php #region region PANEL PERFILES ?>
		<div class="panel panel-inverse mt-3 no-border-radious">
			<div class="panel-heading no-border-radious">
				<h4 class="panel-title">
					Listado de Perfiles Activos
				</h4>
			</div>
			<!-- BEGIN PANEL body -->
			<div class="p-1 table-nowrap" style="overflow: auto">
				<?php #region region TABLE PERFILES ?>
				<table class="table table-hover table-sm">
					<thead>
					<tr>
						<th>Acciones</th>
						<th>ID</th>
						<th>Nombre</th>
						<th>Estado</th>
					</tr>
					</thead>
					<tbody class="fs-12px" id="perfil-table-body">
					<?php foreach ($perfiles as $perfil): ?>
						<tr data-perfil-id="<?php echo $perfil->getId(); ?>">
							<td>
								<?php // Edit Button - Triggers Modal ?>
								<button type="button" class="btn btn-xs btn-primary me-1 btn-edit-perfil"
								        title="Editar Nombre"
								        data-bs-toggle="modal"
								        data-bs-target="#editPerfilModal"
								        data-perfilid="<?php echo $perfil->getId(); ?>"
								        data-nombre="<?php echo htmlspecialchars($perfil->getNombre() ?? ''); ?>">
									<i class="fa fa-edit"></i>
								</button>
								<?php // Edit Acciones Button ?>
								<a href="eperfil?id=<?php echo $perfil->getId(); ?>" class="btn btn-xs btn-info me-1"
								   title="Editar Acciones">
									<i class="fa fa-key"></i>
								</a>
								<?php // Deactivate Button - Only show if active ?>
								<?php if ($perfil->isActivo()): ?>
									<button type="button" class="btn btn-xs btn-danger btn-desactivar-perfil"
									        title="Desactivar"
									        data-perfilid="<?php echo $perfil->getId(); ?>"
									        data-nombre="<?php echo htmlspecialchars($perfil->getNombre() ?? ''); ?>">
										<i class="fa fa-trash-alt"></i>
									</button>
								<?php endif; ?>
							</td>
							<td><?php echo $perfil->getId(); ?></td>
							<td class="perfil-nombre-cell"><?php echo htmlspecialchars($perfil->getNombre()); ?></td>
							<td>
								<?php if ($perfil->isActivo()): ?>
									<span class="badge bg-success">Activo</span>
								<?php else: ?>
									<span class="badge bg-danger">Inactivo</span>
								<?php endif; ?>
							</td>
						</tr>
					<?php endforeach; ?>
					
					<?php if (empty($perfiles)): ?>
						<tr>
							<td colspan="4" class="text-center">No hay perfiles disponibles.</td>
						</tr>
					<?php endif; ?>
					
					</tbody>
				</table>
				<?php #endregion TABLE PERFILES ?>
			</div>
			<!-- END PANEL body -->
		</div>
		<?php #endregion PANEL PERFILES ?>
		
		<?php #region region Create Perfil Modal ?>
		<div class="modal fade" id="createPerfilModal" tabindex="-1" aria-labelledby="createPerfilModalLabel" aria-hidden="true">
			<div class="modal-dialog">
				<div class="modal-content">
					<form id="create-perfil-form">
						<div class="modal-header">
							<h5 class="modal-title" id="createPerfilModalLabel">Crear Nuevo Perfil</h5>
							<button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
						</div>
						<div class="modal-body">
							<input type="hidden" name="action" value="crear">
							
							<div class="mb-3">
								<label for="create-perfil-nombre" class="form-label">Nombre</label>
								<input type="text" class="form-control" id="create-perfil-nombre" name="nombre" required>
							</div>
							
							<div class="alert alert-danger" id="create-perfil-error" style="display: none;"></div>
						</div>
						<div class="modal-footer">
							<button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancelar</button>
							<button type="submit" class="btn btn-primary">Guardar</button>
						</div>
					</form>
				</div>
			</div>
		</div>
		<?php #endregion Create Perfil Modal ?>
		
		<?php #region region Edit Perfil Modal ?>
		<div class="modal fade" id="editPerfilModal" tabindex="-1" aria-labelledby="editPerfilModalLabel" aria-hidden="true">
			<div class="modal-dialog">
				<div class="modal-content">
					<form id="edit-perfil-form">
						<div class="modal-header">
							<h5 class="modal-title" id="editPerfilModalLabel">Editar Nombre de Perfil</h5>
							<button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
						</div>
						<div class="modal-body">
							<input type="hidden" name="perfilId" id="edit-perfil-id">
							<input type="hidden" name="action" value="modificar">
							
							<div class="mb-3">
								<label for="edit-perfil-nombre" class="form-label">Nombre</label>
								<input type="text" class="form-control" id="edit-perfil-nombre" name="nombre" required>
							</div>
							
							<div class="alert alert-danger" id="edit-perfil-error" style="display: none;"></div>
						</div>
						<div class="modal-footer">
							<button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancelar</button>
							<button type="submit" class="btn btn-primary">Guardar Cambios</button>
						</div>
					</form>
				</div>
			</div>
		</div>
		<?php #endregion Edit Perfil Modal ?>
	</div>
	<!-- END #content -->
	
	<?php #region region Hidden Form for Deactivation ?>
	<form id="deactivate-perfil-form" method="POST" action="lperfiles" style="display: none;">
		<input type="hidden" name="action" value="desactivar">
		<input type="hidden" name="perfilId" id="deactivate-perfil-id">
	</form>
	<?php #endregion Hidden Form ?>
	
	<!-- BEGIN scroll-top-btn -->
	<a href="#" class="btn btn-icon btn-circle btn-success btn-scroll-to-top" data-toggle="scroll-to-top"><i class="fa fa-angle-up"></i></a>
	<!-- END scroll-top-btn -->
</div>
<!-- END #app -->

<?php #region region JS ?>
<?php require_once __ROOT__ . '/views/general/core_js.view.php'; ?>

<!-- ================== BEGIN PAGE LEVEL JS ================== -->
<!-- Assuming SweetAlert is loaded in core_js or globally -->
<!-- Assuming SweetAlert helper functions (showSweetAlertSuccess/Error) are available -->
<script>
    document.addEventListener('DOMContentLoaded', function () {
        // Use event delegation on the table body
        const tableBody               = document.getElementById('perfil-table-body');
        const editPerfilModalElement  = document.getElementById('editPerfilModal');
        const editPerfilModal         = new bootstrap.Modal(editPerfilModalElement); // Initialize Bootstrap Modal
        const editPerfilForm          = document.getElementById('edit-perfil-form');
        const editPerfilIdInput       = document.getElementById('edit-perfil-id');
        const editPerfilNombreInput   = document.getElementById('edit-perfil-nombre');
        const editPerfilErrorDiv      = document.getElementById('edit-perfil-error');
        
        const createPerfilModalElement = document.getElementById('createPerfilModal');
        const createPerfilModal        = new bootstrap.Modal(createPerfilModalElement); // Initialize Bootstrap Modal
        const createPerfilForm         = document.getElementById('create-perfil-form');
        const createPerfilNombreInput  = document.getElementById('create-perfil-nombre');
        const createPerfilErrorDiv     = document.getElementById('create-perfil-error');
        
        if (tableBody) {
            tableBody.addEventListener('click', function (event) {
                const deactivateButton = event.target.closest('.btn-desactivar-perfil');
                const editButton       = event.target.closest('.btn-edit-perfil');
                
                // --- Handle Deactivate Click ---
                if (deactivateButton) {
                    event.preventDefault();
                    const perfilId   = deactivateButton.dataset.perfilid;
                    const perfilName = deactivateButton.dataset.nombre || 'este perfil';
                    
                    swal({
                        title     : "Confirmar Desactivación",
                        text      : `¿Seguro que quieres desactivar el perfil '${perfilName}'?`,
                        icon      : "warning",
                        buttons   : {
                            cancel : {text: "Cancelar", value: null, visible: true, className: "", closeModal: true},
                            confirm: {text: "Confirmar", value: true, visible: true, className: "btn-danger", closeModal: true}
                        },
                        dangerMode: true,
                    })
                        .then((willDelete) => {
                            if (willDelete) {
                                // User confirmed, submit the form
                                const deactivateForm = document.getElementById('deactivate-perfil-form');
                                document.getElementById('deactivate-perfil-id').value = perfilId;
                                deactivateForm.submit();
                            }
                        });
                }
                
                // --- Handle Edit Click ---
                if (editButton) {
                    event.preventDefault(); // Prevent default if it was an <a>
                    const perfilId      = editButton.dataset.perfilid;
                    const currentNombre = editButton.dataset.nombre;
                    
                    // Populate the modal form
                    editPerfilIdInput.value          = perfilId;
                    editPerfilNombreInput.value      = currentNombre;
                    editPerfilErrorDiv.style.display = 'none'; // Hide previous errors
                    editPerfilErrorDiv.textContent   = '';
                    
                    // The data-bs-toggle and data-bs-target attributes on the button handle showing the modal
                    // If they weren't there, you'd call: editPerfilModal.show();
                }
            });
        }
        
        // --- Handle Edit Form Submit ---
        if (editPerfilForm) {
            editPerfilForm.addEventListener('submit', function (event) {
                event.preventDefault();
                
                // Clear previous error
                editPerfilErrorDiv.style.display = 'none';
                editPerfilErrorDiv.textContent   = '';
                
                // Get form data
                const formData = new FormData(editPerfilForm);
                
                // Send AJAX request
                fetch('lperfiles', {
                    method     : 'POST',
                    body       : formData,
                    credentials: 'same-origin' // Include cookies
                })
                    .then(response => response.json())
                    .then(data => {
                        if (data.success) {
                            // Update was successful
                            editPerfilModal.hide();
                            
                            // Update the row in the table without reloading
                            const perfilId = editPerfilIdInput.value;
                            const row      = document.querySelector(`tr[data-perfil-id="${perfilId}"]`);
                            if (row) {
                                const nombreCell = row.querySelector('.perfil-nombre-cell');
                                if (nombreCell) {
                                    nombreCell.textContent = data.nombre;
                                }
                                
                                // Also update the data attribute on the edit button
                                const editBtn = row.querySelector('.btn-edit-perfil');
                                if (editBtn) {
                                    editBtn.dataset.nombre = data.nombre;
                                }
                                
                                // Also update the data attribute on the deactivate button
                                const deactivateBtn = row.querySelector('.btn-desactivar-perfil');
                                if (deactivateBtn) {
                                    deactivateBtn.dataset.nombre = data.nombre;
                                }
                            }
                            
                            // Show success message
                            swal({
                                title  : "¡Éxito!",
                                text   : data.message,
                                icon   : "success",
                                buttons: false,
                                timer  : 2000,
                            });
                        } else {
                            // Show error message in the form
                            editPerfilErrorDiv.textContent   = data.message;
                            editPerfilErrorDiv.style.display = 'block';
                        }
                    })
                    .catch(error => {
                        console.error('Error:', error);
                        editPerfilErrorDiv.textContent   = 'Error de conexión. Inténtalo de nuevo.';
                        editPerfilErrorDiv.style.display = 'block';
                    });
            });
        }
        
        // --- Handle Create Form Submit ---
        if (createPerfilForm) {
            createPerfilForm.addEventListener('submit', function (event) {
                event.preventDefault();
                
                // Clear previous error
                createPerfilErrorDiv.style.display = 'none';
                createPerfilErrorDiv.textContent   = '';
                
                // Get form data
                const formData = new FormData(createPerfilForm);
                
                // Send AJAX request
                fetch('lperfiles', {
                    method     : 'POST',
                    body       : formData,
                    credentials: 'same-origin' // Include cookies
                })
                    .then(response => response.json())
                    .then(data => {
                        if (data.success) {
                            // Creation was successful
                            createPerfilModal.hide();
                            
                            // Reset the form for next use
                            createPerfilForm.reset();
                            
                            // Show success message
                            swal({
                                title  : "¡Éxito!",
                                text   : data.message,
                                icon   : "success",
                                buttons: false,
                                timer  : 2000,
                            });
                            
                            // Reload the page to show the new perfil
                            // You could also dynamically add the new row to the table
                            setTimeout(() => {
                                window.location.reload();
                            }, 2000);
                        } else {
                            // Show error message in the form
                            createPerfilErrorDiv.textContent   = data.message;
                            createPerfilErrorDiv.style.display = 'block';
                        }
                    })
                    .catch(error => {
                        console.error('Error:', error);
                        createPerfilErrorDiv.textContent   = 'Error de conexión. Inténtalo de nuevo.';
                        createPerfilErrorDiv.style.display = 'block';
                    });
            });
        }
        
        // --- Handle Success/Error Messages from PHP ---
        <?php if (isset($success_display) && $success_display === 'show'): ?>
        swal({
            title  : "¡Éxito!",
            text   : "<?php echo $success_text ?? 'Operación completada con éxito.'; ?>",
            icon   : "success",
            buttons: false,
            timer  : 3000,
        });
        <?php endif; ?>
        
        <?php if (isset($error_display) && $error_display === 'show'): ?>
        swal({
            title  : "Error",
            text   : "<?php echo $error_text ?? 'Ha ocurrido un error.'; ?>",
            icon   : "error",
            buttons: {
                confirm: {text: "OK", value: true, visible: true, className: "", closeModal: true}
            }
        });
        <?php endif; ?>
    });
</script>
<!-- ================== END PAGE LEVEL JS ================== -->
<?php #endregion JS ?>
</body>
</html>
