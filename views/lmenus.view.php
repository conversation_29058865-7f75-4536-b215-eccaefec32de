<?php
#region region DOCS

/** @var Menu[] $menus */
/** @var string $filtro_texto */

use App\classes\Menu;

#endregion DOCS
?>
<!DOCTYPE html>
<html lang="en" class="dark-mode">
<head>
	<meta charset="utf-8"/>
	<title><?php echo APP_NAME; ?> | Menús</title>
	<meta content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no" name="viewport"/>
	<meta content="" name="description"/>
	<meta content="" name="author"/>

	<?php #region HEAD ?>
	<?php require_once __ROOT__ . '/views/general/head.view.php'; ?>
	<?php #endregion HEAD ?>
</head>
<body>
<!-- BEGIN #loader -->
<div id="loader" class="app-loader">
	<span class="spinner"></span>
</div>
<!-- END #loader -->

<!-- BEGIN #app -->
<div id="app" class="app app-header-fixed app-sidebar-fixed">
	<!-- #header -->
	<?php require_once __ROOT__ . '/views/general/header.view.php'; ?>

	<!-- #sidebar -->
	<?php require_once __ROOT__ . '/views/general/sidebar.view.php'; ?>

	<!-- BEGIN #content -->
	<div id="content" class="app-content">
		<?php #region region PAGE HEADER ?>
		<div class="d-flex align-items-center mb-3">
			<div>
				<h4 class="mb-0">Gestión de Menús</h4>
				<p class="mb-0 text-muted">Administra los menús del sistema</p>
			</div>
			<div class="ms-auto">
				<a href="imenu" class="btn btn-success"><i class="fa fa-plus-circle fa-fw me-1"></i> Crear nuevo</a>
			</div>
		</div>

		<hr>
		<?php #endregion PAGE HEADER ?>

		<?php #region region FILTER FORM ?>
		<div class="mb-3">
			<form action="lmenus" method="GET" class="row g-2">
				<div class="col-md-4">
					<div class="input-group">
						<input type="text" class="form-control" placeholder="Filtrar por texto" name="filtro_texto" value="<?php echo htmlspecialchars($filtro_texto); ?>">
						<button class="btn btn-primary" type="submit"><i class="fa fa-search"></i></button>
						<?php if (!empty($filtro_texto)): ?>
							<a href="lmenus" class="btn btn-secondary"><i class="fa fa-times"></i></a>
						<?php endif; ?>
					</div>
				</div>
			</form>
		</div>
		<?php #endregion FILTER FORM ?>

		<?php #region region PANEL MENUS ?>
		<div class="panel panel-inverse mt-3 no-border-radious">
			<div class="panel-heading no-border-radious">
				<h4 class="panel-title">
					Listado de Menús <?php echo !empty($filtro_texto) ? ' (Filtrado)' : ''; ?>
				</h4>
			</div>
			<!-- BEGIN PANEL body -->
			<div class="p-1 table-nowrap" style="overflow: auto">
				<?php #region region TABLE MENUS ?>
				<table class="table table-hover table-sm">
					<thead>
					<tr>
						<th class="w-70px text-center">Acciones</th>
						<th>Texto</th>
						<th>Icono</th>
						<th>URL</th>
						<th class="text-center">Prioridad</th>
						<th class="text-center">Tiene Submenús</th>
					</tr>
					</thead>
					<tbody class="fs-12px" id="menu-table-body">
					<?php foreach ($menus as $menu): ?>
						<tr data-menu-id="<?php echo $menu->getId(); ?>">
							<td class="text-center">
								<?php // Edit Button ?>
								<a href="imenu?id=<?php echo $menu->getId(); ?>" class="btn btn-xs btn-primary me-1" title="Editar Menú">
									<i class="fa fa-edit"></i>
								</a>
								<?php // Deactivate Button - Only show if active ?>
								<?php if ($menu->isActivo()): ?>
									<button type="button" class="btn btn-xs btn-danger btn-desactivar-menu"
									        title="Desactivar"
									        data-menuid="<?php echo $menu->getId(); ?>"
									        data-texto="<?php echo htmlspecialchars($menu->getTexto() ?? ''); ?>">
										<i class="fa fa-trash-alt"></i>
									</button>
								<?php endif; ?>
							</td>
							<td><?php echo htmlspecialchars($menu->getTexto()); ?></td>
							<td>
								<?php if (!empty($menu->getIcono())): ?>
									<i class="<?php echo htmlspecialchars($menu->getIcono()); ?>"></i>
									<?php echo htmlspecialchars($menu->getIcono()); ?>
								<?php endif; ?>
							</td>
							<td><?php echo htmlspecialchars($menu->getUrl()); ?></td>
							<td class="text-center"><?php echo htmlspecialchars($menu->getPrioridad()); ?></td>
							<td class="text-center">
								<?php if ($menu->hasSubs()): ?>
									<span class="badge bg-success">Sí</span>
								<?php else: ?>
									<span class="badge bg-secondary">No</span>
								<?php endif; ?>
							</td>
						</tr>
					<?php endforeach; ?>
					<?php if (empty($menus)): ?>
						<tr>
							<td colspan="8" class="text-center">No hay menús para mostrar.</td>
						</tr>
					<?php endif; ?>

					</tbody>
				</table>
				<?php #endregion TABLE MENUS ?>
			</div>
			<!-- END PANEL body -->
		</div>
		<?php #endregion PANEL MENUS ?>

	</div>
	<!-- END #content -->

	<?php #region region Hidden Form for Deactivation ?>
	<form id="deactivate-menu-form" method="POST" action="lmenus" style="display: none;">
		<input type="hidden" name="action" value="desactivar">
		<input type="hidden" name="menuId" id="deactivate-menu-id">
	</form>
	<?php #endregion Hidden Form ?>

	<!-- BEGIN scroll-top-btn -->
	<a href="#" class="btn btn-icon btn-circle btn-success btn-scroll-to-top" data-toggle="scroll-to-top"><i class="fa fa-angle-up"></i></a>
	<!-- END scroll-top-btn -->
</div>
<!-- END #app -->

<?php #region region JS ?>
<?php require_once __ROOT__ . '/views/general/core_js.view.php'; ?>

<!-- ================== BEGIN PAGE LEVEL JS ================== -->
<script>
    document.addEventListener('DOMContentLoaded', function () {
        // Use event delegation on the table body
        const tableBody = document.getElementById('menu-table-body');

        if (tableBody) {
            tableBody.addEventListener('click', function (event) {
                const deactivateButton = event.target.closest('.btn-desactivar-menu');

                // --- Handle Deactivate Click ---
                if (deactivateButton) {
                    event.preventDefault();
                    const menuId = deactivateButton.dataset.menuid;
                    const menuTexto = deactivateButton.dataset.texto || 'este menú';

                    swal({
                        title     : "Confirmar Desactivación",
                        text      : `¿Seguro que quieres desactivar el menú '${menuTexto}'?`,
                        icon      : "warning",
                        buttons   : {
                            cancel : {text: "Cancelar", value: null, visible: true, className: "", closeModal: true},
                            confirm: {text: "Confirmar", value: true, visible: true, className: "btn-danger", closeModal: true}
                        },
                        dangerMode: true,
                    })
                        .then((willDelete) => {
                            if (willDelete) {
                                document.getElementById('deactivate-menu-id').value = menuId;
                                document.getElementById('deactivate-menu-form').submit();
                            }
                        });
                }
            });
        }
    });
</script>
<!-- ================== END PAGE LEVEL JS ================== -->

<?php #endregion JS ?>

</body>
</html>
