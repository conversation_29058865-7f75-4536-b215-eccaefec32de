<?php
#region DOCS

/** @var CitaProgramada[] $citas_programadas */
/** @var Empleado[] $empleados */
/** @var Servicio[] $servicios */
/** @var Cliente[] $clientes */
/** @var string|null $success_text Success message to display */
/** @var string|null $success_display Whether to show success message ('show' or null) */
/** @var string|null $error_text Error message to display */
/** @var string|null $error_display Whether to show error message ('show' or null) */
/** @var CentroCosto|null $selected_centro_costo Selected centro de costo from session */
/** @var string $selected_centro_costo_nombre Name of the selected centro de costo */

use App\classes\CitaProgramada;
use App\classes\Empleado;
use App\classes\Servicio;
use App\classes\Cliente;
use App\classes\CentroCosto;

#endregion DOCS

?>
<!DOCTYPE html>
<html lang="es" class="dark-mode">
<head>
    <meta charset="utf-8" />
    <title>Gestión de Citas Programadas | <?php echo NOMBRE_EMPRESA_ABREV; ?></title>
    <meta content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no" name="viewport" />
    <meta content="" name="description" />
    <meta content="" name="author" />

    <?php require_once __ROOT__ . '/views/general/head.view.php'; ?>

    <!-- FullCalendar CSS -->
    <link href="https://cdn.jsdelivr.net/npm/fullcalendar@5.11.3/main.min.css" rel="stylesheet" />

    <!-- Custom CSS for this page -->
    <style>
        /* Calendar container */
        #calendar {
            background-color: #2d353c;
            border-radius: 5px;
            box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
            padding: 15px;
        }

        /* Calendar header */
        .fc-header-toolbar {
            margin-bottom: 1.5em !important;
        }

        /* Calendar events */
        .fc-event {
            cursor: pointer;
            border-radius: 3px;
            font-size: 0.85em;
        }

        /* Detail panel */
        .detail-panel {
            background-color: #2d353c;
            border-radius: 5px;
            padding: 15px;
            height: 100%;
        }

        .detail-panel h5 {
            color: #fff;
            border-bottom: 1px solid #495057;
            padding-bottom: 10px;
            margin-bottom: 15px;
        }

        .detail-panel .detail-item {
            margin-bottom: 10px;
        }

        .detail-panel .detail-label {
            font-weight: 600;
            color: #adb5bd;
        }

        .detail-panel .detail-value {
            color: #fff;
        }

        .detail-panel .services-list {
            margin-top: 15px;
        }

        .detail-panel .service-item {
            background-color: #343a40;
            border-radius: 5px;
            padding: 10px;
            margin-bottom: 8px;
        }

        .detail-panel .service-name {
            font-weight: 600;
            color: #fff;
        }

        .detail-panel .service-duration {
            color: #adb5bd;
            font-size: 0.9em;
        }

        .detail-panel .service-price {
            color: #10c469;
            font-weight: 600;
        }

        .detail-panel .total-section {
            margin-top: 15px;
            padding-top: 15px;
            border-top: 1px solid #495057;
        }

        .detail-panel .total-label {
            font-weight: 600;
            color: #adb5bd;
        }

        .detail-panel .total-value {
            color: #ffffff;
            font-weight: 600;
            font-size: 1.2em;
            background-color: #10c469;
            padding: 5px 10px;
            border-radius: 5px;
        }

        /* Filter panel */
        .filter-panel {
            background-color: #2d353c;
            border-radius: 5px;
            padding: 15px;
            margin-bottom: 20px;
        }

        .filter-panel h5 {
            color: #fff;
            margin-bottom: 15px;
        }

        /* Modal styling */
        .modal-content {
            background-color: #2d353c;
            color: #fff;
        }

        .modal-header {
            border-bottom: 1px solid #495057;
        }

        .modal-footer {
            border-top: 1px solid #495057;
        }

        /* Service selection in modal */
        .service-selection {
            background-color: #343a40;
            border-radius: 5px;
            padding: 15px;
            margin-bottom: 15px;
            cursor: pointer;
            transition: background-color 0.3s;
        }

        .service-selection:hover {
            background-color: #495057;
        }

        .service-selection.selected {
            background-color: #2c7be5;
        }

        .service-selection .service-name {
            font-weight: 600;
            font-size: 1.1rem;
            margin-bottom: 5px;
        }

        .service-selection .service-duration {
            color: #adb5bd;
            font-size: 0.9rem;
        }

        .service-selection .service-price {
            color: #fff;
            font-weight: 600;
        }

        .fc .fc-scrollgrid-section-sticky>*{
            background: #2C3E50 !important;
        }

        .fc-theme-standard .fc-scrollgrid {
            border: 1px solid #2C3E50;
        }

        /* Increase height of time slots */
        .fc .fc-timegrid-slot {
            height: 4em !important;
            border-bottom: 1px solid #2C3E50;
        }

        /* Client search styles */
        .resultados-busqueda {
            max-height: 200px;
            overflow-y: auto;
            border: 1px solid #495057;
            border-radius: 5px;
            background-color: #343a40;
        }

        .resultado-cliente {
            padding: 10px;
            border-bottom: 1px solid #495057;
            cursor: pointer;
            transition: background-color 0.3s;
        }

        .resultado-cliente:hover {
            background-color: #495057;
        }

        .resultado-cliente:last-child {
            border-bottom: none;
        }

        .resultado-cliente .cliente-nombre {
            font-weight: 600;
            color: #fff;
        }

        .resultado-cliente .cliente-celular {
            color: #adb5bd;
            font-size: 0.9em;
        }
    </style>
</head>
<body>
<!-- BEGIN #loader -->
<div id="loader" class="app-loader">
    <span class="spinner"></span>
</div>
<!-- END #loader -->

<!-- BEGIN #app -->
<div id="app" class="app app-header-fixed app-sidebar-fixed">
    <!-- #header -->
    <?php require_once __ROOT__ . '/views/general/header.view.php'; ?>

    <!-- #sidebar -->
    <?php require_once __ROOT__ . '/views/general/sidebar.view.php'; ?>

    <!-- BEGIN #content -->
    <div id="content" class="app-content">
        <?php #region PAGE HEADER ?>
        <div class="d-flex align-items-center mb-3">
            <div>
                <h4 class="mb-0">Gestión de Citas Programadas</h4>
                <p class="mb-0 text-muted">Administra las citas programadas del sistema</p>
            </div>
            <div class="ms-auto">
                <button type="button" class="btn btn-success" id="btn-nueva-cita">
                    <i class="fa fa-plus-circle fa-fw me-1"></i> Crear nueva
                </button>
            </div>
        </div>
        <?php #endregion PAGE HEADER ?>

        <?php #region FILTER PANEL ?>
        <div class="filter-panel mb-3">
            <h5><i class="fa fa-filter me-2"></i>Filtros</h5>
            <div class="row">
                <div class="col-md-3 mb-2">
                    <label for="filtro-empleado" class="form-label">Empleado</label>
                    <select class="form-select" id="filtro-empleado">
                        <option value="">Todos los empleados</option>
                        <option value="null">Sin asignar</option>
                        <?php foreach ($empleados as $empleado): ?>
                            <option value="<?php echo $empleado->getId(); ?>"><?php echo htmlspecialchars($empleado->getNombre()); ?></option>
                        <?php endforeach; ?>
                    </select>
                </div>
                <div class="col-md-3 mb-2">
                    <label for="filtro-servicio" class="form-label">Servicio</label>
                    <select class="form-select" id="filtro-servicio">
                        <option value="">Todos los servicios</option>
                        <?php foreach ($servicios as $servicio): ?>
                            <option value="<?php echo $servicio->getId(); ?>"><?php echo htmlspecialchars($servicio->getDescripcion()); ?></option>
                        <?php endforeach; ?>
                    </select>
                </div>
                <div class="col-md-4 mb-2">
                    <label for="filtro-cliente" class="form-label">Cliente</label>
                    <div class="input-group">
                        <span class="input-group-text"><i class="fa fa-search"></i></span>
                        <input type="text" class="form-control" id="filtro-cliente" placeholder="Buscar por nombre o celular...">
                        <button class="btn btn-outline-secondary" type="button" id="clear-filtro-cliente">
                            <i class="fa fa-times"></i>
                        </button>
                    </div>
                </div>
                <div class="col-md-2 mb-2 d-flex align-items-end">
                    <button type="button" class="btn btn-primary w-100" id="btn-aplicar-filtros">
                        <i class="fa fa-search me-1"></i> Aplicar
                    </button>
                </div>
            </div>
        </div>
        <?php #endregion FILTER PANEL ?>

        <div class="row">
            <?php #region CALENDAR ?>
            <div class="col-lg-8 mb-4">
                <div id="calendar"></div>
            </div>
            <?php #endregion CALENDAR ?>

            <?php #region DETAIL PANEL ?>
            <div class="col-lg-4">
                <div class="detail-panel" id="detail-panel">
                    <h5><i class="fa fa-info-circle me-2"></i>Detalles de la Cita</h5>
                    <div id="no-cita-selected" class="text-center py-5">
                        <i class="fa fa-calendar-alt fa-4x mb-3 text-muted"></i>
                        <p class="text-muted">Selecciona una cita en el calendario para ver sus detalles</p>
                    </div>
                    <div id="cita-details" class="d-none">
                        <!-- Los detalles de la cita se cargarán dinámicamente mediante JavaScript -->
                    </div>
                </div>
            </div>
            <?php #endregion DETAIL PANEL ?>
        </div>
    </div>
    <!-- END #content -->

    <!-- BEGIN scroll-top-btn -->
    <a href="#" class="btn btn-icon btn-circle btn-success btn-scroll-to-top" data-toggle="scroll-to-top"><i class="fa fa-angle-up"></i></a>
    <!-- END scroll-top-btn -->
</div>
<!-- END #app -->

<?php #region MODALS ?>
<!-- Modal para crear nueva cita programada -->
<div class="modal fade" id="modal-nueva-cita" tabindex="-1" aria-labelledby="modal-nueva-cita-label" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="modal-nueva-cita-label">Nueva Cita Programada</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <form id="form-nueva-cita">
                    <div class="mb-3">
                        <label class="form-label">Cliente <span class="text-danger">*</span></label>
                        <div class="cliente-selection-container">
                            <div class="input-group mb-2">
                                <span class="input-group-text"><i class="fa fa-search"></i></span>
                                <input type="text" class="form-control" id="buscar-cliente" placeholder="Buscar cliente por nombre o celular...">
                                <button class="btn btn-outline-secondary" type="button" id="clear-buscar-cliente">
                                    <i class="fa fa-times"></i>
                                </button>
                            </div>
                            <div id="cliente-seleccionado" class="d-none">
                                <div class="alert alert-success d-flex justify-content-between align-items-center">
                                    <div>
                                        <strong>Cliente seleccionado:</strong><br>
                                        <span id="cliente-seleccionado-nombre"></span>
                                        <small>(<span id="cliente-seleccionado-celular"></span>)</small>
                                    </div>
                                    <button type="button" class="btn btn-sm btn-outline-danger" id="deseleccionar-cliente">
                                        <i class="fa fa-times"></i>
                                    </button>
                                </div>
                            </div>
                            <div id="resultados-busqueda-cliente" class="resultados-busqueda d-none">
                                <!-- Los resultados de búsqueda se mostrarán aquí -->
                            </div>
                            <div class="text-center mt-2">
                                <button type="button" class="btn btn-outline-primary btn-sm" id="btn-crear-cliente-nuevo">
                                    <i class="fa fa-plus me-1"></i> Crear nuevo cliente
                                </button>
                            </div>
                        </div>
                        <input type="hidden" id="nuevo-id-cliente" name="id_cliente" required>
                    </div>

                    <div class="mb-3">
                        <label for="nueva-fecha-inicio" class="form-label">Fecha y Hora de Inicio</label>
                        <div class="input-group">
                            <input type="text" class="form-control datepicker" id="nueva-fecha" name="fecha" required>
                            <span class="input-group-text date-icon" id="nueva-fecha-icon">
                                <i class="fa fa-calendar"></i>
                            </span>
                            <input type="text" class="form-control timepicker" id="nueva-hora" name="hora" required>
                            <span class="input-group-text time-icon" id="nueva-hora-icon">
                                <i class="fa fa-clock"></i>
                            </span>
                        </div>
                    </div>

                    <div class="mb-3">
                        <label for="nuevo-empleado" class="form-label">Empleado (Opcional)</label>
                        <select class="form-select" id="nuevo-empleado" name="id_empleado">
                            <option value="">Sin asignar</option>
                            <?php foreach ($empleados as $empleado): ?>
                                <option value="<?php echo $empleado->getId(); ?>"><?php echo htmlspecialchars($empleado->getNombre()); ?></option>
                            <?php endforeach; ?>
                        </select>
                    </div>

                    <div class="mb-3">
                        <label class="form-label">Servicios</label>
                        <div id="servicios-container" class="servicios-container">
                            <!-- Los servicios se cargarán dinámicamente -->
                        </div>
                    </div>

                    <div class="mb-3 text-end">
                        <div class="total-section">
                            <span class="total-label me-2">Total:</span>
                            <span class="total-value" id="total-valor" style="background-color: #10c469; color: #ffffff; padding: 5px 10px; border-radius: 5px; font-weight: 600;">$0</span>
                        </div>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancelar</button>
                <button type="button" class="btn btn-primary" id="btn-guardar-cita">Guardar</button>
            </div>
        </div>
    </div>
</div>

<!-- Modal para editar cita programada -->
<div class="modal fade" id="modal-editar-cita" tabindex="-1" aria-labelledby="modal-editar-cita-label" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="modal-editar-cita-label">Editar Cita Programada</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <form id="form-editar-cita">
                    <input type="hidden" id="editar-id-cita" name="id_cita">

                    <!-- Cliente Information (Read-only) -->
                    <div class="mb-3">
                        <label class="form-label">Cliente Asignado</label>
                        <div class="alert alert-info" id="editar-cliente-info">
                            <div class="d-flex align-items-center">
                                <i class="fa fa-user me-2"></i>
                                <div>
                                    <strong id="editar-cliente-nombre">-</strong>
                                    <br>
                                    <small>Celular: <span id="editar-cliente-celular">-</span></small>
                                </div>
                            </div>
                        </div>
                        <small class="text-muted">El cliente no puede ser modificado después de crear la cita.</small>
                    </div>

                    <div class="mb-3">
                        <label for="editar-fecha-inicio" class="form-label">Fecha y Hora de Inicio</label>
                        <div class="input-group">
                            <input type="text" class="form-control datepicker" id="editar-fecha" name="fecha" required>
                            <span class="input-group-text date-icon" id="editar-fecha-icon">
                                <i class="fa fa-calendar"></i>
                            </span>
                            <input type="text" class="form-control timepicker" id="editar-hora" name="hora" required>
                            <span class="input-group-text time-icon" id="editar-hora-icon">
                                <i class="fa fa-clock"></i>
                            </span>
                        </div>
                    </div>

                    <div class="mb-3">
                        <label for="editar-empleado" class="form-label">Empleado (Opcional)</label>
                        <select class="form-select" id="editar-empleado" name="id_empleado">
                            <option value="">Sin asignar</option>
                            <?php foreach ($empleados as $empleado): ?>
                                <option value="<?php echo $empleado->getId(); ?>"><?php echo htmlspecialchars($empleado->getNombre()); ?></option>
                            <?php endforeach; ?>
                        </select>
                    </div>

                    <div class="mb-3">
                        <label class="form-label">Servicios Actuales</label>
                        <div id="servicios-actuales-container" class="servicios-container">
                            <!-- Los servicios actuales se cargarán dinámicamente -->
                        </div>
                    </div>

                    <div class="mb-3">
                        <label class="form-label">Agregar Servicios</label>
                        <div id="editar-servicios-container" class="servicios-container">
                            <!-- Los servicios disponibles se cargarán dinámicamente -->
                        </div>
                    </div>

                    <div class="mb-3 text-end">
                        <div class="total-section">
                            <span class="total-label me-2">Total:</span>
                            <span class="total-value" id="editar-total-valor" style="background-color: #10c469; color: #ffffff; padding: 5px 10px; border-radius: 5px; font-weight: 600;">$0</span>
                        </div>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-danger me-auto" id="btn-cancelar-cita">Cancelar Cita</button>
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cerrar</button>
                <button type="button" class="btn btn-primary" id="btn-guardar-editar-cita">Guardar Cambios</button>
            </div>
        </div>
    </div>
</div>

<!-- Modal para cancelar cita programada -->
<div class="modal fade" id="modal-cancelar-cita" tabindex="-1" aria-labelledby="modal-cancelar-cita-label" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="modal-cancelar-cita-label">Cancelar Cita Programada</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <form id="form-cancelar-cita">
                    <input type="hidden" id="cancelar-id-cita" name="id_cita">

                    <div class="mb-3">
                        <label for="razon-cancelacion" class="form-label">Razón de Cancelación</label>
                        <textarea class="form-control" id="razon-cancelacion" name="razon_cancelacion" rows="3" required></textarea>
                        <div class="invalid-feedback">La razón de cancelación es obligatoria.</div>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cerrar</button>
                <button type="button" class="btn btn-danger" id="btn-confirmar-cancelar-cita">Confirmar Cancelación</button>
            </div>
        </div>
    </div>
</div>

<!-- Modal para crear nuevo cliente -->
<div class="modal fade" id="modal-crear-cliente" tabindex="-1" aria-labelledby="modal-crear-cliente-label" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="modal-crear-cliente-label">Crear Nuevo Cliente</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <form id="form-crear-cliente">
                    <div class="mb-3">
                        <label for="crear-cliente-nombre" class="form-label">Nombre <span class="text-danger">*</span></label>
                        <input type="text" class="form-control" id="crear-cliente-nombre" name="nombre" required>
                    </div>

                    <div class="mb-3">
                        <label for="crear-cliente-celular" class="form-label">Celular <span class="text-danger">*</span></label>
                        <input type="text" class="form-control" id="crear-cliente-celular" name="celular" required>
                    </div>

                    <div id="crear-cliente-error" class="alert alert-danger mt-2" style="display: none;"></div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancelar</button>
                <button type="button" class="btn btn-success" id="btn-guardar-cliente">Crear Cliente</button>
            </div>
        </div>
    </div>
</div>
<?php #endregion MODALS ?>

<?php #region JS ?>
<?php require_once __ROOT__ . '/views/general/core_js.view.php'; ?>

<!-- FullCalendar JS -->
<script src="https://cdn.jsdelivr.net/npm/fullcalendar@5.11.3/main.min.js"></script>
<script src="https://cdn.jsdelivr.net/npm/fullcalendar@5.11.3/locales/es.js"></script>

<!-- Include Bootstrap Datepicker and Timepicker JS -->
<script src="<?php echo RUTA ?>resources/adm_assets/plugins/bootstrap-datepicker/dist/js/bootstrap-datepicker.js"></script>
<script src="<?php echo RUTA ?>resources/adm_assets/plugins/bootstrap-timepicker/js/bootstrap-timepicker.min.js"></script>

<!-- Include formatcurrency.js for currency formatting -->
<script src="<?php echo RUTA_RESOURCES; ?>js/formatcurrency.js"></script>

<!-- JavaScript para la página de citas programadas -->
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Inicializar componentes
        initializeCalendar();
        initializeDateTimePickers();
        initializeEventListeners();

        // Cargar servicios para el modal de nueva cita
        cargarServicios();
    });

    // Variables globales
    let calendar;
    let selectedCitaId = null;
    let serviciosSeleccionados = [];
    let serviciosActuales = [];
    let allServicios = [];
    let clienteSeleccionado = null;
    let searchTimeout;

    // Elementos DOM
    const btnNuevaCita = document.getElementById('btn-nueva-cita');
    const btnAplicarFiltros = document.getElementById('btn-aplicar-filtros');
    const filtroEmpleado = document.getElementById('filtro-empleado');
    const filtroServicio = document.getElementById('filtro-servicio');
    const filtroCliente = document.getElementById('filtro-cliente');
    const clearFiltroCliente = document.getElementById('clear-filtro-cliente');

    // Modales
    const modalNuevaCita = new bootstrap.Modal(document.getElementById('modal-nueva-cita'));
    const modalEditarCita = new bootstrap.Modal(document.getElementById('modal-editar-cita'));
    const modalCancelarCita = new bootstrap.Modal(document.getElementById('modal-cancelar-cita'));
    const modalCrearCliente = new bootstrap.Modal(document.getElementById('modal-crear-cliente'));

    // Formularios
    const formNuevaCita = document.getElementById('form-nueva-cita');
    const formEditarCita = document.getElementById('form-editar-cita');
    const formCancelarCita = document.getElementById('form-cancelar-cita');
    const formCrearCliente = document.getElementById('form-crear-cliente');

    // Botones
    const btnGuardarCita = document.getElementById('btn-guardar-cita');
    const btnGuardarEditarCita = document.getElementById('btn-guardar-editar-cita');
    const btnCancelarCita = document.getElementById('btn-cancelar-cita');
    const btnConfirmarCancelarCita = document.getElementById('btn-confirmar-cancelar-cita');
    const btnCrearClienteNuevo = document.getElementById('btn-crear-cliente-nuevo');
    const btnGuardarCliente = document.getElementById('btn-guardar-cliente');
    const btnDeseleccionarCliente = document.getElementById('deseleccionar-cliente');
    const clearBuscarCliente = document.getElementById('clear-buscar-cliente');

    // Contenedores
    const serviciosContainer = document.getElementById('servicios-container');
    const serviciosActualesContainer = document.getElementById('servicios-actuales-container');
    const editarServiciosContainer = document.getElementById('editar-servicios-container');
    const citaDetailsContainer = document.getElementById('cita-details');
    const noCitaSelectedContainer = document.getElementById('no-cita-selected');

    // Elementos de cliente
    const buscarClienteInput = document.getElementById('buscar-cliente');
    const resultadosBusquedaCliente = document.getElementById('resultados-busqueda-cliente');
    const clienteSeleccionadoDiv = document.getElementById('cliente-seleccionado');
    const clienteSeleccionadoNombre = document.getElementById('cliente-seleccionado-nombre');
    const clienteSeleccionadoCelular = document.getElementById('cliente-seleccionado-celular');
    const nuevoIdClienteInput = document.getElementById('nuevo-id-cliente');

    // Función helper para formatear fecha y hora - sin conversiones de zona horaria
    function formatDateTime(date) {
        // Formatear fecha para el datepicker (YYYY-MM-DD)
        const year = date.getFullYear();
        const month = String(date.getMonth() + 1).padStart(2, '0');
        const day = String(date.getDate()).padStart(2, '0');
        const formattedDate = `${year}-${month}-${day}`;

        // Formatear hora para el timepicker (HH:MM)
        const hours = String(date.getHours()).padStart(2, '0');
        const minutes = String(Math.floor(date.getMinutes() / 15) * 15).padStart(2, '0');
        const formattedTime = `${hours}:${minutes}`;

        return {
            date: formattedDate,
            time: formattedTime
        };
    }

    // Inicializar el calendario
    function initializeCalendar() {
        const calendarEl = document.getElementById('calendar');

        calendar = new FullCalendar.Calendar(calendarEl, {
            initialView: 'timeGridDay',
            headerToolbar: {
                left: 'prev,next today',
                center: 'title',
                right: 'dayGridMonth,timeGridWeek,timeGridDay'
            },
            locale: 'es',
            slotMinTime: '07:00:00',
            slotMaxTime: '20:00:00',
            allDaySlot: false,
            height: 1350,
            selectable: true,
            selectMirror: true,
            navLinks: true,
            businessHours: {
                daysOfWeek: [0, 1, 2, 3, 4, 5, 6], // 0=Domingo, 1=Lunes, etc.
                startTime: '07:00',
                endTime: '20:00',
            },
            select: function(info) {
                // Al seleccionar un espacio en el calendario, abrir modal para crear cita
                const fechaInicio = info.start;

                // Formatear la fecha y hora considerando la zona horaria
                const dateTime = formatDateTime(fechaInicio);

                console.log('Selected time:', fechaInicio, '-> Formatted time:', dateTime.time);

                // Establecer valores en el formulario
                document.getElementById('nueva-fecha').value = dateTime.date;
                document.getElementById('nueva-hora').value = dateTime.time;

                // Resetear servicios seleccionados y cliente
                serviciosSeleccionados = [];
                deseleccionarCliente();
                actualizarTotalNuevaCita();

                // Mostrar modal
                modalNuevaCita.show();

                calendar.unselect();
            },
            eventClick: function(info) {
                // Al hacer clic en un evento (cita), mostrar detalles y abrir modal de edición
                const citaId = info.event.id;
                selectedCitaId = citaId;

                // Cargar detalles de la cita
                cargarDetallesCita(citaId);
            },
            events: function(info, successCallback, failureCallback) {
                // Cargar eventos (citas) desde el servidor
                const formData = new FormData();
                formData.append('action', 'get_citas_calendario');
                formData.append('start', info.startStr);
                formData.append('end', info.endStr);

                // Agregar filtros si están seleccionados
                if (filtroEmpleado.value) {
                    formData.append('id_empleado', filtroEmpleado.value === 'null' ? null : filtroEmpleado.value);
                }

                if (filtroServicio.value) {
                    formData.append('id_servicio', filtroServicio.value);
                }

                if (filtroCliente.value) {
                    formData.append('filtro_cliente', filtroCliente.value);
                }

                fetch('citas-programadas', {
                    method: 'POST',
                    body: formData
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        successCallback(data.eventos);
                    } else {
                        failureCallback(new Error(data.message));
                        showSweetAlertError('Error', data.message);
                    }
                })
                .catch(error => {
                    failureCallback(error);
                    showSweetAlertError('Error', 'Error al cargar las citas programadas');
                });
            }
        });

        calendar.render();
    }

    // Inicializar datepickers y timepickers
    function initializeDateTimePickers() {
        // Inicializar datepickers
        $('.datepicker').datepicker({
            autoclose: true,
            todayHighlight: true,
            format: 'yyyy-mm-dd'
        });

        // Inicializar timepickers
        $('.timepicker').timepicker({
            showMeridian: false,
            defaultTime: '08:00',
            minuteStep: 15
        });

        // Hacer que los iconos sean clickeables
        document.getElementById('nueva-fecha-icon').addEventListener('click', function() {
            $('#nueva-fecha').datepicker('show');
        });

        document.getElementById('nueva-hora-icon').addEventListener('click', function() {
            $('#nueva-hora').timepicker('showWidget');
        });

        document.getElementById('editar-fecha-icon').addEventListener('click', function() {
            $('#editar-fecha').datepicker('show');
        });

        document.getElementById('editar-hora-icon').addEventListener('click', function() {
            $('#editar-hora').timepicker('showWidget');
        });
    }

    // Inicializar event listeners
    function initializeEventListeners() {
        // Botón para abrir modal de nueva cita
        btnNuevaCita.addEventListener('click', function() {
            // Resetear formulario
            formNuevaCita.reset();

            // Establecer fecha y hora actuales
            const now = new Date();
            const dateTime = formatDateTime(now);

            document.getElementById('nueva-fecha').value = dateTime.date;
            document.getElementById('nueva-hora').value = dateTime.time;

            // Resetear servicios seleccionados y cliente
            serviciosSeleccionados = [];
            deseleccionarCliente();
            actualizarTotalNuevaCita();

            // Mostrar modal
            modalNuevaCita.show();
        });

        // Botón para aplicar filtros
        btnAplicarFiltros.addEventListener('click', function() {
            calendar.refetchEvents();
        });

        // Botón para guardar nueva cita
        btnGuardarCita.addEventListener('click', function() {
            guardarNuevaCita();
        });

        // Botón para guardar cambios en cita existente
        btnGuardarEditarCita.addEventListener('click', function() {
            guardarCambiosCita();
        });

        // Botón para abrir modal de cancelación
        btnCancelarCita.addEventListener('click', function() {
            document.getElementById('cancelar-id-cita').value = document.getElementById('editar-id-cita').value;
            document.getElementById('razon-cancelacion').value = '';

            modalEditarCita.hide();
            modalCancelarCita.show();
        });

        // Botón para confirmar cancelación
        btnConfirmarCancelarCita.addEventListener('click', function() {
            cancelarCita();
        });

        // Event listeners para cliente
        initializeClienteEventListeners();

        // Event listeners para filtros
        initializeFilterEventListeners();
    }

    // Inicializar event listeners para cliente
    function initializeClienteEventListeners() {
        // Búsqueda de cliente
        if (buscarClienteInput) {
            buscarClienteInput.addEventListener('input', function() {
                clearTimeout(searchTimeout);
                searchTimeout = setTimeout(() => {
                    buscarClientes(this.value.trim());
                }, 300);
            });
        }

        // Limpiar búsqueda de cliente
        if (clearBuscarCliente) {
            clearBuscarCliente.addEventListener('click', function() {
                buscarClienteInput.value = '';
                resultadosBusquedaCliente.classList.add('d-none');
                resultadosBusquedaCliente.innerHTML = '';
            });
        }

        // Deseleccionar cliente
        if (btnDeseleccionarCliente) {
            btnDeseleccionarCliente.addEventListener('click', function() {
                deseleccionarCliente();
            });
        }

        // Crear nuevo cliente
        if (btnCrearClienteNuevo) {
            btnCrearClienteNuevo.addEventListener('click', function() {
                modalCrearCliente.show();
            });
        }

        // Guardar nuevo cliente
        if (btnGuardarCliente) {
            btnGuardarCliente.addEventListener('click', function() {
                crearNuevoCliente();
            });
        }
    }

    // Inicializar event listeners para filtros
    function initializeFilterEventListeners() {
        // Filtro de cliente
        if (filtroCliente) {
            filtroCliente.addEventListener('input', function() {
                clearTimeout(searchTimeout);
                searchTimeout = setTimeout(() => {
                    calendar.refetchEvents();
                }, 300);
            });
        }

        // Limpiar filtro de cliente
        if (clearFiltroCliente) {
            clearFiltroCliente.addEventListener('click', function() {
                filtroCliente.value = '';
                calendar.refetchEvents();
            });
        }
    }

    // Cargar servicios disponibles
    function cargarServicios() {
        // Obtener todos los servicios desde el servidor
        fetch('citas-programadas', {
            method: 'POST',
            body: new URLSearchParams({
                'action': 'get_servicios'
            })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                allServicios = data.servicios;

                // Renderizar servicios en el modal de nueva cita
                renderizarServicios(serviciosContainer, allServicios, serviciosSeleccionados, true);
            } else {
                showSweetAlertError('Error', data.message);
            }
        })
        .catch(error => {
            console.error('Error al cargar servicios:', error);
            showSweetAlertError('Error', 'Error al cargar los servicios disponibles');
        });
    }

    // Buscar clientes
    function buscarClientes(termino) {
        if (termino.length < 2) {
            resultadosBusquedaCliente.classList.add('d-none');
            resultadosBusquedaCliente.innerHTML = '';
            return;
        }

        fetch('citas-programadas', {
            method: 'POST',
            body: new URLSearchParams({
                'action': 'buscar_clientes',
                'termino': termino
            })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                mostrarResultadosClientes(data.clientes);
            } else {
                showSweetAlertError('Error', data.message);
            }
        })
        .catch(error => {
            console.error('Error al buscar clientes:', error);
            showSweetAlertError('Error', 'Error al buscar clientes');
        });
    }

    // Mostrar resultados de búsqueda de clientes
    function mostrarResultadosClientes(clientes) {
        resultadosBusquedaCliente.innerHTML = '';

        if (clientes.length === 0) {
            resultadosBusquedaCliente.innerHTML = '<div class="text-center py-3 text-muted">No se encontraron clientes</div>';
        } else {
            clientes.forEach(cliente => {
                const clienteElement = document.createElement('div');
                clienteElement.className = 'resultado-cliente';
                clienteElement.innerHTML = `
                    <div class="cliente-nombre">${cliente.nombre}</div>
                    <div class="cliente-celular">${cliente.celular}</div>
                `;

                clienteElement.addEventListener('click', function() {
                    seleccionarCliente(cliente);
                });

                resultadosBusquedaCliente.appendChild(clienteElement);
            });
        }

        resultadosBusquedaCliente.classList.remove('d-none');
    }

    // Seleccionar cliente
    function seleccionarCliente(cliente) {
        clienteSeleccionado = cliente;

        // Actualizar UI
        clienteSeleccionadoNombre.textContent = cliente.nombre;
        clienteSeleccionadoCelular.textContent = cliente.celular;
        nuevoIdClienteInput.value = cliente.id;

        // Mostrar cliente seleccionado y ocultar búsqueda
        clienteSeleccionadoDiv.classList.remove('d-none');
        resultadosBusquedaCliente.classList.add('d-none');
        buscarClienteInput.value = '';
    }

    // Deseleccionar cliente
    function deseleccionarCliente() {
        clienteSeleccionado = null;
        clienteSeleccionadoDiv.classList.add('d-none');
        nuevoIdClienteInput.value = '';
        buscarClienteInput.value = '';
        resultadosBusquedaCliente.classList.add('d-none');
    }

    // Crear nuevo cliente
    function crearNuevoCliente() {
        const nombre = document.getElementById('crear-cliente-nombre').value.trim();
        const celular = document.getElementById('crear-cliente-celular').value.trim();
        const errorDiv = document.getElementById('crear-cliente-error');

        // Validaciones
        if (!nombre) {
            errorDiv.textContent = 'El nombre es obligatorio';
            errorDiv.style.display = 'block';
            return;
        }

        if (!celular) {
            errorDiv.textContent = 'El celular es obligatorio';
            errorDiv.style.display = 'block';
            return;
        }

        // Deshabilitar botón
        btnGuardarCliente.disabled = true;
        btnGuardarCliente.innerHTML = '<span class="spinner-border spinner-border-sm me-2"></span>Creando...';

        fetch('citas-programadas', {
            method: 'POST',
            body: new URLSearchParams({
                'action': 'crear_cliente',
                'nombre': nombre,
                'celular': celular
            })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                // Seleccionar el cliente recién creado
                seleccionarCliente(data.cliente);

                // Cerrar modal y limpiar formulario
                modalCrearCliente.hide();
                formCrearCliente.reset();
                errorDiv.style.display = 'none';

                showSweetAlertSuccess('Éxito', 'Cliente creado correctamente');
            } else {
                errorDiv.textContent = data.message;
                errorDiv.style.display = 'block';
            }
        })
        .catch(error => {
            console.error('Error al crear cliente:', error);
            errorDiv.textContent = 'Error al crear el cliente';
            errorDiv.style.display = 'block';
        })
        .finally(() => {
            // Restaurar botón
            btnGuardarCliente.disabled = false;
            btnGuardarCliente.innerHTML = 'Crear Cliente';
        });
    }

    // Renderizar servicios en un contenedor
    function renderizarServicios(container, servicios, seleccionados, esSeleccionable = true) {
        container.innerHTML = '';

        if (servicios.length === 0) {
            container.innerHTML = '<div class="text-center py-3 text-muted">No hay servicios disponibles</div>';
            return;
        }

        servicios.forEach(servicio => {
            const isSelected = seleccionados.some(s => s.id === servicio.id);
            const servicioElement = document.createElement('div');
            servicioElement.className = `service-selection${isSelected ? ' selected' : ''}`;
            servicioElement.dataset.id = servicio.id;

            servicioElement.innerHTML = `
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <div class="service-name">${servicio.descripcion}</div>
                        <div class="service-duration"><i class="fa fa-clock me-1"></i>${servicio.duracion} minutos</div>
                    </div>
                    <div class="service-price">$${formatNumber(servicio.valor)}</div>
                </div>
            `;

            if (esSeleccionable) {
                servicioElement.addEventListener('click', function() {
                    toggleServicioSeleccion(this, servicio);
                });
            }

            container.appendChild(servicioElement);
        });
    }

    // Alternar selección de servicio
    function toggleServicioSeleccion(element, servicio) {
        const isSelected = element.classList.contains('selected');

        if (isSelected) {
            // Deseleccionar servicio
            element.classList.remove('selected');
            serviciosSeleccionados = serviciosSeleccionados.filter(s => s.id !== servicio.id);
        } else {
            // Seleccionar servicio
            element.classList.add('selected');
            serviciosSeleccionados.push(servicio);
        }

        // Actualizar total
        actualizarTotalNuevaCita();
    }

    // Actualizar total en el modal de nueva cita
    function actualizarTotalNuevaCita() {
        const totalElement = document.getElementById('total-valor');
        let total = 0;

        serviciosSeleccionados.forEach(servicio => {
            total += parseFloat(servicio.valor);
        });

        totalElement.textContent = `$${formatNumber(total)}`;
    }

    // Actualizar total en el modal de editar cita
    function actualizarTotalEditarCita() {
        const totalElement = document.getElementById('editar-total-valor');
        let total = 0;

        // Sumar servicios actuales
        serviciosActuales.forEach(servicio => {
            total += parseFloat(servicio.valor);
        });

        totalElement.textContent = `$${formatNumber(total)}`;
    }

    // Cargar detalles de una cita
    function cargarDetallesCita(citaId) {
        fetch('citas-programadas', {
            method: 'POST',
            body: new URLSearchParams({
                'action': 'get_cita_detalles',
                'id_cita': citaId
            })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                // Mostrar detalles en el panel lateral
                mostrarDetallesCita(data.cita);

                // Guardar servicios actuales
                serviciosActuales = data.cita.servicios;
            } else {
                showSweetAlertError('Error', data.message);
            }
        })
        .catch(error => {
            console.error('Error al cargar detalles de la cita:', error);
            showSweetAlertError('Error', 'Error al cargar los detalles de la cita');
        });
    }

    // Mostrar detalles de la cita en el panel lateral
    function mostrarDetallesCita(cita) {
        // Ocultar mensaje de "no hay cita seleccionada"
        noCitaSelectedContainer.classList.add('d-none');
        citaDetailsContainer.classList.remove('d-none');

        // Formatear fecha y hora
        const fechaInicio = new Date(cita.fecha_inicio);
        const fechaFin = new Date(cita.fecha_fin);

        const options = {
            weekday: 'long',
            year: 'numeric',
            month: 'long',
            day: 'numeric',
            hour: '2-digit',
            minute: '2-digit'
        };

        const fechaFormateada = fechaInicio.toLocaleDateString('es-CO', options);

        // Construir HTML para los detalles
        let html = `
            <div class="detail-item">
                <div class="detail-label">Cliente:</div>
                <div class="detail-value">${cita.nombre_cliente || 'Cliente no encontrado'}</div>
            </div>
            <div class="detail-item">
                <div class="detail-label">Fecha y Hora:</div>
                <div class="detail-value">${fechaFormateada}</div>
            </div>
            <div class="detail-item">
                <div class="detail-label">Empleado:</div>
                <div class="detail-value">${cita.nombre_empleado}</div>
            </div>
            <div class="detail-item">
                <div class="detail-label">Duración:</div>
                <div class="detail-value">${cita.duracion} minutos</div>
            </div>
        `;

        // Agregar servicios
        html += '<div class="detail-item"><div class="detail-label">Servicios:</div></div>';
        html += '<div class="services-list">';

        if (cita.servicios.length === 0) {
            html += '<div class="text-muted">No hay servicios asociados</div>';
        } else {
            cita.servicios.forEach(servicio => {
                html += `
                    <div class="service-item">
                        <div class="d-flex justify-content-between align-items-center">
                            <div>
                                <div class="service-name">${servicio.descripcion}</div>
                                <div class="service-duration"><i class="fa fa-clock me-1"></i>${servicio.duracion} minutos</div>
                            </div>
                            <div class="service-price">$${formatNumber(servicio.valor)}</div>
                        </div>
                    </div>
                `;
            });
        }
        html += '</div>';

        // Agregar total
        html += `
            <div class="total-section">
                <div class="d-flex justify-content-between align-items-center">
                    <div class="total-label">Total:</div>
                    <div class="total-value">$${formatNumber(cita.total_valor)}</div>
                </div>
            </div>
            <div class="mt-3">
                <button type="button" class="btn btn-primary w-100 mb-2" onclick="editarCita(${cita.id})">
                    <i class="fa fa-edit me-1"></i> Editar Cita
                </button>
                <button type="button" class="btn btn-danger w-100" onclick="abrirModalCancelarCita(${cita.id})">
                    <i class="fa fa-times-circle me-1"></i> Cancelar Cita
                </button>
            </div>
        `;

        // Actualizar contenido
        citaDetailsContainer.innerHTML = html;
    }

    // Abrir modal para editar cita
    function editarCita(citaId) {
        fetch('citas-programadas', {
            method: 'POST',
            body: new URLSearchParams({
                'action': 'get_cita_detalles',
                'id_cita': citaId
            })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                const cita = data.cita;

                // Establecer ID de la cita
                document.getElementById('editar-id-cita').value = cita.id;

                // Formatear fecha y hora
                const fechaInicio = new Date(cita.fecha_inicio);
                const year = fechaInicio.getFullYear();
                const month = String(fechaInicio.getMonth() + 1).padStart(2, '0');
                const day = String(fechaInicio.getDate()).padStart(2, '0');
                const formattedDate = `${year}-${month}-${day}`;

                const hours = String(fechaInicio.getHours()).padStart(2, '0');
                const minutes = String(fechaInicio.getMinutes()).padStart(2, '0');
                const formattedTime = `${hours}:${minutes}`;

                // Establecer valores en el formulario
                document.getElementById('editar-fecha').value = formattedDate;
                document.getElementById('editar-hora').value = formattedTime;
                document.getElementById('editar-empleado').value = cita.id_empleado || '';

                // Mostrar información del cliente
                document.getElementById('editar-cliente-nombre').textContent = cita.nombre_cliente || 'Cliente no encontrado';
                document.getElementById('editar-cliente-celular').textContent = cita.celular_cliente || 'No disponible';

                // Guardar servicios actuales
                serviciosActuales = cita.servicios;

                // Renderizar servicios actuales
                renderizarServiciosActuales(serviciosActuales);

                // Renderizar servicios disponibles para agregar
                renderizarServiciosDisponibles();

                // Actualizar total
                actualizarTotalEditarCita();

                // Mostrar modal
                modalEditarCita.show();
            } else {
                showSweetAlertError('Error', data.message);
            }
        })
        .catch(error => {
            console.error('Error al cargar detalles para editar:', error);
            showSweetAlertError('Error', 'Error al cargar los detalles de la cita para editar');
        });
    }

    // Renderizar servicios actuales en el modal de edición
    function renderizarServiciosActuales(servicios) {
        serviciosActualesContainer.innerHTML = '';

        if (servicios.length === 0) {
            serviciosActualesContainer.innerHTML = '<div class="text-center py-3 text-muted">No hay servicios asociados</div>';
            return;
        }

        servicios.forEach(servicio => {
            const servicioElement = document.createElement('div');
            servicioElement.className = 'service-selection mb-2';

            servicioElement.innerHTML = `
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <div class="service-name">${servicio.descripcion}</div>
                        <div class="service-duration"><i class="fa fa-clock me-1"></i>${servicio.duracion} minutos</div>
                    </div>
                    <div class="d-flex align-items-center">
                        <div class="service-price me-2">$${formatNumber(servicio.valor)}</div>
                        <button type="button" class="btn btn-sm btn-danger" onclick="eliminarServicio(${servicio.id})">
                            <i class="fa fa-trash"></i>
                        </button>
                    </div>
                </div>
            `;

            serviciosActualesContainer.appendChild(servicioElement);
        });
    }

    // Renderizar servicios disponibles para agregar en el modal de edición
    function renderizarServiciosDisponibles() {
        // Filtrar servicios que ya están asociados
        const serviciosIds = serviciosActuales.map(s => s.id);
        const serviciosDisponibles = allServicios.filter(s => !serviciosIds.includes(s.id));

        editarServiciosContainer.innerHTML = '';

        if (serviciosDisponibles.length === 0) {
            editarServiciosContainer.innerHTML = '<div class="text-center py-3 text-muted">No hay más servicios disponibles</div>';
            return;
        }

        serviciosDisponibles.forEach(servicio => {
            const servicioElement = document.createElement('div');
            servicioElement.className = 'service-selection';
            servicioElement.dataset.id = servicio.id;

            servicioElement.innerHTML = `
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <div class="service-name">${servicio.descripcion}</div>
                        <div class="service-duration"><i class="fa fa-clock me-1"></i>${servicio.duracion} minutos</div>
                    </div>
                    <div class="service-price">$${formatNumber(servicio.valor)}</div>
                </div>
            `;

            servicioElement.addEventListener('click', function() {
                agregarServicio(servicio.id);
            });

            editarServiciosContainer.appendChild(servicioElement);
        });
    }

    // Eliminar un servicio de la cita
    function eliminarServicio(idServicio) {
        const idCita = document.getElementById('editar-id-cita').value;

        fetch('citas-programadas', {
            method: 'POST',
            body: new URLSearchParams({
                'action': 'eliminar_servicio',
                'id_cita': idCita,
                'id_servicio': idServicio
            })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                // Actualizar servicios actuales
                serviciosActuales = serviciosActuales.filter(s => s.id !== idServicio);

                // Actualizar duración de la cita
                const cita = data.cita;

                // Renderizar servicios actuales
                renderizarServiciosActuales(serviciosActuales);

                // Renderizar servicios disponibles
                renderizarServiciosDisponibles();

                // Actualizar total
                actualizarTotalEditarCita();

                // Actualizar calendario
                calendar.refetchEvents();

                // Actualizar panel de detalles
                cargarDetallesCita(idCita);
            } else {
                showSweetAlertError('Error', data.message);
            }
        })
        .catch(error => {
            console.error('Error al eliminar servicio:', error);
            showSweetAlertError('Error', 'Error al eliminar el servicio de la cita');
        });
    }

    // Agregar un servicio a la cita
    function agregarServicio(idServicio) {
        const idCita = document.getElementById('editar-id-cita').value;

        fetch('citas-programadas', {
            method: 'POST',
            body: new URLSearchParams({
                'action': 'agregar_servicio',
                'id_cita': idCita,
                'id_servicio': idServicio
            })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                // Actualizar servicios actuales con el nuevo servicio
                const nuevoServicio = allServicios.find(s => s.id === idServicio);
                serviciosActuales.push(nuevoServicio);

                // Actualizar duración de la cita
                const cita = data.cita;

                // Renderizar servicios actuales
                renderizarServiciosActuales(serviciosActuales);

                // Renderizar servicios disponibles
                renderizarServiciosDisponibles();

                // Actualizar total
                actualizarTotalEditarCita();

                // Actualizar calendario
                calendar.refetchEvents();

                // Actualizar panel de detalles
                cargarDetallesCita(idCita);
            } else {
                showSweetAlertError('Error', data.message);
            }
        })
        .catch(error => {
            console.error('Error al agregar servicio:', error);
            showSweetAlertError('Error', 'Error al agregar el servicio a la cita');
        });
    }

    // Guardar nueva cita
    function guardarNuevaCita() {
        // Validar que se haya seleccionado un cliente
        if (!clienteSeleccionado || !nuevoIdClienteInput.value) {
            showSweetAlertError('Error', 'Debe seleccionar un cliente');
            return;
        }

        // Validar que haya al menos un servicio seleccionado
        if (serviciosSeleccionados.length === 0) {
            showSweetAlertError('Error', 'Debe seleccionar al menos un servicio');
            return;
        }

        // Obtener datos del formulario
        const fecha = document.getElementById('nueva-fecha').value;
        const hora = document.getElementById('nueva-hora').value;
        const idEmpleado = document.getElementById('nuevo-empleado').value;
        const idCliente = nuevoIdClienteInput.value;

        // Validar fecha y hora
        if (!fecha || !hora) {
            showSweetAlertError('Error', 'La fecha y hora son obligatorias');
            return;
        }

        // Crear fecha completa
        const fechaHora = `${fecha} ${hora}:00`;

        // Preparar datos para enviar
        const formData = new FormData();
        formData.append('action', 'crear_cita');
        formData.append('fecha_inicio', fechaHora);
        formData.append('id_empleado', idEmpleado);
        formData.append('id_cliente', idCliente);
        formData.append('servicios', JSON.stringify(serviciosSeleccionados.map(s => s.id)));

        // Deshabilitar botón y mostrar indicador de carga
        btnGuardarCita.disabled = true;
        btnGuardarCita.innerHTML = '<span class="spinner-border spinner-border-sm me-2" role="status" aria-hidden="true"></span>Guardando...';

        // Enviar solicitud
        fetch('citas-programadas', {
            method: 'POST',
            body: formData
        })
        .then(response => response.json())
        .then(data => {
            // Restaurar botón
            btnGuardarCita.disabled = false;
            btnGuardarCita.innerHTML = 'Guardar';

            if (data.success) {
                // Cerrar modal
                modalNuevaCita.hide();

                // Mostrar mensaje de éxito
                showSweetAlertSuccess('Éxito', 'Cita programada creada correctamente');

                // Actualizar calendario
                calendar.refetchEvents();

                // Limpiar servicios seleccionados y cliente
                serviciosSeleccionados = [];
                deseleccionarCliente();
            } else {
                showSweetAlertError('Error', data.message);
            }
        })
        .catch(error => {
            // Restaurar botón
            btnGuardarCita.disabled = false;
            btnGuardarCita.innerHTML = 'Guardar';

            console.error('Error al guardar cita:', error);
            showSweetAlertError('Error', 'Error al guardar la cita programada');
        });
    }

    // Guardar cambios en cita existente
    function guardarCambiosCita() {
        // Validar que haya al menos un servicio asociado
        if (serviciosActuales.length === 0) {
            showSweetAlertError('Error', 'La cita debe tener al menos un servicio asociado');
            return;
        }

        // Obtener datos del formulario
        const idCita = document.getElementById('editar-id-cita').value;
        const fecha = document.getElementById('editar-fecha').value;
        const hora = document.getElementById('editar-hora').value;
        const idEmpleado = document.getElementById('editar-empleado').value;

        // Validar fecha y hora
        if (!fecha || !hora) {
            showSweetAlertError('Error', 'La fecha y hora son obligatorias');
            return;
        }

        // Crear fecha completa
        const fechaHora = `${fecha} ${hora}:00`;

        // Preparar datos para enviar
        const formData = new FormData();
        formData.append('action', 'modificar_cita');
        formData.append('id_cita', idCita);
        formData.append('fecha_inicio', fechaHora);
        formData.append('id_empleado', idEmpleado);

        // Deshabilitar botón y mostrar indicador de carga
        btnGuardarEditarCita.disabled = true;
        btnGuardarEditarCita.innerHTML = '<span class="spinner-border spinner-border-sm me-2" role="status" aria-hidden="true"></span>Guardando...';

        // Enviar solicitud
        fetch('citas-programadas', {
            method: 'POST',
            body: formData
        })
        .then(response => response.json())
        .then(data => {
            // Restaurar botón
            btnGuardarEditarCita.disabled = false;
            btnGuardarEditarCita.innerHTML = 'Guardar Cambios';

            if (data.success) {
                // Cerrar modal
                modalEditarCita.hide();

                // Mostrar mensaje de éxito
                showSweetAlertSuccess('Éxito', 'Cita programada actualizada correctamente');

                // Actualizar calendario
                calendar.refetchEvents();

                // Actualizar panel de detalles
                cargarDetallesCita(idCita);
            } else {
                showSweetAlertError('Error', data.message);
            }
        })
        .catch(error => {
            // Restaurar botón
            btnGuardarEditarCita.disabled = false;
            btnGuardarEditarCita.innerHTML = 'Guardar Cambios';

            console.error('Error al guardar cambios:', error);
            showSweetAlertError('Error', 'Error al guardar los cambios en la cita programada');
        });
    }

    // Cancelar cita
    function cancelarCita() {
        // Obtener datos del formulario
        const idCita = document.getElementById('cancelar-id-cita').value;
        const razonCancelacion = document.getElementById('razon-cancelacion').value.trim();

        // Validar razón de cancelación
        if (!razonCancelacion) {
            document.getElementById('razon-cancelacion').classList.add('is-invalid');
            return;
        }

        // Preparar datos para enviar
        const formData = new FormData();
        formData.append('action', 'cancelar_cita');
        formData.append('id_cita', idCita);
        formData.append('razon_cancelacion', razonCancelacion);

        // Deshabilitar botón y mostrar indicador de carga
        btnConfirmarCancelarCita.disabled = true;
        btnConfirmarCancelarCita.innerHTML = '<span class="spinner-border spinner-border-sm me-2" role="status" aria-hidden="true"></span>Cancelando...';

        // Enviar solicitud
        fetch('citas-programadas', {
            method: 'POST',
            body: formData
        })
        .then(response => response.json())
        .then(data => {
            // Restaurar botón
            btnConfirmarCancelarCita.disabled = false;
            btnConfirmarCancelarCita.innerHTML = 'Confirmar Cancelación';

            if (data.success) {
                // Cerrar modal
                modalCancelarCita.hide();

                // Mostrar mensaje de éxito
                showSweetAlertSuccess('Éxito', 'Cita programada cancelada correctamente');

                // Actualizar calendario
                calendar.refetchEvents();

                // Ocultar detalles de la cita
                citaDetailsContainer.classList.add('d-none');
                noCitaSelectedContainer.classList.remove('d-none');
            } else {
                showSweetAlertError('Error', data.message);
            }
        })
        .catch(error => {
            // Restaurar botón
            btnConfirmarCancelarCita.disabled = false;
            btnConfirmarCancelarCita.innerHTML = 'Confirmar Cancelación';

            console.error('Error al cancelar cita:', error);
            showSweetAlertError('Error', 'Error al cancelar la cita programada');
        });
    }

    // Abrir modal para cancelar cita directamente desde el panel de detalles
    function abrirModalCancelarCita(citaId) {
        document.getElementById('cancelar-id-cita').value = citaId;
        document.getElementById('razon-cancelacion').value = '';
        document.getElementById('razon-cancelacion').classList.remove('is-invalid');

        modalCancelarCita.show();
    }

    // Función auxiliar para formatear números como moneda
    function formatNumber(number) {
        return new Intl.NumberFormat('es-CO').format(number);
    }
</script>
<?php #endregion JS ?>
</body>
</html>
