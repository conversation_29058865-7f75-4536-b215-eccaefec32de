<?php
#region region DOCS

/** @var int $id ID of the perfil being edited */
/** @var string $nombre Name of the perfil */
/** @var bool $is_edit_mode Whether we're in edit mode (should always be true for this page) */
/** @var Accion[] $acciones_asociadas Array of Accion objects associated with this perfil */
/** @var Accion[] $todas_acciones Array of all Accion objects for selection */
/** @var string $error_text Error message text if any */
/** @var string $error_display Whether to show error message ('show') or not ('') */

use App\classes\Accion;
use App\classes\Perfil;

#endregion DOCS
?>
<!DOCTYPE html>
<html lang="en" class="dark-mode">
<head>
	<meta charset="utf-8"/>
	<title><?php echo APP_NAME; ?> | Editar Perfil</title>
	<meta content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no" name="viewport"/>
	<meta content="" name="description"/>
	<meta content="" name="author"/>
	
	<?php #region HEAD ?>
	<?php require_once __ROOT__ . '/views/general/head.view.php'; ?>
	<?php #endregion HEAD ?>
	
	<style>
        .acciones-container {
            max-height: 500px;
            overflow-y: auto;
        }
        
        .grupo-acciones {
            margin-bottom: 1rem;
            padding: 0.5rem;
            border-radius: 0.25rem;
            background-color: rgba(0, 0, 0, 0.1);
        }
        
        .grupo-acciones h5 {
            margin-bottom: 0.5rem;
            font-weight: 600;
        }
        
        .accion-item {
            padding: 0.5rem;
            margin-bottom: 0.25rem;
            border-radius: 0.25rem;
            background-color: rgba(0, 0, 0, 0.05);
        }
        
        .accion-item:hover {
            background-color: rgba(0, 0, 0, 0.1);
        }
	</style>
</head>
<body>
<!-- BEGIN #loader -->
<div id="loader" class="app-loader">
	<span class="spinner"></span>
</div>
<!-- END #loader -->

<!-- BEGIN #app -->
<div id="app" class="app app-header-fixed app-sidebar-fixed">
	<!-- #header -->
	<?php require_once __ROOT__ . '/views/general/header.view.php'; ?>
	
	<!-- #sidebar -->
	<?php require_once __ROOT__ . '/views/general/sidebar.view.php'; ?>
	
	<!-- BEGIN #content -->
	<div id="content" class="app-content">
		<?php #region region PAGE HEADER ?>
		<div class="d-flex align-items-center mb-3">
			<div>
				<h4 class="mb-0">Editar Perfil</h4>
				<p class="mb-0 text-muted">Modifica los detalles del perfil y sus acciones asociadas</p>
			</div>
			<div class="ms-auto">
				<a href="lperfiles" class="btn btn-secondary"><i class="fa fa-arrow-left fa-fw me-1"></i> Volver a la Lista</a>
			</div>
		</div>
		<hr>
		<?php #endregion PAGE HEADER ?>
		
		<?php #region region FORM ?>
		<form method="POST" action="eperfil">
			<input type="hidden" name="id" value="<?php echo $id; ?>">
			
			<?php #region region PANEL PERFIL DETAILS ?>
			<div class="panel panel-inverse mt-3 no-border-radious">
				<div class="panel-heading no-border-radious">
					<h4 class="panel-title">Detalles del Perfil</h4>
				</div>
				<div class="panel-body">
					<?php if (isset($error_display) && $error_display === 'show'): ?>
						<div class="alert alert-danger">
							<?php echo $error_text; ?>
						</div>
					<?php endif; ?>
					
					<div class="row mb-3">
						<div class="col-md-6">
							<label for="nombre" class="form-label">Nombre</label>
							<input type="text" class="form-control" id="nombre" name="nombre"
							       value="<?php echo htmlspecialchars($nombre); ?>" required>
						</div>
						<div class="col-md-6">
							<label class="form-label">ID</label>
							<input type="text" class="form-control" value="<?php echo $id; ?>" disabled>
						</div>
					</div>
				</div>
			</div>
			<?php #endregion PANEL PERFIL DETAILS ?>
			
			<?php #region region PANEL ACCIONES ?>
			<div class="panel panel-inverse mt-3 no-border-radious">
				<div class="panel-heading no-border-radious">
					<h4 class="panel-title">Acciones Asociadas</h4>
				</div>
				<div class="panel-body">
					<p class="text-muted mb-3">Selecciona las acciones que deseas asociar a este perfil:</p>
					
					<div class="acciones-container">
						<?php
						// Group actions by grupo
						$acciones_por_grupo = [];
						foreach ($todas_acciones as $accion) {
							$grupo = $accion->getGrupo() ?: 'Sin Grupo';
							if (!isset($acciones_por_grupo[$grupo])) {
								$acciones_por_grupo[$grupo] = [];
							}
							$acciones_por_grupo[$grupo][] = $accion;
						}
						
						// Get IDs of associated actions for easy checking
						$acciones_asociadas_ids = [];
						foreach ($acciones_asociadas as $accion) {
							$acciones_asociadas_ids[] = $accion->getId();
						}
						
						// Sort groups alphabetically
						ksort($acciones_por_grupo);
						
						// Display actions grouped by grupo
						foreach ($acciones_por_grupo as $grupo => $acciones) {
							echo '<div class="grupo-acciones">';
							echo '<h5>' . htmlspecialchars($grupo) . '</h5>';
							
							foreach ($acciones as $accion) {
								$checked = in_array($accion->getId(), $acciones_asociadas_ids) ? 'checked' : '';
								echo '<div class="accion-item">';
								echo '<div class="form-check">';
								echo '<input class="form-check-input" type="checkbox" name="accion_ids[]" value="' . $accion->getId() . '" id="accion-' . $accion->getId() . '" ' . $checked . '>';
								echo '<label class="form-check-label" for="accion-' . $accion->getId() . '">';
								echo htmlspecialchars($accion->getNombre());
								
								// Show additional info if available
								$info_parts = [];
								if ($accion->getNombreMenu()) {
									$info_parts[] = 'Menú: ' . htmlspecialchars($accion->getNombreMenu());
								}
								if ($accion->getNombreSubmenu()) {
									$info_parts[] = 'Submenú: ' . htmlspecialchars($accion->getNombreSubmenu());
								}
								
								if (!empty($info_parts)) {
									echo ' <small class="text-muted">(' . implode(', ', $info_parts) . ')</small>';
								}
								
								echo '</label>';
								echo '</div>';
								echo '</div>';
							}
							
							echo '</div>';
						}
						?>
					</div>
				</div>
				<div class="panel-footer text-end">
					<button type="submit" class="btn btn-primary"><i class="fa fa-save fa-fw me-1"></i> Guardar Cambios</button>
					<a href="lperfiles" class="btn btn-secondary"><i class="fa fa-times fa-fw me-1"></i> Cancelar</a>
				</div>
			</div>
			<?php #endregion PANEL ACCIONES ?>
		</form>
		<?php #endregion FORM ?>
	</div>
	<!-- END #content -->
	
	<!-- BEGIN scroll-top-btn -->
	<a href="#" class="btn btn-icon btn-circle btn-success btn-scroll-to-top" data-toggle="scroll-to-top"><i class="fa fa-angle-up"></i></a>
	<!-- END scroll-top-btn -->
</div>
<!-- END #app -->

<?php #region region JS ?>
<?php require_once __ROOT__ . '/views/general/core_js.view.php'; ?>

<!-- ================== BEGIN PAGE LEVEL JS ================== -->
<script>
    document.addEventListener('DOMContentLoaded', function () {
        // You can add any page-specific JavaScript here
    });
</script>
<!-- ================== END PAGE LEVEL JS ================== -->
<?php #endregion JS ?>
</body>
</html>
