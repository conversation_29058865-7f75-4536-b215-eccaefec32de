<?php
/**
 * Vista para la consulta histórica de citas
 *
 * Variables disponibles:
 * @var CentroCosto[] $centros_costos Lista de centros de costo activos
 * @var Empleado[] $empleados Lista de empleados activos
 * @var Puesto[] $puestos Lista de puestos activos
 * @var MetodoPago[] $metodos_pagos Lista de métodos de pago activos
 * @var array|null $consulta_data Datos de la consulta generada
 * @var string $fecha_inicio Fecha de inicio de la consulta
 * @var string $fecha_fin Fecha de fin de la consulta
 * @var int|null $id_centro_costo ID del centro de costo seleccionado
 * @var string $centro_costo_nombre Nombre del centro de costo seleccionado
 * @var string|null $success_text Mensaje de éxito a mostrar
 * @var string|null $success_display Estado de visualización de éxito ('show' o null)
 * @var string|null $error_text Mensaje de error a mostrar
 * @var string|null $error_display Estado de visualización de error ('show' o null)
 */
?>

<!DOCTYPE html>
<html lang="es" class="dark-mode">
<head>
    <meta charset="utf-8"/>
    <title><?php echo APP_NAME; ?> | Consulta Histórica de Citas</title>
    <meta content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no" name="viewport"/>
    <meta content="" name="description"/>
    <meta content="" name="author"/>

    <?php #region HEAD ?>
    <?php require_once __ROOT__ . '/views/general/head.view.php'; ?>
    <?php #endregion HEAD ?>

    <!-- Include Bootstrap Datepicker CSS -->
    <link href="<?php echo RUTA_ADM_ASSETS; ?>plugins/bootstrap-datepicker/dist/css/bootstrap-datepicker.css" rel="stylesheet"/>
</head>

<body>
<!-- BEGIN #loader -->
<div id="loader" class="app-loader">
    <span class="spinner"></span>
</div>
<!-- END #loader -->

<!-- BEGIN #app -->
<div id="app" class="app app-header-fixed app-sidebar-fixed">
    <!-- #header -->
    <?php require_once __ROOT__ . '/views/general/header.view.php'; ?>

    <!-- #sidebar -->
    <?php require_once __ROOT__ . '/views/general/sidebar.view.php'; ?>

    <!-- BEGIN #content -->
    <div id="content" class="app-content">

                    <?php #region PAGE HEADER ?>
                    <div class="d-flex align-items-center mb-3">
                        <div>
                            <h4 class="mb-0">Consulta Histórica de Citas</h4>
                            <p class="mb-0 text-muted">Consulta de registros históricos de citas finalizadas</p>
                        </div>
                    </div>

                    <hr>
                    <?php #endregion PAGE HEADER ?>

                    <?php #region SEARCH FILTERS ?>
                    <!-- Search Filters Panel -->
                    <div class="panel panel-inverse">
                        <div class="panel-heading">
                            <h4 class="panel-title">Filtros de Búsqueda</h4>
                        </div>
                        <div class="panel-body">
                            <form id="consulta-form">
                                <div class="row g-3">
                                    <!-- Fecha Inicio -->
                                    <div class="col-md-6">
                                        <label for="fecha_inicio" class="form-label">Fecha Inicio: <span class="text-danger">*</span></label>
                                        <div class="input-group">
                                            <input type="text" class="form-control datepicker" id="fecha_inicio" name="fecha_inicio"
                                                   placeholder="yyyy-mm-dd" value="<?= htmlspecialchars($fecha_inicio) ?>" required>
                                            <span class="input-group-text" id="fecha_inicio_icon" style="cursor: pointer;">
                                                <i class="fa fa-calendar"></i>
                                            </span>
                                        </div>
                                    </div>

                                    <!-- Fecha Fin -->
                                    <div class="col-md-6">
                                        <label for="fecha_fin" class="form-label">Fecha Fin: <span class="text-danger">*</span></label>
                                        <div class="input-group">
                                            <input type="text" class="form-control datepicker" id="fecha_fin" name="fecha_fin"
                                                   placeholder="yyyy-mm-dd" value="<?= htmlspecialchars($fecha_fin) ?>" required>
                                            <span class="input-group-text" id="fecha_fin_icon" style="cursor: pointer;">
                                                <i class="fa fa-calendar"></i>
                                            </span>
                                        </div>
                                    </div>

                                    <!-- Hidden Centro de Costo field - automatically set from session -->
                                    <input type="hidden" id="id_centro_costo" name="id_centro_costo" value="<?= htmlspecialchars($selected_centro_costo ? $selected_centro_costo->getId() : '') ?>">
                                </div>

                                <div class="row g-3 mt-1">
                                    <!-- Empleado Filter -->
                                    <div class="col-md-4">
                                        <label for="id_empleado" class="form-label">Empleado:</label>
                                        <select class="form-select" id="id_empleado" name="id_empleado">
                                            <option value="">Todos los Empleados</option>
                                            <?php foreach ($empleados as $empleado): ?>
                                                <option value="<?= $empleado->getId() ?>">
                                                    <?= htmlspecialchars($empleado->getNombre()) ?>
                                                </option>
                                            <?php endforeach; ?>
                                        </select>
                                    </div>

                                    <!-- Puesto Filter -->
                                    <div class="col-md-4">
                                        <label for="id_puesto" class="form-label">Puesto:</label>
                                        <select class="form-select" id="id_puesto" name="id_puesto">
                                            <option value="">Todos los Puestos</option>
                                            <?php foreach ($puestos as $puesto): ?>
                                                <option value="<?= $puesto->getId() ?>">
                                                    <?= htmlspecialchars($puesto->getDescripcion()) ?>
                                                </option>
                                            <?php endforeach; ?>
                                        </select>
                                    </div>

                                    <!-- Método de Pago Filter -->
                                    <div class="col-md-4">
                                        <label for="id_metodo_pago" class="form-label">Método de Pago:</label>
                                        <select class="form-select" id="id_metodo_pago" name="id_metodo_pago">
                                            <option value="">Todos los Métodos de Pago</option>
                                            <?php foreach ($metodos_pagos as $metodo_pago): ?>
                                                <option value="<?= $metodo_pago->getId() ?>">
                                                    <?= htmlspecialchars($metodo_pago->getDescripcion()) ?>
                                                </option>
                                            <?php endforeach; ?>
                                        </select>
                                    </div>
                                </div>

                                <div class="row g-3 mt-1 justify-content-end">
                                    <!-- Search Button -->
                                    <div class="col-md-auto">
                                        <button type="submit" class="btn btn-primary">
                                            <i class="fa fa-search fa-fw me-1"></i> Consultar
                                        </button>
                                    </div>
                                    <!-- Clear Filters Button -->
                                    <div class="col-md-auto">
                                        <button type="button" class="btn btn-secondary" id="clear-filters-btn">
                                            <i class="fa fa-times fa-fw me-1"></i> Limpiar Filtros
                                        </button>
                                    </div>
                                </div>
                            </form>
                        </div>
                    </div>
                    <?php #endregion SEARCH FILTERS ?>

                    <?php #region RESULTS ?>
                    <!-- Results Panel -->
                    <div class="panel panel-inverse">
                        <div class="panel-heading">
                            <div class="d-flex align-items-center w-100">
                                <div>
                                    <h4 class="panel-title">Resultados de la Consulta</h4>
                                </div>
                                <div class="ms-auto">
                                    <form method="POST" style="display: inline;" id="export-excel-form">
                                        <input type="hidden" name="action" value="exportar_excel">
                                        <input type="hidden" name="fecha_inicio" id="export-fecha-inicio" value="">
                                        <input type="hidden" name="fecha_fin" id="export-fecha-fin" value="">
                                        <input type="hidden" name="id_empleado" id="export-empleado" value="">
                                        <input type="hidden" name="id_puesto" id="export-puesto" value="">
                                        <input type="hidden" name="id_metodo_pago" id="export-metodo-pago" value="">
                                        <button type="submit" class="btn btn-success btn-sm" id="export-excel-btn" style="display: none;">
                                            <i class="fa fa-file-excel fa-fw me-1"></i> Exportar a Excel
                                        </button>
                                    </form>
                                </div>
                            </div>
                        </div>
                        <div>
                            <!-- Empty state -->
                            <div id="no-results" class="text-center py-4">
                                <i class="fa fa-search fa-3x text-muted mb-3"></i>
                                <h5 class="text-muted">Utilice los filtros para consultar el histórico de citas</h5>
                                <p class="text-muted">Seleccione un rango de fechas para ver las citas finalizadas.</p>
                            </div>

                            <!-- Loading indicator -->
                            <div id="loading" class="text-center py-4" style="display: none;">
                                <i class="fa fa-spinner fa-spin fa-3x text-primary mb-3"></i>
                                <h5 class="text-primary">Consultando registros...</h5>
                            </div>

                            <!-- Results table -->
                            <div id="results-table-container" style="display: none;">
                                <div class="table-responsive">
                                    <table class="table table-hover table-sm mb-0" id="citas-table">
                                        <thead class="table-dark">
                                            <tr>
                                                <th class="text-center" style="width: 100px;">Acciones</th>
                                                <th>Centro de costo</th>
                                                <th>Puesto</th>
                                                <th>Barbero</th>
                                                <th>Método Pago</th>
                                                <th class="text-center" style="width: 140px;">Fecha Inicio</th>
                                                <th class="text-center" style="width: 140px;">Fecha Fin</th>
                                                <th class="text-center" style="width: 100px;">Estado</th>
                                                <th class="text-end" style="width: 120px;">Total</th>
                                            </tr>
                                        </thead>
                                        <tbody id="citas-table-body">
                                            <!-- Results will be populated via AJAX -->
                                        </tbody>
                                        <tfoot id="citas-table-footer" style="display: none;">
                                            <tr class="table-info">
                                                <th colspan="8" class="text-end fw-bold">Total:</th>
                                                <th class="text-end fw-bold" id="citas-total-valor">$0</th>
                                            </tr>
                                        </tfoot>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>
                    <?php #endregion RESULTS ?>

    </div>
    <!-- END #content -->

    <?php #region View Services Modal ?>
    <div class="modal fade" id="viewServicesModal" tabindex="-1" aria-labelledby="viewServicesModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="viewServicesModalLabel">Servicios de la Cita</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <!-- Loading indicator -->
                    <div id="services-modal-loading" class="text-center py-4">
                        <i class="fa fa-spinner fa-spin fa-3x text-primary mb-3"></i>
                        <h5 class="text-primary">Cargando servicios...</h5>
                    </div>

                    <!-- Services content -->
                    <div id="services-modal-content" style="display: none;">
                        <!-- Cita Header Information -->
                        <div class="panel panel-inverse mb-3">
                            <div class="panel-heading">
                                <h4 class="panel-title">Información de la Cita</h4>
                            </div>
                            <div class="panel-body">
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label class="form-label fw-bold text-primary">Puesto:</label>
                                            <div id="service-detail-puesto" class="form-control-plaintext text-white fs-6"></div>
                                        </div>
                                        <div class="mb-3">
                                            <label class="form-label fw-bold text-primary">Empleado:</label>
                                            <div id="service-detail-empleado" class="form-control-plaintext text-white fs-6"></div>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label class="form-label fw-bold text-primary">Fecha Inicio:</label>
                                            <div id="service-detail-fecha-inicio" class="form-control-plaintext text-white fs-6"></div>
                                        </div>
                                        <div class="mb-3">
                                            <label class="form-label fw-bold text-primary">Fecha Fin:</label>
                                            <div id="service-detail-fecha-fin" class="form-control-plaintext text-white fs-6"></div>
                                        </div>
                                        <div class="mb-3">
                                            <label class="form-label fw-bold text-primary">Método de Pago:</label>
                                            <div id="service-detail-metodo-pago" class="form-control-plaintext text-white fs-6"></div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Services Table -->
                        <div class="panel panel-inverse">
                            <div class="panel-heading">
                                <h4 class="panel-title">Servicios Realizados</h4>
                            </div>
                            <div>
                                <div class="table-responsive">
                                    <table class="table table-hover table-sm mb-0" id="services-table">
                                        <thead class="table-dark">
                                            <tr>
                                                <th>Servicio</th>
                                                <th class="text-end" style="width: 120px;">Valor</th>
                                            </tr>
                                        </thead>
                                        <tbody id="services-table-body">
                                            <!-- Services will be populated via AJAX -->
                                        </tbody>
                                        <tfoot>
                                            <tr class="table-info">
                                                <th class="text-end fw-bold">Total:</th>
                                                <th class="text-end fw-bold" id="services-total-valor"></th>
                                            </tr>
                                        </tfoot>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cerrar</button>
                </div>
            </div>
        </div>
    </div>
    <?php #endregion View Services Modal ?>

    <!-- BEGIN scroll-top-btn -->
    <a href="javascript:;" class="btn btn-icon btn-circle btn-theme btn-scroll-to-top" data-toggle="scroll-to-top">
        <i class="fa fa-angle-up"></i>
    </a>
    <!-- END scroll-top-btn -->
</div>
<!-- END #app -->

<?php #region JS ?>
<?php require_once __ROOT__ . '/views/general/core_js.view.php'; ?>

<!-- Include Bootstrap Datepicker JS -->
<script src="<?php echo RUTA_ADM_ASSETS; ?>plugins/bootstrap-datepicker/dist/js/bootstrap-datepicker.js"></script>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Initialize datepickers
    $('.datepicker').datepicker({
        autoclose     : true,
        todayHighlight: true,
        format        : 'yyyy-mm-dd'
    });

    // Make calendar icons clickable
    document.getElementById('fecha_inicio_icon').addEventListener('click', function() {
        $('#fecha_inicio').datepicker('show');
    });

    document.getElementById('fecha_fin_icon').addEventListener('click', function() {
        $('#fecha_fin').datepicker('show');
    });

    // Form Elements
    const consultaForm          = document.getElementById('consulta-form');
    const noResults             = document.getElementById('no-results');
    const loading               = document.getElementById('loading');
    const resultsTableContainer = document.getElementById('results-table-container');
    const citasTableBody        = document.getElementById('citas-table-body');
    const clearFiltersBtn       = document.getElementById('clear-filters-btn');

    // Form validation
    consultaForm.addEventListener('submit', function(e) {
        const fechaInicio = document.getElementById('fecha_inicio').value;
        const fechaFin    = document.getElementById('fecha_fin').value;

        if (!fechaInicio || !fechaFin) {
            e.preventDefault();
            showSweetAlertError('Error de Validación', 'Las fechas de inicio y fin son obligatorias.');
            return false;
        }

        if (new Date(fechaInicio) > new Date(fechaFin)) {
            e.preventDefault();
            showSweetAlertError('Error de Validación', 'La fecha de inicio no puede ser mayor que la fecha de fin.');
            return false;
        }
    });

    // Search form submission
    consultaForm.addEventListener('submit', function(e) {
        e.preventDefault();
        searchCitas();
    });

    // Clear filters functionality
    clearFiltersBtn.addEventListener('click', function() {
        // Clear optional filter dropdowns (keep required fields)
        document.getElementById('id_empleado').value = '';
        document.getElementById('id_puesto').value = '';
        document.getElementById('id_metodo_pago').value = '';

        // Clear results and show empty state
        noResults.innerHTML = `
            <i class="fa fa-search fa-3x text-muted mb-3"></i>
            <h5 class="text-muted">Utilice los filtros para consultar el histórico de citas</h5>
            <p class="text-muted">Seleccione un rango de fechas para ver las citas finalizadas.</p>
        `;
        noResults.style.display             = 'block';
        resultsTableContainer.style.display = 'none';
        loading.style.display               = 'none';

        // Hide table footer when clearing filters
        const citasTableFooter = document.getElementById('citas-table-footer');
        citasTableFooter.style.display = 'none';

        // Hide export button when clearing filters
        const exportBtn = document.getElementById('export-excel-btn');
        exportBtn.style.display = 'none';
    });

    // Search function
    function searchCitas() {
        const formData = new FormData(consultaForm);
        formData.append('action', 'search_citas');

        // Add optional filter parameters
        const idEmpleado = document.getElementById('id_empleado').value;
        const idPuesto = document.getElementById('id_puesto').value;
        const idMetodoPago = document.getElementById('id_metodo_pago').value;

        if (idEmpleado) formData.append('id_empleado', idEmpleado);
        if (idPuesto) formData.append('id_puesto', idPuesto);
        if (idMetodoPago) formData.append('id_metodo_pago', idMetodoPago);

        // Show loading
        noResults.style.display             = 'none';
        resultsTableContainer.style.display = 'none';
        loading.style.display               = 'block';

        fetch('consulta-citas-historico', {
            method: 'POST',
            body: formData
        })
        .then(response => response.json())
        .then(data => {
            loading.style.display = 'none';

            if (data.success) {
                if (data.citas.length > 0) {
                    displayResults(data.citas);
                    if (data.schema_warning) {
                        showSweetAlertSuccess('Consulta Exitosa', 'NOTA: El filtro por centro de costo no está disponible debido a la estructura actual de la base de datos. Se muestran todas las citas del rango de fechas seleccionado.');
                    }
                } else {
                    showNoResults();
                }
            } else {
                showSweetAlertError('Error', data.message || 'Error al consultar las citas');
                showNoResults();
            }
        })
        .catch(error => {
            loading.style.display = 'none';
            showSweetAlertError('Error de Conexión', 'Error de conexión al consultar las citas');
            showNoResults();
            console.error('Error:', error);
        });
    }

    // Display results in table
    function displayResults(citas) {
        citasTableBody.innerHTML = '';
        let totalGeneral = 0;

        citas.forEach(cita => {
            const row = document.createElement('tr');

            // Determinar el badge y texto del estado
            let estadoHtml;
            if (cita.estado === 1) {
                estadoHtml = `<span class="badge bg-success">${cita.estado_texto}</span>`;
            } else {
                estadoHtml = `<span class="badge bg-danger">${cita.estado_texto}</span>`;
                if (cita.razon_cancelacion) {
                    estadoHtml += `<br><small class="text-muted">(${cita.razon_cancelacion})</small>`;
                }
            }

            // Calcular total (extraer el valor numérico del formato de moneda)
            const valorNumerico = parseFloat(cita.total_valor_servicios) || 0;
            totalGeneral += valorNumerico;

            row.innerHTML = `
                <td class="text-center align-middle">
                    <button class="btn btn-info btn-xs" onclick="verServiciosCita(${cita.id})" title="Ver servicios de la cita">
                        <i class="fa fa-eye"></i>
                    </button>
                </td>
                <td class="align-middle">${cita.centro_costo_nombre || 'N/A'}</td>
                <td class="align-middle">${cita.descripcion_puesto || 'N/A'}</td>
                <td class="align-middle">${cita.turno_empleado_nombre || 'N/A'}</td>
                <td class="align-middle">${cita.nombre_metodo_pago || 'N/A'}</td>
                <td class="text-center align-middle">${cita.fecha_inicio_formateada}</td>
                <td class="text-center align-middle">${cita.fecha_fin_formateada}</td>
                <td class="text-center align-middle">${estadoHtml}</td>
                <td class="text-end align-middle">${cita.total_valor_formateado}</td>
            `;
            citasTableBody.appendChild(row);
        });

        // Mostrar y actualizar el total
        const citasTableFooter = document.getElementById('citas-table-footer');
        const citasTotalValor = document.getElementById('citas-total-valor');

        if (citas.length > 0) {
            // Formatear el total en formato de moneda colombiana
            const totalFormateado = '$' + new Intl.NumberFormat('es-CO').format(totalGeneral);
            citasTotalValor.textContent = totalFormateado;
            citasTableFooter.style.display = 'table-footer-group';
        } else {
            citasTableFooter.style.display = 'none';
        }

        resultsTableContainer.style.display = 'block';
        noResults.style.display             = 'none';

        // Show export button and update hidden fields with current filter values
        updateExportForm();
    }

    // Show no results message
    function showNoResults() {
        noResults.innerHTML = `
            <i class="fa fa-search fa-3x text-muted mb-3"></i>
            <h5 class="text-muted">No se encontraron citas</h5>
            <p class="text-muted">No hay citas finalizadas para los criterios de búsqueda seleccionados.</p>
        `;
        noResults.style.display             = 'block';
        resultsTableContainer.style.display = 'none';

        // Hide table footer when no results
        const citasTableFooter = document.getElementById('citas-table-footer');
        citasTableFooter.style.display = 'none';

        // Hide export button when no results
        const exportBtn = document.getElementById('export-excel-btn');
        exportBtn.style.display = 'none';
    }

    // Global function for viewing cita services
    window.verServiciosCita = function(citaId) {
        const modal        = new bootstrap.Modal(document.getElementById('viewServicesModal'));
        const modalLoading = document.getElementById('services-modal-loading');
        const modalContent = document.getElementById('services-modal-content');

        // Show modal and loading state
        modal.show();
        modalLoading.style.display = 'block';
        modalContent.style.display = 'none';

        // Fetch cita services
        const formData = new FormData();
        formData.append('action', 'get_cita_services');
        formData.append('cita_id', citaId);

        fetch('consulta-citas-historico', {
            method: 'POST',
            body: formData
        })
        .then(response => response.json())
        .then(data => {
            modalLoading.style.display = 'none';

            if (data.success) {
                populateCitaServices(data.cita, data.servicios, data.total_valor_formateado);
                modalContent.style.display = 'block';
            } else {
                showSweetAlertError('Error', data.message || 'Error al cargar los servicios de la cita');
                modal.hide();
            }
        })
        .catch(error => {
            modalLoading.style.display = 'none';
            showSweetAlertError('Error de Conexión', 'Error de conexión al cargar los servicios de la cita');
            modal.hide();
            console.error('Error:', error);
        });
    };

    // Function to populate cita services in modal
    function populateCitaServices(cita, servicios, totalFormateado) {
        // Populate header information
        document.getElementById('service-detail-puesto').textContent = cita.descripcion_puesto || 'N/A';
        document.getElementById('service-detail-empleado').textContent = cita.nombre_empleado || 'N/A';
        document.getElementById('service-detail-metodo-pago').textContent = cita.nombre_metodo_pago || 'N/A';

        // Format dates for display
        const fechaInicio = cita.fecha_inicio ? formatearFechaHoraJS(cita.fecha_inicio) : 'N/A';
        const fechaFin    = cita.fecha_fin ? formatearFechaHoraJS(cita.fecha_fin) : 'N/A';

        document.getElementById('service-detail-fecha-inicio').textContent = fechaInicio;
        document.getElementById('service-detail-fecha-fin').textContent = fechaFin;

        // Populate services table
        const servicesTableBody = document.getElementById('services-table-body');
        servicesTableBody.innerHTML = '';

        if (servicios.length > 0) {
            servicios.forEach(servicio => {
                const row = document.createElement('tr');
                row.innerHTML = `
                    <td class="align-middle">${servicio.descripcion}</td>
                    <td class="text-end align-middle">${servicio.valor_formateado}</td>
                `;
                servicesTableBody.appendChild(row);
            });
        } else {
            const row = document.createElement('tr');
            row.innerHTML = `
                <td colspan="2" class="text-center text-muted py-3">
                    <i class="fa fa-info-circle me-2"></i>No hay servicios registrados para esta cita
                </td>
            `;
            servicesTableBody.appendChild(row);
        }

        // Set total
        document.getElementById('services-total-valor').textContent = totalFormateado;
    }

    // Helper function to format date and time in JavaScript
    function formatearFechaHoraJS(fechaHora) {
        if (!fechaHora) return 'N/A';

        try {
            const fecha   = new Date(fechaHora);
            const year    = fecha.getFullYear();
            const month   = String(fecha.getMonth() + 1).padStart(2, '0');
            const day     = String(fecha.getDate()).padStart(2, '0');
            let   hours   = fecha.getHours();
            const minutes = String(fecha.getMinutes()).padStart(2, '0');
            const ampm    = hours >= 12 ? 'PM' : 'AM';
                  hours   = hours % 12;
                  hours   = hours ? hours : 12;                             // the hour '0' should be '12'

            return `${year}-${month}-${day} ${hours}:${minutes} ${ampm}`;
        } catch (e) {
            return fechaHora;
        }
    }

    // Function to update export form with current filter values
    function updateExportForm() {
        const exportBtn = document.getElementById('export-excel-btn');

        // Update hidden fields with current filter values
        document.getElementById('export-fecha-inicio').value = document.getElementById('fecha_inicio').value;
        document.getElementById('export-fecha-fin').value = document.getElementById('fecha_fin').value;
        document.getElementById('export-empleado').value = document.getElementById('id_empleado').value;
        document.getElementById('export-puesto').value = document.getElementById('id_puesto').value;
        document.getElementById('export-metodo-pago').value = document.getElementById('id_metodo_pago').value;

        // Show export button
        exportBtn.style.display = 'inline-block';
    }

    // Handle Excel Export Button
    const exportForm = document.getElementById('export-excel-form');
    const exportBtn = document.getElementById('export-excel-btn');

    exportForm.addEventListener('submit', function(e) {
        // Show loading state
        const originalText = exportBtn.innerHTML;
        exportBtn.innerHTML = '<i class="fa fa-spinner fa-spin fa-fw me-1"></i> Exportando...';
        exportBtn.disabled = true;

        // Reset button after a delay (since we're downloading a file)
        setTimeout(function() {
            exportBtn.innerHTML = originalText;
            exportBtn.disabled = false;
        }, 3000);
    });

});
</script>

</body>

</html>
<?php #endregion JS ?>
