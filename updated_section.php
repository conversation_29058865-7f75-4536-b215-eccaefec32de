<?php if (isset($puesto->turno_activo->tiene_cita_activa) && $puesto->turno_activo->tiene_cita_activa): ?>
	<div class="appointment-panel">
		<div class="appointment-panel-header">
			<span><i class="fa fa-calendar-check me-1"></i> Cita <span class="ms-2"><?php echo date('H:i', strtotime($puesto->turno_activo->cita_activa->getFecha_inicio())); ?></span></span>
			<div>
				<button type="button" class="btn btn-xs btn-warning btn-editar-cita p-0" title="Editar cita"
						data-id-cita="<?php echo $puesto->turno_activo->cita_activa->getId(); ?>">
					<i class="fa fa-edit"></i>
				</button>

			</div>

		</div>
		<div class="appointment-panel-body">
			<?php if (isset($puesto->turno_activo->servicios_cita) && !empty($puesto->turno_activo->servicios_cita)): ?>
				<ul class="appointment-service-list">
					<?php foreach ($puesto->turno_activo->servicios_cita as $servicio): ?>
						<li class="appointment-service-item">
							<span><?php echo htmlspecialchars($servicio->getDescripcion()); ?></span>
							<span class="appointment-service-price">$<?php echo number_format($servicio->getValor(), 0, ',', '.'); ?></span>
						</li>
					<?php endforeach; ?>
				</ul>
				<div class="appointment-total">
					<span>Total:</span>
					<span class="appointment-total-price">$<?php echo number_format($puesto->turno_activo->total_servicios, 0, ',', '.'); ?></span>
				</div>
			<?php else: ?>
				<p class="text-muted mb-0">No hay servicios asociados a esta cita.</p>
			<?php endif; ?>
		</div>
	</div>
	<div class="mt-2">
		<button type="button" class="btn btn-sm btn-success btn-finalizar-cita w-100"
				data-id-cita="<?php echo $puesto->turno_activo->cita_activa->getId(); ?>">
			<i class="fa fa-check-circle me-1"></i> Finalizar Cita
		</button>
	</div>
<?php endif; ?>
