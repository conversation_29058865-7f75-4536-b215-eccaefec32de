# Commission Calculation Implementation

## Overview
This document describes the implementation of the employee commission calculation functionality for the barbershop management system. The system now automatically calculates and stores employee commissions when appointments (citas) are finalized.

## Implementation Details

### 1. Database Changes
The `valor_comision_empleado` field was already added to the `citas` table and is now properly integrated into all database operations.

### 2. New Methods in Cita Class

#### `getPorcentajeComisionEmpleado(PDO $conexion): float`
- **Purpose**: Retrieves the commission percentage of the employee associated with the appointment
- **Returns**: Employee's commission percentage (0.0 if not found)
- **Usage**: Internal method used by commission calculation

#### `calcularComisionEmpleado(PDO $conexion): float`
- **Purpose**: Calculates and sets the employee commission based on the total value of services
- **Formula**: `round(total_value_of_cita * (Empleado.porc_comision / 100), 0)`
- **Returns**: The calculated commission value
- **Side Effect**: Updates the `valor_comision_empleado` field in the object

### 3. Updated Methods

#### `Cita::finalizar()` - Enhanced
- **Enhancement**: Now automatically calculates and stores employee commission when finalizing appointments
- **Process**:
  1. <PERSON>idates the appointment can be finalized
  2. Calculates employee commission using `calcularComisionEmpleado()`
  3. Updates database with end time, payment method, and commission value
- **Error Handling**: If commission calculation fails, continues with 0 value and logs error

#### `_insert()` and `_update()` - Enhanced
- **Enhancement**: Now include `valor_comision_empleado` field in database operations
- **Ensures**: Commission values are properly persisted to database

## Business Logic

### Commission Calculation Formula
```php
$valorComision = round($valorTotal * ($porcentajeComision / 100), 0);
```

Where:
- `$valorTotal`: Sum of all service values in the appointment
- `$porcentajeComision`: Employee's commission percentage from `empleados.porc_comision`
- Result is rounded to nearest whole number (Colombian Peso format)

### Data Flow
1. **Appointment Creation**: Commission starts at 0.0
2. **Service Addition**: Services are added to appointment with specific values
3. **Appointment Finalization**: 
   - System calculates total service value
   - Retrieves employee commission percentage
   - Calculates commission using formula
   - Stores all data in database

## Integration Points

### Dashboard Integration
The commission calculation is automatically triggered when users click "Finalizar Cita" in the dashboard:

1. User selects payment method and clicks "Finalizar Cita"
2. AJAX request sent to `src/controllers/citas/finalizar_cita.php`
3. Controller calls `Cita::finalizar()` method
4. Method automatically calculates and stores commission
5. Response returned to frontend

### Database Relationships
```
citas
├── id_empleado_turno → empleados_turnos
│   └── id_empleado → empleados
│       └── porc_comision (used for calculation)
└── citas_servicios
    └── valor (summed for total value)
```

## Error Handling

### Graceful Degradation
- If commission calculation fails, appointment finalization continues with 0 commission
- Errors are logged for debugging purposes
- User experience is not interrupted

### Validation
- Validates appointment exists and can be finalized
- Validates employee turn relationship exists
- Handles null/missing commission percentages (defaults to 0.0)

## Testing

### Test Script
Run `test_commission_calculation.php` to verify implementation:
```bash
php test_commission_calculation.php
```

### Test Scenarios
1. Employee with 15% commission
2. Service worth $50,000 COP
3. Expected commission: $7,500 COP
4. Verification of database storage

## Usage Examples

### Manual Commission Calculation
```php
$cita = Cita::get($citaId, $conexion);
$commission = $cita->calcularComisionEmpleado($conexion);
echo "Commission: $" . number_format($commission, 0, ',', '.') . " COP";
```

### Automatic During Finalization
```php
// Commission is automatically calculated and stored
$result = Cita::finalizar($citaId, $fechaFin, $metodoPagoId, $conexion);
```

### Retrieving Stored Commission
```php
$cita = Cita::get($citaId, $conexion);
$commission = $cita->getValor_comision_empleado();
```

## Future Enhancements

### Potential Improvements
1. **Commission Reports**: Generate reports showing employee commissions by period
2. **Commission Adjustments**: Allow manual commission adjustments with audit trail
3. **Variable Commission Rates**: Support different commission rates by service type
4. **Commission Caps**: Implement maximum commission limits per appointment or period

### Payroll Integration
The stored commission values can be used for:
- Monthly payroll calculations
- Commission-based performance reports
- Employee earnings summaries
- Tax reporting and documentation

## Maintenance Notes

### Code Location
- **Main Implementation**: `src/classes/Cita.php`
- **Controller**: `src/controllers/citas/finalizar_cita.php`
- **Frontend**: `views/dashboard.view.php`

### Database Fields
- `citas.valor_comision_empleado`: Stores calculated commission value
- `empleados.porc_comision`: Employee commission percentage (0-100)

### Dependencies
- Requires existing employee and service data
- Depends on proper EmpleadoTurno relationships
- Uses existing appointment finalization workflow
