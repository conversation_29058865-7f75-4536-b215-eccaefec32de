# Centro de Costo Selector - Toast Notification Update

## Overview
Updated the centro de costo selector in the sidebar to use toast notifications instead of SweetAlert dialogs for a more modern, less intrusive user experience.

## Changes Made

### 1. Replaced SweetAlert with Toast Notifications

#### Before (SweetAlert):
```javascript
// Success message
swal({
    title: 'Éxito',
    text: data.message,
    icon: 'success',
    timer: 2000,
    buttons: false
});

// Error message
swal({
    title: 'Error',
    text: data.message || 'Error al cambiar centro de costo',
    icon: 'error',
    button: {
        text: "Cerrar",
        value: true,
        visible: true,
        className: "btn-danger",
        closeModal: true
    }
});
```

#### After (Toast Notifications):
```javascript
// Success message
showCentroCostoToastNotification('success', 'Centro de Costo', data.message);

// Error message
showCentroCostoToastNotification('error', 'Error', data.message || 'Error al cambiar centro de costo');
```

### 2. Added Toast Infrastructure

#### CSS Integration:
```html
<!-- Toast Notifications CSS -->
<link href="<?php echo RUTA; ?>resources/css/toast-notifications.css" rel="stylesheet" />
```

#### HTML Structure:
```html
<!-- Toast Container for Centro de Costo Notifications -->
<div class="toast-container position-fixed top-0 end-0 p-3" style="z-index: 1055;">
    <div id="centro-costo-toast-notification" class="toast" role="alert" aria-live="assertive" aria-atomic="true">
        <div class="toast-header">
            <i id="centro-costo-toast-icon" class="fa fa-check-circle text-success me-2"></i>
            <strong id="centro-costo-toast-title" class="me-auto">Centro de Costo</strong>
            <button type="button" class="btn-close" data-bs-dismiss="toast" aria-label="Close"></button>
        </div>
        <div class="toast-body" id="centro-costo-toast-message">
            Mensaje de notificación
        </div>
    </div>
</div>
```

#### JavaScript Function:
```javascript
function showCentroCostoToastNotification(type, title, message) {
    const toast = document.getElementById('centro-costo-toast-notification');
    const toastIcon = document.getElementById('centro-costo-toast-icon');
    const toastTitle = document.getElementById('centro-costo-toast-title');
    const toastMessage = document.getElementById('centro-costo-toast-message');

    // Configure toast based on type
    if (type === 'success') {
        toastIcon.className = 'fa fa-check-circle text-success me-2';
        toast.className = 'toast border-success';
    } else if (type === 'error') {
        toastIcon.className = 'fa fa-exclamation-circle text-danger me-2';
        toast.className = 'toast border-danger';
    } else if (type === 'warning') {
        toastIcon.className = 'fa fa-exclamation-triangle text-warning me-2';
        toast.className = 'toast border-warning';
    } else {
        toastIcon.className = 'fa fa-info-circle text-info me-2';
        toast.className = 'toast border-info';
    }

    toastTitle.textContent = title;
    toastMessage.textContent = message;

    // Show toast with appropriate settings
    const bsToast = new bootstrap.Toast(toast, {
        autohide: type === 'success', // Auto-hide success, manual dismiss for errors
        delay: type === 'success' ? 3000 : 0 // 3 seconds for success, no auto-hide for errors
    });
    bsToast.show();
}
```

## Benefits of Toast Notifications

### User Experience Improvements:
1. **Less Intrusive**: Toasts appear in corner without blocking the interface
2. **Non-Modal**: Users can continue interacting with the page
3. **Consistent**: Matches existing toast patterns in the application
4. **Accessible**: Proper ARIA attributes for screen readers
5. **Responsive**: Adapts to mobile screens automatically

### Technical Advantages:
1. **Lightweight**: No additional JavaScript libraries required
2. **Bootstrap Native**: Uses Bootstrap's built-in toast component
3. **Customizable**: Easy to modify styling and behavior
4. **Stackable**: Multiple toasts can be shown simultaneously
5. **Dismissible**: Users can manually close error messages

## Visual Comparison

### SweetAlert (Before):
- Modal dialog that blocks the entire interface
- Requires user interaction to dismiss
- Centered on screen
- Heavy visual impact

### Toast Notifications (After):
- Small notification in top-right corner
- Auto-dismisses for success messages
- Allows continued interaction with the page
- Subtle, professional appearance

## Implementation Details

### File Modified:
- `views/general/sidebar.view.php`

### Dependencies Added:
- `resources/css/toast-notifications.css` (existing file)
- Bootstrap Toast component (already available)

### Timing Configuration:
- **Success Messages**: Auto-dismiss after 3 seconds
- **Error Messages**: Manual dismiss required (no auto-hide)
- **Page Reload**: Delayed by 1.5 seconds after success toast

### Styling Features:
- Backdrop blur effect
- Smooth animations
- Color-coded borders (green for success, red for error)
- Consistent with existing application toasts
- Dark mode support
- Mobile responsive design

## Backward Compatibility

The update maintains all existing functionality:
- Same AJAX endpoint and request structure
- Same session management
- Same error handling logic
- Same page reload behavior

Only the notification method has changed from SweetAlert to toast notifications.

## Testing Checklist

- [ ] Success toast appears when centro de costo switch succeeds
- [ ] Success toast auto-dismisses after 3 seconds
- [ ] Error toast appears when switch fails
- [ ] Error toast requires manual dismissal
- [ ] Toast positioning is correct (top-right corner)
- [ ] Toast styling matches existing application toasts
- [ ] Page reload occurs after successful switch
- [ ] Multiple toasts can be displayed if needed
- [ ] Toast is accessible with screen readers
- [ ] Toast works on mobile devices

## Future Considerations

1. **Toast Queue**: Could implement a queue system for multiple simultaneous notifications
2. **Toast Persistence**: Could add option to persist important messages across page reloads
3. **Toast Categories**: Could add more notification types (info, warning)
4. **Toast Actions**: Could add action buttons to toasts for quick operations
5. **Toast Analytics**: Could track user interaction with toast notifications
