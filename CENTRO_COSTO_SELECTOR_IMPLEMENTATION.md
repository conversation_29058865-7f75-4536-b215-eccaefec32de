# Centro de Costo Selector Implementation

## Overview
This document describes the implementation of the centro de costo selector in the sidebar that allows users to switch between their associated centro de costos seamlessly.

## Features Implemented

### 1. Sidebar Centro de Costo Selector
- **Location**: `views/general/sidebar.view.php`
- **Display**: Shows below the user's name in the sidebar
- **Functionality**: 
  - Displays current selected centro de costo
  - Allows switching between associated centro de costos via dropdown
  - Shows appropriate messages for different scenarios

### 2. Session Management
- **Location**: `src/general/preparar.php`
- **Functionality**:
  - Loads user's associated centro de costos on every page load
  - Manages centro de costo selection in session
  - Auto-selects first centro de costo if none selected
  - Validates session centro de costo against user's permissions

### 3. AJAX Switching Endpoint
- **Location**: `src/ajax/switch_centro_costo.php`
- **URL**: `switch-centro-costo` (friendly URL via .htaccess)
- **Functionality**:
  - Handles centro de costo switching requests
  - Validates user permissions
  - Updates session with new selection
  - Returns JSON response with success/error status

### 4. Session Constant
- **Location**: `config/config.php`
- **Constant**: `CENTRO_COSTO_SESSION = 'barberp_centro_costo'`
- **Usage**: Consistent session variable naming across the application

## User Experience Scenarios

### Scenario 1: User with Multiple Centro de Costos
- Shows dropdown with all associated centro de costos
- Current selection is highlighted
- User can switch by selecting different option
- Page reloads after successful switch to reflect changes

### Scenario 2: User with Single Centro de Costo
- Shows centro de costo name as text (no dropdown)
- Auto-selected and stored in session
- No switching interface needed

### Scenario 3: User with No Centro de Costos
- Shows warning message "Sin asignar"
- Displays warning icon
- No switching functionality available

## Technical Implementation Details

### Session Variables
```php
$_SESSION[CENTRO_COSTO_SESSION] // Stores selected centro de costo ID
```

### Available Variables in Views
```php
$user_centros_costos           // Array of CentroCosto objects for current user
$selected_centro_costo         // Currently selected CentroCosto object
$selected_centro_costo_nombre  // Name of currently selected centro de costo
```

### JavaScript Integration
- Uses existing SweetAlert patterns for notifications
- Implements proper error handling
- Shows loading states during switching
- Reloads page after successful switch

### Styling
- Consistent with existing sidebar styling
- Dark theme compatible
- Responsive design
- Bootstrap form components

## Database Relationships Used

### Many-to-Many Relationship
- **Table**: `centros_costos_usuarios`
- **Method**: `CentroCosto::get_by_usuario($user_id, $conexion)`
- **Returns**: Array of CentroCosto objects associated with the user

## Files Modified

1. **`config/config.php`**
   - Added `CENTRO_COSTO_SESSION` constant

2. **`src/general/preparar.php`**
   - Added centro de costo loading and session management logic
   - Added variables for use in views

3. **`views/general/sidebar.view.php`**
   - Added centro de costo selector UI
   - Added JavaScript for AJAX switching
   - Added CSS styling for the selector

4. **`.htaccess`**
   - Added friendly URL rewrite rule for AJAX endpoint

## Files Created

1. **`src/ajax/switch_centro_costo.php`**
   - AJAX endpoint for centro de costo switching
   - Includes validation and error handling

## Usage in Other Parts of Application

### Accessing Selected Centro de Costo
```php
// In any controller or view that includes preparar.php
if (isset($_SESSION[CENTRO_COSTO_SESSION])) {
    $selected_centro_costo_id = $_SESSION[CENTRO_COSTO_SESSION];
    // Use the selected centro de costo ID for filtering, etc.
}

// Or use the prepared variables
if ($selected_centro_costo) {
    $centro_costo_id = $selected_centro_costo->getId();
    $centro_costo_nombre = $selected_centro_costo->getNombre();
}
```

### Filtering Data by Centro de Costo
```php
// Example: Filter data by selected centro de costo
$centro_costo_id = $_SESSION[CENTRO_COSTO_SESSION] ?? null;
if ($centro_costo_id) {
    // Apply centro de costo filter to queries
    $filtered_data = SomeClass::getBycentroCosto($centro_costo_id, $conexion);
}
```

## Security Considerations

1. **Permission Validation**: The system validates that users can only switch to centro de costos they are associated with
2. **Session Security**: Uses established session patterns and constants
3. **Input Validation**: All inputs are validated and sanitized
4. **Error Handling**: Proper error messages without exposing sensitive information

## Notification System Update

### Toast Notifications Implementation
The centro de costo selector now uses toast notifications instead of SweetAlert dialogs for a less intrusive user experience.

#### Toast Features:
- **Success Messages**: Auto-dismiss after 3 seconds with green styling
- **Error Messages**: Manual dismiss required with red styling
- **Consistent Styling**: Matches existing toast implementation in `views/iventas.view.php`
- **Positioning**: Top-right corner with proper z-index
- **Responsive**: Adapts to mobile screens

#### Toast Structure:
```html
<!-- Toast Container -->
<div class="toast-container position-fixed top-0 end-0 p-3" style="z-index: 1055;">
    <div id="centro-costo-toast-notification" class="toast" role="alert" aria-live="assertive" aria-atomic="true">
        <div class="toast-header">
            <i id="centro-costo-toast-icon" class="fa fa-check-circle text-success me-2"></i>
            <strong id="centro-costo-toast-title" class="me-auto">Centro de Costo</strong>
            <button type="button" class="btn-close" data-bs-dismiss="toast" aria-label="Close"></button>
        </div>
        <div class="toast-body" id="centro-costo-toast-message">
            Mensaje de notificación
        </div>
    </div>
</div>
```

#### JavaScript Function:
```javascript
function showCentroCostoToastNotification(type, title, message) {
    // Configure toast based on type (success, error, warning, info)
    // Show toast with appropriate auto-hide settings
    // Success: auto-hide after 3 seconds
    // Error: manual dismiss required
}
```

#### CSS Integration:
- Uses `resources/css/toast-notifications.css` for consistent styling
- Includes backdrop blur effects and proper animations
- Dark mode support and responsive design

## Future Enhancements

1. **Remember Last Selection**: Could store user's preferred centro de costo in database
2. **Quick Switch**: Could add keyboard shortcuts for power users
3. **Visual Indicators**: Could add visual indicators throughout the app showing current centro de costo context
4. **Audit Trail**: Could log centro de costo switches for audit purposes
5. **Toast Stacking**: Could implement toast stacking for multiple simultaneous notifications

## Testing Recommendations

1. Test with users having different numbers of centro de costos (0, 1, multiple)
2. Test session persistence across page navigation
3. Test permission validation (users trying to access unauthorized centro de costos)
4. Test error handling (network errors, server errors)
5. Test UI responsiveness on different screen sizes
6. **Test toast notifications**: Verify success and error toasts display correctly
7. **Test toast timing**: Confirm auto-dismiss for success and manual dismiss for errors
8. **Test toast conflicts**: Ensure centro de costo toasts don't conflict with other page toasts
