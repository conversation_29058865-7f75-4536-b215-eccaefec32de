function pressenterandclick(idinput,idbutton) {
	// Get the input field
	var input = document.getElementById(idinput);
			
	input.addEventListener("keypress", function(event) {
		if (event.key === "Enter") {
			// Cancel the default action, if needed
			event.preventDefault();
			// Trigger the button element with a click
			document.getElementById(idbutton).click();
		}
	});
}

function pressenterandfocus(idinputfrom,idinputto) {
	// Get the input field
	var input = document.getElementById(idinputfrom);
			
	input.addEventListener("keypress", function(event) {
		if (event.key === "Enter") {
			// Cancel the default action, if needed
			event.preventDefault();
			// Trigger the button element with a click
			document.getElementById(idinputto).focus();
		}
	});
}

function formatCurrency(amount) {
    return new Intl.NumberFormat('es-CO', {
        style: 'currency',
        currency: 'COP',
        minimumFractionDigits: 0,
        maximumFractionDigits: 0
    }).format(amount);
}