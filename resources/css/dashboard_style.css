.titulo-puesto{
    font-size: .9rem;
    font-weight: 600;
}
.position-card {
    transition: all 0.3s ease;
    border-radius: 10px;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    margin-bottom: 20px;
    height: 100%;
}

.position-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 15px rgba(0, 0, 0, 0.2);
}

.position-card .card-header {
    border-top-left-radius: 10px;
    border-top-right-radius: 10px;
    font-weight: 600;
}

.position-card .card-body {
    padding: .5rem;
}

.position-card .card-footer {
    border-bottom-left-radius: 10px;
    border-bottom-right-radius: 10px;
    padding: 0.75rem 1.25rem;
}

.position-card.occupied {
    border-left: 4px solid #00acac;
}

.position-card.available {
    border-left: 4px solid #ff5b57;
}

.employee-badge {
    display: inline-block;
    padding: 0.5rem 1rem;
    border-radius: 5px;
    background-color: #00acac;
    color: white;
    font-weight: 600;
    margin-bottom: .5rem;
}

.position-status {
    display: inline-block;
    padding: 0.25rem 0.75rem;
    border-radius: 20px;
    font-size: 0.8rem;
    font-weight: 600;
    margin-left: 0.5rem;
}

.status-occupied {
    background-color: #00acac;
    color: white;
}

.status-available {
    background-color: #ff5b57;
    color: white;
}

/* Estilos para indicador de cita activa */
.appointment-indicator {
    position: absolute;
    top: 7px;
    right: 9px;
    background-color: #f59c1a;
    color: white;
    border-radius: 50%;
    width: 27px;
    height: 27px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 16px;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
    z-index: 2;
}

.appointment-badge {
    display: inline-block;
    padding: 0.25rem 0.75rem;
    border-radius: 20px;
    background-color: #f59c1a;
    color: white;
    font-weight: 600;
    margin-top: 0.5rem;
    font-size: 0.85rem;
}

.appointment-panel {
    background-color: #343a40;
    border-radius: 8px;
    border-left: 4px solid #f59c1a;
    margin-bottom: 15px;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
    overflow: hidden;
}

.appointment-panel-header {
    background-color: #f59c1a;
    color: white;
    padding: 8px 12px;
    font-weight: 600;
    font-size: 0.8rem;
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.appointment-panel-body {
    padding: 5px;
}

.appointment-service-list {
    list-style: none;
    padding: 0 10px;
    margin: 0;
}

.appointment-panel-body .appointment-service-list {
    padding: 0 !important;
}

.appointment-service-item {
    display: flex;
    justify-content: space-between;
    padding: 6px 0;
    /*border-bottom: 1px solid rgba(255, 255, 255, 0.1);*/
    font-size: 0.75rem;
}

.appointment-service-item:last-child {
    border-bottom: none;
}

.appointment-service-price {
    font-weight: 600;
    color: #f59c1a;
}

.appointment-total {
    display: flex;
    justify-content: space-between;
    margin-top: 8px;
    padding-top: 8px;
    border-top: 2px solid rgba(255, 255, 255, 0.2);
    font-weight: 600;
}

.appointment-total-price {
    color: #f59c1a;
    font-size: .75rem;
}

/* Estilos para la selección de turnos en el modal de citas */
.turno-card {
    border: 1px solid #495057;
    border-radius: 6px;
    padding: 10px;
    margin-bottom: 8px;
    cursor: pointer;
    transition: all 0.2s ease;
    background-color: #343a40;
    color: #f8f9fa;
}

.turno-card:hover {
    background-color: #495057;
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);
}

.turno-card.selected {
    border-color: #00acac;
    background-color: #2c3136;
    box-shadow: 0 0 0 2px rgba(0, 172, 172, 0.7);
}

.turno-card .empleado-nombre {
    font-weight: 600;
    font-size: 1.1rem;
    margin-bottom: 3px;
    color: #f8f9fa;
}

.turno-card .puesto-descripcion {
    color: #adb5bd;
    margin-bottom: 0;
    font-size: 0.9rem;
}

.turnos-container {
    max-height: 250px;
    overflow-y: auto;
    padding: 10px 10px 2px 10px;
    border-radius: 8px;
    background-color: #212529;
    border: 1px solid #495057;
}

.turnos-container::-webkit-scrollbar {
    width: 8px;
}

.turnos-container::-webkit-scrollbar-track {
    background: #343a40;
    border-radius: 10px;
}

.turnos-container::-webkit-scrollbar-thumb {
    background: #495057;
    border-radius: 10px;
}

.turnos-container::-webkit-scrollbar-thumb:hover {
    background: #6c757d;
}

/* Estilos para la sección de servicios */
.form-check-label {
    font-size: 1.1rem;
    padding: 5px 0;
    display: flex;
    align-items: center;
    justify-content: space-between;
    cursor: pointer;
}

.form-check {
    margin-bottom: 0;
    padding: 8px 10px;
    border-radius: 6px;
    transition: background-color 0.2s ease;
}

.form-check:hover {
    background-color: rgba(0, 0, 0, 0.05);
}

.form-check-input {
    transform: scale(1.2);
    margin-left: 0 !important;
    cursor: pointer;
}

.badge.bg-primary {
    /*padding: 6px 50px;*/
    font-size: 0.9rem;
    border-radius: 20px;
    margin-left: 10px;
    font-weight: 600;
}

/* Estilos para el total de servicios */
.total-container {
    display: flex;
    justify-content: space-between;
    padding: 10px 10px;
    margin-top: 10px;
    border-top: 1px solid #dee2e6;
}

.total-badge {
    padding: 6px 50px;
    font-size: .9rem;
    border-radius: 20px;
    font-weight: 600;
    background-color: #00acac;
    color: white;
    display: inline-flex;
    align-items: center;
}

.total-label {
    margin-right: 10px;
    font-weight: 600;
    font-size: 1.1rem;
    height: 40px;
    padding-top: 8px;
}

.btn-editar-cita i {
    font-size: .9rem !important;
}

/* Estilo para el valor total de servicios */
.total-value {
    font-size: 1.1rem;
    font-weight: 700;
    color: #00acac;
    padding-top: 8px;
}

/* Estilo para los precios de servicios */
.service-price {
    margin-left: 10px;
    font-weight: 600;
    color: white;
    font-size: 1.1rem;
    white-space: nowrap;
    text-align: right;
}

/* Estilo para las descripciones de servicios */
.service-description {
    font-size: 1.1rem;
    margin-right: 10px;
    overflow: hidden;
    text-overflow: ellipsis;
    flex: 1;
}

span#total-cita-valor {
    padding-top: 0 !important;
}

/* Estilos para los checkboxes de servicios */
.servicio-checkbox {
    width: 18px;
    height: 18px;
    cursor: pointer;
    flex-shrink: 0;
}

/* Estilos para los items de servicio */
.appointment-service-item {
    padding: 8px 0;
}

.appointment-service-item:hover {
    background-color: rgba(255, 255, 255, 0.05);
}

/* Estilos para los contenedores de form-check */
.form-check.d-flex {
    min-height: 40px;
}

/* Asegurar que los labels ocupen el espacio disponible */
.form-check-label.w-100 {
    min-width: 0; /* Permite que el texto se recorte si es necesario */
}

/* Clase para el efecto hover en los items de servicio */
.service-item-hover {
    margin-bottom: 0;
    padding: 8px 10px;
    border-radius: 6px;
    transition: background-color 0.2s ease;
    cursor: pointer;
}

.service-item-hover:hover {
    background-color: rgba(73, 80, 87, 0.5);
}

/* Estilos para el botón de eliminar servicios seleccionados */
#btn-eliminar-servicios-seleccionados {
    transition: all 0.3s ease;
}

#btn-eliminar-servicios-seleccionados:disabled {
    opacity: 0.6;
    cursor: not-allowed;
}

/* Estilos para el contenedor de botones */
.botones-servicios-container {
    margin-top: 15px;
    margin-bottom: 15px;
    padding-top: 10px;
    border-top: 1px solid rgba(255, 255, 255, 0.1);
}

.content-dashboard-citas{
    padding: 15px;
}
