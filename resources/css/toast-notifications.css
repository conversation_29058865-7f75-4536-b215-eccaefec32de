/* Toast Notifications CSS */

/* Toast container positioning */
.toast-container {
    position: fixed !important;
    top: 20px !important;
    right: 20px !important;
    z-index: 1055 !important;
    pointer-events: none;
}

/* Toast base styling */
.toast {
    pointer-events: auto;
    max-width: 350px;
    font-size: 0.875rem;
    background-color: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(0, 0, 0, 0.1);
    border-radius: 0.5rem;
    box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
    margin-bottom: 0.5rem;
}

/* Toast header styling */
.toast-header {
    background-color: rgba(248, 249, 250, 0.95);
    border-bottom: 1px solid rgba(0, 0, 0, 0.1);
    border-radius: 0.5rem 0.5rem 0 0;
    padding: 0.5rem 0.75rem;
    display: flex;
    align-items: center;
}

/* Toast body styling */
.toast-body {
    padding: 0.75rem;
    word-wrap: break-word;
    color: #212529;
}

/* Success toast styling */
.toast.border-success {
    border-color: #00acac !important;
}

.toast.border-success .toast-header {
    background-color: #00acac;
    border-bottom-color: rgba(25, 135, 84, 0.2);
}

/* Error toast styling */
.toast.border-danger {
    border-color: #dc3545 !important;
}

.toast.border-danger .toast-header {
    background-color: rgba(220, 53, 69, 0.1);
    border-bottom-color: rgba(220, 53, 69, 0.2);
}

/* Warning toast styling */
.toast.border-warning {
    border-color: #ffc107 !important;
}

.toast.border-warning .toast-header {
    background-color: rgba(255, 193, 7, 0.1);
    border-bottom-color: rgba(255, 193, 7, 0.2);
}

/* Info toast styling */
.toast.border-info {
    border-color: #0dcaf0 !important;
}

.toast.border-info .toast-header {
    background-color: rgba(13, 202, 240, 0.1);
    border-bottom-color: rgba(13, 202, 240, 0.2);
}

/* Toast animation */
.toast.showing {
    opacity: 1;
}

.toast.hide {
    opacity: 0;
}

/* Close button styling */
.toast .btn-close {
    margin-left: auto;
    margin-right: 0;
    padding: 0.25rem;
    font-size: 0.75rem;
}

/* Icon styling */
.toast-header i {
    font-size: 1rem;
}

/* Dark mode support */
@media (prefers-color-scheme: dark) {
    .toast {
        background-color: rgba(33, 37, 41, 0.95);
        border-color: rgba(255, 255, 255, 0.1);
    }
    
    .toast-header {
        background-color: rgba(52, 58, 64, 0.95);
        border-bottom-color: rgba(255, 255, 255, 0.1);
    }
    
    .toast-body {
        color: #f8f9fa;
    }
}

/* Responsive adjustments */
@media (max-width: 576px) {
    .toast-container {
        top: 10px !important;
        right: 10px !important;
        left: 10px !important;
        width: auto !important;
    }
    
    .toast {
        max-width: none;
        width: 100%;
    }
}
