# Reporte de Ventas y Comisiones

## Descripción

Este módulo implementa un reporte comprehensivo de ventas y comisiones que muestra:

- Ventas por empleado y método de pago en formato de matriz
- Separación entre métodos de pago electrónicos y no electrónicos
- Cálculos de totales de ventas, nómina (comisiones) y ganancias
- Filtrado por rango de fechas y centro de costo

## Archivos Creados

1. **`src/reportes_ventas_comisiones.php`** - Controlador principal
2. **`views/reportes_ventas_comisiones.view.php`** - Vista del reporte
3. **`database_updates/add_centro_costo_to_puestos.sql`** - Script de actualización de BD

## Funcionalidades

### Filtros Obligatorios
- **Fecha de Inicio**: Fecha de inicio del rango a consultar
- **Fecha de Fin**: Fecha de fin del rango a consultar  
- **Centro de Costo**: Centro de costo para filtrar las citas

### Estructura del Reporte
- **Cabecera**: Lista de empleados (barberos) como columnas
- **Filas de Métodos Electrónicos**: NEQUI, BANCOLOMBIA, DAVIPLATA, etc.
- **Subtotal Transferencia**: Suma de todos los métodos electrónicos
- **Filas de Métodos No Electrónicos**: Efectivo, etc.
- **Totales Finales**:
  - Total ventas: Suma de todas las ventas
  - Nómina: Suma de todas las comisiones de empleados
  - Ganancias: Total ventas - Nómina

### Criterios de Filtrado
- Solo citas con `estado = 1` (activas)
- Solo citas con `fecha_fin IS NOT NULL` (finalizadas)
- Fecha de finalización dentro del rango seleccionado
- Centro de costo coincidente (si la relación existe en BD)

## Requisitos de Base de Datos

### Relación Requerida
Para que el filtro por centro de costo funcione correctamente, es necesario agregar una relación entre las tablas `puestos` y `centros_costos`.

### Instalación de la Relación

1. **Ejecutar el script SQL**:
   ```sql
   -- Ver archivo: database_updates/add_centro_costo_to_puestos.sql
   ALTER TABLE puestos 
   ADD COLUMN id_centro_costo INT NULL,
   ADD CONSTRAINT fk_puestos_centro_costo 
       FOREIGN KEY (id_centro_costo) 
       REFERENCES centros_costos(id);
   ```

2. **Asignar centros de costo a puestos existentes**:
   ```sql
   UPDATE puestos SET id_centro_costo = 1 WHERE descripcion = 'Barbero Principal';
   UPDATE puestos SET id_centro_costo = 1 WHERE descripcion = 'Barbero Auxiliar';
   -- Ajustar según los datos específicos
   ```

### Comportamiento sin la Relación
Si la columna `id_centro_costo` no existe en la tabla `puestos`:
- El reporte funcionará pero mostrará todas las citas del rango de fechas
- Se mostrará un mensaje de advertencia informando la limitación
- El filtro por centro de costo será ignorado

## Características Técnicas

### Validaciones
- Fechas obligatorias en formato YYYY-MM-DD
- Centro de costo obligatorio
- Fecha inicio ≤ fecha fin
- Validación de existencia del centro de costo

### Formato de Datos
- **Moneda**: Formato colombiano ($XX.XXX)
- **Fechas**: Formato yyyy-mm-dd con datepicker
- **Zona horaria**: America/Bogota

### Rendimiento
- Uso de JOINs optimizados para evitar consultas N+1
- Índices recomendados en `id_centro_costo`
- Consulta única para obtener todos los datos necesarios

## Integración con el Sistema

### Dependencias
- **Clases utilizadas**:
  - `App\classes\Cita`
  - `App\classes\CentroCosto`
  - `App\classes\MetodoPago`
  - `App\classes\Empleado`

### Archivos de configuración requeridos
- `src/general/validaciones.php`
- `src/general/preparar.php`
- `views/general/header.view.php`
- `views/general/footer.view.php`
- `views/general/core_js.view.php`

### Estilos y Scripts
- Bootstrap Datepicker para selección de fechas
- SweetAlert para notificaciones
- Estilos consistentes con el tema oscuro del sistema

## Uso

1. **Acceder al reporte**: Navegar a `reportes-ventas-comisiones`
2. **Seleccionar filtros**:
   - Elegir fecha de inicio y fin
   - Seleccionar centro de costo
3. **Generar reporte**: Hacer clic en "Generar Reporte"
4. **Interpretar resultados**:
   - Cada celda muestra las ventas del empleado con ese método de pago
   - Los totales se calculan automáticamente
   - Las ganancias se muestran como ventas menos comisiones

## Mantenimiento

### Actualizaciones Futuras
- Agregar filtros adicionales (por empleado, por servicio)
- Exportación a Excel/PDF
- Gráficos visuales de los datos
- Comparación entre períodos

### Consideraciones de Seguridad
- Validación de parámetros de entrada
- Uso de prepared statements
- Manejo de errores sin exposición de información sensible

## Soporte

Para problemas o mejoras, revisar:
1. Logs de errores del servidor
2. Estructura de la base de datos
3. Permisos de usuario
4. Configuración de zona horaria
